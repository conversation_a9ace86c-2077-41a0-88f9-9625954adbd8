{"assets": [{"id": "6", "layers": [{"ind": 5, "ty": 4, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [11, 12]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [22, 24]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 0]}, "o": {"a": 0, "k": 0}}]}, {"ind": 0, "ty": 4, "ks": {"s": {"a": 0, "k": [133.33, 133.33]}}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [-0.28, 0], [-0.29, -0.11], [-0.46, -0.2], [-0.95, -0.27], [-0.42, -0.16], [-0.26, -0.34], [-0.04, -0.42], [0, -0.62], [0, 0], [1.31, -1.42], [1.03, -0.54], [0, 0], [0.54, 0], [0.44, 0.23], [0, 0], [1.3, 1.41], [0, 2.4], [0, 0], [-0.04, 0.41], [-0.25, 0.34], [-0.43, 0.17], [-0.7, 0.2], [-0.67, 0.3], [-0.29, 0.11]], "o": [[0.29, -0.11], [0.28, 0], [0.29, 0.11], [0.67, 0.3], [0.69, 0.2], [0.43, 0.17], [0.25, 0.34], [0.04, 0.41], [0, 0], [0, 2.4], [-1.3, 1.41], [0, 0], [-0.43, 0.23], [-0.54, 0], [0, 0], [-1.03, -0.54], [-1.31, -1.42], [0, 0], [0, -0.62], [0.04, -0.42], [0.26, -0.34], [0.43, -0.16], [0.95, -0.27], [0.46, -0.2], [0, 0]], "v": [[7.4, 0.75], [8.25, 0.55], [9.1, 0.75], [10.22, 1.22], [12.72, 2.13], [14.4, 2.65], [15.47, 3.37], [15.87, 4.51], [15.91, 6.05], [15.91, 8.36], [13.52, 14.08], [9.63, 17], [9.58, 17.03], [8.25, 17.45], [6.92, 17.03], [6.87, 17], [2.98, 14.08], [0.59, 8.36], [0.59, 6.05], [0.63, 4.51], [1.03, 3.37], [2.1, 2.65], [3.78, 2.13], [6.28, 1.22], [7.4, 0.75]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.08, 0], [0, 1.09], [-1.09, 0], [0, -1.09]], "o": [[0, 1.09], [-1.09, 0], [0, -1.09], [1.08, 0], [0, 0]], "v": [[10.21, 6.25], [8.25, 8.21], [6.29, 6.25], [8.25, 4.29], [10.21, 6.25]]}}}, {"ty": "sh", "ks": {"a": 0, "k": {"c": true, "i": [[0, 0], [1.34, -0.79], [0.29, -0.28], [0.04, -0.37], [-0.29, -0.27], [-0.75, 0], [0, 0], [-0.44, 0.41], [0.04, 0.4], [0.18, 0.18], [0.24, 0.14]], "o": [[-1.34, -0.79], [-0.24, 0.14], [-0.18, 0.18], [-0.04, 0.4], [0.44, 0.41], [0, 0], [0.75, 0], [0.29, -0.27], [-0.04, -0.37], [-0.3, -0.28], [0, 0]], "v": [[10.44, 9.79], [6.06, 9.79], [4.96, 10.51], [4.52, 11.33], [4.95, 12.35], [6.71, 13.13], [9.79, 13.13], [11.55, 12.35], [11.98, 11.33], [11.54, 10.51], [10.44, 9.79]]}}}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "r": 2, "o": {"a": 0, "k": 100}}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}]}]}, {"id": "14", "layers": [{"ind": 13, "ty": 4, "td": 1, "parent": 11, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "sh", "ks": {"a": 1, "k": [{"t": 0, "s": [{"c": true, "i": [[0, 0], [-0.30000000000000004, 1.3999999999999986], [-0.5999999999999999, 1.4000000000000004], [-0.8000000000000003, 1.200000000000001], [-1, 1.0999999999999996], [-1.200000000000001, 0.9000000000000004], [-7.3, -2.4], [0, 0], [6, -4.3], [0, -7.4], [0, 0]], "v": [[0, 24], [0.4, 19.5], [1.7, 15.1], [3.7, 11.2], [6.5, 7.6], [9.9, 4.6], [31.4, 1.2], [31.1, 2.1], [10.5, 5.4], [1, 24], [0, 24]], "o": [[0, -1.5], [0.29999999999999993, -1.5], [0.5999999999999999, -1.299999999999999], [0.7999999999999998, -1.299999999999999], [1, -1.0999999999999996], [6.2, -4.5], [0, 0], [-7, -2.3], [-6, 4.3], [0, 0], [0, 0]]}], "h": 1}, {"t": 1, "s": [{"c": true, "i": [[0, 0], [-0.30000000000000004, 1.3999999999999986], [-0.5999999999999999, 1.4000000000000004], [-0.8000000000000003, 1.200000000000001], [-1, 1.0999999999999996], [-1.200000000000001, 0.9000000000000004], [-7.3, -2.4], [0, 0], [6, -4.3], [0, -7.4], [0, 0]], "v": [[0, 24], [0.4, 19.5], [1.7, 15.1], [3.7, 11.2], [6.5, 7.6], [9.9, 4.6], [31.4, 1.2], [31.1, 2.1], [10.5, 5.4], [1, 24], [0, 24]], "o": [[0, -1.5], [0.29999999999999993, -1.5], [0.5999999999999999, -1.299999999999999], [0.7999999999999998, -1.299999999999999], [1, -1.0999999999999996], [6.2, -4.5], [0, 0], [-7, -2.3], [-6, 4.3], [0, 0], [0, 0]]}], "h": 1}, {"t": 2, "s": [{"c": true, "i": [[0, 0], [-0.30000000000000004, 1.5], [-0.5999999999999999, 1.4000000000000004], [-0.8000000000000003, 1.3000000000000007], [-1, 1.0999999999999996], [-1.200000000000001, 0.9000000000000004], [-7.3, -2.4], [0, 0], [6, -4.3], [0, -7.4], [0, 0]], "v": [[0, 24], [0.4, 19.4], [1.7, 15.1], [3.7, 11.1], [6.5, 7.5], [9.9, 4.5], [31.5, 1.2], [31.2, 2.1], [10.5, 5.3], [1, 24], [0, 24]], "o": [[0, -1.5], [0.29999999999999993, -1.3999999999999986], [0.5999999999999999, -1.4000000000000004], [0.7999999999999998, -1.299999999999999], [1, -1.0999999999999996], [6.2, -4.5], [0, 0], [-7, -2.3], [-6, 4.3], [0, 0], [0, 0]]}], "h": 1}, {"t": 3, "s": [{"c": true, "i": [[0, 0], [-6.3, 4.5], [-1.4000000000000004, 0.5999999999999996], [-1.3999999999999986, 0.30000000000000004], [-1.5, 0.1], [-1.5, -0.2], [-1.5, -0.5], [0, 0], [6, -4.3], [0, -7.4], [0, 0]], "v": [[0, 23.9], [10, 4.5], [14, 2.2], [18.2, 0.7], [22.7, 0], [27.2, 0.2], [31.6, 1.2], [31.3, 2.2], [10.6, 5.3], [1, 23.9], [0, 23.9]], "o": [[0, -7.7], [1.3000000000000007, -0.8999999999999999], [1.3000000000000007, -0.7000000000000002], [1.5, -0.39999999999999997], [1.5, -0.1], [1.5, 0.2], [0, 0], [-7, -2.4], [-6, 4.3], [0, 0], [0, 0]]}], "h": 1}, {"t": 4, "s": [{"c": true, "i": [[0, 0], [-0.2, 1.5], [-0.5, 1.3999999999999986], [-0.7999999999999998, 1.3000000000000007], [-1, 1.0999999999999996], [-1.299999999999999, 0.8999999999999995], [-7.3, -2.5], [0, 0], [6.1, -4.3], [0, -7.4], [0, 0]], "v": [[0, 23.9], [0.4, 19.3], [1.7, 15], [3.8, 11], [6.6, 7.4], [10.1, 4.4], [31.8, 1.3], [31.5, 2.2], [10.7, 5.2], [1, 23.9], [0, 23.9]], "o": [[0, -1.5], [0.29999999999999993, -1.4000000000000021], [0.5999999999999999, -1.4000000000000004], [0.7999999999999998, -1.3000000000000007], [1.1000000000000005, -1.1000000000000005], [6.3, -4.5], [0, 0], [-7, -2.4], [-6.1, 4.3], [0, 0], [0, 0]]}], "h": 1}, {"t": 5, "s": [{"c": true, "i": [[0, 0], [-6.4, 4.5], [-1.3999999999999986, 0.6000000000000001], [-1.5, 0.30000000000000004], [-1.5, 0.1], [-1.5, -0.3], [-1.5, -0.4999999999999999], [0, 0], [6.1, -4.3], [0.1, -7.5], [0, 0]], "v": [[0, 23.8], [10.2, 4.3], [14.2, 2], [18.6, 0.6], [23.1, 0], [27.6, 0.3], [32.1, 1.4], [31.7, 2.3], [10.8, 5.1], [1, 23.8], [0, 23.8]], "o": [[0.1, -7.8], [1.3000000000000007, -0.8999999999999999], [1.4000000000000004, -0.6000000000000001], [1.5, -0.39999999999999997], [1.5, -0.1], [1.5, 0.2], [0, 0], [-7, -2.5], [-6.1, 4.3], [0, 0], [0, 0]]}], "h": 1}, {"t": 6, "s": [{"c": true, "i": [[0, 0], [-6.4, 4.4], [-1.4000000000000004, 0.6000000000000001], [-1.5, 0.30000000000000004], [-1.5, 0.1], [-1.5, -0.19999999999999998], [-1.5, -0.5], [0, 0], [6.2, -4.2], [0.1, -7.5], [0, 0]], "v": [[0, 23.7], [10.4, 4.2], [14.4, 2], [18.8, 0.6], [23.3, 0], [27.9, 0.3], [32.4, 1.5], [32, 2.4], [11, 5], [1, 23.7], [0, 23.7]], "o": [[0.1, -7.8], [1.299999999999999, -0.9000000000000004], [1.5, -0.6000000000000001], [1.5, -0.39999999999999997], [1.5999999999999979, 0], [1.5, 0.3], [0, 0], [-7, -2.6], [-6.2, 4.2], [0, 0], [0, 0]]}], "h": 1}, {"t": 7, "s": [{"c": true, "i": [[0, 0], [-6.5, 4.4], [-1.3999999999999986, 0.6000000000000001], [-1.5, 0.30000000000000004], [-1.5, 0], [-1.5, -0.19999999999999998], [-1.5000000000000036, -0.6000000000000001], [0, 0], [6.3, -4.2], [0.1, -7.5], [0, 0]], "v": [[0, 23.6], [10.6, 4.1], [14.7, 1.9], [19.1, 0.5], [23.6, 0], [28.2, 0.3], [32.7, 1.6], [32.4, 2.5], [11.2, 4.9], [1, 23.6], [0, 23.6]], "o": [[0.1, -7.9], [1.3000000000000007, -0.8999999999999995], [1.4000000000000021, -0.5999999999999999], [1.5, -0.3], [1.5999999999999979, -0.1], [1.5, 0.3], [0, 0], [-7, -2.7], [-6.3, 4.2], [0, 0], [0, 0]]}], "h": 1}, {"t": 8, "s": [{"c": true, "i": [[0, 0], [-0.39999999999999997, 1.5999999999999979], [-0.6000000000000001, 1.4000000000000004], [-0.8999999999999999, 1.299999999999999], [-1.2000000000000002, 1.1000000000000005], [-1.3000000000000007, 0.8999999999999999], [-7.3, -3], [0, 0], [6.4, -4.2], [0.2, -7.6], [0, 0]], "v": [[0, 23.5], [0.6, 18.8], [2, 14.4], [4.3, 10.4], [7.3, 6.8], [10.9, 3.9], [33.2, 1.8], [32.8, 2.7], [11.4, 4.7], [1, 23.5], [0, 23.5]], "o": [[0, -1.6000000000000014], [0.30000000000000004, -1.5], [0.6000000000000001, -1.4000000000000004], [0.7999999999999998, -1.3000000000000007], [1.1000000000000005, -1.0999999999999996], [6.6, -4.3], [0, 0], [-7, -2.9], [-6.4, 4.2], [0, 0], [0, 0]]}], "h": 1}, {"t": 9, "s": [{"c": true, "i": [[0, 0], [-0.39999999999999997, 1.5], [-0.7000000000000002, 1.4000000000000004], [-0.9000000000000004, 1.200000000000001], [-1.1000000000000005, 1.1000000000000005], [-1.3999999999999986, 0.8999999999999995], [-7.3, -3.2], [0, 0], [6.5, -4.1], [0.2, -7.7], [0, 0]], "v": [[0, 23.3], [0.6, 18.6], [2.1, 14.2], [4.4, 10.2], [7.4, 6.6], [11.2, 3.7], [33.7, 2.1], [33.3, 2.9], [11.7, 4.5], [1, 23.4], [0, 23.3]], "o": [[0, -1.6000000000000014], [0.30000000000000004, -1.5], [0.6000000000000001, -1.3999999999999986], [0.8999999999999995, -1.299999999999999], [1.1999999999999993, -1.0999999999999996], [6.8, -4.3], [0, 0], [-7, -3.1], [-6.5, 4.1], [0, 0], [0, 0]]}], "h": 1}, {"t": 10, "s": [{"c": true, "i": [[0, 0], [-0.39999999999999997, 1.6000000000000014], [-0.6000000000000001, 1.4000000000000004], [-0.8999999999999995, 1.299999999999999], [-1.2000000000000002, 1.0999999999999996], [-1.4000000000000004, 0.7999999999999998], [-7.3, -3.5], [0, 0], [6.6, -4], [0.3, -7.8], [0, 0]], "v": [[0, 23.2], [0.7, 18.4], [2.2, 14], [4.6, 9.9], [7.8, 6.4], [11.6, 3.5], [34.4, 2.4], [33.9, 3.2], [12.1, 4.3], [1, 23.2], [0, 23.2]], "o": [[0.1, -1.5999999999999979], [0.30000000000000004, -1.5], [0.6999999999999997, -1.5], [1, -1.3000000000000007], [1.1000000000000005, -1.1000000000000005], [6.9, -4.2], [0, 0], [-7, -3.4], [-6.6, 4], [0, 0], [0, 0]]}], "h": 1}, {"t": 11, "s": [{"c": true, "i": [[0, 0], [-0.39999999999999997, 1.5], [-0.7, 1.4000000000000004], [-0.8999999999999999, 1.3000000000000007], [-1.1999999999999993, 1.0999999999999996], [-1.4000000000000004, 0.7999999999999998], [-7.3, -3.8], [0, 0], [6.8, -3.9], [0.3, -7.9], [0, 0]], "v": [[0, 23], [0.7, 18.2], [2.4, 13.7], [4.8, 9.6], [8.1, 6], [12, 3.2], [35.1, 2.7], [34.6, 3.6], [12.5, 4], [1, 23], [0, 23]], "o": [[0.1, -1.6000000000000014], [0.40000000000000013, -1.5999999999999979], [0.6000000000000001, -1.5], [1, -1.299999999999999], [1.200000000000001, -1], [7.1, -4.1], [0, 0], [-7, -3.6], [-6.8, 3.9], [0, 0], [0, 0]]}], "h": 1}, {"t": 12, "s": [{"c": true, "i": [[0, 0], [-7.3, 4], [-1.5, 0.5], [-1.6000000000000014, 0.1], [-1.6999999999999993, -0.2], [-1.5, -0.5], [-1.3999999999999986, -0.8000000000000003], [0, 0], [7, -3.8], [0.4, -8], [0, 0]], "v": [[0, 22.7], [12.5, 2.9], [17, 1], [21.8, 0.1], [26.7, 0.2], [31.4, 1.2], [35.9, 3.2], [35.4, 4], [13, 3.8], [1, 22.8], [0, 22.7]], "o": [[0.5, -8.3], [1.5, -0.7999999999999998], [1.6000000000000014, -0.5], [1.5999999999999979, -0.1], [1.6000000000000014, 0.2], [1.6000000000000014, 0.5], [0, 0], [-6.9, -4], [-7, 3.8], [0, 0], [0, 0]]}], "h": 1}, {"t": 13, "s": [{"c": true, "i": [[0, 0], [-7.6, 3.8], [-1.6000000000000014, 0.3999999999999999], [-1.6999999999999993, 0.1], [-1.6000000000000014, -0.3], [-1.5999999999999979, -0.6], [-1.3999999999999986, -0.9000000000000004], [0, 0], [7.3, -3.7], [0.5, -8.1], [0, 0]], "v": [[0.1, 22.4], [13.1, 2.6], [17.8, 0.8], [22.7, 0], [27.6, 0.3], [32.4, 1.5], [36.9, 3.7], [36.4, 4.6], [13.6, 3.5], [1, 22.5], [0.1, 22.4]], "o": [[0.6, -8.5], [1.5, -0.8], [1.5999999999999979, -0.4], [1.6000000000000014, 0], [1.5999999999999979, 0.2], [1.5, 0.6000000000000001], [0, 0], [-6.9, -4.4], [-7.3, 3.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 14, "s": [{"c": true, "i": [[0, 0], [-0.5000000000000001, 1.5999999999999979], [-0.7999999999999998, 1.5], [-1.1000000000000005, 1.299999999999999], [-1.3000000000000007, 1], [-1.5999999999999996, 0.6999999999999997], [-7, -5], [0, 0], [7.5, -3.5], [0.7, -8.3], [0, 0]], "v": [[0.1, 22.1], [1.1, 17.1], [3, 12.4], [5.9, 8.3], [9.5, 4.8], [13.9, 2.2], [37.9, 4.5], [37.4, 5.2], [14.3, 3.1], [1, 22.2], [0.1, 22.1]], "o": [[0.1, -1.7000000000000028], [0.3999999999999999, -1.6000000000000014], [0.7999999999999998, -1.5], [1.0999999999999996, -1.3000000000000007], [1.4000000000000004, -1], [7.8, -3.6], [0, 0], [-6.8, -4.8], [-7.5, 3.5], [0, 0], [0, 0]]}], "h": 1}, {"t": 15, "s": [{"c": true, "i": [[0, 0], [-8.2, 3.4], [-1.6999999999999993, 0.29999999999999993], [-1.6999999999999993, -0.1], [-1.6999999999999993, -0.4], [-1.5, -0.8], [-1.3999999999999986, -1.1000000000000005], [0, 0], [7.8, -3.3], [0.8, -8.4], [0, 0]], "v": [[0.1, 21.7], [14.7, 1.9], [19.7, 0.4], [24.8, 0.1], [29.9, 0.8], [34.7, 2.6], [39.1, 5.4], [38.5, 6.1], [15.1, 2.7], [1.1, 21.8], [0.1, 21.7]], "o": [[0.9, -8.8], [1.6000000000000014, -0.7], [1.6999999999999993, -0.30000000000000004], [1.6999999999999993, 0], [1.6000000000000014, 0.3999999999999999], [1.5, 0.6999999999999997], [0, 0], [-6.6, -5.3], [-7.8, 3.3], [0, 0], [0, 0]]}], "h": 1}, {"t": 16, "s": [{"c": true, "i": [[0, 0], [-8.5, 3.1], [-1.7999999999999972, 0.3], [-1.8000000000000007, -0.1], [-1.6999999999999993, -0.6], [-1.5, -0.8999999999999999], [-1.2999999999999972, -1.2000000000000002], [0, 0], [8.2, -3], [1, -8.6], [0, 0]], "v": [[0.2, 21.2], [15.7, 1.5], [20.9, 0.2], [26.2, 0.1], [31.3, 1.2], [36.1, 3.3], [40.4, 6.5], [39.8, 7.2], [16.1, 2.4], [1.1, 21.3], [0.2, 21.2]], "o": [[1, -9], [1.6999999999999993, -0.6], [1.8000000000000007, -0.2], [1.6999999999999993, 0.19999999999999998], [1.6999999999999993, 0.5], [1.5, 0.9000000000000004], [0, 0], [-6.3, -6], [-8.2, 3], [0, 0], [0, 0]]}], "h": 1}, {"t": 17, "s": [{"c": true, "i": [[0, 0], [-8.9, 2.7], [-1.8000000000000007, 0.1], [-1.8000000000000007, -0.19999999999999998], [-1.6999999999999993, -0.5999999999999999], [-1.5, -1], [-1.2999999999999972, -1.4000000000000004], [0, 0], [8.6, -2.6], [1.2, -8.9], [0, 0]], "v": [[0.2, 20.7], [16.9, 1.1], [22.3, 0.1], [27.7, 0.3], [32.9, 1.7], [37.6, 4.3], [41.8, 7.9], [41.1, 8.6], [17.2, 2], [1.2, 20.8], [0.2, 20.7]], "o": [[1.3, -9.2], [1.8000000000000007, -0.5000000000000001], [1.8000000000000007, -0.1], [1.8000000000000007, 0.3], [1.6000000000000014, 0.7], [1.5, 1], [0, 0], [-6, -6.7], [-8.6, 2.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 18, "s": [{"c": true, "i": [[0, 0], [-9.4, 2.3], [-1.9000000000000021, 0], [-1.8000000000000007, -0.39999999999999997], [-1.6999999999999957, -0.8], [-1.3999999999999986, -1.2000000000000002], [-1.0999999999999943, -1.5999999999999996], [0, 0], [9, -2.2], [1.5, -9.1], [0, 0]], "v": [[0.3, 20.1], [18.4, 0.7], [24.1, 0], [29.6, 0.6], [34.8, 2.5], [39.4, 5.5], [43.3, 9.7], [42.5, 10.3], [18.6, 1.6], [1.3, 20.3], [0.3, 20.1]], "o": [[1.6, -9.5], [1.9000000000000021, -0.49999999999999994], [1.7999999999999972, 0], [1.7999999999999972, 0.4], [1.6000000000000014, 0.7999999999999998], [1.5, 1.2999999999999998], [0, 0], [-5.5, -7.5], [-9, 2.2], [0, 0], [0, 0]]}], "h": 1}, {"t": 19, "s": [{"c": true, "i": [[0, 0], [-0.8000000000000003, 1.799999999999999], [-1.2000000000000002, 1.5], [-1.5, 1.1999999999999993], [-1.8000000000000007, 0.7000000000000002], [-2, 0.3], [-5, -8.7], [0, 0], [9.5, -1.6], [1.9, -9.4], [0, 0]], "v": [[0.4, 19.4], [2.2, 13.8], [5.3, 8.9], [9.4, 4.9], [14.4, 2], [20.1, 0.3], [44.8, 12.1], [44, 12.5], [20.3, 1.3], [1.4, 19.6], [0.4, 19.4]], "o": [[0.4, -2], [0.8999999999999999, -1.8000000000000007], [1.2000000000000002, -1.5], [1.5999999999999996, -1.2000000000000002], [1.799999999999999, -0.8], [9.9, -1.6], [0, 0], [-4.8, -8.3], [-9.5, 1.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 20, "s": [{"c": true, "i": [[0, 0], [-10.4, 0.8], [-2, -0.30000000000000004], [-1.7999999999999972, -0.8000000000000003], [-1.6000000000000014, -1.2000000000000002], [-1.1999999999999957, -1.5999999999999996], [-0.7999999999999972, -1.9000000000000004], [0, 0], [10, -0.8], [2.3, -9.8], [0, 0]], "v": [[0.6, 18.5], [22.1, 0.1], [28.2, 0.4], [34, 2.2], [39.1, 5.3], [43.3, 9.6], [46.3, 15], [45.4, 15.4], [22.2, 1], [1.6, 18.8], [0.6, 18.5]], "o": [[2.4, -10.2], [2.099999999999998, -0.2], [2, 0.4], [1.7999999999999972, 0.7999999999999998], [1.6000000000000014, 1.2999999999999998], [1.2000000000000028, 1.700000000000001], [0, 0], [-3.8, -9.3], [-10, 0.8], [0, 0], [0, 0]]}], "h": 1}, {"t": 21, "s": [{"c": true, "i": [[0, 0], [-11, -0.3], [-2.1000000000000014, -0.6], [-1.8000000000000043, -1.1], [-1.3999999999999986, -1.5], [-1, -1.8999999999999986], [-0.5, -2.099999999999998], [0, 0], [10.6, 0.3], [2.8, -10.2], [0, 0]], "v": [[0.9, 17.5], [24.6, 0], [31, 1], [36.7, 3.6], [41.5, 7.6], [45.2, 12.7], [47.4, 18.7], [46.5, 18.9], [24.6, 1], [1.8, 17.8], [0.9, 17.5]], "o": [[3, -10.6], [2.1999999999999993, 0.1], [2, 0.6000000000000001], [1.7999999999999972, 1.1], [1.3999999999999986, 1.5], [1, 1.8000000000000007], [0, 0], [-2.3, -10.3], [-10.6, -0.3], [0, 0], [0, 0]]}], "h": 1}, {"t": 22, "s": [{"c": true, "i": [[0, 0], [-11.5, -1.7], [-2, -0.9000000000000001], [-1.7000000000000028, -1.3999999999999995], [-1.2999999999999972, -1.799999999999999], [-0.6999999999999957, -2.1000000000000014], [-0.10000000000000142, -2.3000000000000007], [0, 0], [11.1, 1.6], [3.6, -10.6], [0, 0]], "v": [[1.2, 16.4], [27.5, 0.3], [34, 2.2], [39.6, 5.8], [44, 10.7], [46.9, 16.6], [48, 23.3], [47, 23.4], [27.4, 1.2], [2.2, 16.7], [1.2, 16.4]], "o": [[3.7, -11], [2.3000000000000007, 0.3], [2.1000000000000014, 1], [1.6999999999999957, 1.4000000000000004], [1.2000000000000028, 1.8000000000000007], [0.6000000000000014, 2.099999999999998], [0, 0], [-0.3, -11.2], [-11.1, -1.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 23, "s": [{"c": true, "i": [[0, 0], [-11.9, -3.6], [-1.8999999999999986, -1.2999999999999998], [-1.5, -1.8000000000000007], [-0.9000000000000057, -2.1000000000000014], [-0.19999999999999574, -2.3999999999999986], [0.5, -2.3999999999999986], [0, 0], [11.4, 3.5], [4.5, -11.1], [0, 0]], "v": [[1.8, 15], [31, 1], [37.5, 4.1], [42.7, 8.9], [46.2, 14.8], [47.9, 21.7], [47.5, 28.9], [46.6, 28.7], [30.7, 2], [2.6, 15.4], [1.8, 15]], "o": [[4.7, -11.5], [2.3999999999999986, 0.7], [2, 1.3000000000000007], [1.3999999999999986, 1.799999999999999], [0.8999999999999986, 2.1999999999999993], [0.20000000000000284, 2.3000000000000007], [0, 0], [2.4, -11.7], [-11.4, -3.5], [0, 0], [0, 0]]}], "h": 1}, {"t": 24, "s": [{"c": true, "i": [[0, 0], [-8.6, 0.5], [-2.6999999999999993, -0.8], [-2.1999999999999957, -1.6999999999999997], [-1.6000000000000014, -2.4000000000000004], [4, -7.7], [0, 0], [4.5, 6.9], [8.3, -0.5], [3.6, -7.4], [0, 0]], "v": [[2.5, 13.4], [22.5, 0], [30.9, 1], [38.3, 4.8], [44.1, 10.9], [45.3, 35], [44.5, 34.6], [43.3, 11.4], [22.6, 1], [3.3, 13.9], [2.5, 13.4]], "o": [[3.8, -7.8], [2.8999999999999986, -0.2], [2.700000000000003, 0.8], [2.3000000000000043, 1.6000000000000005], [4.7, 7.2], [0, 0], [3.8, -7.4], [-4.5, -6.9], [-8.3, 0.5], [0, 0], [0, 0]]}], "h": 1}, {"t": 25, "s": [{"c": true, "i": [[0, 0], [-9.2, -0.9], [-3.2, -8.7], [0.10000000000000142, -3], [1.1000000000000014, -2.799999999999997], [2.200000000000003, -2.1999999999999957], [0, 0], [3, 8.4], [8.9, 0.8], [4.6, -7.7], [0, 0]], "v": [[3.4, 11.7], [26.3, 0.1], [46.6, 15.8], [48, 24.7], [46.1, 33.4], [41, 40.9], [40.4, 40.2], [45.7, 16.1], [26.2, 1.1], [4.2, 12.2], [3.4, 11.7]], "o": [[4.7, -8], [9.2, 0.9], [1.1000000000000014, 2.8999999999999986], [-0.10000000000000142, 3], [-1.2000000000000028, 2.700000000000003], [0, 0], [6.3, -6.3], [-3, -8.4], [-8.9, -0.8], [0, 0], [0, 0]]}], "h": 1}, {"t": 26, "s": [{"c": true, "i": [[0, 0], [-9.6, -2.5], [-1.1, -9.9], [0.8999999999999986, -3], [2, -2.5], [3, -1.5], [0, 0], [1, 9.5], [9.3, 2.4], [5.6, -7.8], [0, 0]], "v": [[4.5, 10.1], [30.1, 0.8], [47.9, 21.4], [47, 31], [42.5, 39.4], [34.9, 45.4], [34.4, 44.5], [46.9, 21.5], [29.8, 1.7], [5.3, 10.6], [4.5, 10.1]], "o": [[5.8, -8.1], [9.6, 2.5], [0.3999999999999986, 3.3000000000000007], [-0.8999999999999986, 3.1000000000000014], [-2.1000000000000014, 2.3999999999999986], [0, 0], [8.5, -4.3], [-1, -9.5], [-9.3, -2.4], [0, 0], [0, 0]]}], "h": 1}, {"t": 27, "s": [{"c": true, "i": [[0, 0], [-9.7, -4.3], [1.3, -10.5], [1.8000000000000043, -2.9000000000000057], [2.8000000000000043, -1.8999999999999986], [3.5, -0.6000000000000014], [0, 0], [-1.3, 10.1], [9.3, 4.1], [6.6, -7.8], [0, 0]], "v": [[5.7, 8.5], [33.7, 2], [47.8, 27], [44.4, 36.7], [37.4, 43.9], [27.8, 47.7], [27.7, 46.7], [46.9, 26.9], [33.3, 2.9], [6.4, 9.1], [5.7, 8.5]], "o": [[6.9, -8.1], [9.7, 4.3], [-0.3999999999999986, 3.5], [-1.7999999999999972, 2.799999999999997], [-2.8999999999999986, 1.8999999999999986], [0, 0], [10.1, -1.6], [1.3, -10.1], [-9.3, -4.1], [0, 0], [0, 0]]}], "h": 1}, {"t": 28, "s": [{"c": true, "i": [[0, 0], [-9.5, -6], [3.8, -10.6], [2.5, -2.5], [3.3000000000000007, -1.2000000000000028], [3.6999999999999993, 0.5], [0, 0], [-3.6, 10.2], [9.2, 5.7], [7.6, -7.7], [0, 0]], "v": [[6.9, 7.2], [36.7, 3.7], [46.6, 32], [40.8, 41.1], [31.8, 46.7], [21, 47.8], [21.2, 46.9], [45.7, 31.7], [36.2, 4.5], [7.6, 7.9], [6.9, 7.2]], "o": [[7.9, -8], [9.5, 6], [-1.3000000000000043, 3.5], [-2.5999999999999943, 2.5], [-3.400000000000002, 1.0999999999999943], [0, 0], [10.7, 1.3], [3.6, -10.2], [-9.2, -5.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 29, "s": [{"c": true, "i": [[0, 0], [-3.5999999999999996, 0.8999999999999999], [-3.6000000000000014, -0.8], [-3, -2.5], [6, -10.2], [11, 4.4], [0, 0], [-5.8, 9.8], [8.8, 7.2], [8.5, -7.5], [0, 0]], "v": [[8, 6.1], [18, 0.8], [29, 0.6], [39.2, 5.5], [44.7, 36.2], [15.2, 46.3], [15.5, 45.4], [43.8, 35.7], [38.6, 6.2], [8.7, 6.8], [8, 6.1]], "o": [[2.9000000000000004, -2.5999999999999996], [3.6000000000000014, -1], [3.700000000000003, 0.7999999999999999], [9.1, 7.5], [-6, 10.2], [0, 0], [10.6, 4.2], [5.8, -9.8], [-8.8, -7.2], [0, 0], [0, 0]]}], "h": 1}, {"t": 30, "s": [{"c": true, "i": [[0, 0], [-8.6, -8.9], [-1.1000000000000014, -3.6999999999999993], [0.7999999999999972, -3.8000000000000007], [2.700000000000003, -3.1000000000000014], [10.2, 7], [0, 0], [-7.6, 9], [8.2, 8.5], [9.3, -7.3], [0, 0]], "v": [[9.1, 5.2], [41.2, 7.3], [47.1, 17.5], [47.5, 29], [42.3, 39.5], [10.4, 43.8], [11, 43], [41.6, 38.9], [40.6, 8], [9.7, 5.9], [9.1, 5.2]], "o": [[9.7, -7.6], [2.8999999999999986, 3.000000000000001], [1, 3.6999999999999993], [-0.7999999999999972, 3.700000000000003], [-8, 9.4], [0, 0], [9.8, 6.7], [7.6, -9], [-8.2, -8.5], [0, 0], [0, 0]]}], "h": 1}, {"t": 31, "s": [{"c": true, "i": [[0, 0], [-7.9, -10], [9.6, -8.4], [3.8999999999999986, -0.7999999999999972], [3.8000000000000007, 1.1000000000000014], [3.000000000000001, 3.0999999999999943], [0, 0], [-9.2, 8.1], [7.6, 9.6], [10, -7.1], [0, 0]], "v": [[10.1, 4.4], [42.8, 9.1], [39.8, 42], [29, 47.4], [17.2, 47], [6.8, 40.7], [7.5, 40.1], [39.2, 41.3], [42.1, 9.7], [10.7, 5.2], [10.1, 4.4]], "o": [[10.4, -7.4], [7.9, 10], [-3.1999999999999957, 2.799999999999997], [-3.8999999999999986, 0.8999999999999986], [-3.799999999999999, -1.1000000000000014], [0, 0], [8.5, 8.8], [9.2, -8.1], [-7.6, -9.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 32, "s": [{"c": true, "i": [[0, 0], [-4.099999999999998, 0.2], [-3.6999999999999957, -1.8], [-2.3999999999999986, -3.7], [11, -7.3], [7.4, 10.9], [0, 0], [-10.5, 7], [6.9, 10.6], [10.6, -6.8], [0, 0]], "v": [[11, 3.8], [22.9, 0], [34.8, 2.6], [44.1, 10.9], [37.3, 44], [4.1, 37.5], [4.9, 36.9], [36.8, 43.2], [43.3, 11.4], [11.6, 4.6], [11, 3.8]], "o": [[3.6999999999999993, -2.4], [4.100000000000001, -0.1], [3.700000000000003, 1.8000000000000003], [7.2, 11], [-11, 7.3], [0, 0], [7.1, 10.5], [10.5, -7], [-6.9, -10.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 33, "s": [{"c": true, "i": [[0, 0], [-7.8, -6.2], [2.9, -9.5], [9.9, -0.7], [4.2, 9], [0, 0], [-9.5, 0.6], [-2.8, 9.1], [7.4, 5.9], [8.2, -4.8], [0, 0]], "v": [[11.9, 3.3], [38.9, 5.2], [46.9, 31.1], [25.6, 47.9], [2.3, 34.2], [3.1, 33.8], [25.6, 47], [46, 30.9], [38.3, 6], [12.4, 4.1], [11.9, 3.3]], "o": [[8.6, -5], [7.8, 6.2], [-2.9, 9.5], [-9.9, 0.7], [0, 0], [4, 8.6], [9.5, -0.6], [2.8, -9.1], [-7.4, -5.9], [0, 0], [0, 0]]}], "h": 1}, {"t": 34, "s": [{"c": true, "i": [[0, 0], [-7.5, -6.8], [3.9, -9.4], [10.1, 0.5], [3, 9.7], [0, 0], [-9.7, -0.5], [-3.7, 9], [7.2, 6.5], [8.6, -4.6], [0, 0]], "v": [[12.7, 2.8], [40.1, 6.2], [46.2, 33.2], [22.8, 48], [1, 31], [2, 30.7], [22.9, 47], [45.3, 32.8], [39.5, 6.9], [13.1, 3.7], [12.7, 2.8]], "o": [[8.9, -4.8], [7.5, 6.8], [-3.9, 9.4], [-10.1, -0.5], [0, 0], [2.8, 9.3], [9.7, 0.5], [3.7, -9], [-7.2, -6.5], [0, 0], [0, 0]]}], "h": 1}, {"t": 35, "s": [{"c": true, "i": [[0, 0], [-7.3, -7.4], [4.7, -9.2], [10.2, 1.6], [1.7, 10.2], [0, 0], [-9.8, -1.5], [-4.5, 8.8], [7, 7.1], [8.9, -4.4], [0, 0]], "v": [[13.4, 2.5], [41.1, 7.2], [45.4, 34.9], [20.3, 47.7], [0.3, 28], [1.3, 27.8], [20.5, 46.8], [44.5, 34.5], [40.4, 7.8], [13.8, 3.3], [13.4, 2.5]], "o": [[9.3, -4.6], [7.3, 7.4], [-4.7, 9.2], [-10.2, -1.6], [0, 0], [1.7, 9.8], [9.8, 1.5], [4.5, -8.8], [-7, -7.1], [0, 0], [0, 0]]}], "h": 1}, {"t": 36, "s": [{"c": true, "i": [[0, 0], [-7, -7.9], [5.5, -9], [10.2, 2.6], [0.5, 10.5], [0, 0], [-9.8, -2.5], [-5.3, 8.6], [6.7, 7.6], [9.2, -4.2], [0, 0]], "v": [[14.1, 2.1], [42, 8.1], [44.5, 36.5], [18.1, 47.3], [0, 25.2], [1, 25.1], [18.3, 46.3], [43.7, 36], [41.2, 8.7], [14.5, 3], [14.1, 2.1]], "o": [[9.6, -4.4], [7, 7.9], [-5.5, 9], [-10.2, -2.6], [0, 0], [0.5, 10.1], [9.8, 2.5], [5.3, -8.6], [-6.7, -7.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 37, "s": [{"c": true, "i": [[0, 0], [-6.7, -8.4], [6.2, -8.7], [10.1, 3.5], [-0.6, 10.7], [0, 0], [-9.7, -3.4], [-5.9, 8.4], [6.4, 8], [9.5, -4], [0, 0]], "v": [[14.7, 1.9], [42.7, 9], [43.6, 37.9], [16.1, 46.7], [0, 22.6], [1, 22.7], [16.4, 45.7], [42.8, 37.3], [42, 9.6], [15.1, 2.8], [14.7, 1.9]], "o": [[9.9, -4.2], [6.7, 8.4], [-6.2, 8.7], [-10.1, -3.5], [0, 0], [-0.6, 10.3], [9.7, 3.4], [5.9, -8.4], [-6.4, -8], [0, 0], [0, 0]]}], "h": 1}, {"t": 38, "s": [{"c": true, "i": [[0, 0], [-6.4, -8.8], [6.8, -8.5], [9.9, 4.4], [-1.7, 10.7], [0, 0], [-9.5, -4.2], [-6.5, 8.1], [6.2, 8.4], [9.7, -3.8], [0, 0]], "v": [[15.3, 1.6], [43.4, 9.8], [42.7, 39.1], [14.3, 45.9], [0.3, 20.2], [1.2, 20.4], [14.7, 45.1], [41.9, 38.5], [42.6, 10.4], [15.6, 2.5], [15.3, 1.6]], "o": [[10.1, -4], [6.4, 8.8], [-6.8, 8.5], [-9.9, -4.4], [0, 0], [-1.6, 10.3], [9.5, 4.2], [6.5, -8.1], [-6.2, -8.4], [0, 0], [0, 0]]}], "h": 1}, {"t": 39, "s": [{"c": true, "i": [[0, 0], [-6.1, -9.2], [7.4, -8.2], [9.7, 5.2], [-2.7, 10.7], [0, 0], [-9.3, -5], [-7.1, 7.8], [5.9, 8.8], [10, -3.6], [0, 0]], "v": [[15.8, 1.4], [43.9, 10.6], [41.8, 40.1], [12.7, 45.1], [0.7, 18.1], [1.7, 18.3], [13.1, 44.3], [41.1, 39.5], [43.1, 11.2], [16.1, 2.3], [15.8, 1.4]], "o": [[10.4, -3.8], [6.1, 9.2], [-7.4, 8.2], [-9.7, -5.2], [0, 0], [-2.6, 10.3], [9.3, 5], [7.1, -7.8], [-5.9, -8.8], [0, 0], [0, 0]]}], "h": 1}, {"t": 40, "s": [{"c": true, "i": [[0, 0], [-5.9, -9.5], [7.9, -7.9], [9.5, 5.9], [-3.7, 10.5], [0, 0], [-9.1, -5.7], [-7.6, 7.5], [5.6, 9.1], [10.2, -3.4], [0, 0]], "v": [[16.3, 1.3], [44.4, 11.4], [40.9, 41], [11.2, 44.3], [1.3, 16.1], [2.2, 16.5], [11.7, 43.5], [40.2, 40.4], [43.6, 11.9], [16.6, 2.2], [16.3, 1.3]], "o": [[10.6, -3.6], [5.9, 9.5], [-7.9, 7.9], [-9.5, -5.9], [0, 0], [-3.5, 10.1], [9.1, 5.7], [7.6, -7.5], [-5.6, -9.1], [0, 0], [0, 0]]}], "h": 1}, {"t": 41, "s": [{"c": true, "i": [[0, 0], [-5.6, -9.8], [8.4, -7.5], [9.2, 6.6], [-4.5, 10.3], [0, 0], [-8.8, -6.4], [-8.1, 7.2], [5.4, 9.4], [10.3, -3.3], [0, 0]], "v": [[16.8, 1.1], [44.9, 12.1], [40, 41.9], [9.9, 43.4], [2, 14.4], [2.9, 14.8], [10.5, 42.7], [39.4, 41.1], [44, 12.6], [17.1, 2], [16.8, 1.1]], "o": [[10.8, -3.4], [5.6, 9.8], [-8.4, 7.5], [-9.2, -6.6], [0, 0], [-4.4, 9.9], [8.8, 6.4], [8.1, -7.2], [-5.4, -9.4], [0, 0], [0, 0]]}], "h": 1}, {"t": 42, "s": [{"c": true, "i": [[0, 0], [-5.3, -10.1], [8.8, -7.2], [8.8, 7.2], [-5.3, 10.1], [0, 0], [-8.5, -6.9], [-8.5, 6.9], [5.1, 9.7], [10.5, -3.1], [0, 0]], "v": [[17.2, 1], [45.2, 12.8], [39.2, 42.6], [8.8, 42.6], [2.8, 12.8], [3.6, 13.2], [9.4, 41.8], [38.6, 41.8], [44.4, 13.3], [17.5, 1.9], [17.2, 1]], "o": [[11, -3.2], [5.3, 10.1], [-8.8, 7.2], [-8.8, -7.2], [0, 0], [-5.1, 9.7], [8.5, 6.9], [8.5, -6.9], [-5.1, -9.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 43, "s": [{"c": true, "i": [[0, 0], [-5.1, -10.4], [9.2, -6.9], [8.5, 7.8], [-6.1, 9.8], [0, 0], [-8.2, -7.5], [-8.9, 6.6], [4.9, 9.9], [10.7, -2.9], [0, 0]], "v": [[17.7, 0.9], [45.6, 13.5], [38.4, 43.2], [7.8, 41.7], [3.6, 11.3], [4.4, 11.9], [8.4, 41], [37.8, 42.5], [44.7, 13.9], [17.9, 1.8], [17.7, 0.9]], "o": [[11.1, -3], [5.1, 10.4], [-9.2, 6.9], [-8.5, -7.8], [0, 0], [-5.8, 9.4], [8.2, 7.5], [8.9, -6.6], [-4.9, -9.9], [0, 0], [0, 0]]}], "h": 1}, {"t": 44, "s": [{"c": true, "i": [[0, 0], [-4.8, -10.6], [9.6, -6.6], [8.2, 8.3], [-6.8, 9.5], [0, 0], [-7.8, -8], [-9.2, 6.3], [4.6, 10.2], [10.8, -2.8], [0, 0]], "v": [[18.1, 0.7], [45.9, 14.1], [37.6, 43.8], [6.9, 40.8], [4.5, 10.1], [5.2, 10.6], [7.6, 40.1], [37, 43], [45, 14.5], [18.3, 1.7], [18.1, 0.7]], "o": [[11.3, -2.9], [4.8, 10.6], [-9.6, 6.6], [-8.2, -8.3], [0, 0], [-6.5, 9.1], [7.8, 8], [9.2, -6.3], [-4.6, -10.2], [0, 0], [0, 0]]}], "h": 1}, {"t": 45, "s": [{"c": true, "i": [[0, 0], [-4.6, -10.8], [9.9, -6.3], [7.8, 8.8], [-7.4, 9.1], [0, 0], [-7.5, -8.4], [-9.5, 6], [4.4, 10.4], [11, -2.6], [0, 0]], "v": [[18.4, 0.7], [46.1, 14.7], [36.8, 44.3], [6.1, 39.9], [5.3, 8.9], [6.1, 9.5], [6.8, 39.3], [36.3, 43.5], [45.2, 15.1], [18.6, 1.6], [18.4, 0.7]], "o": [[11.4, -2.7], [4.6, 10.8], [-9.9, 6.3], [-7.8, -8.8], [0, 0], [-7.1, 8.8], [7.5, 8.4], [9.5, -6], [-4.4, -10.4], [0, 0], [0, 0]]}], "h": 1}, {"t": 46, "s": [{"c": true, "i": [[0, 0], [-4.3, -11], [10.2, -5.9], [7.4, 9.2], [-8, 8.8], [0, 0], [-7.1, -8.8], [-9.8, 5.7], [4.1, 10.6], [11.1, -2.5], [0, 0]], "v": [[18.8, 0.6], [46.3, 15.3], [36.1, 44.7], [5.3, 39.1], [6.2, 7.9], [6.9, 8.5], [6.1, 38.5], [35.6, 43.9], [45.5, 15.6], [19, 1.5], [18.8, 0.6]], "o": [[11.6, -2.6], [4.3, 11], [-10.2, 5.9], [-7.4, -9.2], [0, 0], [-7.6, 8.4], [7.1, 8.8], [9.8, -5.7], [-4.1, -10.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 47, "s": [{"c": true, "i": [[0, 0], [-4.1, -11.2], [10.5, -5.6], [7.1, 9.6], [-8.5, 8.4], [0, 0], [-6.8, -9.2], [-10.1, 5.4], [3.9, 10.8], [11.2, -2.3], [0, 0]], "v": [[19.1, 0.5], [46.6, 15.8], [35.4, 45.1], [4.7, 38.3], [7.1, 6.9], [7.8, 7.6], [5.5, 37.7], [34.9, 44.3], [45.7, 16.1], [19.3, 1.4], [19.1, 0.5]], "o": [[11.7, -2.4], [4.1, 11.2], [-10.5, 5.6], [-7.1, -9.6], [0, 0], [-8.1, 8.1], [6.8, 9.2], [10.1, -5.4], [-3.9, -10.8], [0, 0], [0, 0]]}], "h": 1}, {"t": 48, "s": [{"c": true, "i": [[0, 0], [-3.9, -11.4], [10.8, -5.3], [6.7, 9.9], [-8.9, 8], [0, 0], [-6.5, -9.5], [-10.3, 5.1], [3.7, 10.9], [11.3, -2.2], [0, 0]], "v": [[19.4, 0.4], [46.7, 16.3], [34.7, 45.5], [4.1, 37.5], [8, 6.1], [8.6, 6.8], [4.9, 36.9], [34.3, 44.6], [45.8, 16.6], [19.6, 1.4], [19.4, 0.4]], "o": [[11.8, -2.3], [3.9, 11.4], [-10.8, 5.3], [-6.7, -9.9], [0, 0], [-8.6, 7.7], [6.5, 9.5], [10.3, -5.1], [-3.7, -10.9], [0, 0], [0, 0]]}], "h": 1}, {"t": 49, "s": [{"c": true, "i": [[0, 0], [-3.6, -11.5], [11, -5.1], [6.4, 10.3], [-9.4, 7.6], [0, 0], [-6.1, -9.9], [-10.5, 4.9], [3.5, 11.1], [11.4, -2.1], [0, 0]], "v": [[19.7, 0.4], [46.9, 16.8], [34, 45.8], [3.6, 36.7], [8.9, 5.4], [9.5, 6.1], [4.4, 36.2], [33.6, 44.9], [46, 17.1], [19.9, 1.3], [19.7, 0.4]], "o": [[11.9, -2.2], [3.6, 11.5], [-11, 5.1], [-6.4, -10.3], [0, 0], [-9, 7.3], [6.1, 9.9], [10.5, -4.9], [-3.5, -11.1], [0, 0], [0, 0]]}], "h": 1}, {"t": 50, "s": [{"c": true, "i": [[0, 0], [-3.4, -11.7], [11.2, -4.8], [6, 10.6], [-9.8, 7.2], [0, 0], [-5.8, -10.1], [-10.7, 4.6], [3.3, 11.2], [11.5, -2], [0, 0]], "v": [[20, 0.3], [47, 17.2], [33.4, 46.1], [3.2, 35.9], [9.7, 4.7], [10.3, 5.5], [4, 35.5], [33, 45.2], [46.1, 17.5], [20.2, 1.3], [20, 0.3]], "o": [[12, -2], [3.4, 11.7], [-11.2, 4.8], [-6, -10.6], [0, 0], [-9.4, 7], [5.8, 10.1], [10.7, -4.6], [-3.3, -11.2], [0, 0], [0, 0]]}], "h": 1}, {"t": 51, "s": [{"c": true, "i": [[0, 0], [-3.2, -11.8], [11.4, -4.5], [5.7, 10.8], [-10.1, 6.9], [0, 0], [-5.5, -10.4], [-10.9, 4.3], [3.1, 11.3], [11.6, -1.8], [0, 0]], "v": [[20.3, 0.3], [47.2, 17.7], [32.8, 46.3], [2.8, 35.2], [10.5, 4.1], [11.1, 4.9], [3.6, 34.8], [32.5, 45.4], [46.2, 17.9], [20.4, 1.2], [20.3, 0.3]], "o": [[12.1, -1.9], [3.2, 11.8], [-11.4, 4.5], [-5.7, -10.8], [0, 0], [-9.7, 6.6], [5.5, 10.4], [10.9, -4.3], [-3.1, -11.3], [0, 0], [0, 0]]}], "h": 1}, {"t": 52, "s": [{"c": true, "i": [[0, 0], [-3, -11.9], [11.6, -4.2], [5.4, 11.1], [-10.5, 6.5], [0, 0], [-5.2, -10.6], [-11.1, 4.1], [2.9, 11.4], [11.7, -1.7], [0, 0]], "v": [[20.5, 0.3], [47.3, 18.1], [32.3, 46.5], [2.4, 34.5], [11.3, 3.6], [11.8, 4.4], [3.3, 34.1], [31.9, 45.6], [46.3, 18.3], [20.6, 1.2], [20.5, 0.3]], "o": [[12.2, -1.8], [3, 11.9], [-11.6, 4.2], [-5.4, -11.1], [0, 0], [-10, 6.2], [5.2, 10.6], [11.1, -4.1], [-2.9, -11.4], [0, 0], [0, 0]]}], "h": 1}, {"t": 53, "s": [{"c": true, "i": [[0, 0], [-2.8, -12], [11.7, -4], [5.1, 11.3], [-10.7, 6.1], [0, 0], [-4.9, -10.8], [-11.2, 3.8], [2.7, 11.6], [11.8, -1.6], [0, 0]], "v": [[20.7, 0.2], [47.4, 18.5], [31.7, 46.7], [2.1, 33.8], [12.1, 3.2], [12.6, 4], [3, 33.5], [31.4, 45.8], [46.4, 18.7], [20.9, 1.2], [20.7, 0.2]], "o": [[12.3, -1.7], [2.8, 12], [-11.7, 4], [-5.1, -11.3], [0, 0], [-10.3, 5.9], [4.9, 10.8], [11.2, -3.8], [-2.7, -11.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 54, "s": [{"c": true, "i": [[0, 0], [-2.7, -12.1], [11.9, -3.7], [4.8, 11.5], [-11, 5.8], [0, 0], [-4.6, -11], [-11.4, 3.6], [2.6, 11.7], [11.8, -1.5], [0, 0]], "v": [[21, 0.2], [47.4, 18.9], [31.2, 46.9], [1.8, 33.2], [12.9, 2.7], [13.3, 3.6], [2.7, 32.8], [30.9, 46], [46.5, 19.1], [21.1, 1.1], [21, 0.2]], "o": [[12.3, -1.6], [2.7, 12.1], [-11.9, 3.7], [-4.8, -11.5], [0, 0], [-10.6, 5.5], [4.6, 11], [11.4, -3.6], [-2.6, -11.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 55, "s": [{"c": true, "i": [[0, 0], [-2.5, -12.2], [12, -3.5], [4.5, 11.7], [-11.2, 5.4], [0, 0], [-4.3, -11.2], [-11.5, 3.4], [2.4, 11.7], [11.9, -1.4], [0, 0]], "v": [[21.2, 0.2], [47.5, 19.2], [30.7, 47], [1.6, 32.6], [13.6, 2.4], [14, 3.2], [2.5, 32.3], [30.4, 46.1], [46.6, 19.4], [21.3, 1.1], [21.2, 0.2]], "o": [[12.4, -1.5], [2.5, 12.2], [-12, 3.5], [-4.5, -11.7], [0, 0], [-10.8, 5.2], [4.3, 11.2], [11.5, -3.4], [-2.4, -11.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 56, "s": [{"c": true, "i": [[0, 0], [-2.3, -12.3], [12.1, -3.3], [4.2, 11.8], [-11.5, 5.1], [0, 0], [-4, -11.3], [-11.6, 3.1], [2.2, 11.8], [12, -1.3], [0, 0]], "v": [[21.4, 0.1], [47.6, 19.5], [30.2, 47.2], [1.4, 32], [14.3, 2.1], [14.7, 2.9], [2.3, 31.7], [30, 46.2], [46.6, 19.7], [21.5, 1.1], [21.4, 0.1]], "o": [[12.5, -1.4], [2.3, 12.3], [-12.1, 3.3], [-4.2, -11.8], [0, 0], [-11, 4.9], [4, 11.3], [11.6, -3.1], [-2.2, -11.8], [0, 0], [0, 0]]}], "h": 1}, {"t": 57, "s": [{"c": true, "i": [[0, 0], [-2.2, -12.4], [12.2, -3], [3.9, 12], [-11.7, 4.8], [0, 0], [-3.8, -11.5], [-11.7, 2.9], [2.1, 11.9], [12, -1.2], [0, 0]], "v": [[21.6, 0.1], [47.6, 19.9], [29.8, 47.3], [1.2, 31.5], [14.9, 1.8], [15.3, 2.7], [2.1, 31.2], [29.6, 46.4], [46.7, 20], [21.7, 1.1], [21.6, 0.1]], "o": [[12.5, -1.3], [2.2, 12.4], [-12.2, 3], [-3.9, -12], [0, 0], [-11.2, 4.6], [3.8, 11.5], [11.7, -2.9], [-2.1, -11.9], [0, 0], [0, 0]]}], "h": 1}, {"t": 58, "s": [{"c": true, "i": [[0, 0], [-2, -12.5], [12.3, -2.8], [3.6, 12.1], [-11.8, 4.4], [0, 0], [-3.5, -11.6], [-11.8, 2.7], [1.9, 12], [12.1, -1.1], [0, 0]], "v": [[21.7, 0.1], [47.7, 20.2], [29.4, 47.4], [1, 30.9], [15.6, 1.5], [15.9, 2.4], [1.9, 30.6], [29.2, 46.5], [46.7, 20.3], [21.8, 1.1], [21.7, 0.1]], "o": [[12.6, -1.2], [2, 12.5], [-12.3, 2.8], [-3.6, -12.1], [0, 0], [-11.4, 4.3], [3.5, 11.6], [11.8, -2.7], [-1.9, -12], [0, 0], [0, 0]]}], "h": 1}, {"t": 59, "s": [{"c": true, "i": [[0, 0], [-1.9, -12.5], [12.4, -2.6], [3.4, 12.2], [-12, 4.1], [0, 0], [-3.3, -11.7], [-11.9, 2.5], [1.8, 12], [12.1, -1.1], [0, 0]], "v": [[21.9, 0.1], [47.7, 20.5], [29, 47.5], [0.9, 30.4], [16.2, 1.3], [16.5, 2.2], [1.8, 30.2], [28.8, 46.5], [46.8, 20.6], [22, 1], [21.9, 0.1]], "o": [[12.6, -1.1], [1.9, 12.5], [-12.4, 2.6], [-3.4, -12.2], [0, 0], [-11.5, 4], [3.3, 11.7], [11.9, -2.5], [-1.8, -12], [0, 0], [0, 0]]}], "h": 1}, {"t": 60, "s": [{"c": true, "i": [[0, 0], [-1.7, -12.6], [12.5, -2.4], [3.1, 12.3], [-12.1, 3.8], [0, 0], [-3, -11.8], [-12, 2.3], [1.7, 12.1], [12.2, -1], [0, 0]], "v": [[22.1, 0.1], [47.8, 20.7], [28.6, 47.6], [0.7, 29.9], [16.8, 1.1], [17.1, 2], [1.7, 29.7], [28.4, 46.6], [46.8, 20.9], [22.1, 1], [22.1, 0.1]], "o": [[12.7, -1], [1.7, 12.6], [-12.5, 2.4], [-3.1, -12.3], [0, 0], [-11.7, 3.7], [3, 11.8], [12, -2.3], [-1.7, -12.1], [0, 0], [0, 0]]}], "h": 1}, {"t": 61, "s": [{"c": true, "i": [[0, 0], [-1.6, -12.7], [12.6, -2.3], [2.9, 12.4], [-12.3, 3.6], [0, 0], [-2.8, -11.9], [-12.1, 2.2], [1.5, 12.2], [12.2, -0.9], [0, 0]], "v": [[22.2, 0.1], [47.8, 21], [28.3, 47.6], [0.6, 29.5], [17.3, 0.9], [17.6, 1.9], [1.6, 29.3], [28.1, 46.7], [46.9, 21.1], [22.3, 1], [22.2, 0.1]], "o": [[12.7, -0.9], [1.6, 12.7], [-12.6, 2.3], [-2.9, -12.4], [0, 0], [-11.8, 3.4], [2.8, 11.9], [12.1, -2.2], [-1.5, -12.2], [0, 0], [0, 0]]}], "h": 1}, {"t": 62, "s": [{"c": true, "i": [[0, 0], [-1.5, -12.7], [12.6, -2.1], [2.7, 12.5], [-12.4, 3.3], [0, 0], [-2.6, -12], [-12.1, 2], [1.4, 12.2], [12.3, -0.8], [0, 0]], "v": [[22.4, 0.1], [47.8, 21.2], [27.9, 47.7], [0.5, 29], [17.8, 0.8], [18.1, 1.7], [1.5, 28.8], [27.8, 46.7], [46.9, 21.3], [22.4, 1], [22.4, 0.1]], "o": [[12.8, -0.9], [1.5, 12.7], [-12.6, 2.1], [-2.7, -12.5], [0, 0], [-11.9, 3.2], [2.6, 12], [12.1, -2], [-1.4, -12.2], [0, 0], [0, 0]]}], "h": 1}, {"t": 63, "s": [{"c": true, "i": [[0, 0], [-1.4, -12.8], [12.7, -1.9], [2.5, 12.6], [-12.5, 3], [0, 0], [-2.4, -12.1], [-12.2, 1.8], [1.3, 12.3], [12.3, -0.8], [0, 0]], "v": [[22.5, 0], [47.9, 21.4], [27.6, 47.7], [0.5, 28.6], [18.3, 0.7], [18.6, 1.6], [1.4, 28.4], [27.5, 46.8], [46.9, 21.6], [22.6, 1], [22.5, 0]], "o": [[12.8, -0.8], [1.4, 12.8], [-12.7, 1.9], [-2.5, -12.6], [0, 0], [-12, 2.9], [2.4, 12.1], [12.2, -1.8], [-1.3, -12.3], [0, 0], [0, 0]]}], "h": 1}, {"t": 64, "s": [{"c": true, "i": [[0, 0], [-1.3, -12.8], [12.8, -1.8], [2.3, 12.7], [-12.6, 2.8], [0, 0], [-2.2, -12.2], [-12.2, 1.7], [1.2, 12.3], [12.3, -0.7], [0, 0]], "v": [[22.6, 0], [47.9, 21.7], [27.3, 47.8], [0.4, 28.2], [18.8, 0.6], [19, 1.5], [1.3, 28.1], [27.2, 46.8], [46.9, 21.8], [22.7, 1], [22.6, 0]], "o": [[12.9, -0.7], [1.3, 12.8], [-12.8, 1.8], [-2.3, -12.7], [0, 0], [-12.1, 2.7], [2.2, 12.2], [12.2, -1.7], [-1.2, -12.3], [0, 0], [0, 0]]}], "h": 1}, {"t": 65, "s": [{"c": true, "i": [[0, 0], [-1.1, -12.9], [12.8, -1.6], [2.1, 12.7], [-12.7, 2.6], [0, 0], [-2, -12.2], [-12.3, 1.6], [1.1, 12.3], [12.4, -0.6], [0, 0]], "v": [[22.7, 0], [47.9, 21.9], [27, 47.8], [0.3, 27.9], [19.3, 0.5], [19.4, 1.4], [1.3, 27.7], [26.9, 46.9], [46.9, 22], [22.8, 1], [22.7, 0]], "o": [[12.9, -0.7], [1.1, 12.9], [-12.8, 1.6], [-2.1, -12.7], [0, 0], [-12.2, 2.5], [2, 12.2], [12.3, -1.6], [-1.1, -12.3], [0, 0], [0, 0]]}], "h": 1}, {"t": 66, "s": [{"c": true, "i": [[0, 0], [-1, -12.9], [12.9, -1.5], [1.9, 12.8], [-12.7, 2.3], [0, 0], [-1.8, -12.3], [-12.3, 1.4], [1, 12.4], [12.4, -0.6], [0, 0]], "v": [[22.9, 0], [47.9, 22.1], [26.7, 47.8], [0.3, 27.5], [19.7, 0.4], [19.9, 1.3], [1.2, 27.4], [26.6, 46.9], [47, 22.1], [22.9, 1], [22.9, 0]], "o": [[12.9, -0.6], [1, 12.9], [-12.9, 1.5], [-1.9, -12.8], [0, 0], [-12.2, 2.2], [1.8, 12.3], [12.3, -1.4], [-1, -12.4], [0, 0], [0, 0]]}], "h": 1}, {"t": 67, "s": [{"c": true, "i": [[0, 0], [-1, -12.9], [12.9, -1.3], [1.7, 12.9], [-12.8, 2.1], [0, 0], [-1.7, -12.3], [-12.4, 1.3], [0.9, 12.4], [12.4, -0.5], [0, 0]], "v": [[23, 0], [47.9, 22.2], [26.5, 47.9], [0.2, 27.2], [20.1, 0.3], [20.2, 1.3], [1.2, 27.1], [26.4, 46.9], [47, 22.3], [23, 1], [23, 0]], "o": [[13, -0.6], [1, 12.9], [-12.9, 1.3], [-1.7, -12.9], [0, 0], [-12.3, 2], [1.7, 12.3], [12.4, -1.3], [-0.9, -12.4], [0, 0], [0, 0]]}], "h": 1}, {"t": 68, "s": [{"c": true, "i": [[0, 0], [-0.9, -13], [12.9, -1.2], [1.6, 12.9], [-12.9, 1.9], [0, 0], [-1.5, -12.4], [-12.4, 1.2], [0.8, 12.5], [12.5, -0.5], [0, 0]], "v": [[23.1, 0], [47.9, 22.4], [26.2, 47.9], [0.2, 26.9], [20.4, 0.3], [20.6, 1.2], [1.1, 26.8], [26.2, 46.9], [47, 22.5], [23.1, 1], [23.1, 0]], "o": [[13, -0.5], [0.9, 13], [-12.9, 1.2], [-1.6, -12.9], [0, 0], [-12.3, 1.8], [1.5, 12.4], [12.4, -1.2], [-0.8, -12.5], [0, 0], [0, 0]]}], "h": 1}, {"t": 69, "s": [{"c": true, "i": [[0, 0], [-0.8, -13], [13, -1.1], [1.4, 12.9], [-12.9, 1.7], [0, 0], [-1.4, -12.4], [-12.5, 1.1], [0.7, 12.5], [12.5, -0.4], [0, 0]], "v": [[23.2, 0], [48, 22.6], [26, 47.9], [0.1, 26.6], [20.8, 0.2], [20.9, 1.2], [1.1, 26.5], [25.9, 47], [47, 22.6], [23.2, 1], [23.2, 0]], "o": [[13, -0.5], [0.8, 13], [-13, 1.1], [-1.4, -12.9], [0, 0], [-12.4, 1.7], [1.4, 12.4], [12.5, -1.1], [-0.7, -12.5], [0, 0], [0, 0]]}], "h": 1}, {"t": 70, "s": [{"c": true, "i": [[0, 0], [-0.7, -13], [13, -1], [1.3, 13], [-13, 1.6], [0, 0], [-1.2, -12.5], [-12.5, 0.9], [0.7, 12.5], [12.5, -0.4], [0, 0]], "v": [[23.2, 0], [48, 22.7], [25.8, 47.9], [0.1, 26.3], [21.1, 0.2], [21.2, 1.1], [1.1, 26.2], [25.7, 47], [47, 22.8], [23.3, 1], [23.2, 0]], "o": [[13, -0.4], [0.7, 13], [-13, 1], [-1.3, -13], [0, 0], [-12.4, 1.5], [1.2, 12.5], [12.5, -0.9], [-0.7, -12.5], [0, 0], [0, 0]]}], "h": 1}, {"t": 71, "s": [{"c": true, "i": [[0, 0], [-0.6, -13.1], [13, -0.9], [1.1, 13], [-13, 1.4], [0, 0], [-1.1, -12.5], [-12.5, 0.8], [0.6, 12.5], [12.5, -0.4], [0, 0]], "v": [[23.3, 0], [48, 22.9], [25.6, 47.9], [0.1, 26.1], [21.4, 0.1], [21.5, 1.1], [1, 26], [25.6, 47], [47, 22.9], [23.4, 1], [23.3, 0]], "o": [[13.1, -0.4], [0.6, 13.1], [-13, 0.9], [-1.1, -13], [0, 0], [-12.5, 1.3], [1.1, 12.5], [12.5, -0.8], [-0.6, -12.5], [0, 0], [0, 0]]}], "h": 1}, {"t": 72, "s": [{"c": true, "i": [[0, 0], [-0.6, -13.1], [13.1, -0.8], [1, 13.1], [-13, 1.2], [0, 0], [-1, -12.5], [-12.5, 0.8], [0.5, 12.6], [12.6, -0.3], [0, 0]], "v": [[23.4, 0], [48, 23], [25.4, 48], [0.1, 25.9], [21.7, 0.1], [21.8, 1.1], [1, 25.8], [25.4, 47], [47, 23], [23.4, 1], [23.4, 0]], "o": [[13.1, -0.3], [0.6, 13.1], [-13.1, 0.8], [-1, -13.1], [0, 0], [-12.5, 1.2], [1, 12.5], [12.5, -0.8], [-0.5, -12.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 73, "s": [{"c": true, "i": [[0, 0], [-0.5, -13.1], [13.1, -0.7], [0.9, 13.1], [-13.1, 1.1], [0, 0], [-0.9, -12.6], [-12.6, 0.7], [0.5, 12.6], [12.6, -0.3], [0, 0]], "v": [[23.5, 0], [48, 23.1], [25.3, 48], [0.1, 25.6], [22, 0.1], [22.1, 1], [1, 25.6], [25.2, 47], [47, 23.1], [23.5, 1], [23.5, 0]], "o": [[13.1, -0.3], [0.5, 13.1], [-13.1, 0.7], [-0.9, -13.1], [0, 0], [-12.5, 1.1], [0.9, 12.6], [12.6, -0.7], [-0.5, -12.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 74, "s": [{"c": true, "i": [[0, 0], [-0.4, -13.1], [13.1, -0.6], [0.8, 13.1], [-13.1, 1], [0, 0], [-0.8, -12.6], [-12.6, 0.6], [0.4, 12.6], [12.6, -0.2], [0, 0]], "v": [[23.5, 0], [48, 23.2], [25.1, 48], [0, 25.4], [22.2, 0.1], [22.3, 1], [1, 25.4], [25.1, 47], [47, 23.2], [23.6, 1], [23.5, 0]], "o": [[13.1, -0.3], [0.4, 13.1], [-13.1, 0.6], [-0.8, -13.1], [0, 0], [-12.6, 0.9], [0.8, 12.6], [12.6, -0.6], [-0.4, -12.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 75, "s": [{"c": true, "i": [[0, 0], [-0.4, -13.1], [13.1, -0.5], [0.7, 13.1], [-13.1, 0.8], [0, 0], [-0.7, -12.6], [-12.6, 0.5], [0.4, 12.6], [12.6, -0.2], [0, 0]], "v": [[23.6, 0], [48, 23.3], [25, 48], [0, 25.2], [22.5, 0], [22.5, 1], [1, 25.2], [24.9, 47], [47, 23.3], [23.6, 1], [23.6, 0]], "o": [[13.1, -0.2], [0.4, 13.1], [-13.1, 0.5], [-0.7, -13.1], [0, 0], [-12.6, 0.8], [0.7, 12.6], [12.6, -0.5], [-0.4, -12.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 76, "s": [{"c": true, "i": [[0, 0], [-0.3, -13.2], [13.2, -0.5], [0.6, 13.1], [-13.1, 0.7], [0, 0], [-0.6, -12.6], [-12.6, 0.4], [0.3, 12.6], [12.6, -0.2], [0, 0]], "v": [[23.7, 0], [48, 23.4], [24.8, 48], [0, 25.1], [22.7, 0], [22.7, 1], [1, 25], [24.8, 47], [47, 23.4], [23.7, 1], [23.7, 0]], "o": [[13.2, -0.2], [0.3, 13.2], [-13.2, 0.5], [-0.6, -13.1], [0, 0], [-12.6, 0.7], [0.6, 12.6], [12.6, -0.4], [-0.3, -12.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 77, "s": [{"c": true, "i": [[0, 0], [-0.3, -13.2], [13.2, -0.4], [0.5, 13.2], [-13.2, 0.6], [0, 0], [-0.5, -12.6], [-12.6, 0.4], [0.3, 12.6], [12.6, -0.2], [0, 0]], "v": [[23.7, 0], [48, 23.5], [24.7, 48], [0, 24.9], [22.9, 0], [22.9, 1], [1, 24.9], [24.7, 47], [47, 23.5], [23.7, 1], [23.7, 0]], "o": [[13.2, -0.2], [0.3, 13.2], [-13.2, 0.4], [-0.5, -13.2], [0, 0], [-12.6, 0.6], [0.5, 12.6], [12.6, -0.4], [-0.3, -12.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 78, "s": [{"c": true, "i": [[0, 0], [-0.2, -13.2], [13.2, -0.3], [0.4, 13.2], [-13.2, 0.5], [0, 0], [-0.4, -12.7], [-12.7, 0.3], [0.2, 12.7], [12.7, -0.1], [0, 0]], "v": [[23.8, 0], [48, 23.6], [24.6, 48], [0, 24.8], [23.1, 0], [23.1, 1], [1, 24.7], [24.6, 47], [47, 23.6], [23.8, 1], [23.8, 0]], "o": [[13.2, -0.1], [0.2, 13.2], [-13.2, 0.3], [-0.4, -13.2], [0, 0], [-12.6, 0.5], [0.4, 12.7], [12.7, -0.3], [-0.2, -12.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 79, "s": [{"c": true, "i": [[0, 0], [-0.2, -13.2], [13.2, -0.3], [0.4, 13.2], [-13.2, 0.4], [0, 0], [-0.3, -12.7], [-12.7, 0.3], [0.2, 12.7], [12.7, -0.1], [0, 0]], "v": [[23.8, 0], [48, 23.6], [24.5, 48], [0, 24.6], [23.2, 0], [23.2, 1], [1, 24.6], [24.5, 47], [47, 23.7], [23.8, 1], [23.8, 0]], "o": [[13.2, -0.1], [0.2, 13.2], [-13.2, 0.3], [-0.4, -13.2], [0, 0], [-12.7, 0.4], [0.3, 12.7], [12.7, -0.3], [-0.2, -12.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 80, "s": [{"c": true, "i": [[0, 0], [-0.2, -13.2], [13.2, -0.2], [0.3, 13.2], [-13.2, 0.4], [0, 0], [-0.3, -12.7], [-12.7, 0.2], [0.2, 12.7], [12.7, -0.1], [0, 0]], "v": [[23.8, 0], [48, 23.7], [24.4, 48], [0, 24.5], [23.4, 0], [23.4, 1], [1, 24.5], [24.4, 47], [47, 23.7], [23.8, 1], [23.8, 0]], "o": [[13.2, -0.1], [0.2, 13.2], [-13.2, 0.2], [-0.3, -13.2], [0, 0], [-12.7, 0.3], [0.3, 12.7], [12.7, -0.2], [-0.2, -12.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 81, "s": [{"c": true, "i": [[0, 0], [-0.1, -13.2], [13.2, -0.2], [0.2, 13.2], [-13.2, 0.3], [0, 0], [-0.2, -12.7], [-12.7, 0.2], [0.1, 12.7], [12.7, -0.1], [0, 0]], "v": [[23.9, 0], [48, 23.8], [24.3, 48], [0, 24.4], [23.5, 0], [23.5, 1], [1, 24.4], [24.3, 47], [47, 23.8], [23.9, 1], [23.9, 0]], "o": [[13.2, -0.1], [0.1, 13.2], [-13.2, 0.2], [-0.2, -13.2], [0, 0], [-12.7, 0.3], [0.2, 12.7], [12.7, -0.2], [-0.1, -12.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 82, "s": [{"c": true, "i": [[0, 0], [-0.1, -13.2], [13.2, -0.1], [0.2, 13.2], [-13.2, 0.2], [0, 0], [-0.2, -12.7], [-12.7, 0.1], [0.1, 12.7], [12.7, -0.1], [0, 0]], "v": [[23.9, 0], [48, 23.8], [24.3, 48], [0, 24.3], [23.6, 0], [23.6, 1], [1, 24.3], [24.2, 47], [47, 23.8], [23.9, 1], [23.9, 0]], "o": [[13.2, -0.1], [0.1, 13.2], [-13.2, 0.1], [-0.2, -13.2], [0, 0], [-12.7, 0.2], [0.2, 12.7], [12.7, -0.1], [-0.1, -12.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 83, "s": [{"c": true, "i": [[0, 0], [-0.1, -13.2], [13.2, -0.1], [0.1, 13.2], [-13.2, 0.2], [0, 0], [-0.1, -12.7], [-12.7, 0.1], [0.1, 12.7], [12.7, 0], [0, 0]], "v": [[23.9, 0], [48, 23.9], [24.2, 48], [0, 24.3], [23.7, 0], [23.7, 1], [1, 24.2], [24.2, 47], [47, 23.9], [23.9, 1], [23.9, 0]], "o": [[13.2, 0], [0.1, 13.2], [-13.2, 0.1], [-0.1, -13.2], [0, 0], [-12.7, 0.2], [0.1, 12.7], [12.7, -0.1], [-0.1, -12.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 84, "s": [{"c": true, "i": [[0, 0], [-0.1, -13.2], [13.2, -0.1], [0.1, 13.2], [-13.2, 0.1], [0, 0], [-0.1, -12.7], [-12.7, 0.1], [0.1, 12.7], [12.7, 0], [0, 0]], "v": [[23.9, 0], [48, 23.9], [24.1, 48], [0, 24.2], [23.8, 0], [23.8, 1], [1, 24.2], [24.1, 47], [47, 23.9], [23.9, 1], [23.9, 0]], "o": [[13.2, 0], [0.1, 13.2], [-13.2, 0.1], [-0.1, -13.2], [0, 0], [-12.7, 0.1], [0.1, 12.7], [12.7, -0.1], [-0.1, -12.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 85, "s": [{"c": true, "i": [[0, 0], [0, -13.2], [13.2, -0.1], [0.1, 13.2], [-13.2, 0.1], [0, 0], [-0.1, -12.7], [-12.7, 0.1], [0, 12.7], [12.7, 0], [0, 0]], "v": [[24, 0], [48, 23.9], [24.1, 48], [0, 24.1], [23.8, 0], [23.9, 1], [1, 24.1], [24.1, 47], [47, 23.9], [24, 1], [24, 0]], "o": [[13.2, 0], [0, 13.2], [-13.2, 0.1], [-0.1, -13.2], [0, 0], [-12.7, 0.1], [0.1, 12.7], [12.7, -0.1], [0, -12.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 86, "s": [{"c": true, "i": [[0, 0], [0, -13.2], [13.2, 0], [0, 13.2], [-13.2, 0.1], [0, 0], [0, -12.7], [-12.7, 0], [0, 12.7], [12.7, 0], [0, 0]], "v": [[24, 0], [48, 24], [24.1, 48], [0, 24.1], [23.9, 0], [23.9, 1], [1, 24.1], [24.1, 47], [47, 24], [24, 1], [24, 0]], "o": [[13.2, 0], [0, 13.2], [-13.2, 0], [0, -13.2], [0, 0], [-12.7, 0.1], [0, 12.7], [12.7, 0], [0, -12.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 87, "s": [{"c": true, "i": [[0, 0], [0, -13.3], [13.3, 0], [0, 13.3], [-13.3, 0], [0, 0], [0, -12.7], [-12.7, 0], [0, 12.7], [12.7, 0], [0, 0]], "v": [[24, 0], [48, 24], [24, 48], [0, 24], [23.9, 0], [23.9, 1], [1, 24], [24, 47], [47, 24], [24, 1], [24, 0]], "o": [[13.3, 0], [0, 13.3], [-13.3, 0], [0, -13.3], [0, 0], [-12.7, 0], [0, 12.7], [12.7, 0], [0, -12.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 88, "s": [{"c": true, "i": [[0, 0], [0, -13.3], [13.3, 0], [0, 13.3], [-13.3, 0], [0, 0], [0, -12.7], [-12.7, 0], [0, 12.7], [12.7, 0], [0, 0]], "v": [[24, 0], [48, 24], [24, 48], [0, 24], [24, 0], [24, 1], [1, 24], [24, 47], [47, 24], [24, 1], [24, 0]], "o": [[13.3, 0], [0, 13.3], [-13.3, 0], [0, -13.3], [0, 0], [-12.7, 0], [0, 12.7], [12.7, 0], [0, -12.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 89, "s": [{"c": true, "i": [[0, 0], [0, -13.3], [13.3, 0], [0, 13.3], [-13.3, 0], [0, 0], [0, -12.7], [-12.7, 0], [0, 12.7], [12.7, 0], [0, 0]], "v": [[24, 0], [48, 24], [24, 48], [0, 24], [24, 0], [24, 1], [1, 24], [24, 47], [47, 24], [24, 1], [24, 0]], "o": [[13.3, 0], [0, 13.3], [-13.3, 0], [0, -13.3], [0, 0], [-12.7, 0], [0, 12.7], [12.7, 0], [0, -12.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 90, "s": [{"c": true, "i": [[0, 0], [13.2, 0], [0, 13.2], [-13.2, 0], [-2.099999999999998, -0.6000000000000001], [-1.8000000000000043, -1.0999999999999996], [-1.5, -1.5], [-1, -1.8000000000000007], [-0.5, -2.0000000000000018], [0, -2.1999999999999993], [0, 0]], "v": [[48, 24], [24, 48], [0, 24], [24, 0], [30.4, 0.9], [36.1, 3.3], [41, 7.1], [44.7, 11.9], [47.1, 17.6], [48, 24], [48, 24]], "o": [[0, 13.2], [-13.2, 0], [0, -13.2], [2.1999999999999993, 0], [2, 0.4999999999999999], [1.7999999999999972, 1], [1.3999999999999986, 1.4000000000000004], [1.0999999999999943, 1.799999999999999], [0.6000000000000014, 2.099999999999998], [0, 0], [0, 0]]}], "h": 1}]}}, {"ty": "sh", "ks": {"a": 1, "k": [{"t": 0, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 1, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 2, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 3, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 4, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 5, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 6, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 7, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 8, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 9, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 10, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 11, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 12, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 13, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 14, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 15, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 16, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 17, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 18, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 19, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 20, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 21, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 22, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 23, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 24, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 25, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 26, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 27, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 28, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 29, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 30, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 31, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 32, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 33, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 34, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 35, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 36, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 37, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 38, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 39, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 40, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 41, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 42, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 43, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 44, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 45, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 46, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 47, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 48, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 49, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 50, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 51, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 52, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 53, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 54, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 55, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 56, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 57, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 58, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 59, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 60, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 61, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 62, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 63, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 64, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 65, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 66, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 67, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 68, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 69, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 70, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 71, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 72, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 73, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 74, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 75, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 76, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 77, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 78, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 79, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 80, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 81, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 82, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 83, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 84, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 85, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 86, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 87, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 88, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 89, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 90, "s": [{"c": true, "i": [[0, 0], [12.7, 0], [0, -12.7], [-12.7, 0], [0, 12.7], [0, 0]], "v": [[47, 24], [24, 1], [1, 24], [24, 47], [47, 24], [47, 24]], "o": [[0, -12.7], [-12.7, 0], [0, 12.7], [12.7, 0], [0, 0], [0, 0]]}], "h": 1}]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 12, "ty": 4, "tt": 1, "parent": 11, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "p": {"a": 0, "k": [24, 24]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [68.009, 68.009]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 0]}, "o": {"a": 0, "k": 0}}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}, {"ty": "gr", "it": [{"ty": "sh", "ks": {"a": 1, "k": [{"t": 0, "s": [{"c": true, "i": [[0, 0], [-0.30000000000000004, 1.3999999999999986], [-0.5999999999999999, 1.4000000000000004], [-0.8000000000000003, 1.200000000000001], [-1, 1.0999999999999996], [-1.200000000000001, 0.9000000000000004], [-7.3, -2.4], [0, 0], [6, -4.3], [0, -7.4], [0, 0]], "v": [[0, 24], [0.4, 19.5], [1.7, 15.1], [3.7, 11.2], [6.5, 7.6], [9.9, 4.6], [31.4, 1.2], [31.1, 2.1], [10.5, 5.4], [1, 24], [0, 24]], "o": [[0, -1.5], [0.29999999999999993, -1.5], [0.5999999999999999, -1.299999999999999], [0.7999999999999998, -1.299999999999999], [1, -1.0999999999999996], [6.2, -4.5], [0, 0], [-7, -2.3], [-6, 4.3], [0, 0], [0, 0]]}], "h": 1}, {"t": 1, "s": [{"c": true, "i": [[0, 0], [-0.30000000000000004, 1.3999999999999986], [-0.5999999999999999, 1.4000000000000004], [-0.8000000000000003, 1.200000000000001], [-1, 1.0999999999999996], [-1.200000000000001, 0.9000000000000004], [-7.3, -2.4], [0, 0], [6, -4.3], [0, -7.4], [0, 0]], "v": [[0, 24], [0.4, 19.5], [1.7, 15.1], [3.7, 11.2], [6.5, 7.6], [9.9, 4.6], [31.4, 1.2], [31.1, 2.1], [10.5, 5.4], [1, 24], [0, 24]], "o": [[0, -1.5], [0.29999999999999993, -1.5], [0.5999999999999999, -1.299999999999999], [0.7999999999999998, -1.299999999999999], [1, -1.0999999999999996], [6.2, -4.5], [0, 0], [-7, -2.3], [-6, 4.3], [0, 0], [0, 0]]}], "h": 1}, {"t": 2, "s": [{"c": true, "i": [[0, 0], [-0.30000000000000004, 1.5], [-0.5999999999999999, 1.4000000000000004], [-0.8000000000000003, 1.3000000000000007], [-1, 1.0999999999999996], [-1.200000000000001, 0.9000000000000004], [-7.3, -2.4], [0, 0], [6, -4.3], [0, -7.4], [0, 0]], "v": [[0, 24], [0.4, 19.4], [1.7, 15.1], [3.7, 11.1], [6.5, 7.5], [9.9, 4.5], [31.5, 1.2], [31.2, 2.1], [10.5, 5.3], [1, 24], [0, 24]], "o": [[0, -1.5], [0.29999999999999993, -1.3999999999999986], [0.5999999999999999, -1.4000000000000004], [0.7999999999999998, -1.299999999999999], [1, -1.0999999999999996], [6.2, -4.5], [0, 0], [-7, -2.3], [-6, 4.3], [0, 0], [0, 0]]}], "h": 1}, {"t": 3, "s": [{"c": true, "i": [[0, 0], [-6.3, 4.5], [-1.4000000000000004, 0.5999999999999996], [-1.3999999999999986, 0.30000000000000004], [-1.5, 0.1], [-1.5, -0.2], [-1.5, -0.5], [0, 0], [6, -4.3], [0, -7.4], [0, 0]], "v": [[0, 23.9], [10, 4.5], [14, 2.2], [18.2, 0.7], [22.7, 0], [27.2, 0.2], [31.6, 1.2], [31.3, 2.2], [10.6, 5.3], [1, 23.9], [0, 23.9]], "o": [[0, -7.7], [1.3000000000000007, -0.8999999999999999], [1.3000000000000007, -0.7000000000000002], [1.5, -0.39999999999999997], [1.5, -0.1], [1.5, 0.2], [0, 0], [-7, -2.4], [-6, 4.3], [0, 0], [0, 0]]}], "h": 1}, {"t": 4, "s": [{"c": true, "i": [[0, 0], [-0.2, 1.5], [-0.5, 1.3999999999999986], [-0.7999999999999998, 1.3000000000000007], [-1, 1.0999999999999996], [-1.299999999999999, 0.8999999999999995], [-7.3, -2.5], [0, 0], [6.1, -4.3], [0, -7.4], [0, 0]], "v": [[0, 23.9], [0.4, 19.3], [1.7, 15], [3.8, 11], [6.6, 7.4], [10.1, 4.4], [31.8, 1.3], [31.5, 2.2], [10.7, 5.2], [1, 23.9], [0, 23.9]], "o": [[0, -1.5], [0.29999999999999993, -1.4000000000000021], [0.5999999999999999, -1.4000000000000004], [0.7999999999999998, -1.3000000000000007], [1.1000000000000005, -1.1000000000000005], [6.3, -4.5], [0, 0], [-7, -2.4], [-6.1, 4.3], [0, 0], [0, 0]]}], "h": 1}, {"t": 5, "s": [{"c": true, "i": [[0, 0], [-6.4, 4.5], [-1.3999999999999986, 0.6000000000000001], [-1.5, 0.30000000000000004], [-1.5, 0.1], [-1.5, -0.3], [-1.5, -0.4999999999999999], [0, 0], [6.1, -4.3], [0.1, -7.5], [0, 0]], "v": [[0, 23.8], [10.2, 4.3], [14.2, 2], [18.6, 0.6], [23.1, 0], [27.6, 0.3], [32.1, 1.4], [31.7, 2.3], [10.8, 5.1], [1, 23.8], [0, 23.8]], "o": [[0.1, -7.8], [1.3000000000000007, -0.8999999999999999], [1.4000000000000004, -0.6000000000000001], [1.5, -0.39999999999999997], [1.5, -0.1], [1.5, 0.2], [0, 0], [-7, -2.5], [-6.1, 4.3], [0, 0], [0, 0]]}], "h": 1}, {"t": 6, "s": [{"c": true, "i": [[0, 0], [-6.4, 4.4], [-1.4000000000000004, 0.6000000000000001], [-1.5, 0.30000000000000004], [-1.5, 0.1], [-1.5, -0.19999999999999998], [-1.5, -0.5], [0, 0], [6.2, -4.2], [0.1, -7.5], [0, 0]], "v": [[0, 23.7], [10.4, 4.2], [14.4, 2], [18.8, 0.6], [23.3, 0], [27.9, 0.3], [32.4, 1.5], [32, 2.4], [11, 5], [1, 23.7], [0, 23.7]], "o": [[0.1, -7.8], [1.299999999999999, -0.9000000000000004], [1.5, -0.6000000000000001], [1.5, -0.39999999999999997], [1.5999999999999979, 0], [1.5, 0.3], [0, 0], [-7, -2.6], [-6.2, 4.2], [0, 0], [0, 0]]}], "h": 1}, {"t": 7, "s": [{"c": true, "i": [[0, 0], [-6.5, 4.4], [-1.3999999999999986, 0.6000000000000001], [-1.5, 0.30000000000000004], [-1.5, 0], [-1.5, -0.19999999999999998], [-1.5000000000000036, -0.6000000000000001], [0, 0], [6.3, -4.2], [0.1, -7.5], [0, 0]], "v": [[0, 23.6], [10.6, 4.1], [14.7, 1.9], [19.1, 0.5], [23.6, 0], [28.2, 0.3], [32.7, 1.6], [32.4, 2.5], [11.2, 4.9], [1, 23.6], [0, 23.6]], "o": [[0.1, -7.9], [1.3000000000000007, -0.8999999999999995], [1.4000000000000021, -0.5999999999999999], [1.5, -0.3], [1.5999999999999979, -0.1], [1.5, 0.3], [0, 0], [-7, -2.7], [-6.3, 4.2], [0, 0], [0, 0]]}], "h": 1}, {"t": 8, "s": [{"c": true, "i": [[0, 0], [-0.39999999999999997, 1.5999999999999979], [-0.6000000000000001, 1.4000000000000004], [-0.8999999999999999, 1.299999999999999], [-1.2000000000000002, 1.1000000000000005], [-1.3000000000000007, 0.8999999999999999], [-7.3, -3], [0, 0], [6.4, -4.2], [0.2, -7.6], [0, 0]], "v": [[0, 23.5], [0.6, 18.8], [2, 14.4], [4.3, 10.4], [7.3, 6.8], [10.9, 3.9], [33.2, 1.8], [32.8, 2.7], [11.4, 4.7], [1, 23.5], [0, 23.5]], "o": [[0, -1.6000000000000014], [0.30000000000000004, -1.5], [0.6000000000000001, -1.4000000000000004], [0.7999999999999998, -1.3000000000000007], [1.1000000000000005, -1.0999999999999996], [6.6, -4.3], [0, 0], [-7, -2.9], [-6.4, 4.2], [0, 0], [0, 0]]}], "h": 1}, {"t": 9, "s": [{"c": true, "i": [[0, 0], [-0.39999999999999997, 1.5], [-0.7000000000000002, 1.4000000000000004], [-0.9000000000000004, 1.200000000000001], [-1.1000000000000005, 1.1000000000000005], [-1.3999999999999986, 0.8999999999999995], [-7.3, -3.2], [0, 0], [6.5, -4.1], [0.2, -7.7], [0, 0]], "v": [[0, 23.3], [0.6, 18.6], [2.1, 14.2], [4.4, 10.2], [7.4, 6.6], [11.2, 3.7], [33.7, 2.1], [33.3, 2.9], [11.7, 4.5], [1, 23.4], [0, 23.3]], "o": [[0, -1.6000000000000014], [0.30000000000000004, -1.5], [0.6000000000000001, -1.3999999999999986], [0.8999999999999995, -1.299999999999999], [1.1999999999999993, -1.0999999999999996], [6.8, -4.3], [0, 0], [-7, -3.1], [-6.5, 4.1], [0, 0], [0, 0]]}], "h": 1}, {"t": 10, "s": [{"c": true, "i": [[0, 0], [-0.39999999999999997, 1.6000000000000014], [-0.6000000000000001, 1.4000000000000004], [-0.8999999999999995, 1.299999999999999], [-1.2000000000000002, 1.0999999999999996], [-1.4000000000000004, 0.7999999999999998], [-7.3, -3.5], [0, 0], [6.6, -4], [0.3, -7.8], [0, 0]], "v": [[0, 23.2], [0.7, 18.4], [2.2, 14], [4.6, 9.9], [7.8, 6.4], [11.6, 3.5], [34.4, 2.4], [33.9, 3.2], [12.1, 4.3], [1, 23.2], [0, 23.2]], "o": [[0.1, -1.5999999999999979], [0.30000000000000004, -1.5], [0.6999999999999997, -1.5], [1, -1.3000000000000007], [1.1000000000000005, -1.1000000000000005], [6.9, -4.2], [0, 0], [-7, -3.4], [-6.6, 4], [0, 0], [0, 0]]}], "h": 1}, {"t": 11, "s": [{"c": true, "i": [[0, 0], [-0.39999999999999997, 1.5], [-0.7, 1.4000000000000004], [-0.8999999999999999, 1.3000000000000007], [-1.1999999999999993, 1.0999999999999996], [-1.4000000000000004, 0.7999999999999998], [-7.3, -3.8], [0, 0], [6.8, -3.9], [0.3, -7.9], [0, 0]], "v": [[0, 23], [0.7, 18.2], [2.4, 13.7], [4.8, 9.6], [8.1, 6], [12, 3.2], [35.1, 2.7], [34.6, 3.6], [12.5, 4], [1, 23], [0, 23]], "o": [[0.1, -1.6000000000000014], [0.40000000000000013, -1.5999999999999979], [0.6000000000000001, -1.5], [1, -1.299999999999999], [1.200000000000001, -1], [7.1, -4.1], [0, 0], [-7, -3.6], [-6.8, 3.9], [0, 0], [0, 0]]}], "h": 1}, {"t": 12, "s": [{"c": true, "i": [[0, 0], [-7.3, 4], [-1.5, 0.5], [-1.6000000000000014, 0.1], [-1.6999999999999993, -0.2], [-1.5, -0.5], [-1.3999999999999986, -0.8000000000000003], [0, 0], [7, -3.8], [0.4, -8], [0, 0]], "v": [[0, 22.7], [12.5, 2.9], [17, 1], [21.8, 0.1], [26.7, 0.2], [31.4, 1.2], [35.9, 3.2], [35.4, 4], [13, 3.8], [1, 22.8], [0, 22.7]], "o": [[0.5, -8.3], [1.5, -0.7999999999999998], [1.6000000000000014, -0.5], [1.5999999999999979, -0.1], [1.6000000000000014, 0.2], [1.6000000000000014, 0.5], [0, 0], [-6.9, -4], [-7, 3.8], [0, 0], [0, 0]]}], "h": 1}, {"t": 13, "s": [{"c": true, "i": [[0, 0], [-7.6, 3.8], [-1.6000000000000014, 0.3999999999999999], [-1.6999999999999993, 0.1], [-1.6000000000000014, -0.3], [-1.5999999999999979, -0.6], [-1.3999999999999986, -0.9000000000000004], [0, 0], [7.3, -3.7], [0.5, -8.1], [0, 0]], "v": [[0.1, 22.4], [13.1, 2.6], [17.8, 0.8], [22.7, 0], [27.6, 0.3], [32.4, 1.5], [36.9, 3.7], [36.4, 4.6], [13.6, 3.5], [1, 22.5], [0.1, 22.4]], "o": [[0.6, -8.5], [1.5, -0.8], [1.5999999999999979, -0.4], [1.6000000000000014, 0], [1.5999999999999979, 0.2], [1.5, 0.6000000000000001], [0, 0], [-6.9, -4.4], [-7.3, 3.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 14, "s": [{"c": true, "i": [[0, 0], [-0.5000000000000001, 1.5999999999999979], [-0.7999999999999998, 1.5], [-1.1000000000000005, 1.299999999999999], [-1.3000000000000007, 1], [-1.5999999999999996, 0.6999999999999997], [-7, -5], [0, 0], [7.5, -3.5], [0.7, -8.3], [0, 0]], "v": [[0.1, 22.1], [1.1, 17.1], [3, 12.4], [5.9, 8.3], [9.5, 4.8], [13.9, 2.2], [37.9, 4.5], [37.4, 5.2], [14.3, 3.1], [1, 22.2], [0.1, 22.1]], "o": [[0.1, -1.7000000000000028], [0.3999999999999999, -1.6000000000000014], [0.7999999999999998, -1.5], [1.0999999999999996, -1.3000000000000007], [1.4000000000000004, -1], [7.8, -3.6], [0, 0], [-6.8, -4.8], [-7.5, 3.5], [0, 0], [0, 0]]}], "h": 1}, {"t": 15, "s": [{"c": true, "i": [[0, 0], [-8.2, 3.4], [-1.6999999999999993, 0.29999999999999993], [-1.6999999999999993, -0.1], [-1.6999999999999993, -0.4], [-1.5, -0.8], [-1.3999999999999986, -1.1000000000000005], [0, 0], [7.8, -3.3], [0.8, -8.4], [0, 0]], "v": [[0.1, 21.7], [14.7, 1.9], [19.7, 0.4], [24.8, 0.1], [29.9, 0.8], [34.7, 2.6], [39.1, 5.4], [38.5, 6.1], [15.1, 2.7], [1.1, 21.8], [0.1, 21.7]], "o": [[0.9, -8.8], [1.6000000000000014, -0.7], [1.6999999999999993, -0.30000000000000004], [1.6999999999999993, 0], [1.6000000000000014, 0.3999999999999999], [1.5, 0.6999999999999997], [0, 0], [-6.6, -5.3], [-7.8, 3.3], [0, 0], [0, 0]]}], "h": 1}, {"t": 16, "s": [{"c": true, "i": [[0, 0], [-8.5, 3.1], [-1.7999999999999972, 0.3], [-1.8000000000000007, -0.1], [-1.6999999999999993, -0.6], [-1.5, -0.8999999999999999], [-1.2999999999999972, -1.2000000000000002], [0, 0], [8.2, -3], [1, -8.6], [0, 0]], "v": [[0.2, 21.2], [15.7, 1.5], [20.9, 0.2], [26.2, 0.1], [31.3, 1.2], [36.1, 3.3], [40.4, 6.5], [39.8, 7.2], [16.1, 2.4], [1.1, 21.3], [0.2, 21.2]], "o": [[1, -9], [1.6999999999999993, -0.6], [1.8000000000000007, -0.2], [1.6999999999999993, 0.19999999999999998], [1.6999999999999993, 0.5], [1.5, 0.9000000000000004], [0, 0], [-6.3, -6], [-8.2, 3], [0, 0], [0, 0]]}], "h": 1}, {"t": 17, "s": [{"c": true, "i": [[0, 0], [-8.9, 2.7], [-1.8000000000000007, 0.1], [-1.8000000000000007, -0.19999999999999998], [-1.6999999999999993, -0.5999999999999999], [-1.5, -1], [-1.2999999999999972, -1.4000000000000004], [0, 0], [8.6, -2.6], [1.2, -8.9], [0, 0]], "v": [[0.2, 20.7], [16.9, 1.1], [22.3, 0.1], [27.7, 0.3], [32.9, 1.7], [37.6, 4.3], [41.8, 7.9], [41.1, 8.6], [17.2, 2], [1.2, 20.8], [0.2, 20.7]], "o": [[1.3, -9.2], [1.8000000000000007, -0.5000000000000001], [1.8000000000000007, -0.1], [1.8000000000000007, 0.3], [1.6000000000000014, 0.7], [1.5, 1], [0, 0], [-6, -6.7], [-8.6, 2.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 18, "s": [{"c": true, "i": [[0, 0], [-9.4, 2.3], [-1.9000000000000021, 0], [-1.8000000000000007, -0.39999999999999997], [-1.6999999999999957, -0.8], [-1.3999999999999986, -1.2000000000000002], [-1.0999999999999943, -1.5999999999999996], [0, 0], [9, -2.2], [1.5, -9.1], [0, 0]], "v": [[0.3, 20.1], [18.4, 0.7], [24.1, 0], [29.6, 0.6], [34.8, 2.5], [39.4, 5.5], [43.3, 9.7], [42.5, 10.3], [18.6, 1.6], [1.3, 20.3], [0.3, 20.1]], "o": [[1.6, -9.5], [1.9000000000000021, -0.49999999999999994], [1.7999999999999972, 0], [1.7999999999999972, 0.4], [1.6000000000000014, 0.7999999999999998], [1.5, 1.2999999999999998], [0, 0], [-5.5, -7.5], [-9, 2.2], [0, 0], [0, 0]]}], "h": 1}, {"t": 19, "s": [{"c": true, "i": [[0, 0], [-0.8000000000000003, 1.799999999999999], [-1.2000000000000002, 1.5], [-1.5, 1.1999999999999993], [-1.8000000000000007, 0.7000000000000002], [-2, 0.3], [-5, -8.7], [0, 0], [9.5, -1.6], [1.9, -9.4], [0, 0]], "v": [[0.4, 19.4], [2.2, 13.8], [5.3, 8.9], [9.4, 4.9], [14.4, 2], [20.1, 0.3], [44.8, 12.1], [44, 12.5], [20.3, 1.3], [1.4, 19.6], [0.4, 19.4]], "o": [[0.4, -2], [0.8999999999999999, -1.8000000000000007], [1.2000000000000002, -1.5], [1.5999999999999996, -1.2000000000000002], [1.799999999999999, -0.8], [9.9, -1.6], [0, 0], [-4.8, -8.3], [-9.5, 1.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 20, "s": [{"c": true, "i": [[0, 0], [-10.4, 0.8], [-2, -0.30000000000000004], [-1.7999999999999972, -0.8000000000000003], [-1.6000000000000014, -1.2000000000000002], [-1.1999999999999957, -1.5999999999999996], [-0.7999999999999972, -1.9000000000000004], [0, 0], [10, -0.8], [2.3, -9.8], [0, 0]], "v": [[0.6, 18.5], [22.1, 0.1], [28.2, 0.4], [34, 2.2], [39.1, 5.3], [43.3, 9.6], [46.3, 15], [45.4, 15.4], [22.2, 1], [1.6, 18.8], [0.6, 18.5]], "o": [[2.4, -10.2], [2.099999999999998, -0.2], [2, 0.4], [1.7999999999999972, 0.7999999999999998], [1.6000000000000014, 1.2999999999999998], [1.2000000000000028, 1.700000000000001], [0, 0], [-3.8, -9.3], [-10, 0.8], [0, 0], [0, 0]]}], "h": 1}, {"t": 21, "s": [{"c": true, "i": [[0, 0], [-11, -0.3], [-2.1000000000000014, -0.6], [-1.8000000000000043, -1.1], [-1.3999999999999986, -1.5], [-1, -1.8999999999999986], [-0.5, -2.099999999999998], [0, 0], [10.6, 0.3], [2.8, -10.2], [0, 0]], "v": [[0.9, 17.5], [24.6, 0], [31, 1], [36.7, 3.6], [41.5, 7.6], [45.2, 12.7], [47.4, 18.7], [46.5, 18.9], [24.6, 1], [1.8, 17.8], [0.9, 17.5]], "o": [[3, -10.6], [2.1999999999999993, 0.1], [2, 0.6000000000000001], [1.7999999999999972, 1.1], [1.3999999999999986, 1.5], [1, 1.8000000000000007], [0, 0], [-2.3, -10.3], [-10.6, -0.3], [0, 0], [0, 0]]}], "h": 1}, {"t": 22, "s": [{"c": true, "i": [[0, 0], [-11.5, -1.7], [-2, -0.9000000000000001], [-1.7000000000000028, -1.3999999999999995], [-1.2999999999999972, -1.799999999999999], [-0.6999999999999957, -2.1000000000000014], [-0.10000000000000142, -2.3000000000000007], [0, 0], [11.1, 1.6], [3.6, -10.6], [0, 0]], "v": [[1.2, 16.4], [27.5, 0.3], [34, 2.2], [39.6, 5.8], [44, 10.7], [46.9, 16.6], [48, 23.3], [47, 23.4], [27.4, 1.2], [2.2, 16.7], [1.2, 16.4]], "o": [[3.7, -11], [2.3000000000000007, 0.3], [2.1000000000000014, 1], [1.6999999999999957, 1.4000000000000004], [1.2000000000000028, 1.8000000000000007], [0.6000000000000014, 2.099999999999998], [0, 0], [-0.3, -11.2], [-11.1, -1.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 23, "s": [{"c": true, "i": [[0, 0], [-11.9, -3.6], [-1.8999999999999986, -1.2999999999999998], [-1.5, -1.8000000000000007], [-0.9000000000000057, -2.1000000000000014], [-0.19999999999999574, -2.3999999999999986], [0.5, -2.3999999999999986], [0, 0], [11.4, 3.5], [4.5, -11.1], [0, 0]], "v": [[1.8, 15], [31, 1], [37.5, 4.1], [42.7, 8.9], [46.2, 14.8], [47.9, 21.7], [47.5, 28.9], [46.6, 28.7], [30.7, 2], [2.6, 15.4], [1.8, 15]], "o": [[4.7, -11.5], [2.3999999999999986, 0.7], [2, 1.3000000000000007], [1.3999999999999986, 1.799999999999999], [0.8999999999999986, 2.1999999999999993], [0.20000000000000284, 2.3000000000000007], [0, 0], [2.4, -11.7], [-11.4, -3.5], [0, 0], [0, 0]]}], "h": 1}, {"t": 24, "s": [{"c": true, "i": [[0, 0], [-8.6, 0.5], [-2.6999999999999993, -0.8], [-2.1999999999999957, -1.6999999999999997], [-1.6000000000000014, -2.4000000000000004], [4, -7.7], [0, 0], [4.5, 6.9], [8.3, -0.5], [3.6, -7.4], [0, 0]], "v": [[2.5, 13.4], [22.5, 0], [30.9, 1], [38.3, 4.8], [44.1, 10.9], [45.3, 35], [44.5, 34.6], [43.3, 11.4], [22.6, 1], [3.3, 13.9], [2.5, 13.4]], "o": [[3.8, -7.8], [2.8999999999999986, -0.2], [2.700000000000003, 0.8], [2.3000000000000043, 1.6000000000000005], [4.7, 7.2], [0, 0], [3.8, -7.4], [-4.5, -6.9], [-8.3, 0.5], [0, 0], [0, 0]]}], "h": 1}, {"t": 25, "s": [{"c": true, "i": [[0, 0], [-9.2, -0.9], [-3.2, -8.7], [0.10000000000000142, -3], [1.1000000000000014, -2.799999999999997], [2.200000000000003, -2.1999999999999957], [0, 0], [3, 8.4], [8.9, 0.8], [4.6, -7.7], [0, 0]], "v": [[3.4, 11.7], [26.3, 0.1], [46.6, 15.8], [48, 24.7], [46.1, 33.4], [41, 40.9], [40.4, 40.2], [45.7, 16.1], [26.2, 1.1], [4.2, 12.2], [3.4, 11.7]], "o": [[4.7, -8], [9.2, 0.9], [1.1000000000000014, 2.8999999999999986], [-0.10000000000000142, 3], [-1.2000000000000028, 2.700000000000003], [0, 0], [6.3, -6.3], [-3, -8.4], [-8.9, -0.8], [0, 0], [0, 0]]}], "h": 1}, {"t": 26, "s": [{"c": true, "i": [[0, 0], [-9.6, -2.5], [-1.1, -9.9], [0.8999999999999986, -3], [2, -2.5], [3, -1.5], [0, 0], [1, 9.5], [9.3, 2.4], [5.6, -7.8], [0, 0]], "v": [[4.5, 10.1], [30.1, 0.8], [47.9, 21.4], [47, 31], [42.5, 39.4], [34.9, 45.4], [34.4, 44.5], [46.9, 21.5], [29.8, 1.7], [5.3, 10.6], [4.5, 10.1]], "o": [[5.8, -8.1], [9.6, 2.5], [0.3999999999999986, 3.3000000000000007], [-0.8999999999999986, 3.1000000000000014], [-2.1000000000000014, 2.3999999999999986], [0, 0], [8.5, -4.3], [-1, -9.5], [-9.3, -2.4], [0, 0], [0, 0]]}], "h": 1}, {"t": 27, "s": [{"c": true, "i": [[0, 0], [-9.7, -4.3], [1.3, -10.5], [1.8000000000000043, -2.9000000000000057], [2.8000000000000043, -1.8999999999999986], [3.5, -0.6000000000000014], [0, 0], [-1.3, 10.1], [9.3, 4.1], [6.6, -7.8], [0, 0]], "v": [[5.7, 8.5], [33.7, 2], [47.8, 27], [44.4, 36.7], [37.4, 43.9], [27.8, 47.7], [27.7, 46.7], [46.9, 26.9], [33.3, 2.9], [6.4, 9.1], [5.7, 8.5]], "o": [[6.9, -8.1], [9.7, 4.3], [-0.3999999999999986, 3.5], [-1.7999999999999972, 2.799999999999997], [-2.8999999999999986, 1.8999999999999986], [0, 0], [10.1, -1.6], [1.3, -10.1], [-9.3, -4.1], [0, 0], [0, 0]]}], "h": 1}, {"t": 28, "s": [{"c": true, "i": [[0, 0], [-9.5, -6], [3.8, -10.6], [2.5, -2.5], [3.3000000000000007, -1.2000000000000028], [3.6999999999999993, 0.5], [0, 0], [-3.6, 10.2], [9.2, 5.7], [7.6, -7.7], [0, 0]], "v": [[6.9, 7.2], [36.7, 3.7], [46.6, 32], [40.8, 41.1], [31.8, 46.7], [21, 47.8], [21.2, 46.9], [45.7, 31.7], [36.2, 4.5], [7.6, 7.9], [6.9, 7.2]], "o": [[7.9, -8], [9.5, 6], [-1.3000000000000043, 3.5], [-2.5999999999999943, 2.5], [-3.400000000000002, 1.0999999999999943], [0, 0], [10.7, 1.3], [3.6, -10.2], [-9.2, -5.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 29, "s": [{"c": true, "i": [[0, 0], [-3.5999999999999996, 0.8999999999999999], [-3.6000000000000014, -0.8], [-3, -2.5], [6, -10.2], [11, 4.4], [0, 0], [-5.8, 9.8], [8.8, 7.2], [8.5, -7.5], [0, 0]], "v": [[8, 6.1], [18, 0.8], [29, 0.6], [39.2, 5.5], [44.7, 36.2], [15.2, 46.3], [15.5, 45.4], [43.8, 35.7], [38.6, 6.2], [8.7, 6.8], [8, 6.1]], "o": [[2.9000000000000004, -2.5999999999999996], [3.6000000000000014, -1], [3.700000000000003, 0.7999999999999999], [9.1, 7.5], [-6, 10.2], [0, 0], [10.6, 4.2], [5.8, -9.8], [-8.8, -7.2], [0, 0], [0, 0]]}], "h": 1}, {"t": 30, "s": [{"c": true, "i": [[0, 0], [-8.6, -8.9], [-1.1000000000000014, -3.6999999999999993], [0.7999999999999972, -3.8000000000000007], [2.700000000000003, -3.1000000000000014], [10.2, 7], [0, 0], [-7.6, 9], [8.2, 8.5], [9.3, -7.3], [0, 0]], "v": [[9.1, 5.2], [41.2, 7.3], [47.1, 17.5], [47.5, 29], [42.3, 39.5], [10.4, 43.8], [11, 43], [41.6, 38.9], [40.6, 8], [9.7, 5.9], [9.1, 5.2]], "o": [[9.7, -7.6], [2.8999999999999986, 3.000000000000001], [1, 3.6999999999999993], [-0.7999999999999972, 3.700000000000003], [-8, 9.4], [0, 0], [9.8, 6.7], [7.6, -9], [-8.2, -8.5], [0, 0], [0, 0]]}], "h": 1}, {"t": 31, "s": [{"c": true, "i": [[0, 0], [-7.9, -10], [9.6, -8.4], [3.8999999999999986, -0.7999999999999972], [3.8000000000000007, 1.1000000000000014], [3.000000000000001, 3.0999999999999943], [0, 0], [-9.2, 8.1], [7.6, 9.6], [10, -7.1], [0, 0]], "v": [[10.1, 4.4], [42.8, 9.1], [39.8, 42], [29, 47.4], [17.2, 47], [6.8, 40.7], [7.5, 40.1], [39.2, 41.3], [42.1, 9.7], [10.7, 5.2], [10.1, 4.4]], "o": [[10.4, -7.4], [7.9, 10], [-3.1999999999999957, 2.799999999999997], [-3.8999999999999986, 0.8999999999999986], [-3.799999999999999, -1.1000000000000014], [0, 0], [8.5, 8.8], [9.2, -8.1], [-7.6, -9.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 32, "s": [{"c": true, "i": [[0, 0], [-4.099999999999998, 0.2], [-3.6999999999999957, -1.8], [-2.3999999999999986, -3.7], [11, -7.3], [7.4, 10.9], [0, 0], [-10.5, 7], [6.9, 10.6], [10.6, -6.8], [0, 0]], "v": [[11, 3.8], [22.9, 0], [34.8, 2.6], [44.1, 10.9], [37.3, 44], [4.1, 37.5], [4.9, 36.9], [36.8, 43.2], [43.3, 11.4], [11.6, 4.6], [11, 3.8]], "o": [[3.6999999999999993, -2.4], [4.100000000000001, -0.1], [3.700000000000003, 1.8000000000000003], [7.2, 11], [-11, 7.3], [0, 0], [7.1, 10.5], [10.5, -7], [-6.9, -10.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 33, "s": [{"c": true, "i": [[0, 0], [-7.8, -6.2], [2.9, -9.5], [9.9, -0.7], [4.2, 9], [0, 0], [-9.5, 0.6], [-2.8, 9.1], [7.4, 5.9], [8.2, -4.8], [0, 0]], "v": [[11.9, 3.3], [38.9, 5.2], [46.9, 31.1], [25.6, 47.9], [2.3, 34.2], [3.1, 33.8], [25.6, 47], [46, 30.9], [38.3, 6], [12.4, 4.1], [11.9, 3.3]], "o": [[8.6, -5], [7.8, 6.2], [-2.9, 9.5], [-9.9, 0.7], [0, 0], [4, 8.6], [9.5, -0.6], [2.8, -9.1], [-7.4, -5.9], [0, 0], [0, 0]]}], "h": 1}, {"t": 34, "s": [{"c": true, "i": [[0, 0], [-7.5, -6.8], [3.9, -9.4], [10.1, 0.5], [3, 9.7], [0, 0], [-9.7, -0.5], [-3.7, 9], [7.2, 6.5], [8.6, -4.6], [0, 0]], "v": [[12.7, 2.8], [40.1, 6.2], [46.2, 33.2], [22.8, 48], [1, 31], [2, 30.7], [22.9, 47], [45.3, 32.8], [39.5, 6.9], [13.1, 3.7], [12.7, 2.8]], "o": [[8.9, -4.8], [7.5, 6.8], [-3.9, 9.4], [-10.1, -0.5], [0, 0], [2.8, 9.3], [9.7, 0.5], [3.7, -9], [-7.2, -6.5], [0, 0], [0, 0]]}], "h": 1}, {"t": 35, "s": [{"c": true, "i": [[0, 0], [-7.3, -7.4], [4.7, -9.2], [10.2, 1.6], [1.7, 10.2], [0, 0], [-9.8, -1.5], [-4.5, 8.8], [7, 7.1], [8.9, -4.4], [0, 0]], "v": [[13.4, 2.5], [41.1, 7.2], [45.4, 34.9], [20.3, 47.7], [0.3, 28], [1.3, 27.8], [20.5, 46.8], [44.5, 34.5], [40.4, 7.8], [13.8, 3.3], [13.4, 2.5]], "o": [[9.3, -4.6], [7.3, 7.4], [-4.7, 9.2], [-10.2, -1.6], [0, 0], [1.7, 9.8], [9.8, 1.5], [4.5, -8.8], [-7, -7.1], [0, 0], [0, 0]]}], "h": 1}, {"t": 36, "s": [{"c": true, "i": [[0, 0], [-7, -7.9], [5.5, -9], [10.2, 2.6], [0.5, 10.5], [0, 0], [-9.8, -2.5], [-5.3, 8.6], [6.7, 7.6], [9.2, -4.2], [0, 0]], "v": [[14.1, 2.1], [42, 8.1], [44.5, 36.5], [18.1, 47.3], [0, 25.2], [1, 25.1], [18.3, 46.3], [43.7, 36], [41.2, 8.7], [14.5, 3], [14.1, 2.1]], "o": [[9.6, -4.4], [7, 7.9], [-5.5, 9], [-10.2, -2.6], [0, 0], [0.5, 10.1], [9.8, 2.5], [5.3, -8.6], [-6.7, -7.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 37, "s": [{"c": true, "i": [[0, 0], [-6.7, -8.4], [6.2, -8.7], [10.1, 3.5], [-0.6, 10.7], [0, 0], [-9.7, -3.4], [-5.9, 8.4], [6.4, 8], [9.5, -4], [0, 0]], "v": [[14.7, 1.9], [42.7, 9], [43.6, 37.9], [16.1, 46.7], [0, 22.6], [1, 22.7], [16.4, 45.7], [42.8, 37.3], [42, 9.6], [15.1, 2.8], [14.7, 1.9]], "o": [[9.9, -4.2], [6.7, 8.4], [-6.2, 8.7], [-10.1, -3.5], [0, 0], [-0.6, 10.3], [9.7, 3.4], [5.9, -8.4], [-6.4, -8], [0, 0], [0, 0]]}], "h": 1}, {"t": 38, "s": [{"c": true, "i": [[0, 0], [-6.4, -8.8], [6.8, -8.5], [9.9, 4.4], [-1.7, 10.7], [0, 0], [-9.5, -4.2], [-6.5, 8.1], [6.2, 8.4], [9.7, -3.8], [0, 0]], "v": [[15.3, 1.6], [43.4, 9.8], [42.7, 39.1], [14.3, 45.9], [0.3, 20.2], [1.2, 20.4], [14.7, 45.1], [41.9, 38.5], [42.6, 10.4], [15.6, 2.5], [15.3, 1.6]], "o": [[10.1, -4], [6.4, 8.8], [-6.8, 8.5], [-9.9, -4.4], [0, 0], [-1.6, 10.3], [9.5, 4.2], [6.5, -8.1], [-6.2, -8.4], [0, 0], [0, 0]]}], "h": 1}, {"t": 39, "s": [{"c": true, "i": [[0, 0], [-6.1, -9.2], [7.4, -8.2], [9.7, 5.2], [-2.7, 10.7], [0, 0], [-9.3, -5], [-7.1, 7.8], [5.9, 8.8], [10, -3.6], [0, 0]], "v": [[15.8, 1.4], [43.9, 10.6], [41.8, 40.1], [12.7, 45.1], [0.7, 18.1], [1.7, 18.3], [13.1, 44.3], [41.1, 39.5], [43.1, 11.2], [16.1, 2.3], [15.8, 1.4]], "o": [[10.4, -3.8], [6.1, 9.2], [-7.4, 8.2], [-9.7, -5.2], [0, 0], [-2.6, 10.3], [9.3, 5], [7.1, -7.8], [-5.9, -8.8], [0, 0], [0, 0]]}], "h": 1}, {"t": 40, "s": [{"c": true, "i": [[0, 0], [-5.9, -9.5], [7.9, -7.9], [9.5, 5.9], [-3.7, 10.5], [0, 0], [-9.1, -5.7], [-7.6, 7.5], [5.6, 9.1], [10.2, -3.4], [0, 0]], "v": [[16.3, 1.3], [44.4, 11.4], [40.9, 41], [11.2, 44.3], [1.3, 16.1], [2.2, 16.5], [11.7, 43.5], [40.2, 40.4], [43.6, 11.9], [16.6, 2.2], [16.3, 1.3]], "o": [[10.6, -3.6], [5.9, 9.5], [-7.9, 7.9], [-9.5, -5.9], [0, 0], [-3.5, 10.1], [9.1, 5.7], [7.6, -7.5], [-5.6, -9.1], [0, 0], [0, 0]]}], "h": 1}, {"t": 41, "s": [{"c": true, "i": [[0, 0], [-5.6, -9.8], [8.4, -7.5], [9.2, 6.6], [-4.5, 10.3], [0, 0], [-8.8, -6.4], [-8.1, 7.2], [5.4, 9.4], [10.3, -3.3], [0, 0]], "v": [[16.8, 1.1], [44.9, 12.1], [40, 41.9], [9.9, 43.4], [2, 14.4], [2.9, 14.8], [10.5, 42.7], [39.4, 41.1], [44, 12.6], [17.1, 2], [16.8, 1.1]], "o": [[10.8, -3.4], [5.6, 9.8], [-8.4, 7.5], [-9.2, -6.6], [0, 0], [-4.4, 9.9], [8.8, 6.4], [8.1, -7.2], [-5.4, -9.4], [0, 0], [0, 0]]}], "h": 1}, {"t": 42, "s": [{"c": true, "i": [[0, 0], [-5.3, -10.1], [8.8, -7.2], [8.8, 7.2], [-5.3, 10.1], [0, 0], [-8.5, -6.9], [-8.5, 6.9], [5.1, 9.7], [10.5, -3.1], [0, 0]], "v": [[17.2, 1], [45.2, 12.8], [39.2, 42.6], [8.8, 42.6], [2.8, 12.8], [3.6, 13.2], [9.4, 41.8], [38.6, 41.8], [44.4, 13.3], [17.5, 1.9], [17.2, 1]], "o": [[11, -3.2], [5.3, 10.1], [-8.8, 7.2], [-8.8, -7.2], [0, 0], [-5.1, 9.7], [8.5, 6.9], [8.5, -6.9], [-5.1, -9.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 43, "s": [{"c": true, "i": [[0, 0], [-5.1, -10.4], [9.2, -6.9], [8.5, 7.8], [-6.1, 9.8], [0, 0], [-8.2, -7.5], [-8.9, 6.6], [4.9, 9.9], [10.7, -2.9], [0, 0]], "v": [[17.7, 0.9], [45.6, 13.5], [38.4, 43.2], [7.8, 41.7], [3.6, 11.3], [4.4, 11.9], [8.4, 41], [37.8, 42.5], [44.7, 13.9], [17.9, 1.8], [17.7, 0.9]], "o": [[11.1, -3], [5.1, 10.4], [-9.2, 6.9], [-8.5, -7.8], [0, 0], [-5.8, 9.4], [8.2, 7.5], [8.9, -6.6], [-4.9, -9.9], [0, 0], [0, 0]]}], "h": 1}, {"t": 44, "s": [{"c": true, "i": [[0, 0], [-4.8, -10.6], [9.6, -6.6], [8.2, 8.3], [-6.8, 9.5], [0, 0], [-7.8, -8], [-9.2, 6.3], [4.6, 10.2], [10.8, -2.8], [0, 0]], "v": [[18.1, 0.7], [45.9, 14.1], [37.6, 43.8], [6.9, 40.8], [4.5, 10.1], [5.2, 10.6], [7.6, 40.1], [37, 43], [45, 14.5], [18.3, 1.7], [18.1, 0.7]], "o": [[11.3, -2.9], [4.8, 10.6], [-9.6, 6.6], [-8.2, -8.3], [0, 0], [-6.5, 9.1], [7.8, 8], [9.2, -6.3], [-4.6, -10.2], [0, 0], [0, 0]]}], "h": 1}, {"t": 45, "s": [{"c": true, "i": [[0, 0], [-4.6, -10.8], [9.9, -6.3], [7.8, 8.8], [-7.4, 9.1], [0, 0], [-7.5, -8.4], [-9.5, 6], [4.4, 10.4], [11, -2.6], [0, 0]], "v": [[18.4, 0.7], [46.1, 14.7], [36.8, 44.3], [6.1, 39.9], [5.3, 8.9], [6.1, 9.5], [6.8, 39.3], [36.3, 43.5], [45.2, 15.1], [18.6, 1.6], [18.4, 0.7]], "o": [[11.4, -2.7], [4.6, 10.8], [-9.9, 6.3], [-7.8, -8.8], [0, 0], [-7.1, 8.8], [7.5, 8.4], [9.5, -6], [-4.4, -10.4], [0, 0], [0, 0]]}], "h": 1}, {"t": 46, "s": [{"c": true, "i": [[0, 0], [-4.3, -11], [10.2, -5.9], [7.4, 9.2], [-8, 8.8], [0, 0], [-7.1, -8.8], [-9.8, 5.7], [4.1, 10.6], [11.1, -2.5], [0, 0]], "v": [[18.8, 0.6], [46.3, 15.3], [36.1, 44.7], [5.3, 39.1], [6.2, 7.9], [6.9, 8.5], [6.1, 38.5], [35.6, 43.9], [45.5, 15.6], [19, 1.5], [18.8, 0.6]], "o": [[11.6, -2.6], [4.3, 11], [-10.2, 5.9], [-7.4, -9.2], [0, 0], [-7.6, 8.4], [7.1, 8.8], [9.8, -5.7], [-4.1, -10.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 47, "s": [{"c": true, "i": [[0, 0], [-4.1, -11.2], [10.5, -5.6], [7.1, 9.6], [-8.5, 8.4], [0, 0], [-6.8, -9.2], [-10.1, 5.4], [3.9, 10.8], [11.2, -2.3], [0, 0]], "v": [[19.1, 0.5], [46.6, 15.8], [35.4, 45.1], [4.7, 38.3], [7.1, 6.9], [7.8, 7.6], [5.5, 37.7], [34.9, 44.3], [45.7, 16.1], [19.3, 1.4], [19.1, 0.5]], "o": [[11.7, -2.4], [4.1, 11.2], [-10.5, 5.6], [-7.1, -9.6], [0, 0], [-8.1, 8.1], [6.8, 9.2], [10.1, -5.4], [-3.9, -10.8], [0, 0], [0, 0]]}], "h": 1}, {"t": 48, "s": [{"c": true, "i": [[0, 0], [-3.9, -11.4], [10.8, -5.3], [6.7, 9.9], [-8.9, 8], [0, 0], [-6.5, -9.5], [-10.3, 5.1], [3.7, 10.9], [11.3, -2.2], [0, 0]], "v": [[19.4, 0.4], [46.7, 16.3], [34.7, 45.5], [4.1, 37.5], [8, 6.1], [8.6, 6.8], [4.9, 36.9], [34.3, 44.6], [45.8, 16.6], [19.6, 1.4], [19.4, 0.4]], "o": [[11.8, -2.3], [3.9, 11.4], [-10.8, 5.3], [-6.7, -9.9], [0, 0], [-8.6, 7.7], [6.5, 9.5], [10.3, -5.1], [-3.7, -10.9], [0, 0], [0, 0]]}], "h": 1}, {"t": 49, "s": [{"c": true, "i": [[0, 0], [-3.6, -11.5], [11, -5.1], [6.4, 10.3], [-9.4, 7.6], [0, 0], [-6.1, -9.9], [-10.5, 4.9], [3.5, 11.1], [11.4, -2.1], [0, 0]], "v": [[19.7, 0.4], [46.9, 16.8], [34, 45.8], [3.6, 36.7], [8.9, 5.4], [9.5, 6.1], [4.4, 36.2], [33.6, 44.9], [46, 17.1], [19.9, 1.3], [19.7, 0.4]], "o": [[11.9, -2.2], [3.6, 11.5], [-11, 5.1], [-6.4, -10.3], [0, 0], [-9, 7.3], [6.1, 9.9], [10.5, -4.9], [-3.5, -11.1], [0, 0], [0, 0]]}], "h": 1}, {"t": 50, "s": [{"c": true, "i": [[0, 0], [-3.4, -11.7], [11.2, -4.8], [6, 10.6], [-9.8, 7.2], [0, 0], [-5.8, -10.1], [-10.7, 4.6], [3.3, 11.2], [11.5, -2], [0, 0]], "v": [[20, 0.3], [47, 17.2], [33.4, 46.1], [3.2, 35.9], [9.7, 4.7], [10.3, 5.5], [4, 35.5], [33, 45.2], [46.1, 17.5], [20.2, 1.3], [20, 0.3]], "o": [[12, -2], [3.4, 11.7], [-11.2, 4.8], [-6, -10.6], [0, 0], [-9.4, 7], [5.8, 10.1], [10.7, -4.6], [-3.3, -11.2], [0, 0], [0, 0]]}], "h": 1}, {"t": 51, "s": [{"c": true, "i": [[0, 0], [-3.2, -11.8], [11.4, -4.5], [5.7, 10.8], [-10.1, 6.9], [0, 0], [-5.5, -10.4], [-10.9, 4.3], [3.1, 11.3], [11.6, -1.8], [0, 0]], "v": [[20.3, 0.3], [47.2, 17.7], [32.8, 46.3], [2.8, 35.2], [10.5, 4.1], [11.1, 4.9], [3.6, 34.8], [32.5, 45.4], [46.2, 17.9], [20.4, 1.2], [20.3, 0.3]], "o": [[12.1, -1.9], [3.2, 11.8], [-11.4, 4.5], [-5.7, -10.8], [0, 0], [-9.7, 6.6], [5.5, 10.4], [10.9, -4.3], [-3.1, -11.3], [0, 0], [0, 0]]}], "h": 1}, {"t": 52, "s": [{"c": true, "i": [[0, 0], [-3, -11.9], [11.6, -4.2], [5.4, 11.1], [-10.5, 6.5], [0, 0], [-5.2, -10.6], [-11.1, 4.1], [2.9, 11.4], [11.7, -1.7], [0, 0]], "v": [[20.5, 0.3], [47.3, 18.1], [32.3, 46.5], [2.4, 34.5], [11.3, 3.6], [11.8, 4.4], [3.3, 34.1], [31.9, 45.6], [46.3, 18.3], [20.6, 1.2], [20.5, 0.3]], "o": [[12.2, -1.8], [3, 11.9], [-11.6, 4.2], [-5.4, -11.1], [0, 0], [-10, 6.2], [5.2, 10.6], [11.1, -4.1], [-2.9, -11.4], [0, 0], [0, 0]]}], "h": 1}, {"t": 53, "s": [{"c": true, "i": [[0, 0], [-2.8, -12], [11.7, -4], [5.1, 11.3], [-10.7, 6.1], [0, 0], [-4.9, -10.8], [-11.2, 3.8], [2.7, 11.6], [11.8, -1.6], [0, 0]], "v": [[20.7, 0.2], [47.4, 18.5], [31.7, 46.7], [2.1, 33.8], [12.1, 3.2], [12.6, 4], [3, 33.5], [31.4, 45.8], [46.4, 18.7], [20.9, 1.2], [20.7, 0.2]], "o": [[12.3, -1.7], [2.8, 12], [-11.7, 4], [-5.1, -11.3], [0, 0], [-10.3, 5.9], [4.9, 10.8], [11.2, -3.8], [-2.7, -11.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 54, "s": [{"c": true, "i": [[0, 0], [-2.7, -12.1], [11.9, -3.7], [4.8, 11.5], [-11, 5.8], [0, 0], [-4.6, -11], [-11.4, 3.6], [2.6, 11.7], [11.8, -1.5], [0, 0]], "v": [[21, 0.2], [47.4, 18.9], [31.2, 46.9], [1.8, 33.2], [12.9, 2.7], [13.3, 3.6], [2.7, 32.8], [30.9, 46], [46.5, 19.1], [21.1, 1.1], [21, 0.2]], "o": [[12.3, -1.6], [2.7, 12.1], [-11.9, 3.7], [-4.8, -11.5], [0, 0], [-10.6, 5.5], [4.6, 11], [11.4, -3.6], [-2.6, -11.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 55, "s": [{"c": true, "i": [[0, 0], [-2.5, -12.2], [12, -3.5], [4.5, 11.7], [-11.2, 5.4], [0, 0], [-4.3, -11.2], [-11.5, 3.4], [2.4, 11.7], [11.9, -1.4], [0, 0]], "v": [[21.2, 0.2], [47.5, 19.2], [30.7, 47], [1.6, 32.6], [13.6, 2.4], [14, 3.2], [2.5, 32.3], [30.4, 46.1], [46.6, 19.4], [21.3, 1.1], [21.2, 0.2]], "o": [[12.4, -1.5], [2.5, 12.2], [-12, 3.5], [-4.5, -11.7], [0, 0], [-10.8, 5.2], [4.3, 11.2], [11.5, -3.4], [-2.4, -11.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 56, "s": [{"c": true, "i": [[0, 0], [-2.3, -12.3], [12.1, -3.3], [4.2, 11.8], [-11.5, 5.1], [0, 0], [-4, -11.3], [-11.6, 3.1], [2.2, 11.8], [12, -1.3], [0, 0]], "v": [[21.4, 0.1], [47.6, 19.5], [30.2, 47.2], [1.4, 32], [14.3, 2.1], [14.7, 2.9], [2.3, 31.7], [30, 46.2], [46.6, 19.7], [21.5, 1.1], [21.4, 0.1]], "o": [[12.5, -1.4], [2.3, 12.3], [-12.1, 3.3], [-4.2, -11.8], [0, 0], [-11, 4.9], [4, 11.3], [11.6, -3.1], [-2.2, -11.8], [0, 0], [0, 0]]}], "h": 1}, {"t": 57, "s": [{"c": true, "i": [[0, 0], [-2.2, -12.4], [12.2, -3], [3.9, 12], [-11.7, 4.8], [0, 0], [-3.8, -11.5], [-11.7, 2.9], [2.1, 11.9], [12, -1.2], [0, 0]], "v": [[21.6, 0.1], [47.6, 19.9], [29.8, 47.3], [1.2, 31.5], [14.9, 1.8], [15.3, 2.7], [2.1, 31.2], [29.6, 46.4], [46.7, 20], [21.7, 1.1], [21.6, 0.1]], "o": [[12.5, -1.3], [2.2, 12.4], [-12.2, 3], [-3.9, -12], [0, 0], [-11.2, 4.6], [3.8, 11.5], [11.7, -2.9], [-2.1, -11.9], [0, 0], [0, 0]]}], "h": 1}, {"t": 58, "s": [{"c": true, "i": [[0, 0], [-2, -12.5], [12.3, -2.8], [3.6, 12.1], [-11.8, 4.4], [0, 0], [-3.5, -11.6], [-11.8, 2.7], [1.9, 12], [12.1, -1.1], [0, 0]], "v": [[21.7, 0.1], [47.7, 20.2], [29.4, 47.4], [1, 30.9], [15.6, 1.5], [15.9, 2.4], [1.9, 30.6], [29.2, 46.5], [46.7, 20.3], [21.8, 1.1], [21.7, 0.1]], "o": [[12.6, -1.2], [2, 12.5], [-12.3, 2.8], [-3.6, -12.1], [0, 0], [-11.4, 4.3], [3.5, 11.6], [11.8, -2.7], [-1.9, -12], [0, 0], [0, 0]]}], "h": 1}, {"t": 59, "s": [{"c": true, "i": [[0, 0], [-1.9, -12.5], [12.4, -2.6], [3.4, 12.2], [-12, 4.1], [0, 0], [-3.3, -11.7], [-11.9, 2.5], [1.8, 12], [12.1, -1.1], [0, 0]], "v": [[21.9, 0.1], [47.7, 20.5], [29, 47.5], [0.9, 30.4], [16.2, 1.3], [16.5, 2.2], [1.8, 30.2], [28.8, 46.5], [46.8, 20.6], [22, 1], [21.9, 0.1]], "o": [[12.6, -1.1], [1.9, 12.5], [-12.4, 2.6], [-3.4, -12.2], [0, 0], [-11.5, 4], [3.3, 11.7], [11.9, -2.5], [-1.8, -12], [0, 0], [0, 0]]}], "h": 1}, {"t": 60, "s": [{"c": true, "i": [[0, 0], [-1.7, -12.6], [12.5, -2.4], [3.1, 12.3], [-12.1, 3.8], [0, 0], [-3, -11.8], [-12, 2.3], [1.7, 12.1], [12.2, -1], [0, 0]], "v": [[22.1, 0.1], [47.8, 20.7], [28.6, 47.6], [0.7, 29.9], [16.8, 1.1], [17.1, 2], [1.7, 29.7], [28.4, 46.6], [46.8, 20.9], [22.1, 1], [22.1, 0.1]], "o": [[12.7, -1], [1.7, 12.6], [-12.5, 2.4], [-3.1, -12.3], [0, 0], [-11.7, 3.7], [3, 11.8], [12, -2.3], [-1.7, -12.1], [0, 0], [0, 0]]}], "h": 1}, {"t": 61, "s": [{"c": true, "i": [[0, 0], [-1.6, -12.7], [12.6, -2.3], [2.9, 12.4], [-12.3, 3.6], [0, 0], [-2.8, -11.9], [-12.1, 2.2], [1.5, 12.2], [12.2, -0.9], [0, 0]], "v": [[22.2, 0.1], [47.8, 21], [28.3, 47.6], [0.6, 29.5], [17.3, 0.9], [17.6, 1.9], [1.6, 29.3], [28.1, 46.7], [46.9, 21.1], [22.3, 1], [22.2, 0.1]], "o": [[12.7, -0.9], [1.6, 12.7], [-12.6, 2.3], [-2.9, -12.4], [0, 0], [-11.8, 3.4], [2.8, 11.9], [12.1, -2.2], [-1.5, -12.2], [0, 0], [0, 0]]}], "h": 1}, {"t": 62, "s": [{"c": true, "i": [[0, 0], [-1.5, -12.7], [12.6, -2.1], [2.7, 12.5], [-12.4, 3.3], [0, 0], [-2.6, -12], [-12.1, 2], [1.4, 12.2], [12.3, -0.8], [0, 0]], "v": [[22.4, 0.1], [47.8, 21.2], [27.9, 47.7], [0.5, 29], [17.8, 0.8], [18.1, 1.7], [1.5, 28.8], [27.8, 46.7], [46.9, 21.3], [22.4, 1], [22.4, 0.1]], "o": [[12.8, -0.9], [1.5, 12.7], [-12.6, 2.1], [-2.7, -12.5], [0, 0], [-11.9, 3.2], [2.6, 12], [12.1, -2], [-1.4, -12.2], [0, 0], [0, 0]]}], "h": 1}, {"t": 63, "s": [{"c": true, "i": [[0, 0], [-1.4, -12.8], [12.7, -1.9], [2.5, 12.6], [-12.5, 3], [0, 0], [-2.4, -12.1], [-12.2, 1.8], [1.3, 12.3], [12.3, -0.8], [0, 0]], "v": [[22.5, 0], [47.9, 21.4], [27.6, 47.7], [0.5, 28.6], [18.3, 0.7], [18.6, 1.6], [1.4, 28.4], [27.5, 46.8], [46.9, 21.6], [22.6, 1], [22.5, 0]], "o": [[12.8, -0.8], [1.4, 12.8], [-12.7, 1.9], [-2.5, -12.6], [0, 0], [-12, 2.9], [2.4, 12.1], [12.2, -1.8], [-1.3, -12.3], [0, 0], [0, 0]]}], "h": 1}, {"t": 64, "s": [{"c": true, "i": [[0, 0], [-1.3, -12.8], [12.8, -1.8], [2.3, 12.7], [-12.6, 2.8], [0, 0], [-2.2, -12.2], [-12.2, 1.7], [1.2, 12.3], [12.3, -0.7], [0, 0]], "v": [[22.6, 0], [47.9, 21.7], [27.3, 47.8], [0.4, 28.2], [18.8, 0.6], [19, 1.5], [1.3, 28.1], [27.2, 46.8], [46.9, 21.8], [22.7, 1], [22.6, 0]], "o": [[12.9, -0.7], [1.3, 12.8], [-12.8, 1.8], [-2.3, -12.7], [0, 0], [-12.1, 2.7], [2.2, 12.2], [12.2, -1.7], [-1.2, -12.3], [0, 0], [0, 0]]}], "h": 1}, {"t": 65, "s": [{"c": true, "i": [[0, 0], [-1.1, -12.9], [12.8, -1.6], [2.1, 12.7], [-12.7, 2.6], [0, 0], [-2, -12.2], [-12.3, 1.6], [1.1, 12.3], [12.4, -0.6], [0, 0]], "v": [[22.7, 0], [47.9, 21.9], [27, 47.8], [0.3, 27.9], [19.3, 0.5], [19.4, 1.4], [1.3, 27.7], [26.9, 46.9], [46.9, 22], [22.8, 1], [22.7, 0]], "o": [[12.9, -0.7], [1.1, 12.9], [-12.8, 1.6], [-2.1, -12.7], [0, 0], [-12.2, 2.5], [2, 12.2], [12.3, -1.6], [-1.1, -12.3], [0, 0], [0, 0]]}], "h": 1}, {"t": 66, "s": [{"c": true, "i": [[0, 0], [-1, -12.9], [12.9, -1.5], [1.9, 12.8], [-12.7, 2.3], [0, 0], [-1.8, -12.3], [-12.3, 1.4], [1, 12.4], [12.4, -0.6], [0, 0]], "v": [[22.9, 0], [47.9, 22.1], [26.7, 47.8], [0.3, 27.5], [19.7, 0.4], [19.9, 1.3], [1.2, 27.4], [26.6, 46.9], [47, 22.1], [22.9, 1], [22.9, 0]], "o": [[12.9, -0.6], [1, 12.9], [-12.9, 1.5], [-1.9, -12.8], [0, 0], [-12.2, 2.2], [1.8, 12.3], [12.3, -1.4], [-1, -12.4], [0, 0], [0, 0]]}], "h": 1}, {"t": 67, "s": [{"c": true, "i": [[0, 0], [-1, -12.9], [12.9, -1.3], [1.7, 12.9], [-12.8, 2.1], [0, 0], [-1.7, -12.3], [-12.4, 1.3], [0.9, 12.4], [12.4, -0.5], [0, 0]], "v": [[23, 0], [47.9, 22.2], [26.5, 47.9], [0.2, 27.2], [20.1, 0.3], [20.2, 1.3], [1.2, 27.1], [26.4, 46.9], [47, 22.3], [23, 1], [23, 0]], "o": [[13, -0.6], [1, 12.9], [-12.9, 1.3], [-1.7, -12.9], [0, 0], [-12.3, 2], [1.7, 12.3], [12.4, -1.3], [-0.9, -12.4], [0, 0], [0, 0]]}], "h": 1}, {"t": 68, "s": [{"c": true, "i": [[0, 0], [-0.9, -13], [12.9, -1.2], [1.6, 12.9], [-12.9, 1.9], [0, 0], [-1.5, -12.4], [-12.4, 1.2], [0.8, 12.5], [12.5, -0.5], [0, 0]], "v": [[23.1, 0], [47.9, 22.4], [26.2, 47.9], [0.2, 26.9], [20.4, 0.3], [20.6, 1.2], [1.1, 26.8], [26.2, 46.9], [47, 22.5], [23.1, 1], [23.1, 0]], "o": [[13, -0.5], [0.9, 13], [-12.9, 1.2], [-1.6, -12.9], [0, 0], [-12.3, 1.8], [1.5, 12.4], [12.4, -1.2], [-0.8, -12.5], [0, 0], [0, 0]]}], "h": 1}, {"t": 69, "s": [{"c": true, "i": [[0, 0], [-0.8, -13], [13, -1.1], [1.4, 12.9], [-12.9, 1.7], [0, 0], [-1.4, -12.4], [-12.5, 1.1], [0.7, 12.5], [12.5, -0.4], [0, 0]], "v": [[23.2, 0], [48, 22.6], [26, 47.9], [0.1, 26.6], [20.8, 0.2], [20.9, 1.2], [1.1, 26.5], [25.9, 47], [47, 22.6], [23.2, 1], [23.2, 0]], "o": [[13, -0.5], [0.8, 13], [-13, 1.1], [-1.4, -12.9], [0, 0], [-12.4, 1.7], [1.4, 12.4], [12.5, -1.1], [-0.7, -12.5], [0, 0], [0, 0]]}], "h": 1}, {"t": 70, "s": [{"c": true, "i": [[0, 0], [-0.7, -13], [13, -1], [1.3, 13], [-13, 1.6], [0, 0], [-1.2, -12.5], [-12.5, 0.9], [0.7, 12.5], [12.5, -0.4], [0, 0]], "v": [[23.2, 0], [48, 22.7], [25.8, 47.9], [0.1, 26.3], [21.1, 0.2], [21.2, 1.1], [1.1, 26.2], [25.7, 47], [47, 22.8], [23.3, 1], [23.2, 0]], "o": [[13, -0.4], [0.7, 13], [-13, 1], [-1.3, -13], [0, 0], [-12.4, 1.5], [1.2, 12.5], [12.5, -0.9], [-0.7, -12.5], [0, 0], [0, 0]]}], "h": 1}, {"t": 71, "s": [{"c": true, "i": [[0, 0], [-0.6, -13.1], [13, -0.9], [1.1, 13], [-13, 1.4], [0, 0], [-1.1, -12.5], [-12.5, 0.8], [0.6, 12.5], [12.5, -0.4], [0, 0]], "v": [[23.3, 0], [48, 22.9], [25.6, 47.9], [0.1, 26.1], [21.4, 0.1], [21.5, 1.1], [1, 26], [25.6, 47], [47, 22.9], [23.4, 1], [23.3, 0]], "o": [[13.1, -0.4], [0.6, 13.1], [-13, 0.9], [-1.1, -13], [0, 0], [-12.5, 1.3], [1.1, 12.5], [12.5, -0.8], [-0.6, -12.5], [0, 0], [0, 0]]}], "h": 1}, {"t": 72, "s": [{"c": true, "i": [[0, 0], [-0.6, -13.1], [13.1, -0.8], [1, 13.1], [-13, 1.2], [0, 0], [-1, -12.5], [-12.5, 0.8], [0.5, 12.6], [12.6, -0.3], [0, 0]], "v": [[23.4, 0], [48, 23], [25.4, 48], [0.1, 25.9], [21.7, 0.1], [21.8, 1.1], [1, 25.8], [25.4, 47], [47, 23], [23.4, 1], [23.4, 0]], "o": [[13.1, -0.3], [0.6, 13.1], [-13.1, 0.8], [-1, -13.1], [0, 0], [-12.5, 1.2], [1, 12.5], [12.5, -0.8], [-0.5, -12.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 73, "s": [{"c": true, "i": [[0, 0], [-0.5, -13.1], [13.1, -0.7], [0.9, 13.1], [-13.1, 1.1], [0, 0], [-0.9, -12.6], [-12.6, 0.7], [0.5, 12.6], [12.6, -0.3], [0, 0]], "v": [[23.5, 0], [48, 23.1], [25.3, 48], [0.1, 25.6], [22, 0.1], [22.1, 1], [1, 25.6], [25.2, 47], [47, 23.1], [23.5, 1], [23.5, 0]], "o": [[13.1, -0.3], [0.5, 13.1], [-13.1, 0.7], [-0.9, -13.1], [0, 0], [-12.5, 1.1], [0.9, 12.6], [12.6, -0.7], [-0.5, -12.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 74, "s": [{"c": true, "i": [[0, 0], [-0.4, -13.1], [13.1, -0.6], [0.8, 13.1], [-13.1, 1], [0, 0], [-0.8, -12.6], [-12.6, 0.6], [0.4, 12.6], [12.6, -0.2], [0, 0]], "v": [[23.5, 0], [48, 23.2], [25.1, 48], [0, 25.4], [22.2, 0.1], [22.3, 1], [1, 25.4], [25.1, 47], [47, 23.2], [23.6, 1], [23.5, 0]], "o": [[13.1, -0.3], [0.4, 13.1], [-13.1, 0.6], [-0.8, -13.1], [0, 0], [-12.6, 0.9], [0.8, 12.6], [12.6, -0.6], [-0.4, -12.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 75, "s": [{"c": true, "i": [[0, 0], [-0.4, -13.1], [13.1, -0.5], [0.7, 13.1], [-13.1, 0.8], [0, 0], [-0.7, -12.6], [-12.6, 0.5], [0.4, 12.6], [12.6, -0.2], [0, 0]], "v": [[23.6, 0], [48, 23.3], [25, 48], [0, 25.2], [22.5, 0], [22.5, 1], [1, 25.2], [24.9, 47], [47, 23.3], [23.6, 1], [23.6, 0]], "o": [[13.1, -0.2], [0.4, 13.1], [-13.1, 0.5], [-0.7, -13.1], [0, 0], [-12.6, 0.8], [0.7, 12.6], [12.6, -0.5], [-0.4, -12.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 76, "s": [{"c": true, "i": [[0, 0], [-0.3, -13.2], [13.2, -0.5], [0.6, 13.1], [-13.1, 0.7], [0, 0], [-0.6, -12.6], [-12.6, 0.4], [0.3, 12.6], [12.6, -0.2], [0, 0]], "v": [[23.7, 0], [48, 23.4], [24.8, 48], [0, 25.1], [22.7, 0], [22.7, 1], [1, 25], [24.8, 47], [47, 23.4], [23.7, 1], [23.7, 0]], "o": [[13.2, -0.2], [0.3, 13.2], [-13.2, 0.5], [-0.6, -13.1], [0, 0], [-12.6, 0.7], [0.6, 12.6], [12.6, -0.4], [-0.3, -12.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 77, "s": [{"c": true, "i": [[0, 0], [-0.3, -13.2], [13.2, -0.4], [0.5, 13.2], [-13.2, 0.6], [0, 0], [-0.5, -12.6], [-12.6, 0.4], [0.3, 12.6], [12.6, -0.2], [0, 0]], "v": [[23.7, 0], [48, 23.5], [24.7, 48], [0, 24.9], [22.9, 0], [22.9, 1], [1, 24.9], [24.7, 47], [47, 23.5], [23.7, 1], [23.7, 0]], "o": [[13.2, -0.2], [0.3, 13.2], [-13.2, 0.4], [-0.5, -13.2], [0, 0], [-12.6, 0.6], [0.5, 12.6], [12.6, -0.4], [-0.3, -12.6], [0, 0], [0, 0]]}], "h": 1}, {"t": 78, "s": [{"c": true, "i": [[0, 0], [-0.2, -13.2], [13.2, -0.3], [0.4, 13.2], [-13.2, 0.5], [0, 0], [-0.4, -12.7], [-12.7, 0.3], [0.2, 12.7], [12.7, -0.1], [0, 0]], "v": [[23.8, 0], [48, 23.6], [24.6, 48], [0, 24.8], [23.1, 0], [23.1, 1], [1, 24.7], [24.6, 47], [47, 23.6], [23.8, 1], [23.8, 0]], "o": [[13.2, -0.1], [0.2, 13.2], [-13.2, 0.3], [-0.4, -13.2], [0, 0], [-12.6, 0.5], [0.4, 12.7], [12.7, -0.3], [-0.2, -12.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 79, "s": [{"c": true, "i": [[0, 0], [-0.2, -13.2], [13.2, -0.3], [0.4, 13.2], [-13.2, 0.4], [0, 0], [-0.3, -12.7], [-12.7, 0.3], [0.2, 12.7], [12.7, -0.1], [0, 0]], "v": [[23.8, 0], [48, 23.6], [24.5, 48], [0, 24.6], [23.2, 0], [23.2, 1], [1, 24.6], [24.5, 47], [47, 23.7], [23.8, 1], [23.8, 0]], "o": [[13.2, -0.1], [0.2, 13.2], [-13.2, 0.3], [-0.4, -13.2], [0, 0], [-12.7, 0.4], [0.3, 12.7], [12.7, -0.3], [-0.2, -12.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 80, "s": [{"c": true, "i": [[0, 0], [-0.2, -13.2], [13.2, -0.2], [0.3, 13.2], [-13.2, 0.4], [0, 0], [-0.3, -12.7], [-12.7, 0.2], [0.2, 12.7], [12.7, -0.1], [0, 0]], "v": [[23.8, 0], [48, 23.7], [24.4, 48], [0, 24.5], [23.4, 0], [23.4, 1], [1, 24.5], [24.4, 47], [47, 23.7], [23.8, 1], [23.8, 0]], "o": [[13.2, -0.1], [0.2, 13.2], [-13.2, 0.2], [-0.3, -13.2], [0, 0], [-12.7, 0.3], [0.3, 12.7], [12.7, -0.2], [-0.2, -12.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 81, "s": [{"c": true, "i": [[0, 0], [-0.1, -13.2], [13.2, -0.2], [0.2, 13.2], [-13.2, 0.3], [0, 0], [-0.2, -12.7], [-12.7, 0.2], [0.1, 12.7], [12.7, -0.1], [0, 0]], "v": [[23.9, 0], [48, 23.8], [24.3, 48], [0, 24.4], [23.5, 0], [23.5, 1], [1, 24.4], [24.3, 47], [47, 23.8], [23.9, 1], [23.9, 0]], "o": [[13.2, -0.1], [0.1, 13.2], [-13.2, 0.2], [-0.2, -13.2], [0, 0], [-12.7, 0.3], [0.2, 12.7], [12.7, -0.2], [-0.1, -12.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 82, "s": [{"c": true, "i": [[0, 0], [-0.1, -13.2], [13.2, -0.1], [0.2, 13.2], [-13.2, 0.2], [0, 0], [-0.2, -12.7], [-12.7, 0.1], [0.1, 12.7], [12.7, -0.1], [0, 0]], "v": [[23.9, 0], [48, 23.8], [24.3, 48], [0, 24.3], [23.6, 0], [23.6, 1], [1, 24.3], [24.2, 47], [47, 23.8], [23.9, 1], [23.9, 0]], "o": [[13.2, -0.1], [0.1, 13.2], [-13.2, 0.1], [-0.2, -13.2], [0, 0], [-12.7, 0.2], [0.2, 12.7], [12.7, -0.1], [-0.1, -12.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 83, "s": [{"c": true, "i": [[0, 0], [-0.1, -13.2], [13.2, -0.1], [0.1, 13.2], [-13.2, 0.2], [0, 0], [-0.1, -12.7], [-12.7, 0.1], [0.1, 12.7], [12.7, 0], [0, 0]], "v": [[23.9, 0], [48, 23.9], [24.2, 48], [0, 24.3], [23.7, 0], [23.7, 1], [1, 24.2], [24.2, 47], [47, 23.9], [23.9, 1], [23.9, 0]], "o": [[13.2, 0], [0.1, 13.2], [-13.2, 0.1], [-0.1, -13.2], [0, 0], [-12.7, 0.2], [0.1, 12.7], [12.7, -0.1], [-0.1, -12.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 84, "s": [{"c": true, "i": [[0, 0], [-0.1, -13.2], [13.2, -0.1], [0.1, 13.2], [-13.2, 0.1], [0, 0], [-0.1, -12.7], [-12.7, 0.1], [0.1, 12.7], [12.7, 0], [0, 0]], "v": [[23.9, 0], [48, 23.9], [24.1, 48], [0, 24.2], [23.8, 0], [23.8, 1], [1, 24.2], [24.1, 47], [47, 23.9], [23.9, 1], [23.9, 0]], "o": [[13.2, 0], [0.1, 13.2], [-13.2, 0.1], [-0.1, -13.2], [0, 0], [-12.7, 0.1], [0.1, 12.7], [12.7, -0.1], [-0.1, -12.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 85, "s": [{"c": true, "i": [[0, 0], [0, -13.2], [13.2, -0.1], [0.1, 13.2], [-13.2, 0.1], [0, 0], [-0.1, -12.7], [-12.7, 0.1], [0, 12.7], [12.7, 0], [0, 0]], "v": [[24, 0], [48, 23.9], [24.1, 48], [0, 24.1], [23.8, 0], [23.9, 1], [1, 24.1], [24.1, 47], [47, 23.9], [24, 1], [24, 0]], "o": [[13.2, 0], [0, 13.2], [-13.2, 0.1], [-0.1, -13.2], [0, 0], [-12.7, 0.1], [0.1, 12.7], [12.7, -0.1], [0, -12.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 86, "s": [{"c": true, "i": [[0, 0], [0, -13.2], [13.2, 0], [0, 13.2], [-13.2, 0.1], [0, 0], [0, -12.7], [-12.7, 0], [0, 12.7], [12.7, 0], [0, 0]], "v": [[24, 0], [48, 24], [24.1, 48], [0, 24.1], [23.9, 0], [23.9, 1], [1, 24.1], [24.1, 47], [47, 24], [24, 1], [24, 0]], "o": [[13.2, 0], [0, 13.2], [-13.2, 0], [0, -13.2], [0, 0], [-12.7, 0.1], [0, 12.7], [12.7, 0], [0, -12.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 87, "s": [{"c": true, "i": [[0, 0], [0, -13.3], [13.3, 0], [0, 13.3], [-13.3, 0], [0, 0], [0, -12.7], [-12.7, 0], [0, 12.7], [12.7, 0], [0, 0]], "v": [[24, 0], [48, 24], [24, 48], [0, 24], [23.9, 0], [23.9, 1], [1, 24], [24, 47], [47, 24], [24, 1], [24, 0]], "o": [[13.3, 0], [0, 13.3], [-13.3, 0], [0, -13.3], [0, 0], [-12.7, 0], [0, 12.7], [12.7, 0], [0, -12.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 88, "s": [{"c": true, "i": [[0, 0], [0, -13.3], [13.3, 0], [0, 13.3], [-13.3, 0], [0, 0], [0, -12.7], [-12.7, 0], [0, 12.7], [12.7, 0], [0, 0]], "v": [[24, 0], [48, 24], [24, 48], [0, 24], [24, 0], [24, 1], [1, 24], [24, 47], [47, 24], [24, 1], [24, 0]], "o": [[13.3, 0], [0, 13.3], [-13.3, 0], [0, -13.3], [0, 0], [-12.7, 0], [0, 12.7], [12.7, 0], [0, -12.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 89, "s": [{"c": true, "i": [[0, 0], [0, -13.3], [13.3, 0], [0, 13.3], [-13.3, 0], [0, 0], [0, -12.7], [-12.7, 0], [0, 12.7], [12.7, 0], [0, 0]], "v": [[24, 0], [48, 24], [24, 48], [0, 24], [24, 0], [24, 1], [1, 24], [24, 47], [47, 24], [24, 1], [24, 0]], "o": [[13.3, 0], [0, 13.3], [-13.3, 0], [0, -13.3], [0, 0], [-12.7, 0], [0, 12.7], [12.7, 0], [0, -12.7], [0, 0], [0, 0]]}], "h": 1}, {"t": 90, "s": [{"c": true, "i": [[0, 0], [13.2, 0], [0, 13.2], [-13.2, 0], [-2.099999999999998, -0.6000000000000001], [-1.8000000000000043, -1.0999999999999996], [-1.5, -1.5], [-1, -1.8000000000000007], [-0.5, -2.0000000000000018], [0, -2.1999999999999993], [0, 0]], "v": [[48, 24], [24, 48], [0, 24], [24, 0], [30.4, 0.9], [36.1, 3.3], [41, 7.1], [44.7, 11.9], [47.1, 17.6], [48, 24], [48, 24]], "o": [[0, 13.2], [-13.2, 0], [0, -13.2], [2.1999999999999993, 0], [2, 0.4999999999999999], [1.7999999999999972, 1], [1.3999999999999986, 1.4000000000000004], [1.0999999999999943, 1.799999999999999], [0.6000000000000014, 2.099999999999998], [0, 0], [0, 0]]}], "h": 1}]}}, {"ty": "sh", "ks": {"a": 1, "k": [{"t": 0, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 1, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 2, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 3, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 4, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 5, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 6, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 7, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 8, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 9, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 10, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 11, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 12, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 13, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 14, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 15, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 16, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 17, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 18, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 19, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 20, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 21, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 22, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 23, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 24, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 25, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 26, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 27, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 28, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 29, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 30, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 31, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 32, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 33, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 34, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 35, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 36, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 37, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 38, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 39, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 40, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 41, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 42, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 43, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 44, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 45, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 46, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 47, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 48, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 49, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 50, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 51, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 52, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 53, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 54, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 55, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 56, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 57, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 58, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 59, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 60, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 61, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 62, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 63, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 64, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 65, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 66, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 67, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 68, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 69, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 70, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 71, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 72, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 73, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 74, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 75, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 76, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 77, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 78, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 79, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 80, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 81, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 82, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 83, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 84, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 85, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 86, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 87, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 88, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 89, "s": [{"c": true, "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000], [1000000, 1000000]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}], "h": 1}, {"t": 90, "s": [{"c": true, "i": [[0, 0], [12.7, 0], [0, -12.7], [-12.7, 0], [0, 12.7], [0, 0]], "v": [[47, 24], [24, 1], [1, 24], [24, 47], [47, 24], [47, 24]], "o": [[0, -12.7], [-12.7, 0], [0, 12.7], [12.7, 0], [0, 0], [0, 0]]}], "h": 1}]}}, {"ty": "st", "c": {"a": 0, "k": [0.733, 0.518, 1, 0.702]}, "lc": 1, "lj": 1, "ml": 10, "o": {"a": 0, "k": 70.2}, "w": {"a": 0, "k": 2}}, {"ty": "tr", "o": {"a": 0, "k": 100}}]}]}, {"ind": 11, "ty": 3, "ks": {"p": {"a": 0, "k": [1, 1]}}, "ip": 0, "op": 91, "st": 0}]}, {"id": "21", "layers": [{"ind": 20, "ty": 4, "parent": 19, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "rc", "p": {"a": 0, "k": [24, 0]}, "r": {"a": 0, "k": 0}, "s": {"a": 0, "k": [10000, 10000]}}, {"ty": "gf", "e": {"a": 0, "k": [24, -45.16]}, "g": {"p": 2, "k": {"a": 0, "k": [0.078, 0.592, 0.384, 0.996, 1, 0.137, 0, 0.408]}}, "t": 2, "o": {"a": 0, "k": 100}, "s": {"a": 0, "k": [24, 0]}}]}, {"ind": 19, "ty": 3, "parent": 18, "ks": {"a": {"a": 0, "k": [24, 0]}, "p": {"a": 0, "k": [24, 0]}, "r": {"a": 0, "k": 90}, "s": {"a": 0, "k": [108.7, 100]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 18, "ty": 3, "ks": {"p": {"a": 0, "k": [4977, 5435]}}, "ip": 0, "op": 91, "st": 0}]}], "fr": 60, "h": 56, "ip": 0, "layers": [{"ind": 3, "ty": 4, "parent": 2, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "el", "p": {"a": 0, "k": [2.62, 2.62]}, "s": {"a": 0, "k": [5.24, 5.24]}}, {"ty": "fl", "c": {"a": 0, "k": [0.365, 0.2, 0.698]}, "o": {"a": 0, "k": 100}}]}, {"ind": 2, "ty": 3, "parent": 1, "ks": {"p": {"a": 0, "k": [25.38, 21.72]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 8, "ty": 0, "parent": 4, "ks": {}, "w": 22, "h": 24, "ip": 0, "op": 91, "st": 0, "refId": "6"}, {"ind": 4, "ty": 3, "parent": 1, "ks": {"p": {"a": 0, "k": [17, 16]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 16, "ty": 0, "parent": 10, "ks": {"a": {"a": 0, "k": [1, 1]}, "o": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": 1, "y": 1}, "o": {"x": 0, "y": 0}}, {"t": 43.8, "s": [40], "h": 1}, {"t": 90, "s": [40], "h": 1}]}}, "w": 50, "h": 50, "ip": 0, "op": 91, "st": 0, "refId": "14"}, {"ind": 10, "ty": 3, "parent": 9, "ks": {}, "ip": 0, "op": 91, "st": 0}, {"ind": 9, "ty": 3, "parent": 1, "ks": {"p": {"a": 0, "k": [4, 4]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 24, "ty": 4, "td": 1, "parent": 17, "ks": {}, "ip": 0, "op": 91, "st": 0, "shapes": [{"ty": "el", "p": {"a": 0, "k": [24, 24]}, "s": {"a": 0, "k": [48, 48]}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0]}, "o": {"a": 0, "k": 100}}]}, {"ind": 23, "ty": 0, "tt": 1, "parent": 17, "ks": {"a": {"a": 0, "k": [4977, 5435]}}, "w": 10002, "h": 10870, "ip": 0, "op": 91, "st": 0, "refId": "21"}, {"ind": 17, "ty": 3, "parent": 1, "ks": {"p": {"a": 0, "k": [4, 4]}}, "ip": 0, "op": 91, "st": 0}, {"ind": 1, "ty": 3, "parent": 0, "ks": {}, "ip": 0, "op": 91, "st": 0}, {"ind": 0, "ty": 3, "ks": {}, "ip": 0, "op": 91, "st": 0}], "meta": {"g": "https://jitter.video"}, "op": 90, "v": "5.7.4", "w": 56}