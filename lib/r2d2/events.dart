import 'dart:async';
import 'dart:io';

import 'package:acko_flutter/common/model/OfflineData.dart';
import 'package:acko_flutter/common/model/drawer_item.dart';
import 'package:acko_flutter/common/model/vehicle_search_model.dart';
import 'package:acko_flutter/common/util/PreferenceHelper.dart' as prefs;
import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/feature/acko_services/bloc/map_location_bloc.dart';
import 'package:acko_flutter/feature/app_policy_page/model/policy_list_response.dart'
    as Policy;

import 'package:acko_flutter/feature/my_account/model/AutoPolicyEntity.dart';
import 'package:acko_flutter/feature/my_account/model/MyAccountItem.dart';
import 'package:acko_flutter/feature/my_account/model/internet_policies_by_group.dart';
import 'package:acko_flutter/feature/policy_details/models/policy_details_response.dart';
import 'package:acko_flutter/feature/profile/bloc/edit_details_bloc.dart';
import 'package:acko_flutter/feature/profile/model/update_user_request.dart';
import 'package:acko_flutter/r2d2/model/utm_params_model.dart';
import 'package:acko_flutter/r2d2/r2d2_helper.dart';
import 'package:acko_flutter/r2d2/r2d2_repository.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/deeplink/stored_deeplink.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/analytics.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:analytics/events/tap_events.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:session_manager_module/session_manager.dart';
import 'package:utilities/constants/constants.dart';

import '../common/util/PreferenceHelper.dart';
import '../feature/acko_services/model/map_location_model.dart';
import '../feature/acko_services/model/rto_model.dart';
import '../feature/acko_services/ui/acko_service_common_header.dart';
import '../feature/fnol/model/FnolStageApiResponse.dart';
import '../feature/fnol/model/fnol_google_place_api_response.dart';
import 'model/r2d2Request.dart';

enum ClickEventCardType {
  POLICY,
  CROSS_SELL,
  HEALTH_CARD,
  HEALTH_RETAIL_CARD,
  GMC_LINK_POLICY,
  ENDORSEMENT,
  PROPOSAL,
  PI,
  RENEWAL,
  POLICY_STATUS,
  PI_STATUS,
  PROPOSAL_STATUS,
  BANNER_CTA,
  BUY_CARDS,
  EMPTY_POLICY_BUY_NOW,
  REFRESH_MY_ACCOUNT
}

class R2D2Events {
  String? _app;
  late Map _staticEdata;
  R2D2Helper helper = R2D2Helper();
  late R2D2Repository _repository;

  R2D2Events._privateConstructor() {
    _staticEdata = Map<String, dynamic>();
    _repository = R2D2Repository();
  }

  static final R2D2Events _instance = R2D2Events._privateConstructor();

  static R2D2Events get instance => _instance;

  void init() async {
    if (Platform.isAndroid) {
      _app = 'android';
    } else if (Platform.isIOS) {
      _app = 'ios';
    }
  }

  Future<String?> setTrackerId() async {
    return await helper.addTrackerId();
  }

  Future<String> _getDeviceDetails() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String model, manufacturer, version;
    Map<String, dynamic>? deviceIdData =
        await Constants.PLATFORM_CHANNEL.invokeMapMethod("get_device_id");
    if (deviceIdData != null) {
      _staticEdata['acko_device_id'] = deviceIdData['device_id'];
    }
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await DeviceInfoPlugin().androidInfo;
      model = androidInfo.model;
      manufacturer = androidInfo.manufacturer;
      version = androidInfo.version.release;
      debugPrint('---- ${androidInfo.model} && ${packageInfo.version}');
      _staticEdata['android_app_version'] = packageInfo.version;
      _staticEdata['android'] = true;
      _staticEdata['gaid'] = Util.adId;
      _staticEdata['uuid'] = Util.userId;
    } else {
      IosDeviceInfo iOSInfo = await DeviceInfoPlugin().iosInfo;
      model = iOSInfo.model;
      manufacturer = 'Apple';
      version = iOSInfo.systemVersion;
      _staticEdata['ios_app_version'] = packageInfo.version;
      _staticEdata['ios'] = true;
      _staticEdata['idfv'] = Util.adId;
      _staticEdata['idfa'] = Util.idfaValue;
      _staticEdata['uuid'] = Util.userId;
    }
    _staticEdata['storefront'] = true;
    _staticEdata['platform'] = 'native';
    _staticEdata['web_engage'] = true;
    _staticEdata['device_info'] = model;
    _staticEdata['OS_info'] = Platform.operatingSystem;

    return "app_version=" +
        packageInfo.version +
        " " +
        "Platform " +
        Platform.operatingSystem +
        " " +
        model +
        " " +
        manufacturer +
        " " +
        version;
  }

  void trackHomePageCardClicks(
      Policy.PolicyList? item,
      Policy.PolicyStatusSubCards? statuses,
      bool isStatus,
      bool isHomePage,
      String? product,
      bool isExpired) {
    ClickEventCardType? cardType;
    if (isStatus == false && statuses != null) {
      trackBuyCardClick(statuses, isHomePage);
      return;
    } else {
      cardType = isStatus ? statuses!.cardType : item!.cardType;
    }
    debugPrint('----- ${cardType.toString()}');
    if (isStatus) {
      if (statuses != null) {
        if (product == auto) {
          product =
              statuses.redirectUrl?.contains('car') == true ? 'car' : 'bike';
        }
        sendProposalStatusClickEvent(statuses, isHomePage, isExpired, product);
      }
    } else {
      if (item != null) {
        switch (item.cardType) {
          case ClickEventCardType.POLICY:
            {
              sendPolicyClickEvent(item, isHomePage);
              break;
            }
          case ClickEventCardType.CROSS_SELL:
            {
              sendCrossSellsCardClickEvent(item, isHomePage);
              break;
            }
          case ClickEventCardType.PROPOSAL:
            {
              sendProposalClickEvent(item, isHome: isHomePage);
              break;
            }
          case ClickEventCardType.PI:
            {
              sendPiCardClickEvent(item, isHome: isHomePage);
              break;
            }
          case ClickEventCardType.BANNER_CTA:
            {
              sendContestBannerEvent(item.redirectUrl);
              break;
            }
          case ClickEventCardType.GMC_LINK_POLICY:
            {
              sendGMCCardClick();
              break;
            }
          case ClickEventCardType.HEALTH_CARD:
          case ClickEventCardType.HEALTH_RETAIL_CARD:
            {
              sendGMCPolicyClick();
              break;
            }
          default:
            break;
        }
      }
    }
  }

  void sendGMCCardClick() async {
    await helper.setEventDetails(
        ekind: 'gmc_link_card_clicked', okind: 'myaccount');
    await _getOkind();
    await helper.sendEvent();
  }

  void sendGMCPolicyClick() async {
    await helper.setEventDetails(ekind: 'gmc_card_clicked', okind: 'myaccount');
    await _getOkind();
    await helper.sendEvent();
  }

  void sendGMCCardLoaded() async {
    await helper.setEventDetails(
        ekind: 'gmc_link_card_loaded', okind: 'myaccount');
    await _getOkind();
    await helper.sendEvent();
  }

  void sendRecentSearchCardLoaded({bool? isHome}) async {
    await helper.setEventDetails(
        ekind: 'recent_search_loaded',
        okind: isHome == null ? 'myaccount' : 'tracker');
    if (isHome == true) {
      helper.setOData(key: 'user_flow', value: 'home');
      helper.setEData('product_state', 'Home Page');
      helper.setEData('screen', 'home_page');
      helper.setEData('tab', 'home');
    }
    await _getOkind();
    await helper.sendEvent();
  }

  void sendContestBannerEvent(String? actionValue) async {
    await helper.setEventDetails(
        ekind: 'catch_the_logo_enter_now_clicked', okind: 'tracker');
    await _getOkind();
    helper.setOData(key: "url", value: actionValue);
    await helper.sendEvent();
  }

  void sendPolicyClickEvent(Policy.PolicyList item, bool isHome) async {
    await helper.setEventDetails(
        ekind: 'policy_card_clicked', okind: isHome ? 'tracker' : 'myaccount');
    await _getOkind();
    helper.setEData('lob', item.lob);
    // helper.setEData('product', item.productType);
    helper.setEData('plan_type', item.subtitle);
    // helper.setEData('partner', item.partner);
    helper.setEData('policy_id', item.id);
    helper.setEData('page_title', isHome ? 'Home' : 'My Account');
    helper.setEData('product_state', isHome ? 'Home Page' : 'My Account');
    helper.setEData('screen', isHome ? 'home_page' : 'my_account');
    helper.setOData(key: 'user_flow', value: isHome ? 'home' : 'my_account');
    // helper.setOData(key: 'customer_email', value: item.email);
    await helper.sendEvent();
  }

  _getOkind() async {
    String? userPhone = await _addUserPhone();
    helper.setOData(key: 'customer_name', value: null);
    helper.setOData(key: 'customer_mobile', value: userPhone);
    //helper.setOData(key: 'customer_email', value: await _addEmail());
    helper.setOData(key: 'phone', value: userPhone);
  }

  Future<Map> getDefaultOkind() async {
    String? userPhone = await _addUserPhone();
    Map oData = Map();
    oData['customer_mobile'] = userPhone;
    oData['phone'] = userPhone;
    // oData['customer_email'] = await _addEmail();
    return oData;
  }

  Future<String?> _addUserPhone() async {
    return await prefs
        .getStringPrefs(StringDataSharedPreferenceKeys.MOBILE_NUMBER);
  }

  void sendProfileAvatarClickEvent() async {
    await helper.setEventDetails(
        ekind: 'customer_avatar_clicked', okind: 'myaccount');
    await _getOkind();
    helper.setEData('page_title', 'My Account');
    helper.setEData('product_state', 'My Account');
    helper.setEData('screen', 'my_account');
    helper.setOData(key: 'user_flow', value: 'home');
    await helper.sendEvent();
  }

  void sendCrossSellsCardClickEvent(Policy.PolicyList item, bool isHome) async {
    await helper.setEventDetails(
        ekind: 'cross_sell_card_clicked',
        okind: isHome ? 'tracker' : 'myaccount');
    await _getOkind();
    helper.setEData('lob', item.lob);
    helper.setEData('product', item.subtitle);
    helper.setEData('card_url', item.redirectUrl);
    helper.setEData('page_title', isHome ? 'Home' : 'My Account');
    helper.setEData('product_state', isHome ? 'Home Page' : 'My Account');
    helper.setEData('screen', isHome ? 'home_page' : 'my_account');
    helper.setOData(key: 'user_flow', value: isHome ? 'home' : 'my_account');
    await helper.sendEvent();
  }

  void sendRecentSearchClickEvent(MyAccountItem item, bool isHome) async {
    await helper.setEventDetails(
        ekind: 'recent_search_clicked', okind: 'myaccount');
    await _getOkind();
    helper.setEData('lob', auto);
    helper.setEData('product', item.productType);
    helper.setEData('plan_type', item.planType);
    helper.setEData('partner', item.partner);
    helper.setEData('recent_search_link', item.actionValue);
    helper.setEData('page_title', isHome ? 'Home' : 'My Account');
    helper.setEData('product_state', isHome ? 'Home Page' : 'My Account');
    helper.setEData('screen', isHome ? 'home_page' : 'my_account');
    helper.setOData(key: 'user_flow', value: isHome ? 'home' : 'my_account');
    await helper.sendEvent();
  }

  void sendEndorsementClickEvent(Policy.PolicyStatusSubCards item, bool isHome,
      bool isPolicyExpired, String? product) async {
    await helper.setEventDetails(
        ekind: 'endorsement_card_clicked',
        okind: isHome ? 'tracker' : 'myaccount');
    await _getOkind();
    helper.setEData('lob', item.productType);
    helper.setEData('product', product ?? 'car');
    helper.setEData('policy_status', isPolicyExpired ? 'expired' : 'active');
    helper.setEData('plan_type', item.type);
    helper.setEData('endorsement_link', item.redirectUrl);
    helper.setEData('endorsement_type', item.type);
    helper.setEData('page_title', isHome ? 'Home' : 'My Account');
    helper.setEData('product_state', isHome ? 'Home Page' : 'My Account');
    helper.setEData('screen', isHome ? 'home_page' : 'my_account');
    helper.setOData(key: 'user_flow', value: isHome ? 'home' : 'my_account');
    await helper.sendEvent();
  }

  void sendProposalStatusClickEvent(Policy.PolicyStatusSubCards item,
      bool isHome, bool isPolicyExpired, String? product) async {
    await helper.setEventDetails(
        ekind: 'proposal_card_clicked',
        okind: isHome ? 'tracker' : 'myaccount');
    await _getOkind();
    helper.setEData('lob', item.productType);
    helper.setEData('visit_entry_tag', 'entry');
    helper.setEData('product', product ?? 'car');
    helper.setEData('policy_status', isPolicyExpired ? 'expired' : 'active');
    helper.setEData('plan_type', Util.getPolicySubCardType(item));
    helper.setEData('proposal_link', item.redirectUrl);
    // helper.setEData('payment_status', item.statusLabel);
    // helper.setEData('is_created_by_advisor', item.isCreatedByAdviser);
    helper.setEData('page_title', isHome ? 'Home' : 'My Account');
    helper.setEData('product_state', isHome ? 'Home Page' : 'My Account');
    helper.setEData('screen', isHome ? 'home_page' : 'my_account');
    helper.setOData(key: 'user_flow', value: isHome ? 'home' : 'my_account');
    await helper.sendEvent();
  }

  void sendProposalClickEvent(Policy.PolicyList item,
      {bool isHome = false}) async {
    debugPrint('Its Proposal');
    await helper.setEventDetails(
        ekind: 'proposal_card_clicked',
        okind: isHome ? 'tracker' : 'myaccount');
    await _getOkind();
    helper.setEData('lob', auto);
    helper.setEData('product', item.title);
    helper.setEData('plan_type', item.subtitle);
    // helper.setEData('partner', item.partner);
    helper.setEData('proposal_link', item.redirectUrl);
    // helper.setEData('payment_status', item.statusLabel);
    // helper.setEData('is_created_by_advisor', item.isCreatedByAdviser);
    helper.setEData('page_title', isHome ? 'Home' : 'My Account');
    helper.setEData('product_state', isHome ? 'Home Page' : 'My Account');
    helper.setEData('screen', isHome ? 'home_page' : 'my_account');
    helper.setOData(key: 'user_flow', value: isHome ? 'home' : 'my_account');
    await helper.sendEvent();
  }

  void sendPiCardStatusClickEvent(Policy.PolicyStatusSubCards item, bool isHome,
      bool isPolicyExpired, String? product) async {
    await helper.setEventDetails(
        ekind: 'pi_card_clicked', okind: isHome ? 'tracker' : 'myaccount');
    await _getOkind();
    helper.setEData('lob', item.productType);
    helper.setEData('product', product ?? 'car');
    helper.setEData('policy_status', isPolicyExpired ? 'expired' : 'active');
    helper.setEData('plan_type', item.type);
    helper.setEData('pi_link', item.redirectUrl);
    helper.setEData('page_title', isHome ? 'Home' : 'My Account');
    helper.setEData('product_state', isHome ? 'Home Page' : 'My Account');
    helper.setEData('screen', isHome ? 'home_page' : 'my_account');
    helper.setOData(key: 'user_flow', value: isHome ? 'home' : 'my_account');
    await helper.sendEvent();
  }

  void sendPiCardClickEvent(Policy.PolicyList item,
      {bool isHome = false}) async {
    await helper.setEventDetails(
        ekind: 'pi_card_clicked',
        okind: isHome == true ? 'tracker' : 'myaccount');
    await _getOkind();
    helper.setEData('lob', auto);
    helper.setEData('product', item.title);
    helper.setEData('plan_type', item.subtitle);
    // helper.setEData('partner', item.partner);
    helper.setEData('pi_link', item.redirectUrl);
    helper.setEData('page_title', isHome ? 'Home' : 'My Account');
    helper.setEData('product_state', isHome ? 'Home Page' : 'My Account');
    helper.setEData('screen', isHome ? 'home_page' : 'my_account');
    helper.setOData(key: 'user_flow', value: isHome ? 'home' : 'my_account');
    await helper.sendEvent();
  }

  void sendRenewalCardClickEvent(Policy.PolicyStatusSubCards item, bool isHome,
      bool isPolicyExpired, String? product) async {
    await helper.setEventDetails(
        ekind: 'renewal_card_clicked', okind: isHome ? 'tracker' : 'myaccount');
    await _getOkind();
    helper.setEData('lob', item.productType);
    helper.setEData('product', product ?? 'car');
    helper.setEData('policy_status', isPolicyExpired ? 'expired' : 'active');
    helper.setEData('plan_type', item.type);
    helper.setEData('renewal_link', item.redirectUrl);
    helper.setEData('policy_id', item.id);
    helper.setEData('page_title', isHome ? 'Home' : 'My Account');
    helper.setEData('product_state', isHome ? 'Home Page' : 'My Account');
    helper.setEData('screen', isHome ? 'home_page' : 'my_account');
    helper.setOData(key: 'user_flow', value: isHome ? 'home' : 'my_account');
    await helper.sendEvent();
  }

  void sendRenewalCardLoad(
      String lob, String? planType, String? policyId, String? product,
      {bool isHome = false, bool renewCTALoaded = false}) async {
    Map oData = await getDefaultOkind();
    oData['user_flow'] = 'home';
    R2D2Request r2d2request = R2D2Request(
        app: _app,
        ekind: "renewal_card_loaded",
        okind: isHome == true ? 'tracker' : "myaccount",
        oid: await setTrackerId(),
        ua: await _getDeviceDetails(),
        edata: {
          'lob': lob,
          'plan_type': planType,
          'policy_id': policyId,
          'product': product,
          "page_title": isHome ? 'My Account' : 'Home Page',
          'product_state': isHome ? 'My Account' : 'Home Page',
          'screen': isHome ? 'my_account' : 'home_page',
          'tab': isHome ? 'policies' : 'home',
          'renew_cta_loaded': renewCTALoaded,
        }..addAll(_staticEdata),
        odata: oData);
    await _repository.postR2D2Event(r2d2request);
  }

  void sendClaimCardLoad(String lob, String? policyId, String? product,
      {bool? isHome}) async {
    Map oData = await getDefaultOkind();
    oData['user_flow'] = 'home';
    R2D2Request r2d2request = R2D2Request(
        app: _app,
        ekind: "claim_card_loaded",
        okind: isHome == true ? 'tracker' : "myaccount",
        oid: await setTrackerId(),
        ua: await _getDeviceDetails(),
        edata: {
          'lob': lob,
          'policy_id': policyId,
          'product': product,
          "page_title": isHome == null ? 'My Account' : 'Home Page',
          'product_state': isHome == null ? 'My Account' : 'Home Page',
          'screen': isHome == null ? 'my_account' : 'home_page',
          'tab': isHome == null ? 'policies' : 'home',
        }..addAll(_staticEdata),
        odata: oData);
    await _repository.postR2D2Event(r2d2request);
  }

  void sendInternetCardLoad(int totalInternetPolicies, {bool? isHome}) async {
    Map oData = await getDefaultOkind();
    oData['user_flow'] = 'home';
    R2D2Request r2d2request = R2D2Request(
        app: _app,
        ekind: "internet_aggregate_card_loaded",
        okind: isHome == true ? 'tracker' : "myaccount",
        oid: await setTrackerId(),
        ua: await _getDeviceDetails(),
        edata: {
          "no_of_cards": totalInternetPolicies,
          "page_title": isHome == null ? 'My Account' : 'Home Page',
          'product_state': isHome == null ? 'My Account' : 'Home Page',
          'screen': isHome == null ? 'my_account' : 'home_page',
          'tab': isHome == null ? 'policies' : 'home',
        }..addAll(_staticEdata),
        odata: oData);
    await _repository.postR2D2Event(r2d2request);
  }

  sendEndorsementCardLoad(String lob, String? policyNumber, String? product,
      {bool? isHome}) async {
    Map oData = await getDefaultOkind();
    oData['user_flow'] = 'home';
    R2D2Request r2d2request = R2D2Request(
        app: _app,
        ekind: "endorsement_card_loaded",
        okind: isHome == true ? 'tracker' : "myaccount",
        oid: await setTrackerId(),
        ua: await _getDeviceDetails(),
        edata: {
          'lob': lob,
          'policy_number': policyNumber,
          'product': product,
          "page_title": isHome == null ? 'My Account' : 'Home Page',
          'product_state': isHome == null ? 'My Account' : 'Home Page',
          'screen': 'home_page',
          'tab': 'home',
        }..addAll(_staticEdata),
        odata: oData);
    await _repository.postR2D2Event(r2d2request);
  }

  sendMyAccountLoad(double fontSize) async {
    Map oData = await getDefaultOkind();
    oData['user_flow'] = 'home';
    R2D2Request r2d2request = R2D2Request(
        app: _app,
        ekind: "myaccount_page_loaded",
        okind: "myaccount",
        oid: await setTrackerId(),
        ua: await _getDeviceDetails(),
        edata: {
          "page_title": 'My Account',
          'product_state': 'My Account',
          'screen': 'my_account',
          'system_font_size': fontSize,
        }..addAll(_staticEdata),
        odata: oData);
    await _repository.postR2D2Event(r2d2request);
  }

  //**** newly copied r2d2's
  trackGetStartedLoading() async {
    Map oData = await getDefaultOkind();
    oData['user_flow'] = 'on_boarding';
    R2D2Request r2d2request = R2D2Request(
        app: _app,
        ekind: "app_login_screen_loaded",
        okind: "tracker",
        oid: await setTrackerId(),
        ua: await _getDeviceDetails(),
        edata: {
          "page_title": 'Get started',
          'product_state': 'App Login',
          'screen': 'app_login'
        }..addAll(_staticEdata),
        odata: oData);
    await _repository.postR2D2Event(r2d2request);
  }

  trackPrivacyPolicyClicked() async {
    await helper.setEventDetails(
        ekind: 'privacy_policy_onboarding_clicked', okind: 'tracker');
    helper.setOData(key: 'user_flow', value: 'on_boarding');
    helper.setEData('page_title', 'Get started');
    helper.setEData('product_state', 'App Login');
    helper.setEData('screen', 'app_login');
    await helper.sendEvent();
  }

  trackMobileNumberEntry(String? phoneNumber) async {
    await helper.setEventDetails(
        ekind: 'mobile_number_entered', okind: 'tracker');
    helper.setOData(key: 'user_flow', value: 'on_boarding');
    helper.setOData(key: 'phone', value: phoneNumber);
    helper.setEData('page_title', 'Get started');
    helper.setEData('product_state', 'App Login');
    helper.setEData('screen', 'app_login');
    await helper.sendEvent();
  }

  trackIncorrectPhoneEntry(String phoneNumber) async {
    await helper.setEventDetails(
        ekind: 'invalid_mobile_number', okind: 'tracker');
    helper.setOData(key: 'phone', value: phoneNumber);
    helper.setOData(key: 'user_flow', value: 'on_boarding');
    helper.setEData('page_title', 'Get started');
    helper.setEData('product_state', 'App Login');
    helper.setEData('screen', 'app_login');
    await helper.sendEvent();
  }

  trackOtpEntry(bool autoRead, String? phoneNumber) async {
    await helper.setEventDetails(ekind: 'otp_verified', okind: 'tracker');
    helper.setOData(key: 'phone', value: phoneNumber);
    helper.setOData(key: 'user_flow', value: 'on_boarding');
    helper.setEData('page_title', 'Enter OTP');
    helper.setEData('product_state', 'App OTP Verification');
    helper.setEData('screen', 'app_otp_verification');
    helper.setEData('auto_read_sms', autoRead);
    await helper.sendEvent();
  }

  trackResendOtp(String? phoneNumber) async {
    await helper.setEventDetails(ekind: 'resend_otp_clicked', okind: 'tracker');
    helper.setOData(key: 'phone', value: phoneNumber);
    helper.setOData(key: 'user_flow', value: 'on_boarding');
    helper.setEData('page_title', 'Enter OTP');
    helper.setEData('product_state', 'App OTP Verification');
    helper.setEData('screen', 'app_otp_verification');
    await helper.sendEvent();
  }

  trackOtpError(String? phoneNumber) async {
    await helper.setEventDetails(ekind: 'otp_brute_force', okind: 'tracker');
    helper.setOData(key: 'phone', value: phoneNumber);
    helper.setOData(key: 'user_flow', value: 'on_boarding');
    helper.setEData('page_title', 'Enter OTP');
    helper.setEData('product_state', 'App OTP Verification');
    helper.setEData('screen', 'app_otp_verification');
    await helper.sendEvent();
  }

  trackSendOtp(String phoneNumber) async {
    await helper.setEventDetails(ekind: 'send_otp_clicked', okind: 'tracker');
    helper.setOData(key: 'phone', value: phoneNumber);
    helper.setOData(key: 'user_flow', value: 'on_boarding');
    helper.setEData('page_title', 'Get started');
    helper.setEData('product_state', 'App Login');
    helper.setEData('screen', 'app_login');
    helper.setEData('invalid_mobile_number', phoneNumber);
    await helper.sendEvent();
  }

  sendFeedBack(String vehicleType, bool isHome, String feedback,
      ResponseCallBack callBack) async {
    await helper.setEventDetails(
        ekind: 'in_app_rating_feedback_submitted', okind: 'tracker');
    helper.setEData('page_title', 'Feedback bottom sheet');
    helper.setEData('product_state', isHome ? 'My Account' : 'Policy success');
    helper.setEData('screen', isHome ? 'my_account' : 'payment_success_screen');
    helper.setEData('feedback', feedback);
    await _getOkind();
    helper.setOData(
        key: 'user_flow', value: isHome ? 'home' : 'policy_success');
    helper.setOData(key: 'product', value: vehicleType);
    helper.sendEventWithResult(callBack);
  }

  void trackPolicySuccess(String? vehicleType, String ekind) async {
    await helper.setEventDetails(ekind: ekind, okind: 'tracker');
    helper.setEData('page_title', 'Policy Success');
    if (ekind.equalIgnoreCase("payment_success_screen_loaded")) {
      helper.setEData('product_state', 'Transaction Successful');
    }
    helper.setEData('screen', 'payment_success_screen');
    await _getOkind();
    helper.setOData(key: 'user_flow', value: 'policy_success');
    helper.setOData(key: 'product', value: vehicleType);
    await helper.sendEvent();
  }

  void trackAppRatingNoClick(String vehicleType, bool isHome) async {
    await helper.setEventDetails(
        ekind: 'in_app_rating_no_clicked', okind: 'tracker');
    helper.setEData('page_title', 'Feedback bottom sheet');
    helper.setEData('product_state', isHome ? 'My Account' : 'Policy Success');
    helper.setEData('screen', isHome ? 'my_account' : 'payment_success_screen');
    await _getOkind();
    helper.setOData(
        key: 'user_flow', value: isHome ? 'home' : 'policy_success');
    helper.setOData(key: 'product', value: vehicleType);
    await helper.sendEvent();
  }

  void trackAppRatingYesClick(String vehicleType, bool isHome) async {
    await helper.setEventDetails(
        ekind: 'in_app_rating_yes_clicked', okind: 'tracker');
    helper.setEData('page_title', 'Feedback bottom sheet');
    helper.setEData('product_state', isHome ? 'My Account' : 'Policy Success');
    helper.setEData('screen', isHome ? 'my_account' : 'payment_success_screen');
    await _getOkind();
    helper.setOData(
        key: 'user_flow', value: isHome ? 'home' : 'policy_success');
    helper.setOData(key: 'product', value: vehicleType);
    await helper.sendEvent();
  }

  void trackReferClick(String? vehicleType, bool isHome) async {
    await helper.setEventDetails(
        ekind: 'referral_yes_clicked', okind: 'tracker');
    helper.setEData('page_title', 'Feedback bottom sheet');
    helper.setEData('product_state', isHome ? 'My Account' : 'Policy Success');
    helper.setEData('screen', isHome ? 'my_account' : 'payment_success_screen');
    await _getOkind();
    helper.setOData(
        key: 'user_flow', value: isHome ? 'home' : 'policy_success');
    helper.setOData(key: 'product', value: vehicleType);
    await helper.sendEvent();
  }

  // Onboarding r2d2 events
  Future trackOnboardingPageEvent(String event, String pageTitle) async {
    await helper.setEventDetails(ekind: event, okind: 'tracker');
    await _getOkind();
    helper.setOData(key: 'user_flow', value: 'onboarding');
    helper.setEData('page_title', pageTitle);
    helper.setEData('product_state', 'App Onboarding');
    helper.setEData('screen', 'app_onboarding');
    return await helper.sendEvent();
  }

  Future trackSkipPageEvent(String event, int screen) async {
    await helper.setEventDetails(ekind: event, okind: 'tracker');
    await _getOkind();
    helper.setOData(key: 'user_flow', value: 'onboarding');
    helper.setEData('page_title', 'onboarding');
    helper.setEData('product_state', 'App Onboarding');
    helper.setEData('screen', screen);
    return await helper.sendEvent();
  }

  trackSplashScreenLoadEvent() async {
    //await sendGAID();
    await helper.setEventDetails(
        ekind: "splash_screen_loaded", okind: 'tracker');
    await _getOkind();
    helper.setOData(key: 'user_flow', value: 'onboarding');
    helper.setEData('page_title', "Splash Screen");
    helper.setEData('product_state', 'Splash Screen');
    helper.setEData('screen', 'splash_screen');
    trackAppOpenEvent("open");
    await helper.sendEvent();
  }

  trackhasGigPolicyStatusEvent(bool gigPolicyValue) async {
    await helper.setEventDetails(ekind: "gig_policy_status", okind: 'tracker');
    await _getOkind();
    helper.setEData('page_title', "HomePage Screen");
    helper.setEData('product_state', 'HomePage Screen');
    helper.setEData('has_gig_policy_value', gigPolicyValue.toString());
    helper.setOData(key: 'has_gig_policy', value: gigPolicyValue.toString());
    helper.setEData('screen', 'home_screen');
    await helper.sendEvent();
  }

  void trackAppOpenEvent(String type) async {
    UTMParamsModel? utmParams = await getUTMParamsForAppOpen();
    await helper.setEventDetails(ekind: "app_open", okind: 'tracker');
    await _getOkind();
    bool isLoggedIn = await isLogIn();
    helper.setEData("is_login", isLoggedIn);
    helper.setEData("type", type);
    helper.setEData("utm_url", utmParams?.url);
    helper.setEData("utm_source", utmParams?.utm_source);
    helper.setEData("utm_campaign", utmParams?.utm_campaign);
    helper.setEData("utm_term", utmParams?.utm_term);
    helper.setEData("utm_medium", utmParams?.utm_medium);
    helper.setEData("utm_content", utmParams?.utm_content);
    sendAppOpenSegmentEvent(isLoggedIn, type, utmParams);
    await helper.sendEvent();
  }

  void sendAppOpenSegmentEvent(
      bool isLoggedIn, String type, UTMParamsModel? utmParams) async {
    String? phone = await prefs
        .getStringPrefs(StringDataSharedPreferenceKeys.MOBILE_NUMBER);
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    AnalyticsTrackerManager.instance
        .sendEvent(event: PageLoadedConstants.ACKO_APP_OPEN, properties: {
      'logged_in': isLoggedIn,
      'app_version': packageInfo.version,
      'phone': phone,
      'type': type,
      'platform': Util.getPlatform(),
      "utm_url": utmParams?.url,
      'utm_source': utmParams?.utm_source,
      'utm_campaign': utmParams?.utm_campaign,
      'utm_term': utmParams?.utm_term,
      'utm_medium': utmParams?.utm_medium,
      'utm_content': utmParams?.utm_content,
    });
    debugPrint("sendAppOpenSegmentEvent with ${utmParams?.utm_source}");
  }

  Future<UTMParamsModel?> getUTMParamsForAppOpen() async {
    Map? deeplinkMap = (await Constants.PLATFORM_CHANNEL
        .invokeMethod("get_app_open_deep_link"));
    StoredDeeplink? _storedDeeplink =
        deeplinkMap != null ? StoredDeeplink.fromJson(deeplinkMap) : null;

    String? appOpenDeepLink = _storedDeeplink?.value;

    UTMParamsModel? utmParams;
    if (appOpenDeepLink.isNotNullOrEmpty) {
      Uri deepLinkUri = Uri.parse(appOpenDeepLink!);
      var deepLinkParams = deepLinkUri.queryParameters;
      utmParams = UTMParamsModel(
          url: deepLinkParams['utm_source'].isNotNullOrEmpty
              ? appOpenDeepLink
              : null,
          utm_source: deepLinkParams['utm_source'],
          utm_campaign: deepLinkParams['utm_campaign'],
          utm_term: deepLinkParams['utm_term'],
          utm_medium: deepLinkParams['utm_medium'],
          utm_content: deepLinkParams['utm_content']);
    }
    debugPrint("getParamsForAppOpen with params $utmParams");
    return utmParams;
  }

  Future sendLocationEventOnSplashScreen() async {
    await helper.setEventDetails(ekind: "location_acquired", okind: 'tracker');
    await _getOkind();
    helper.setOData(key: 'user_flow', value: 'onboarding');
    helper.setEData('page_title', "Splash Screen");
    helper.setEData('product_state', 'Splash Screen');
    helper.setEData('screen', 'splash_screen');

    final Map<dynamic, dynamic>? data =
        await Constants.PLATFORM_CHANNEL.invokeMethod("splash_location_event");

    if (data != null) {
      if (data["accuracy"] != null) {
        helper.setEData('location_type', data["accuracy"]);
      }
      if (data["latitude"] != null) {
        helper.setEData('lat', data["latitude"]);
      }
      if (data["longitude"] != null) {
        helper.setEData('long', data["longitude"]);
      }
      helper.setEData('permission_given', data["permission"]);
    }
    return await helper.sendEvent();
  }

  Future sendGAID() async {
    debugPrint('gaid sending');
    await helper.setEventDetails(ekind: "ad_id_set", okind: 'tracker');
    await _getOkind();
    helper.setOData(key: 'user_flow', value: 'onboarding');
    helper.setEData('page_title', "Splash Screen");
    helper.setEData('product_state', 'Splash Screen');
    helper.setEData('screen', 'splash_screen');
    return await helper.sendEvent();
  }

  // policy details events
  void trackPolicyDetailsEvents({
    required String event,
    String? vehicleType,
    String? urlEventKey,
    String? url,
    String? policyId,
    String? email,
  }) async {
    await helper.setEventDetails(ekind: event, okind: 'tracker');

    helper.setEData('page_title', "Policy Details");
    helper.setEData('screen', 'policy_screen');
    if (policyId != null) {
      helper.setEData('policy_id', policyId);
    }
    if (urlEventKey != null && url != null) {
      helper.setEData(urlEventKey, url);
    }

    await _getOkind();
    helper.setOData(key: 'phone', value: await _addUserPhone());
    helper.setOData(key: 'user_flow', value: 'policy_screen');
    helper.setOData(key: 'customer_email', value: email);
    if (vehicleType != null) {
      helper.setOData(key: 'product', value: vehicleType);
    }
    await helper.sendEvent();
  }

  // Track in-app rating events
  void trackHomeInAppRatingEvents(
      String? event, String? pageTitle, String? feedback) async {
    await helper.setEventDetails(ekind: event, okind: 'tracker');

    helper.setEData('page_title', pageTitle != null ? pageTitle : "App Rating");
    helper.setEData('product_state', 'In App Rating');
    helper.setEData('screen', 'home_screen');
    if (feedback != null) {
      helper.setEData('user_feedback', feedback);
    }

    await _getOkind();
    helper.setOData(key: 'phone', value: await _addUserPhone());
    helper.setOData(key: 'user_flow', value: 'home_screen');

    await helper.sendEvent();
  }

  trackNoNetworkEvent(NoNetworkResponse response) async {
    await helper.setEventDetails(
        ekind: "internet_offline_dino", okind: 'tracker');

    helper.setEData('previous_page', response.page);
    helper.setEData("time_stamp", response.timeStamp);

    await _getOkind();
    helper.setOData(key: 'phone', value: await _addUserPhone());
    helper.setOData(key: 'user_flow', value: 'home_screen');

    await helper.sendEvent();
  }

  trackAppUpdateEvent(String? trackerState) async {
    String? ekind;
    switch (trackerState) {
      case Constants.FORCE_UPDATE_TRIGGERED:
        ekind = 'force_push_update_triggered';
        break;
      case Constants.FLEXI_UPDATE_DIALOG_CTA:
        ekind = 'flexi_google_popup_update_clicked';
        break;
      case Constants.FLEXI_DIALOG_CANCEL_CTA:
        ekind = 'flexi_google_popup_no_thanks_clicked';
        break;
      case Constants.APP_UPDATE_SUCCESS:
        ekind = 'app_update_success';
        break;
      case Constants.APP_INSTALL_SUCCESS:
        ekind = 'app_install_success';
        break;
    }

    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    prefs.setStringPrefs(StringDataSharedPreferenceKeys.PREVIOUS_APP_VERSION,
        packageInfo.version);
  }

  Future trackAppsFlyerEvent(Map? afParams) async {
    if (afParams != null) {
      helper.setEventDetails(ekind: afParams["event_name"], okind: 'tracker');
      afParams.forEach((key, value) {
        helper.setEData(key, value);
      });
    }
    helper.setEData('product_state', 'My Account');
    helper.setEData('screen', 'my_account');
    helper.setOData(key: 'user_flow', value: 'home');
    helper.setEData('page_title', 'My Account');
    await _getOkind();
    return helper.sendEvent();
  }

  Future setUserLogs(Map? params, String screen) async {
    helper.setEventDetails(ekind: 'user_logs', okind: 'user');
    if (params != null) {
      params.forEach((key, value) {
        helper.setEData(key, value);
      });
      helper.setEData("timestmap", DateTime.now().microsecondsSinceEpoch);
    }
    helper.setEData('screen', screen);
    await _getOkind();
    return helper.sendEvent();
  }

  void trackBuyCardClick(Policy.PolicyStatusSubCards item, bool isHome) async {
    await helper.setEventDetails(
        ekind: 'product_cta_clicked', okind: isHome ? 'tracker' : 'myaccount');
    await _getOkind();
    if (item.title != null) {
      helper.setEData('product', item.title!.toLowerCase());
    }
    helper.setEData('page_title', isHome ? 'Home' : 'My Account');
    helper.setEData('product_state', isHome ? 'Home page' : 'My Account');
    helper.setEData('screen', isHome ? 'home_page' : 'my_account');
    helper.setOData(key: 'user_flow', value: isHome ? 'home' : 'policies');
    helper.setOData(key: 'url', value: item.redirectUrl);
    await helper.sendEvent();
  }

  Future trackDrawerItemClick(
      String ekind, String? redirectUrl, String? product) async {
    await helper.setEventDetails(ekind: ekind, okind: 'hamburger_login');
    await _getOkind();
    if (product != null) {
      helper.setEData('product', product.toLowerCase());
    }
    helper.setEData('page_title', 'My Account');
    helper.setEData('product_state', 'My Account');
    helper.setEData('screen', 'my_account');
    helper.setOData(key: 'user_flow', value: 'Drawer');
    helper.setOData(key: 'url', value: redirectUrl);
    return await helper.sendEvent();
  }

  Future trackAssetAddedSuccessBottomSheetEvents(String ekind) async {
    await helper.setEventDetails(ekind: ekind, okind: 'tracker');
    await _getOkind();
    helper.setEData('from_page', 'explore');
    helper.setEData('journey', 'rto_info');
    helper.setEData('platform', 'app');
    helper.setEData('product', 'auto');
    return await helper.sendEvent();
  }

  Future trackAssetManagementButtonTappedEvents(String ekind, String screen,
      {String? regNumber, Map<String, String?>? extraInfo}) async {
    await helper.setEventDetails(ekind: ekind, okind: 'tracker');
    await _getOkind();
    helper.setEData('from_page', screen);
    helper.setEData('journey', 'rto_info');
    helper.setEData('platform', 'app');
    helper.setEData('product', 'auto');
    if (regNumber != null) helper.setEData('registration_number', regNumber);
    if (extraInfo != null) {
      extraInfo.forEach((key, value) {
        helper.setEData(key, value);
      });
    }
    return await helper.sendEvent();
  }

  Future trackPushNotificationEvent(String ekind,
      {Map<String, dynamic>? extraInfo}) async {
    await helper.setEventDetails(ekind: ekind, okind: 'tracker');
    await _getOkind();
    if (extraInfo != null) {
      extraInfo.forEach((key, value) {
        helper.setEData(key, value.toString());
      });
    }
    return await helper.sendEvent();
  }

  sendIllogicalEvents(
      {String? ekind,
      String? regNum,
      bool? isLoggedIn,
      String? modalType,
      String? vehicleType}) async {
    await helper.setEventDetails(ekind: ekind, okind: 'illogical_flow');
    helper.setEData('registration_number', regNum);
    if (isLoggedIn != null) helper.setEData('isLoggedIn', isLoggedIn);
    if (modalType != null) helper.setEData('context', modalType);
    await _getOkind();
    helper.setOData(key: 'product', value: vehicleType ?? 'car');
    return await helper.sendEvent();
  }

  getPolicyDetailsEData(PolicyData model) {
    helper.setEData('screen', 'policy_details_screen');
    if (model.product != null) {
      helper.setEData('product', model.product!.toLowerCase());
      helper.setOData(key: 'product', value: model.product!.toLowerCase());
    }
    helper.setEData('lob', 'auto');
    helper.setEData('plan', model.plan);
    helper.setEData('registration_number', model.asset!.registrationNumber);
    helper.setEData('policy_number', model.policyNumber);
    helper.setEData('policy_document', model.policyDocument);
  }

  Future trackRenewalCardClick(
      RenewalProposalPlan renewalPlan, PolicyData policy) async {
    await helper.setEventDetails(
        ekind: 'renewal_card_clicked', okind: 'myaccount_pdp');
    await _getOkind();
    getPolicyDetailsEData(policy);
    helper.setEData('policy_status',
        (policy.policyExpired ?? false) ? 'expired' : 'active');
    helper.setEData('plan', renewalPlan.planId);
    helper.setEData('renewal_link', renewalPlan.paymentUrl);
    return await helper.sendEvent(oid: policy.id);
  }

  Future trackRenewalLoad(PolicyData policy) async {
    bool? isAutoRenew = policy.autoRenewalDetails?.autoRenew;
    if (isAutoRenew == true) {
      await R2D2Events.instance.trackAutoRenewLoad(policy);
    }
    if (policy.renewalProposalPlans != null &&
        policy.renewalProposalPlans!.isNotEmpty) {
      for (var renewal in policy.renewalProposalPlans!) {
        await sendRenewals(renewal, policy);
      }
    }
  }

  Future sendRenewals(
      RenewalProposalPlan renewalPlan, PolicyData policy) async {
    await helper.setEventDetails(
        ekind: 'renewal_card_loaded', okind: 'myaccount_pdp');
    await _getOkind();
    getPolicyDetailsEData(policy);
    helper.setEData('plan', renewalPlan.planId);
    helper.setEData('renewal_link', renewalPlan.paymentUrl);
    helper.setEData('policy_start_date', policy.startDate);
    helper.setEData('policy_end_date', policy.endDate);
    return await helper.sendEvent(oid: policy.id);
  }

  Future trackAutoRenewLoad(PolicyData policy) async {
    await helper.setEventDetails(
        ekind: 'auto_mandate_card_loaded', okind: 'myaccount_pdp');
    await _getOkind();
    getPolicyDetailsEData(policy);
    helper.setEData('plan', policy.autoRenewalDetails!.plan);
    helper.setEData('idv', policy.autoRenewalDetails!.recommendedIdv);
    helper.setEData('renewal_date', policy.autoRenewalDetails!.autoRenewalDate);
    helper.setEData('premium', policy.autoRenewalDetails!.premium);
    return await helper.sendEvent(oid: policy.id);
  }

  Future trackAutoRenewCancelClick(PolicyData policy) async {
    await helper.setEventDetails(
        ekind: 'auto_mandate_cancel_clicked', okind: 'myaccount_pdp');
    await _getOkind();
    getPolicyDetailsEData(policy);
    helper.setEData('plan', policy.autoRenewalDetails!.plan);
    return await helper.sendEvent(oid: policy.id);
  }

  Future trackAutoRenewCancel(PolicyData policy) async {
    await helper.setEventDetails(
        ekind: 'auto_mandate_cancelled', okind: 'myaccount_pdp');
    await _getOkind();
    getPolicyDetailsEData(policy);
    helper.setEData('plan', policy.autoRenewalDetails!.plan);
    return await helper.sendEvent(oid: policy.id);
  }

  Future trackCalendarRenewal(String eKind, String? policyType,
      String? policyExpiry, String? id) async {
    await helper.setEventDetails(ekind: eKind, okind: 'policy_success');
    await _getOkind();
    helper.setEData('policy_type', policyType);
    helper.setEData("policy_expiry", policyExpiry);
    helper.setEData("calendar_id", id);
    return await helper.sendEvent();
  }

  Future trackInfoGraphLoad(
    String? uid,
  ) async {
    await helper.setEventDetails(
        ekind: 'infographics_card_loaded', okind: 'myaccount');
    await _getOkind();
    helper.setEData('type', uid);
    helper.setEData('page_title', 'My Account');
    helper.setEData('product_state', 'My Account');
    helper.setEData('screen', 'my_account');
    helper.setOData(key: 'user_flow', value: 'home');
    return await helper.sendEvent();
  }

  void trackSurveyCardDisplayed(String oKind) async {
    await helper.setEventDetails(ekind: 'curious_survey_shown', okind: oKind);
    await _getOkind();
    helper.setEData('page_title', 'My Account');
    helper.setEData('product_state', 'My Account');
    helper.setEData('screen', 'my_account');
    helper.setOData(key: 'user_flow', value: 'home');
    return await helper.sendEvent();
  }

  void trackProfileClicked(EditDetails userDetails) async {
    await helper.setEventDetails(
        ekind: 'profile_clicked', okind: 'hamburger_login');
    await _getOkind();
    helper.setEData('page_title', 'hamburger_login');
    helper.setEData('product_state', 'hamburger_login');
    helper.setEData('screen', 'my_account');
    helper.setOData(key: 'user_flow', value: 'hamburger_login');
    helper.setEData('name', userDetails.name);
    helper.setEData('email', userDetails.email);
    helper.setEData('phone', userDetails.phoneNumber);
    helper.setEData('whatsapp opt-in', userDetails.whatsappOptIn);
    await helper.sendEvent();
  }

  void trackPersonalDetailClicked(EditDetails userDetails) async {
    await helper.setEventDetails(
        ekind: 'personal_details_clicked', okind: 'myprofile');
    await _getOkind();
    helper.setEData('page_title', 'myprofile');
    helper.setEData('product_state', 'myprofile');
    helper.setEData('screen', 'myprofile');
    helper.setOData(key: 'user_flow', value: 'hamburger_login');
    helper.setEData('name', userDetails.name);
    helper.setEData('email', userDetails.email);
    helper.setEData('phone', userDetails.phoneNumber);
    await helper.sendEvent();
  }

  void trackPersonalDetailItemClicked(
      EditableUserDetail? editableFields) async {
    if (editableFields == EditableUserDetail.EmailAddress) {
      await helper.setEventDetails(
          ekind: 'email_edit_clicked', okind: 'myprofile');
    } else {
      await helper.setEventDetails(
          ekind: 'name_edit_clicked', okind: 'myprofile');
    }
    await _getOkind();
    helper.setEData('page_title', 'myprofile');
    helper.setEData('product_state', 'myprofile');
    helper.setEData('screen', 'myprofile');
    helper.setOData(key: 'user_flow', value: 'hamburger_login');
    await helper.sendEvent();
  }

  void trackDetailsEdited(UpdateUserRequest request,
      EditableUserDetail? editableFields, EditDetails? userDetail) async {
    if (userDetail == null) {
      return;
    }
    String? newValue;
    String? oldValue;
    if (editableFields == EditableUserDetail.Name) {
      newValue = request.firstName! + " " + request.lastName!;
      oldValue = userDetail.name;
      await helper.setEventDetails(ekind: 'name_updated', okind: 'myprofile');
    } else {
      newValue = request.email;
      oldValue = userDetail.email;
      await helper.setEventDetails(ekind: 'email_updated', okind: 'myprofile');
    }
    await _getOkind();
    helper.setEData('page_title', 'myprofile');
    helper.setEData('product_state', 'myprofile');
    helper.setEData('screen', 'myprofile');
    helper.setEData('old_value', oldValue);
    helper.setEData('new_value', newValue);
    helper.setOData(key: 'user_flow', value: 'hamburger_login');
    await helper.sendEvent();
  }

  void trackWhatsappClicked(bool? optIn, {bool loginPage = false}) async {
    await helper.setEventDetails(
        ekind: 'whatsapp_opt_in_clicked',
        okind: loginPage ? 'tracker' : 'myprofile');
    await _getOkind();
    helper.setEData('page_title', loginPage ? 'Get Started' : 'myprofile');
    helper.setEData('product_state', loginPage ? 'App Login' : 'myprofile');
    helper.setEData('screen', loginPage ? 'app_login' : 'myprofile');
    helper.setEData('current state of opt-in', optIn);
    helper.setOData(
        key: 'user_flow', value: loginPage ? 'on_boarding' : 'hamburger_login');
    await helper.sendEvent();
  }

  void trackWhatsappOptin(bool optin) async {
    if (optin) {
      await helper.setEventDetails(
          ekind: 'whatsapp_opt_in_subscribe', okind: 'myprofile');
    } else {
      await helper.setEventDetails(
          ekind: 'whatsapp_opt_in_unsubscribe', okind: 'myprofile');
    }
    await _getOkind();
    helper.setEData('page_title', 'myprofile');
    helper.setEData('product_state', 'myprofile');
    helper.setEData('screen', 'myprofile');
    helper.setOData(key: 'user_flow', value: 'hamburger_login');
    await helper.sendEvent();
  }

  void trackSupportClicked() async {
    await helper.setEventDetails(
        ekind: 'support_section_clicked', okind: 'myprofile');
    await _getOkind();
    helper.setEData('page_title', 'myprofile');
    helper.setEData('product_state', 'myprofile');
    helper.setEData('screen', 'myprofile');
    helper.setOData(key: 'user_flow', value: 'hamburger_login');
    await helper.sendEvent();
  }

  void internetPolicyClicked(Policies policy) async {
    await helper.setEventDetails(
        ekind: 'internet_listing_card_clicked', okind: 'myaccount');
    await _getOkind();
    helper.setEData('lob', "internet");
    helper.setEData('link', policy.policyUrl);
    helper.setEData('plan_type', policy.plan);
    await helper.sendEvent();
  }

  void internetPolicyClaimClicked(Claims claim) async {
    await helper.setEventDetails(
        ekind: 'claim_card_clicked', okind: 'myaccount');
    await _getOkind();
    helper.setEData('lob', "internet");
    helper.setEData('claim_request_number', claim.claimRequestNumber);
    await helper.sendEvent();
  }

  void bikeLandingPage(String event, bool? isNew) async {
    await helper.setEventDetails(ekind: event, okind: 'bike_journey');
    await _getOkind();
    helper.setOData(key: 'product', value: 'bike');
    helper.setEData('page_title', 'bike_landing');
    helper.setEData('product_state', 'bike_landing');
    helper.setEData('screen', 'bike_index_screen');
    if (isNew != null) {
      helper.setEData('journey', isNew ? 'new_bike' : 'old_bike');
    }
    await helper.sendEvent();
  }

  void bikeRegNumEntered(String regNo, bool isNew) async {
    await helper.setEventDetails(
        ekind: 'bike_reg_no_entered', okind: 'bike_journey');
    await _getOkind();
    helper.setOData(key: 'product', value: 'bike');
    helper.setEData('page_title', 'bike_landing');
    helper.setEData('product_state', 'bike_landing');
    helper.setEData('screen', 'bike_index_screen');
    helper.setEData('reg_no', regNo);
    helper.setEData('journey', isNew ? 'new_bike' : 'old_bike');
    await helper.sendEvent();
  }

  void loaderScreenLoad(String? regNo, bool? isNew) async {
    await helper.setEventDetails(
        ekind: 'loader_screen_loaded', okind: 'bike_journey');
    await _getOkind();
    helper.setOData(key: 'product', value: 'bike');
    helper.setEData('page_title', 'bike_loader');
    helper.setEData('product_state', 'bike_loader');
    helper.setEData('screen', 'bike_loader_screen');
    helper.setEData('reg_no', regNo);
    if (isNew != null)
      helper.setEData('journey', isNew ? 'new_bike' : 'old_bike');
    await helper.sendEvent();
  }

// #28
  void bikeProposalViewPricesClicks(
      {int? regYear,
      String? previousPolicyStatus,
      bool? policyExpiry,
      String? registrationNumber,
      String? mmv,
      String? fuelType,
      int? cc,
      String? variant,
      bool? isNew}) async {
    await helper.setEventDetails(
        ekind: 'view_prices_cta_clicked', okind: 'bike_journey');
    await _getOkind();
    helper.setOData(key: 'product', value: 'bike');
    helper.setEData('page_title', 'bike_details');
    helper.setEData('product_state', 'bike_details');
    helper.setEData('screen', 'bike_aadrilla_success_screen');
    helper.setEData('reg_no', registrationNumber);
    helper.setEData('cc', cc);
    helper.setEData('fuel_type', fuelType);
    helper.setEData('previous_policy_expired', policyExpiry);
    helper.setEData('previous_policy_expired_status', previousPolicyStatus);
    helper.setEData('registration_year', regYear);
    helper.setEData('vehicle_make_model', '$mmv');
    helper.setEData('vehicle_variant', variant);
    if (isNew != null)
      helper.setEData('journey', isNew ? 'new_bike' : 'old_bike');
    await helper.sendEvent();
  }

//#29 31,
  Future bikeMMVScreenLoaded(String event, String? regNo, bool? isNew) async {
    await helper.setEventDetails(ekind: event, okind: 'bike_journey');
    await _getOkind();
    helper.setOData(key: 'product', value: 'bike');
    helper.setEData('page_title', 'mmv_screen');
    helper.setEData('product_state', 'mmv_screen');
    helper.setEData('screen', 'bike_aadrilla_failure_screen');
    helper.setEData('reg_no', regNo);
    if (isNew != null)
      helper.setEData('journey', isNew ? 'new_bike' : 'old_bike');
    return await helper.sendEvent();
  }

  //#30,  32
  void popularBikeSelected(String? regNo, String mmv, String event,
      {bool? isNew}) async {
    await helper.setEventDetails(ekind: event, okind: 'bike_journey');
    await _getOkind();
    helper.setOData(key: 'product', value: 'bike');
    helper.setEData('page_title', 'mmv_screen');
    helper.setEData('product_state', 'mmv_screen');
    helper.setEData('screen', 'bike_aadrilla_failure_screen');
    helper.setEData('reg_no', regNo);
    helper.setEData('vehicle_make_model', mmv);
    if (isNew != null) {
      helper.setEData('journey', isNew ? 'new_bike' : 'old_bike');
    }
    await helper.sendEvent();
  }

//40, 41, 42, 43, 49 50 51
  void bikeMmvStraightFlow(String event, String screen, String? mmv,
      String? regNum, bool? isNew) async {
    await helper.setEventDetails(ekind: event, okind: 'bike_journey');
    await _getOkind();
    helper.setOData(key: 'product', value: 'bike');
    helper.setEData('page_title', 'bike_landing');
    helper.setEData('product_state', 'bike_landing');
    helper.setEData('screen', screen);
    helper.setEData('reg_no', regNum);
    if (mmv != null) helper.setEData('vehicle_make_model', mmv);
    if (isNew != null)
      helper.setEData('journey', isNew ? 'new_bike' : 'old_bike');
    await helper.sendEvent();
  }

// 44, 46, 48, 52, 53
  void noAadrillaFlow(VehicleSearch? vehicle, int? regYear, bool? isExpired,
      String? expiryStatus, String event, String screen, bool? isNew) async {
    if (vehicle != null) {
      await helper.setEventDetails(ekind: event, okind: 'bike_journey');
      await _getOkind();
      helper.setOData(key: 'product', value: 'bike');
      helper.setEData('page_title', 'bike_details');
      helper.setEData('product_state', 'bike_details');
      helper.setEData('screen', screen);
      helper.setEData('cc', vehicle.cc);
      helper.setEData('fuel_type', vehicle.fuelType);
      helper.setEData('previous_policy_expired', isExpired);
      helper.setEData('previous_policy_expired_status', expiryStatus);
      helper.setEData('registration_year', regYear);
      helper.setEData('vehicle_make_model', vehicle.makeModel);
      helper.setEData('vehicle_variant', vehicle.variant);
      if (isNew != null)
        helper.setEData('journey', isNew ? 'new_bike' : 'old_bike');
      await helper.sendEvent();
    }
  }

  void trackPayoutPreferenceEvents(
      {String? event, String? bankType, String? bankSubType}) async {
    await helper.setEventDetails(ekind: event, okind: 'tracker');
    helper.setEData('screen', 'payout_preference');
    helper.setEData('page_title', 'Payout preference');
    if (bankType != null) {
      helper.setEData('bank_type', bankType);
    }
    if (bankSubType != null) {
      helper.setEData('bank_subtype', bankSubType);
    }
    await _getOkind();
    await helper.sendEvent();
  }

  void sendHealthEvents(
      String? event, Map<String, dynamic> properties, String feature) async {
    if (event != null) {
      await helper.setEventDetails(ekind: event, okind: feature);
      properties.entries.forEach((element) {
        helper.setEData(element.key, element.value);
      });
      await _getOkind();

      helper.setOData(
          key: 'customer_email',
          value: await prefs
              .getStringPrefs(StringDataSharedPreferenceKeys.EMAIL_ID));
      await helper.sendEvent();
    }
  }

  String? getPayoutName(String? type) {
    switch (type) {
      case Constants.paytm:
      case Constants.amazonPay:
      case Constants.wallet:
        return 'wallet';
      case Constants.upi:
        return 'UPI';
      case Constants.bank:
        return 'bank_account';
      default:
        return type;
    }
  }

  void requestCallback(
    String phoneNumber,
    ResponseCallBack? callBack,
  ) async {
    await helper.setEventDetails(
        ekind: "help_me_form_submitted", okind: "ackore_proposal");
    await _getOkind();
    helper.setOData(key: 'product', value: 'car');
    helper.setEData('product_state', 'Callback Requested Before Quotation');
    helper.setEData('phone', phoneNumber);
    helper.setEData('screen', "car_landing");
    await helper.sendEventWithResult(callBack);
  }

  String? getBankSubType(String? type) {
    switch (type) {
      case Constants.paytm:
        return 'paytm';
      case Constants.amazonPay:
        return 'amazonpay';
      default:
        return type;
    }
  }

  void trackCarLandingLoaded() async {
    await helper.setEventDetails(
        ekind: 'landing_screen_loaded', okind: 'tracker');
    helper.setEData('screen', 'registration_number_screen');
    helper.setEData('page_title', 'Car Insurance');
    helper.setOData(key: 'product', value: 'car');
    await _getOkind();
    await helper.sendEvent();
  }

  void trackCarLandingEvents(
      {String? ekind,
      String? catalogPlan,
      String? catalogProduct,
      String? product}) async {
    await helper.setEventDetails(ekind: ekind, okind: 'tracker');
    helper.setEData('screen', 'registration_number_screen');
    helper.setEData('page_title', 'Car Insurance');
    helper.setEData('journey', 'old_car');
    if (catalogPlan != null) {
      helper.setEData('catalog_plan', catalogPlan);
    } else if (catalogProduct != null) {
      helper.setEData('catalog_product', catalogProduct);
    }
    helper.setOData(key: 'product', value: product);
    await _getOkind();
    await helper.sendEvent();
  }

  void trackRegistrationNumberEntered({
    String? registrationNumber,
  }) async {
    await helper.setEventDetails(
        ekind: 'registration_number_cta_clicked', okind: 'tracker');
    helper.setEData('screen', 'registration_number_screen');
    helper.setEData('page_title', 'Car Insurance');
    helper.setEData('journey', 'old_car');
    helper.setEData('registration_number', registrationNumber);
    helper.setOData(key: 'product', value: 'car');
    await _getOkind();
    await helper.sendEvent();
  }

  void trackBrandNewCarClicked() async {
    await helper.setEventDetails(
        ekind: 'brand_new_car_cta_clicked', okind: 'tracker');
    helper.setEData('screen', 'registration_number_screen');
    helper.setEData('page_title', 'Car Insurance');
    helper.setEData('journey', 'new_car');
    helper.setOData(key: 'product', value: 'car');
    await _getOkind();
    await helper.sendEvent();
  }

  void trackHelpClicked() async {
    await helper.setEventDetails(
        ekind: 'help_me_popup_loaded', okind: 'tracker');
    helper.setEData('screen', 'registration_number_screen');
    helper.setEData('page_title', 'Car Insurance');
    helper.setEData('journey', 'old_car');
    helper.setEData('product_state', 'Callback Requested Before Quotation');
    helper.setOData(key: 'product', value: 'car');
    await _getOkind();
    await helper.sendEvent();
  }

  void trackLandingScreenLoaded(
      {String? catalogProduct, String? catalogPlan}) async {
    await helper.setEventDetails(
        ekind: 'landing_screen_loaded', okind: 'tracker');
    helper.setEData('screen', 'registration_number_screen');
    helper.setEData('page_title', 'Car Insurance');
    helper.setEData('journey', 'old_car');
    if (catalogPlan != null) {
      helper.setEData('catalog_plan', catalogPlan);
    } else if (catalogProduct != null) {
      helper.setEData('catalog_product', catalogProduct);
    }
    helper.setOData(key: 'product', value: 'car');
    await _getOkind();
    await helper.sendEvent();
  }

  void trackErrorOnCarReg({
    String? registrationNumber,
    String? reason,
  }) async {
    await helper.setEventDetails(
        ekind: 'error_on_registration_number_cta_clicked', okind: 'tracker');
    helper.setEData('screen', 'registration_number_screen');
    helper.setEData('page_title', 'Car Insurance');
    helper.setEData('journey', 'old_car');
    helper.setEData('registration_number', registrationNumber);
    helper.setEData('reason_for_failure', reason);

    helper.setOData(key: 'product', value: 'car');
    await _getOkind();
    await helper.sendEvent();
  }

  String getEkindforToggleMenu(int index, ServiceType? service) {
    switch (service) {
      case ServiceType.MOBILE_REPAIR:
        return (index == 0)
            ? tap_btn_acko_partnered_mobile_centers
            : tap_btn_all_mobile_centers;
      default:
        return '';
    }
  }

  String getBrandNameFromToggleMenu(
      int index, ServiceType? service, String? brandName) {
    MapLocationBloc bloc = MapLocationBloc(service!);
    List<MapTabData> data = bloc.getTabData(service);
    return brandName ?? '';
  }

  String getServiceString(ServiceType? service) {
    switch (service) {
      case ServiceType.VALUATION:
        return 'valuation';
      case ServiceType.MOBILE_REPAIR:
        return 'mobile_repairs';
      case ServiceType.RTO:
        return 'rto';
      case ServiceType.PUC_CENTER:
        return 'puc';
      case ServiceType.CHALLAN:
        return 'challan';
      case ServiceType.PUC_VALIDITY:
        return 'puc_validity';
      case ServiceType.FASTAG_RECHARGE:
        return 'fastag';
      default:
        return '';
    }
  }

  void selectedFilterinMapView(
      {int? index,
      ServiceType? service,
      String? serviceLob,
      String? brandName}) async {
    String ekind = getEkindforToggleMenu(index!, service);
    String brand = getBrandNameFromToggleMenu(index, service, brandName);
    await helper.setEventDetails(ekind: ekind, okind: 'tap_to_filter');
    helper.setOData(key: 'user_flow', value: 'mobile_repairs');
    helper.setEData('screen', 'map_view');
    helper.setEData('page_title', 'Select your Mobile Brand');
    helper.setEData('service', getServiceString(service));
    helper.setEData('brand', brand);
    helper.setEData('product_state', 'Mobile Repairs Grid Page');
    helper.setEData('service_lob', serviceLob);
    await _getOkind();
    await helper.sendEvent();
  }

  void brandScreenLoadedEvent(
      {String? ekind, ServiceType? service, String? serviceLob}) async {
    await helper.setEventDetails(ekind: ekind, okind: 'tracker');
    helper.setOData(key: 'user_flow', value: getServiceString(service));
    helper.setEData('screen', 'brand_selection_grid');
    helper.setEData('page_title', 'Select your Mobile Brand');
    helper.setEData('service', getServiceString(service));
    helper.setEData('product_state', 'Mobile Repairs Grid Page');
    helper.setEData('service_lob', serviceLob);
    await _getOkind();
    await helper.sendEvent();
  }

  void brandScreenTapEvent(
      {String? ekind,
      ServiceType? service,
      String? serviceLob,
      String? brand}) async {
    await helper.setEventDetails(ekind: ekind, okind: 'tracker');
    helper.setOData(key: 'user_flow', value: getServiceString(service));
    helper.setEData('screen', 'brand_selection_grid');
    helper.setEData('page_title', 'Select your Mobile Brand');
    helper.setEData('service', getServiceString(service));
    helper.setEData('brand', brand);
    helper.setEData('product_state', 'Mobile Repairs Grid Page');
    helper.setEData('service_lob', serviceLob);
    await _getOkind();
    await helper.sendEvent();
  }

  void raiseAClaimBannerLoadedEvent(
      {String? ekind, String? service, String? serviceLob}) async {
    await helper.setEventDetails(ekind: ekind, okind: 'tracker');
    helper.setOData(key: 'user_flow', value: 'mobile_repairs');
    helper.setEData('screen', 'brand_selection_grid');
    helper.setEData('page_title', 'Select your Mobile Brand');
    helper.setEData('service', service);
    helper.setEData('product_state', 'Mobile Repairs Grid Page');
    helper.setEData('service_lob', serviceLob);
    await _getOkind();
    await helper.sendEvent();
  }

  void raiseAClaimBannerTapEvent(
      {String? ekind,
      String? service,
      String? serviceLob,
      bool isGridPage = false}) async {
    await helper.setEventDetails(ekind: ekind, okind: 'tap_to_claim');
    helper.setOData(key: 'user_flow', value: 'mobile_repairs');
    helper.setEData(
        'screen', isGridPage ? 'brand_selection_grid' : 'map_service_sheet');
    helper.setEData('page_title',
        isGridPage ? 'Select your Mobile Brand' : 'Search for service centers');
    helper.setEData('service', service);
    helper.setEData('product_state',
        isGridPage ? 'Mobile Repairs Grid Page' : 'Mobile Repairs Map View');
    helper.setEData('service_lob', serviceLob);
    await _getOkind();
    await helper.sendEvent();
  }

  void trackExploreTabEvents(
      {String? ekind, String? service, String? serviceLob}) async {
    await helper.setEventDetails(ekind: ekind, okind: 'tracker');

    helper.setOData(key: 'user_flow', value: 'explore');
    helper.setEData('screen', 'explore_page');
    helper.setEData('page_title', 'Do more with ACKO');
    helper.setEData('tab', 'explore');
    helper.setEData('service', service);
    helper.setEData('product_state', 'Explore Page');
    helper.setEData('service_lob', serviceLob);
    await _getOkind();
    await helper.sendEvent();
  }

  void trackUnlockHealthServiceEvents(
      {String? ekind, String? service, required String screen}) async {
    await helper.setEventDetails(ekind: ekind, okind: 'tracker');

    helper.setOData(key: 'user_flow', value: 'explore');
    helper.setEData('screen', screen);
    helper.setEData(
        'tab', screen.contains('explore') ? 'explore' : 'home_page');
    helper.setEData('service', service);
    helper.setEData('product_state',
        screen.contains('explore') ? 'Explore Page' : 'Home Page');
    helper.setEData('service_lob', 'health');
    await _getOkind();
    await helper.sendEvent();
  }

  void trackHealthServicesTapXsell(
      {String? ekind, String? service, required String screen}) async {
    await helper.setEventDetails(ekind: ekind, okind: 'tracker');

    helper.setOData(key: 'user_flow', value: 'explore');
    helper.setEData('screen', screen);
    helper.setEData('service_name', service);
    helper.setEData('product_state',
        screen.contains('explore') ? 'Explore Page' : 'Home Page');
    helper.setEData('service_lob', 'health');
    await _getOkind();
    await helper.sendEvent();
  }

  void trackExploreUnlockHealthButtonEvent(
      {String? ekind,
      String? service,
      String? serviceLob,
      required String screen}) async {
    await helper.setEventDetails(ekind: ekind, okind: 'tracker');

    helper.setOData(key: 'user_flow', value: 'explore');
    helper.setEData('screen', screen);
    helper.setEData(
        'tab', screen.contains('explore') ? 'explore' : 'home_page');
    helper.setEData('service', service);
    helper.setEData('product_state',
        screen.contains('explore') ? 'Explore Page' : 'Home Page');
    helper.setEData('service_lob', 'health');
    await _getOkind();
    await helper.sendEvent();
  }

  void tncReadMoreClick({bool? isHome}) async {
    await helper.setEventDetails(
        ekind: 'learn_more_explore_cta_clicked', okind: 'tracker');
    helper.setOData(key: 'user_flow', value: 'explore');
    helper.setEData('screen', 'explore_page');
    helper.setEData('page_title', 'Do more with ACKO');
    helper.setEData('tab', 'explore');
    helper.setEData('product_state', 'Explore Page');
    await _getOkind();
    await helper.sendEvent();
  }

  void trackServicePageLoad(
      {String? pageTitle, String? service, String? serviceLob}) async {
    await helper.setEventDetails(
        ekind: 'service_page_loaded', okind: 'tracker');
    helper.setOData(key: 'user_flow', value: 'service');
    helper.setEData('screen', 'services_page');
    helper.setEData('page_title', pageTitle);
    helper.setEData('service', service);
    helper.setEData('product_state', 'Service Page');
    helper.setEData('service_lob', serviceLob);
    await _getOkind();
    await helper.sendEvent();
  }

  void trackChallanSuccessPageLoad(bool isSuccess,
      {String? regNum,
      String? numberOfChallans,
      String? challanDetails}) async {
    await helper.setEventDetails(
        ekind: isSuccess
            ? 'challan_success_screen_loaded'
            : 'challan_failure_screen_loaded',
        okind: 'tracker');
    helper.setOData(key: 'user_flow', value: 'service');
    helper.setEData(
        'screen', isSuccess ? 'challan_success_page' : 'challan_failure_page');
    helper.setEData('page_title', 'Pending Challans');
    helper.setEData('service', 'challan');
    helper.setEData('product_state',
        isSuccess ? 'Challan Success Page' : 'Challan Failure Page');
    helper.setEData('service_lob', 'auto');
    helper.setEData('registration_number', regNum?.toUpperCase());
    if (isSuccess) {
      helper.setEData('challan_details', challanDetails);
      helper.setEData('number_of_pending_challans', numberOfChallans);
    }
    await _getOkind();
    await helper.sendEvent();
  }

  void trackChallanAPI(
    bool isSuccess, {
    String? regNum,
    String? challanDetails,
  }) async {
    await helper.setEventDetails(
        ekind: isSuccess ? 'challan_api_success' : 'challan_api_failure',
        okind: 'tracker');
    /*"product_state: Challan Success Page
  number_of_pending_challans:""6""
    challan_details: [{
    challan_number: abcxyz1, challan_cost: 200; challan_category: ""challan name receive from the API eg. helmet missing"", challan_violation_date: dd-mm-yyy hh:mm:ss in our existing DB format},{challan_number: ""abcxyz2,...}
  }]
*/
    helper.setOData(key: 'user_flow', value: 'service');
    helper.setEData('service', 'challan');
    helper.setEData('service_lob', 'auto');
    helper.setEData('registration_number', regNum);
    if (isSuccess) {
      helper.setEData('challan_details', challanDetails);
    }
    AnalyticsTrackerManager.instance.sendEvent(
        event: isSuccess
            ? TapConstants.CHALLAN_SUCCESS_API_LOADED
            : TapConstants.CHALLAN_FAILURE_API_LOADED,
        properties: {
          "journey": "challan",
          "product": "auto",
          "registration_number": regNum,
          "platform": Util.getPlatform()
        });
    await _getOkind();
    await helper.sendEvent();
  }

  void trackAnotherRegNum({
    String? regNum,
  }) async {
    await helper.setEventDetails(
        ekind: 'check_another_reg_no_clicked', okind: 'tracker');
    helper.setOData(key: 'user_flow', value: 'service');
    helper.setEData('screen', 'challan_success_page');
    helper.setEData('page_title', 'Pending Challans');
    helper.setEData('service', 'challan');
    helper.setEData('product_state', 'Challan Success Page');
    helper.setEData('service_lob', 'auto');
    helper.setEData('registration_number', regNum?.toUpperCase());
    await _getOkind();
    await helper.sendEvent();
  }

  void trackVerifyDetailScreen(String ekind, bool isApi,
      {String? regNum, bool? isValuationApiSuccess}) async {
    await helper.setEventDetails(ekind: ekind, okind: 'tracker');
    helper.setOData(key: 'user_flow', value: 'service');
    helper.setEData('screen', 'Valuation_mmv');
    helper.setEData('page_title', 'Verify Details');
    helper.setEData('service', 'valuation');
    helper.setEData('product_state', 'Valuation MMV');
    helper.setEData('service_lob', 'auto');
    helper.setEData('registration_number', regNum?.toUpperCase());
    if (isApi) {
      helper.setEData('valuation_api_success', isValuationApiSuccess);
    }
    await _getOkind();
    await helper.sendEvent();
  }

  Future<void> trackScheduleCallback(
      String? regNum, bool isError, String ownerName) async {
    await helper.setEventDetails(ekind: "schedule_callback", okind: 'tracker');
    helper.setOData(key: 'user_flow', value: 'service');
    helper.setEData('screen', 'Valuation_mmv');
    helper.setEData('service', 'valuation');
    helper.setEData('registration_number', regNum?.toUpperCase());
    helper.setEData("is_error_screen", isError);
    helper.setEData("owner_name", ownerName);
    await _getOkind();
    await helper.sendEvent();
    AnalyticsTrackerManager.instance
        .sendEvent(event: TapConstants.TAP_BTN_SCHEDULE_CALLBACK, properties: {
      "platform": Util.getPlatform(),
      "product": "auto",
      "journey": "valuation",
      "registration_number": regNum?.toUpperCase(),
    });
  }

  void trackBottomNavBarEvents(String ekind, int index) async {
    await helper.setEventDetails(ekind: ekind, okind: 'tracker');
    helper.setOData(key: 'user_flow', value: 'navbar');
    helper.setEData('screen', 'app_nav_bar');
    helper.setEData('product_state', 'App Navigation Bar');
    helper.setEData('tab', _getTab(index));
    await _getOkind();
    await helper.sendEvent();
  }

  String _getTab(int index) {
    switch (index) {
      case 0:
        return 'home';
      case 1:
        return 'my_policies';
      case 2:
        return 'explore';
      case 3:
        return 'support';
      default:
        return 'unknown';
    }
  }

  void trackSupportPageEvents(String support) async {
    /* "tab: support
    product_state: Support Page
    screen: support_page
    support: write/call/faq/guides/articles/ebooks

    Rest, as is"*/
    await helper.setEventDetails(
        ekind: 'support_link_clicked', okind: 'tracker');
    helper.setOData(key: 'user_flow', value: 'support');
    helper.setEData('screen', 'support_page');
    helper.setEData('product_state', 'Support Page');
    helper.setEData('support', support);
    helper.setEData('tab', 'support');
    await _getOkind();
    await helper.sendEvent();
  }

  void trackCallUsPageEvents(String ekind, {String? lob}) async {
    await helper.setEventDetails(ekind: ekind, okind: 'tracker');
    helper.setOData(key: 'user_flow', value: 'call');
    helper.setEData('screen', 'call_us_page');
    helper.setEData('product_state', 'Call Us Page');
    if (lob != null) {
      helper.setEData('lob', lob);
    }
    await _getOkind();
    await helper.sendEvent();
  }

  Future trackServiceScreenEvents(String ekind, ServiceType? serviceType,
      {String? regNum}) async {
    String? productState, screen, service;
    switch (serviceType) {
      case ServiceType.RTO:
        {
          productState = 'RTO Home Page';
          screen = 'rto_home_page';
          service = 'rto';
        }
        break;
      case ServiceType.CHALLAN:
        {
          productState = 'Challan Home Page';
          screen = 'rto_home_page';
          service = 'challan';
        }
        break;
      case ServiceType.PUC_VALIDITY:
        {
          productState = 'PUC Validity Page';
          screen = 'puc_validity_page';
          service = 'puc_validity';
        }
        break;
      case ServiceType.VALUATION:
        {
          productState = 'Valuation Home Page';
          screen = 'valuation_home_page';
          service = 'valuation';
        }
        break;
      case ServiceType.MOBILE_REPAIR:
        {
          productState = 'Popular Mobile Page';
          screen = 'mobile_search_page';
          service = 'mobile_services';
        }
        break;
      default:
        {}
    }
    await helper.setEventDetails(ekind: ekind, okind: 'tracker');
    helper.setOData(key: 'user_flow', value: 'service');
    helper.setEData('screen', screen);
    helper.setEData('product_state', productState);
    helper.setEData('service_lob', 'auto');
    helper.setEData('service', service);
    if (regNum != null) {
      helper.setEData('registration_number', regNum);
    }
    await _getOkind();
    return await helper.sendEvent();
  }

  sendRtoEvents(String ekind, bool isSuccess,
      {String? regNum, RtoResponse? response}) async {
    await helper.setEventDetails(ekind: ekind, okind: 'tracker');
    helper.setOData(key: 'user_flow', value: 'rto');
    helper.setEData(
        'screen', isSuccess ? 'rto_success_page' : 'rto_failure_page');
    helper.setEData(
        'product_state', isSuccess ? 'RTO Success Page' : 'RTO Failure Page');
    helper.setEData('service_lob', 'auto');
    helper.setEData('service', 'rto');
    if (isSuccess) {
      helper.setEData('registration_number', regNum);
      if (response != null) {
        helper.setEData('make', response.makeName);
        helper.setEData('model', response.modelName);
        helper.setEData('variant', response.modelName);
        helper.setEData('rto_owner_name', response.ownerName);
        helper.setEData('rto_registration_date', response.registrationDate);
        helper.setEData(
            'rto_transmission', response.transmission ?? 'Not Available');
        helper.setEData('rto_fuel_type', response.fuelType);
      }
    }
    await _getOkind();
    return await helper.sendEvent();
  }

  sendHomePageCTA(String ekind, bool isHomePage,
      {String? product,
      bool? isQuickAction,
      String? quickAction,
      String? registrationNumber,
      bool? isNew,
      String? status,
      String? visitEntryTag}) async {
    await helper.setEventDetails(ekind: ekind, okind: 'tracker');
    helper.setOData(key: 'user_flow', value: isHomePage ? 'home' : 'my_policy');
    helper.setEData('screen', isHomePage ? 'home_page' : 'policy_page');
    helper.setEData('product_state', isHomePage ? 'Home Page' : 'Policy Page');
    helper.setEData('tab', isHomePage ? 'home' : 'my_policy');
    if (registrationNumber != null) {
      helper.setEData('registration_number', registrationNumber);
    }
    if (product != null) {
      helper.setEData('product', product);
    }
    if (isQuickAction == true) {
      helper.setEData('quick_action', quickAction);
    }
    if (isNew != null) {
      helper.setEData('journey', isNew ? 'new_bike' : 'old_bike');
    }
    if (status != null) {
      helper.setEData('status', status);
    }
    if (visitEntryTag != null) {
      helper.setEData('visit_entry_tag', visitEntryTag);
    }
    await _getOkind();
    return await helper.sendEvent();
  }

  trackDoMoreEvents(String? type) async {
    String? service, serviceLob;
    switch (type) {
      case 'vehicle_estimate':
        {
          service = 'valuation';
          serviceLob = 'auto';
          break;
        }
      case 'rto':
        {
          service = 'rto';
          serviceLob = 'auto';
          break;
        }
      case 'challan':
        {
          service = 'challan';
          serviceLob = 'auto';
          break;
        }
      case 'mobile_repairs_nearme':
        {
          service = 'mobile_repairs';
          serviceLob = 'auto';
          break;
        }
      case 'ev_centers_nearme':
        {
          service = 'ev_centers';
          serviceLob = 'auto';
          break;
        }
      case 'puc_center_nearme':
        {
          service = 'puc_center';
          serviceLob = 'auto';
          break;
        }

      case 'other_medicine':
        {
          service = 'order_medicine';
          serviceLob = 'health';
          break;
        }
      case 'TTD':
        {
          service = 'doctor_on_call';
          serviceLob = 'health';
          break;
        }
      case 'lab_test':
        {
          service = 'book_lab_test';
          serviceLob = 'health';
          break;
        }
    }
    await helper.setEventDetails(ekind: 'service_clicked', okind: 'tracker');
    helper.setOData(key: 'user_flow', value: 'home');
    helper.setEData('screen', 'home_page');
    helper.setEData('product_state', 'Home Page');
    helper.setEData('tab', 'home');
    helper.setEData('service', service);
    helper.setEData('service_lob', serviceLob);
    await _getOkind();
    return await helper.sendEvent();
  }

  trackActionClaim(bool isEdit,
      {Policy.PolicyList? item, int? totalPolicy}) async {
    await helper.setEventDetails(
        ekind: isEdit
            ? 'quick_action_edit_selected'
            : 'quick_action_claim_selected',
        okind: 'tracker');
    helper.setOData(key: 'user_flow', value: 'quick_actions');
    helper.setEData('screen', 'home_page');
    helper.setEData('product_state', 'Home Page');
    helper.setEData('tab', 'home');
    helper.setEData('quick_action', isEdit ? 'edit' : 'claim');
    helper.setEData('policy_number', item?.policyNumber);
    helper.setEData('vehicle_name', item?.title);
    helper.setEData('lob', item?.lob);
    helper.setEData('plan_type', item?.subtitle);
    helper.setEData('number_of_policies', totalPolicy);
    await _getOkind();
    return await helper.sendEvent();
  }

  getHeadsUpEvents(Policy.PolicyList item) async {
    helper.setEData('screen', 'home_page');
    helper.setEData('product_state', 'Home Page');
    helper.setEData('tab', 'home');
    switch (item.cardType) {
      case ClickEventCardType.PI_STATUS:
        await helper.setEventDetails(
            ekind: 'pi_card_clicked', okind: 'myaccount');
        await _getOkind();
        helper.setEData('lob', auto);
        helper.setEData('product', item.type);
        // helper.setEData('plan_type', item.);
        // helper.setEData('partner', item.partner);
        helper.setEData('pi_link', item.redirectUrl);
        await helper.sendEvent();
        break;
      case ClickEventCardType.RENEWAL:
        await helper.setEventDetails(
            ekind: 'renewal_card_clicked', okind: 'myaccount');
        await _getOkind();
        helper.setEData('lob', auto);
        helper.setEData('product', item.type);
        // helper.setEData('plan_type', item.planType);
        helper.setEData('renewal_link', item.redirectUrl);
        helper.setEData('policy_number', item.policyNumber);
        await helper.sendEvent();
        break;
      case ClickEventCardType.ENDORSEMENT:
        await helper.setEventDetails(
            ekind: 'endorsement_card_clicked', okind: 'myaccount');
        await _getOkind();
        helper.setEData('lob', auto);
        helper.setEData('product', item.type);
        // helper.setEData('plan_type', item.planType);
        helper.setEData('endorsement_link', item.redirectUrl);
        helper.setEData('endorsement_type', item.type);
        await helper.sendEvent();
        break;
      case ClickEventCardType.PROPOSAL_STATUS:
        await helper.setEventDetails(
            ekind: 'proposal_card_clicked', okind: 'myaccount');
        await _getOkind();
        helper.setEData('lob', auto);
        helper.setEData('product', item.type);
        // helper.setEData('plan_type', item.planType);
        helper.setEData('proposal_link', item.redirectUrl);
        helper.setEData('payment_status', item.cardType);
        // helper.setEData('is_created_by_advisor', item.isCreatedByAdviser);
        await helper.sendEvent();
        break;
      case ClickEventCardType.PROPOSAL:
        {
          sendProposalClickEvent(item, isHome: true);
          break;
        }
      case ClickEventCardType.PI:
        {
          sendPiCardClickEvent(item, isHome: true);
          break;
        }
      default:
        break;
    }
  }

  trackMapServiceHomeEvents(
    String ekind,
    String serviceName, {
    List<MapLocationResults>? apiData,
    int? numberOfFuelStations,
    Predictions? prediction,
    Location? location,
    String? fuelStationName,
    double? fuelStationDistance,
  }) async {
    await helper.setEventDetails(ekind: ekind, okind: 'tracker');
    helper.setOData(key: 'user_flow', value: 'quick_actions');
    helper.setEData('screen', '${serviceName}_home');
    helper.setEData('product_state', 'Fuel Station Home');
    helper.setEData('service', '$serviceName');
    helper.setEData('service_lob', 'auto');
    if (prediction != null) {
      helper.setEData('current_location_name', prediction.mainText);
    }
    if (location != null) {
      helper.setEData('current_location_present', true);
      helper.setEData('current_location_coordinates', location);
    }
    if (numberOfFuelStations != null) {
      helper.setEData(
          'number_of_${serviceName}s_visible', numberOfFuelStations);
    }
    await _getOkind();
    return await helper.sendEvent();
  }

  trackGPSPageEvents(String ekind,
      {bool? isPrefilled,
      Predictions? prediction,
      Location? location,
      String? selectedLocationName,
      Location? selectedLocation}) async {
    /*
  current_location_name: ""Thane"" (or location coordinates if the location is already available for a user)
  current_location_coordinates: ""lat long combination""
  current_location_present: ""true""/""false"" (false if current_location was not already pre-filled)"*/
    await helper.setEventDetails(ekind: ekind, okind: 'tracker');
    helper.setOData(key: 'user_flow', value: 'quick_actions');
    helper.setEData('service_lob', 'auto');
    if (selectedLocationName != null) {
      helper.setEData('selected_location_name', selectedLocationName);
    }
    if (selectedLocation != null) {
      helper.setEData('selected_location_coordinates', selectedLocation);
    }
    if (prediction != null) {
      helper.setEData('current_location_name', prediction.mainText);
    }
    if (location != null) {
      helper.setEData('current_location_coordinates', location);
    }
    if (isPrefilled != null && isPrefilled == true) {
      helper.setEData('current_location_present', true);
    }

    await _getOkind();
    return await helper.sendEvent();
  }
}
