
import 'package:flutter/material.dart';

class RoundedTabIndicator extends Decoration {
  final Color color;

  const RoundedTabIndicator({required this.color});

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _RoundedPainter(color);
  }
}

class _RoundedPainter extends BoxPainter {
  final Color color;

  _RoundedPainter(this.color);

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    final Paint paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final double indicatorHeight = 4; // Height of rounded pill
    final double indicatorTop = configuration.size!.height - indicatorHeight;

    final Rect rect = Offset(offset.dx, indicatorTop) &
    Size(configuration.size!.width, indicatorHeight);

    final RRect rRect = RRect.fromRectAndRadius(rect, const Radius.circular(16));
    canvas.drawRRect(rRect, paint);
  }
}
