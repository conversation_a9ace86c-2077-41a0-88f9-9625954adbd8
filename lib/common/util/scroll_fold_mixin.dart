import 'package:acko_flutter/util/Utility.dart';
import 'package:analytics/analytics.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:flutter/cupertino.dart';

mixin ScrollFoldMixin<T extends StatefulWidget> on State<T> {
  double _lastLoggedOffset = 0.0;
  final double _scrollInterval = 300.0;
  double? _screenHeight;

  void addScrollFoldListener(ScrollController scrollController, String fromPage) {
    _screenHeight = MediaQuery.of(context).size.height;

    scrollController.addListener(() {
      if (!scrollController.hasClients) return;

      final scrollPosition = scrollController.position.pixels;
      final delta = (scrollPosition - _lastLoggedOffset).abs();

      if (delta >= _scrollInterval) {
        final direction = scrollPosition > _lastLoggedOffset ? 'down' : 'up';
        _lastLoggedOffset = scrollPosition;

        final scrollFold = (scrollPosition / _screenHeight!) + 1;

        AnalyticsTrackerManager.instance.sendEvent(
            event: PageLoadedConstants.SCROLL,
            properties: {
              'platform': Util.getPlatform(),
              'from_page': fromPage,
              'scroll_fold': double.parse(scrollFold.toStringAsFixed(1)),
              'screen_height': _screenHeight?.toInt(),
              'direction': direction
            });
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
  }
}
