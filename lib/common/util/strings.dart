const String policies = 'Policies';
const String insure = 'Insure';
const String insure_now = 'Insure now';
const String exploreMore = 'Explore more';
const String coveredByAcko = 'Covered by ACKO';
const String auto = 'auto';
const String electronics = 'electronics';
const String almost_there = 'Almost there';
const String congrats_on_completing_all_steps =
    'Congratulations on completing\n all steps';
const String your_profile_is_ready =
    'You\'re now ready to start using \nthe app to its full potential.';
const String profile_from_good_to_great =
    'Let’s take your profile from good\n to great. The details matter!';

const String profile_setup_complete = 'All set!';
const String profile_setup_complete_desc =
    "You're now ready to start using the app to its full potential.";
const String add_your_address = 'Addresses';
const String saved_addresses = 'Saved Addresses';
const String save_address_details = 'Save address details';

const String add_address = 'Add address';
const String update_address = 'Update address';
const String required_string = 'Required';
const String pincode_required_string = 'Pincode Required';
const String add_string = 'Add';
const String member_info = 'Member info';
const String enter_member_details = 'Enter member details';
const String finish_profile_setup = 'Set up your profile';
const String save_and_add_another = 'Save & add another';
const String internet = 'internet';
const String gotACar = 'Got a car?';
const String saveBigOnCar = 'Save big on your car insurance.';
const String gotABike = 'Got a bike?';
const String sweetDealForBike = 'We\'ve got a sweet deal for your bike!';
const String policyOnHold = "Policy on hold";

const String assetImage = 'assets/images/';
const String assetImageHome = 'assets/images/home/';
const String close = 'Close';
const String noInternetSettings =
    "Please check your internet connectivity. Go to settings.";
const String settings = "Go to settings";

// Policy details strings
const String unableToLoadPage = "Unable to fetch remote data";
const String inTheNameOf = "IN THE NAME OF";
const String overview = "Overview";
const String coverages = "Coverages";

const String rupeeSymbol = ' \u{20B9}';
const String internetPolicyPage = 'InternetPolicyPage';
const String noInternetMessage = "Please check your internet connection";
const String noInternetError = "Uh-oh. Unable to connect to the Internet";
const String connectionTimeout = "Connection Timeout recieved";
const String recieveTimeout = "Recieve Timeout recieved";
const String viewDetails = 'View Details';
const String premium = 'PREMIUM';
const String idv = 'IDV';
const String cancel = 'Cancel';

const String appRatingDefault =
    '{   "show_in_app_review": true,   "show_old_app_rating": true,   "show_home_app_rating": true }';

const String health_challenge_completed = "Challenge Complete!";
const String health_limited_period = "Limited time only";
const String health_explore_policy = "Enrol your family for ";
const String health_claims = "Claims";
const String health_cashless_hospitals = "Cashless hospitals";
const String health_policy_benefits_title = "Wellness offers";
const String health_challenge_title = "Challenges";
const String health_challenge_daily = "Walk %s steps today";
const String health_challenge_weekly = "Walk %s steps this week";
const String health_challenge_pre_cta = "Sync step data to start challenges";
const String health_challenge_post_cta = "Your activity history";
const String health_challenge_sync = "Steps data not synced";

const String health_reward_card_title = 'Stay healthy, get wealthy!';
const String health_reward_card_subtitle =
    'A surprise awaits you on completing %s coins';
const String health_reward_card_cta = 'View ACKO coins history';
const String health_reward_coins_achieved = '%s/';
const String health_reward_coins_total = '%s coins';
const String health_tnc = 'Terms & Conditions';
const String claim_details_title = 'Claim details';
const String claim_progress = 'Claim progress';
const String claim_status_title = 'Claim status';
const String claim_sub_status_title = 'Sub claim status';
const String see_sample = "See sample";
const String health_doctor_on_call_nav_title = 'Doctor on call';

const String health_you_have_earned = 'You have earned';
const String health_hooray = 'Hooray!';

const String health_track_your_Steps = 'Track your Steps and';
const String health_earn_acko_coins = 'Earn ACKO Coins';
const String health_meet_your_step_goal = 'Meet your step goals';
const String health_track_your_health_data = 'Track your health data';
const String health_participate_in_challange = 'Participate in challenges';
const String health_connect_to_google_fit =
    'Connect to Google Fit to track your steps and earn ';
const String health_connect_to_health_fit =
    'Connect to Apple Health to track your steps and earn ACKO Coins daily';
const String health_acko_coins = 'ACKO Coins ';
const String health_daily = 'daily';
const String health_connect_to_google_fit_btn = 'Connect to Google Fit';
const String health_connect_to_apple_health_btn = 'Connect to Apple Health';

const String skip = 'Skip';
const String skip_for_now = 'Skip for now';
const String camera = 'Camera';

const String files = 'Files';
const String gallery = 'Gallery';

const String submit = 'Submit';
const String txt_continue = 'Continue';
const String check_prices = "Check prices";
const String dismiss = 'Dismiss';
const String web_title_doctor_on_call = "Doctor on Call";
const String web_title_1mg_med = "Order Medicine";
const String web_title_1mg_lab = "Book Lab Test";
const String bike_checkout_TnC =
    "By tapping on Continue, I declare that I have provided correct details and have read";
const String bike_previous_policy =
    "The previous year policy document is required if the claim is within 30 days from the start date of the ACKO policy.";
String bikeCheckoutInstantMsg =
    "You will receive your policy instantly after the payment is made, and the policy will start effective from ";
const String default_bike_checkout_instant_msg =
    "You will receive your policy instantly as you make the payment, and your policy will start on 12 am, from your chosen date";
const add_new_address = "Add new address";
//scree names
const String bikeLandingScreen = 'bike_landing';
const String mmvScreen = 'mmv';
const String checkoutScreen = 'checkout';
const String plansScreen = 'plans';
const String summaryScreen = 'summary';
const String loginScreen = 'login';
const String otpScreen = 'otp';
const String challan_automation_otp = 'challan_automation_otp';
const String buyCards = 'buy_cards';
const String crossSell = 'cross_sell';
const String myAccountScreen = 'my_account_screen';
const String bottomSheet = 'bottom_sheet';
const String claimBottomSheet = 'claim_bottom_sheet';
const String ackoAssuredScreen = 'acko_assured_screen';
const String incidentDetails = 'incident_details';
const String homeTabScreen = 'home_tab_screen';

const String healthId = 'id_health';

const String health_claim_error = 'Sorry, something went wrong at our end';
const String health_my_claims_nav_title = 'New claim';
const String lets_go = 'Let\'s go!';
const String ongoing_claims = 'Ongoing claims';
const String advance_cash_label = 'advance_cash';
const String advance_cash = 'Advance Cash';
const String advance_cash_wallet = '$advance_cash wallet';
const String past_claims = 'Past claims';
const String gmcClaim = 'gmc-claim';
const String claimReviewTitle = 'Please review your claim';
const String patient = 'Patient';
const String selected_cover = 'Claim category';
const String admission_date = 'Admission date';
const String claim_amount = 'Claim amount';
const String amount = 'Amount';
const String claim_amt = 'Claim amount';
const String other_documents = 'Other documents';
const String others = 'others';
const String tax_documents = '80D, Proposal and Policy document';
const String account_number = 'Account number';
const String ipd_success_heading = 'Your claim is filed!';
const String ipd_success_description =
    'You can check the claim status on the ACKO app under the claims section';
const String ipd_claim_expected_resolution = 'Claim resolution expected by: ';
const String completion_date_msg = 'Will be completed by: %s';
const String expected_settlement = 'Expected settlement';
const String get_claim_updates =
    "To get claim updates on an alternate email address ";
const String tap_here = "tap here →";
const String before_we_continue = "Before we continue:";
const String claim_id = 'Claim ID';
const String to_be_generated = 'To be generated';
const String claim_submitted = 'Claim submitted on';
const String track_claim = 'Track claim';
const String documents_pending = 'Documents pending';
const String documents_heading = 'Upload additional documents';
const String add_document_heading = 'Add your documents';
const String patient_details = 'Patient details';
const String contact_info = 'Contact info';
const String policy_details = 'Policy details';
const String UHID = 'UHID';
const String policy_number = 'Policy Number';
const String mobile_txt = 'Mobile';
const String email = 'Email';
const String email_address = 'Email address';
const String upload = 'Upload';
const String select = 'Select';
const String organisation = 'Organisation';
const String image_error = 'Can\'t load Image...';
const String mandatory_field_error =
    'This is a mandatory requirement for filing a claim';
const String settlement_txt = 'Will be available soon';

//Doc Upload strings
const String health_ipd_doc_title_bills =
    "Upload bills, reports, receipts and other documents";
const String health_title_docs_acr = "Upload the required documents";
const String submit_docs = "Submit documents";
const String health_ipd_doc_add_more = "Add more";
const String health_ipd_title_skip_alert = "Stop upload?";
const String health_ipd_date_selection_alert_title = "Are you sure?";
const String health_ipd_date_selection_alert_description =
    "The date of admission is the date when you were admitted to hospital. You can find this in your discharge summary or hospital bill.";
const String health_ipd_date_confirmation = "Yes. I am sure";
const String health_ipd_desc_skip_alert =
    "Your in-progress document upload will stop if you continue. Still want to continue?";
const String health_ipd_title_review_alert = "Go back to review page?";
const String select_user_review_alert =
    "This change requires re-selection of member in the next screen";
const String health_ipd_desc_review_alert =
    "Don't worry your changes are saved.";

//ipd

const String resend_otp = "Resend OTP";
const String resend_access_code = "Resend access code";
const String select_dependent = "Select dependant";
const String select_source = "Select source";

const String showing_policy_of = "Showing policy of:";
const String switch_dependent = "Switch";

const String reimbursement_clicked = 'id_health_fnol_cta_file_a_claim';
const String reimbursement = 'Reimbursement';
const String cashless_detail_title =
    'Quick cashless claims at 14300+ hospitals';
const String cashless_title = 'Cashless treatment';
const String cashless_sub_title =
    'Get treated at our cashless network hospital and we will pay the hospital directly';
const String policy_card_title =
    'Show your $e_card at the hospital to avail cashless treatment.';
const String get_policy_card = 'See policy cards';

const String upload_doc_screen_title = 'Upload documents';
const String file_claim_header = 'Who needed medical assistance?';
const String file_claim_header_acr = 'Who needs medical assistance?';
const String submit_claim = 'Submit claim';
const String upload_now = 'Upload now';
const String health_ipd_upload_file = 'Upload file';
const String medical_visit_header = 'Tell us more about your medical visit';
const String medical_visit_acr = 'Tell us more about your treatment';
const String admission_header = 'When was the visit?';
const String admission_header_acr = 'Planned date of admission';
const String claim_amount_field = 'Approximate total bill';
const String claim_amount_field_acr = 'Estimated treatment amount';
const String claim_amount_description =
    'Roughly, what was the total cost of your treatment?';
const String policy_holder_name_text = 'Policy holder\'s name';
const String policy_holder_bank_text = 'Policy holder’s bank account number';
const String policy_holder_bank_re_enter_text = 'Re-enter bank account number';
const String policy_holder_ifsc_text = 'IFSC';
const String help_text = 'Help';
const String upload_successful = 'Upload successful';
const String health_acko_email_id = "<EMAIL>";

const String health_ipd_claim_ready =
    'Your claim is ready to be processed by ACKO.';
const String sample_image_is_only_for_refernce =
    "Sample document only: real documents may differ.";
const String otp_resent = "OTP resent successfully";
const String bank_details_header =
    'Provide the bank details of the policy holder for reimbursement';
const String health_doc_upload_multiple_files =
    "You can upload multiple files:";
const String additional_documents = "Additional documents";
const String documents = 'Documents';
const String claim_draft_load_alert =
    "You already have an ongoing claim, do you want to continue that or register a new claim?";
const String claim_draft_load_positive = "Continue editing draft";
const String claim_draft_load_negative = "File new claim";
const String claim_draft_save_alert =
    "Do you want to save your claim as a draft?";
const String claim_draft_save_positive = "Save as draft";
const String claim_draft_save_negative = "Discard";

const String what_covered = "What’s covered";
const String addons = "Add-ons";
const String health_card = "Health cards";

const String name_txt = "Name";
const String uhid_txt = "UHID";

const String download = "Download";
const String share_policy = "Share with your family members";

//Bank details page errors
const String error_enter_name = 'Please enter name';
const String error_enter_account = 'Please enter account number';
const String error_re_enter_account = 'Please re-enter account number';
const String error_accounts_not_matching = 'Account numbers not matching';
const String error_enter_ifsc = 'Please enter IFSC code';
const String error_enter_valid_ifsc = 'Please enter valid IFSC code';
const String error_enter_valid_account_number =
    'Please enter valid account number';

//File claim page errors
const String error_enter_amount = 'Please enter a valid amount';
const String enter_amount = 'Enter amount';
const String error_file_format = 'You can upload only %s file formats.';
const String error_claim_amount_exceed =
    'Claim amount exceeding maximum sum insured';
const String no_treatment_found =
    'Sorry, we cannot find the treatment you searched';
const String treatment_coverage = 'All covered treatments';

//Coin History
const String title_coin_history = "Your ACKO Coins History";

//Fitness tracking
const String title_fitness_tracking = "Your Health Data";
const String steps = "Steps";
const String distance = "Distance";
const String calories = "Calories";
const String daily = "Daily";
const String weekly = "Weekly";
const String monthly = "Monthly";
const String daily_goal = "Daily goal";
const String weekly_goal = "Weekly goal";
const String total = "Total";
const String average = "Average";
//EVENTS PROPERTIES
const String tracker = 'tracker';
const String cid = "cid";
const String plan = "plan";
const String event_premium = "premium";
const String referText =
    'Hey, I had an amazing experience insuring myself with ACKO!\nYou too can insure your loved one\'s by downloading the ACKO app today. bit.ly/refer_acko';
//
const String restart_required = 'Restart required!';
const String looks_like_something_went_wrong =
    'Looks like something went wrong. But don’t worry, there is nothing a restart can’t fix.';
const String close_app = 'Close app';
const String this_will_take_you_to_homescreen =
    'This will take you to the homescreen. Tap the Acko icon to relaunch the app.';

//Profile Payout Preference
const String bank_account_info =
    'Bank account transfers are done via NEFT & will take 2-3 working days to transfer the claim amount.';
const String add_bank_account = 'Add bank account';
const String upi_info =
    'Immediately transfer money to bank account using Unified Payment Interface (UPI).';
const String add_upi = 'Add UPI';
const String wallet = 'Wallet';
const String walletInfo =
    'Immediately transfer money to Paytm and Amazon pay wallet services.';
const String add_wallet = 'Add wallet';
const String payout_methods = 'Payout methods';
const String kyc_information = 'KYC information';
const String payout_preference_info = 'How do you like us to pay you?';
const String whatsapp = "WhatsApp";
const String notifications = 'Notifications';
const String profile_ownership_transfer = 'Transfer ownership';
const String support = 'Support';
const String personal_info = 'Personal info';
const String you_and_your_family = 'You & your family';
const String family = 'Family';
const String assets = 'Vehicles';
const String addresses = 'Addresses';
const String about_us = "About us";
const String privacy_policy = "Privacy policy";
const String terms_of_use = "Terms of use";
const terms_n_conditions = "Terms & conditions";
const manage_account = "Manage account";
const String personal_details_info = 'Name, Contact, Email..';
const payment_preference_alert =
    'Payment details will be used for claim reimbursements or in any case of refunds.';
const String wallets = 'Wallets';
const String add_new_bank_account = 'Add new bank account';
const String add_new_upi = 'Add new UPI';
const String add_wallets = 'Add wallets';
const String re_enter_account_number = 'Re-enter account number';
const String account_holder_name = 'Account holder’s name';
const String upi_id = 'UPI ID';
const String save = 'Save';
const String update = 'Update';
const String add_name = 'Add name';
const String edit_name = 'Edit name';
const String add_email = 'Add email';
const String edit_email = 'Edit email';
const String add_phone = 'Add phone';
const String edit_phone = 'Edit phone';
const String edit = 'Edit';
const String edit_bank_account = 'Edit bank account';
const String edit_upi = 'Edit UPI';
const String successfully_added = 'Successfully added ';
const String email_successfully_removed = 'Email successfully removed ';
const String remove_txt = 'Remove';
const String remove_bottom_sheet_address =
    'Are you sure you want to remove\naddress details';
const String remove_bottom_sheet_family_member =
    'Are you sure you want to remove\nmember details';
const String update_bank_success = 'Successfully updated ';
const String delete_bank_success = 'Successfully removed ';
const String paytm = 'Paytm';
const String amazon_pay = 'Amazon Pay';
const String choose_wallet = 'Choose wallet';
const String wallet_account = 'Mobile number linked to the wallet';
const String phone_number = 'Phone number';
const String mobile_number = 'Mobile number';
const String gender = 'Gender';
const String declaration =
    'I declare that the account provided herein for receiving refund/claim belongs to me.';
const String edit_wallet = 'Edit wallet';
const String accountNumberError = 'Account number does not match';
const String delete = 'Delete';
const String select_wallet_error = 'Please select wallet';
const String check_declaration = 'Please check the declaration box';
const String remove_bank_details =
    'Do you want to remove the \nbank account details?';
const String yes = 'Yes';
const String no = 'No';
const String name_updated = 'Your Name has been updated successfully!';
const String email_update = 'Your Email has been updated successfully!';
const String remove_upi_details = 'Do you want to remove the \nUPI ID details?';
const String remove_wallet_details =
    'Do you want to remove the \nwallet details?';
const String looking_to_edit = 'Looking to edit?';
const String remove_bank_account = 'Remove bank account';
const String bank_holder_name = 'Bank holder name';
const String skip_payout_label = 'Continue and add bank details later';

//Health Card
const String sum_insured_txt = 'Sum insured';
const String top_up_txt = 'Top up value'; //ahp-5605
const String ecode_txt = 'EMPLOYEE ID';
const String policy_no_txt = 'POLICY NUMBER';
const String corporate_txt = 'Health Plan';
const String you_your_family = 'You & your family';
const String acko_health_plans = 'ACKO health';
const String card_holder = 'CARD HOLDER';
const String dob_txt = 'DOB';
const String date_of_birth = 'Date of Birth';

const String permissionOnboardDefaultString =
    "{\"data\":[{\"image\":\"assets\/images\/ic_camera_permission.svg\",\"title\":\"Camera\",\"desc\":\"Raise claims or provide documents by clicking pictures on your phone\"},{\"image\":\"assets\/images\/ic_location_permission.svg\",\"title\":\"Location\",\"desc\":\"For Road Side Assistance & inspecting your car at a live location\"},{\"image\":\"assets\/images\/ic_gallery_permission.svg\",\"title\":\"Photos\/Media\",\"desc\":\"Raise claims or provide documents by attaching pictures from your gallery\"}]}";
const String mediAssist = "medi_assist";
const String tap_zoom_in_txt = 'Tap on image to zoom in.';

const String for_exchange_to_your_policy =
    'For changes to your policy, reach out to us';
const String edit_your_policy_detail =
    'To edit your policy details, please call our customer support team on 1800 266 2256 or write to us at';
const String somethingWentWrong =
    "Something went wrong, Please try again later!";

const String up_to_seven_days = "*For upto 7 days";
const String we_do_all_hard_work = 'We do all the hard work for you';
const String We_will_pick_up_your_car =
    'We will pick up your car, repair it and bring it back to you even if you have dropped it off at a non Acko certified garage, for free. What’s more, we even warrant your repairs';
const String we_need_to_know =
    'We need to know more about the accident to register your claim';
const String how_did_it_happen = 'How did it happen?';
const String briefly_describe_where =
    'Briefly describe where and how the accident happened';
const String incident_city = 'Incident city';
const String describe_the_incident = 'Describe the incident...';
const String in_which_city = 'In which city is your vehicle right now?';
const String when_did_it_happen = 'When did it happen?';
const String today = 'Today';
const String yesterday = 'Yesterday';
const String who_was_driving =
    'Was the vehicle being driven at the time of accident?';
const String driver_name = 'Enter name of the Driver';
const String field_required = 'This field is required';
const String garage_details_desc =
    'Please provide us garage details we can review the vehicle and approve your claim';
const String garage_name = 'Garage Name';
const String garage_contact = 'Phone Number of Garage Contact';
const String repair_guarantee = '3 day repair guarantee';
const String repair_guarantee_desc =
    'We can pick up your car, repair it and bring it back to you in 3 days even if you have dropped the car off at a non Acko garage';
const String done = 'Done';
const String garage_location = 'Garage Location';
const String select_date = 'Select date';
const String tp_claim = 'To Register a claim on TP policy';
const String go_back = 'Go back';
const String chat_hint = 'What can we help you with?';
const String start_typing = 'Start typing ...';
const String your_messages = 'Your messages';
const String get_quick_help = 'Get quick help from our expert';
const String query_chat =
    'Just enter your query in the message box and we will get back to you to quickly resolve it.';
const String request_callback = 'Request Callback';
const String message_sent =
    'Thanks for reaching out, we will get back to you soon';
const String need_help_with = 'Need Help with';
const String need_help_with_smthng = 'Need help with something?';
const String select_time_slot = 'Select a time slot';
const String req_callback_success_title =
    'Thank you for submitting your request.';
const String req_callback_success_desc =
    'We will get on top of your issue and quickly get back to you.';
const String comment = 'Comment';
const String survey_scheduled = 'Survey Scheduled';
const String crew_member_assigned =
    'Our crew member will be automatically assigned 1 hour before the scheduled time.';
const String scheduled_time = 'Scheduled time';
const String survey_complete = 'Survey complete';
const String survey_complete_desc =
    'A survey of your vehicle has been completed. We\'re now assessing your vehicle damage.';
const String survey_photos = 'Survey photos';
const String see_all_photos = 'See all photos';
const String see_all = 'See all';
const String pickup_scheduled = 'Pickup scheduled';
const String your_claim_registered =
    'Your claim has been registered. Sit back & relax as we process your claim.';
const String claim_number = 'CLAIM NUMBER : ';
const String callback_scheduled_desc =
    'An ACKO claim handler will call you to guide you through the next step.';
const String scheduled_time_upper = 'SCHEDULED TIME : ';
const String survey_scheduled_desc =
    'Our claim handlers will be assessing your claim shortly.';
const String survey_photos_history = 'SURVEY PHOTOS : ';
const String reschedule_call = 'Reschedule call';
const String drop_scheduled = 'Drop scheduled';
const String health_policy_txt = "health policy";

const String hereby = 'I, %s, hereby declare that :';
const String agreeStatement = 'Yes, I agree';
const String registerClaim = 'Register claim';
const String declarations_txt = 'Declarations';
const String review_declarations_txt = 'Review Declarations';
const String declaration_alert_notes_title =
    'What happens if my declarations\nare wrong?';
const String declaration_alert_notes =
    'If you intentionally make an untrue\nstatement, we will have no choice but to\nreject your claim.';

const String dec_note = 'Please note: ';
const String declaration_notes =
    'False declarations might cause your claim to be rejected.';

const String receive_email_updates =
    "You will receive updates on your email address ";

const String receive_whatsapp_updates =
    "I would like to receive claim updates on";
const String plan_benefits = "Plan benefits";
const String deductible_start_text = 'Top up kicks in after ';
const String deductible_end_text = ' deductible runs out.';
const String see_benefits = "See benefits";
const String card_rejected = "Card rejected?";
const String multiple_addons =
    "This plan has addons - in case your treatment is not processed, please try the right UHID for your topup";
const String plan_details = 'About your plan';
const String your_plans = 'Your plans';
const String benefits = "Benefits";
const String e_cards = "${e_card}s";
const String acko_retail_top_up = "ACKO Platinum Super Top-up";
const String acko_retail_plan = "ACKO Platinum Health Plan";
const String policy_cards = "Policy cards";
const String e_card = "E-card";
const String view_e_card = "Click here to view your E-card";
const String view_e_card_cta = "View e-card";
const String id_proof = "ID proof";
const String past_consultation = "Doctor’s past consultation papers";
const String investigation_related =
    "Investigation reports related to the ailment";
const String if_available = "(if available)";
const String pan_adhar_bill_dl =
    "PAN card, Aadhar card, Electricity bill or Driving license";
const String e_cardsAndDoc = "${e_card}s & documents";
const String view_policy_card = "View policy card";
const String add_another_email = "Want to add another email address?";
const String add_here = "Add here";
const String call_us = "Need help? Call us";
const String latest_status = "Latest status";
const String claim_status_update = "Get claims status updates on your email";
const String expired = 'Expired';
const String rejected_on = 'Rejected on';
const String expired_on = 'Expired on %s';
const String deactivated_on = 'Deactivated on %s';
const String uw_review = 'Reactivation request is under review';
const String valid_until = 'Valid until %s';
const String plan_expired_on = 'Plan expired on %s';
const String dontWorryExpired =
    "Don’t worry, we got you covered! You can still use your wellness offers";
const String renewed = 'Renewed';
const String date_error = 'Please enter valid date';
const String no_member_date_error =
    'No member policy available on the selected date.\nPlease select the valid date.';
const String healthSupportNumber = "tel:18002662256";
const String type = "Type";
const String filed_on = "Filed on";
const String patient_name = "Patient name";
const String employee_code = "Employee code";
const String claim_breakdown =
    'This claim has been split into parts for faster payout';
const String claim_parts = "Parts are paid as they are resolved";
const String partial_claim = "Claim part";
const String split_1 = "Your claim was ";
const String split_2 =
    " for a faster payout. As each part is settled, you get paid.";
const String excluded_policy_heading = 'Following are excluded from new policy';

const String request_follow_up =
    'A followup call has been scheduled with your claim handler';
const String settlement_amount = 'SETTLEMENT AMOUNT: ';
const String settlement_accepted =
    'You have accepted the settlement offer. We will update you shortly for more information.';
const String see_repair_estimate = 'See repair estimate';
const String repairs_done_desc =
    'Congratulations! Your car is ready to be back on the road';
const String see_delivery_order = 'See delivery order';
const String pickup_scheduled_desc =
    'Pickup of your vehicle has been completed & it has reached the garage. We are now assessing vehicle damages.';
const String pickup_complete = 'Pickup complete';
const String documents_uploaded =
    'We\'ve received all the required documents for us to continue processing your claim';
const claim_closed_desc =
    'For any queries, please request a callback or send a message for the fastest response regarding your claim';
const String pickup_photos = 'Pickup Photos';
const String pickup_photos_history = 'PICKUP PHOTOS : ';
const String call_help = 'Talk to our health insurance experts at ';
const String partial_email_us_msg = 'or email us at ';
const String contact_no = '1800 266 2256';
const String claim_formatted_date_format = "dd-MMM-yy, h:mm a";
const String advance_cash_date_format = "dd-MMM-yyyy";
const String claim_home_date_format = "dd-MMM, hh:mm a";
const String ongoing = 'Ongoing';
const String settled = 'Settled';
const String raise_new_claim_title = 'Looking to raise a new claim?';
const String file_new_claim = 'Register a new claim';
const String claims = 'Claims';
const String refer_acko = 'Refer the Acko app';
const String refer_acko_desc =
    'Recommend the ACKO app to your friends & family today!';
const String refer_now = 'Refer now';
const String we_make_insurance_easy = 'We make\ninsurance simpler';
const String we_make_insurance_easy_desc =
    'That’s why 6 Crore+ Indians prefer\nACKO to insure what matters';
const String incredibly_low_prices =
    'More savings when you\nbuy direct from ACKO';
const String incredibly_low_prices_desc =
    'No middlemen, no commission.\nInsure at incredibly low prices.';
const String everything_is_fast = 'Everything is fast &\n100% paperless';
const String everything_is_fast_desc =
    'Do it all on the app — from buying\nand renewing a plan to managing\npolicies and tracking claims';
const String settle_claims_quick = 'We settle claims\nquick and cash-free';
const String settle_claims_quick_desc =
    'With a claim settlement ratio of 94%\*,\nwe keep you relaxed when you claim';
const String login_with_phone_number = 'Can we have\nyour mobile number?';
const String login_with_mobile_number = 'Log in with mobile number';
const String enter_phone_to_link =
    'Enter your phone number to link your health policy';
const String enter_phone_no = 'Enter your phone number';
const String enter_mobile_no = 'Enter mobile number';
const String verify_no = 'Verify your number';
const String log_in_cta = 'Log in';
const String recover = "Recover my account";
const String enter_otp = 'Enter OTP';
const String category_deduction_title = "Category-wise deductions";
const String rupee_symbol = "₹";
const String category_deduction_Header1 = "Category";
const String category_deduction_Header2 = "Billed";
const String category_deduction_Header3 = "Deducted";

//Claim Introduction
const String claim_bill = 'Provide treatment info and the bill';
const String claim_documents = 'Upload medical documents';
const String claim_payment = 'Share bank details for payment';

const String to_continue_txt = 'To continue:';

const String claim_tips = '👉Tip: ';
const String claim_cashless_desc = 'Planning a cashless treatment?';
const String claim_clickhere = 'Check here';

const String treatment_info = 'Treatment info';
const String claim_document = "Documents";
const String claim_bankDetails = "Bank details";

const String provide_treatment_info = 'Provide treatment info';
const String provide_treatment_desc =
    'Enter the patient information, admission date, claim amount and treatment information';

const String share_account_details = 'Share account details';
const String account_detail_desc = 'Enter the account number and IFSC code';

const String treatment_covered_check = 'Find out if your treatment is covered';
const String check_benefits = 'Check benefits section';

const String select_network_hospital = 'Select network hospital';
const String network_hospital_list = 'View list of network hospitals';
const String network_hospital_location = 'Showing results in ';
const String no_hospital_found_message =
    'Sorry, we couldn’t find the hospital you searched';
const String no_hospital_found_suggestion =
    'Please try another hospital name or you can go to any hospital and get it reimbursed later.';
const String other_hospitals = 'Other hospitals';
const String share_document_helpdesk = 'Share documents at helpdesk';
const String document_list = 'Check the list of documents';

const String pre_post_claim_edu =
    'For Pre or post hospitalization for cashless, file a Reimbursement claim';

//Claim KYC
//DOC
const String pan_card_error_text = "Please provide the correct PAN card number";
const String pan_card_number = "PAN card number";
const String kyc_proof = "Identity proof";
const String kyc_select = "Select who you want to pay";
const String kyc_details = "KYC details for %s";
const String kyc_authorise =
    "I, %s, authorise Acko to make my claim payment to %s";
const String choose_photo = "Choose photo";
const String take_photo = "Take photo";
const String retake = "Retake";
const String check_photo = "Check photograph";
const String your_face =
    "Your face must be clearly visible, with a clear background and no obstructions.";
const String upload_photo = "Upload a photo of %s";
const String photo_is_used = "The photo will only be used for verification.";

//treatment-search
const treated_for = 'What did %s get treated for?';
const search_treatments = "Search treatments";
const cant_find_treatment = "Cant find your treatment?";

const prePostTitle = 'Choose your claim for pre/post hospitalization';

//BottomSheet
const String claim_updates = 'Claim updates';
const String alternate_email_desc =
    'Enter an alternate email address to get claim updates';
const String enter_email = 'Enter email address';
const String receive_email = 'You will now recieve claim updates on';
const String confirm_title = 'Confirm that these bank details belong to %s';
const String policy_update_title = 'Which policy do you want to update?';
const String policy_payment_title = 'Which payment schedule you want to view';
const String gmc_sub_title = "Add, edit or remove members";
const String retail_sub_title = "Add members or edit policy";
const String payment_status = "Tap to check payment status";
const String confirm_desc =
    "In compliance with regulations, we can only reimburse the policyholder's account. ";
const String submit_bank_detail = "Submit bank details";
const String confirm_cta_txt = "Confirm";
const String you_and_family = "You & your family";
const String see_card_for = "See card for";
const String edit_update_details = "Edit / Update details";
const String see_benefits_for = "Plans for";
const String download_policy_txt = "Downloading health policy";
const String policy_period = "Policy period";
const String policy_expired = "This policy expired on ";
const String my_policies_title_txt = "My policies";
const String personal_health_plan_txt = "ACKO health plan";
const String personal_plan_super_txt = "Personal plan";
const String change_details_txt = "Change details";
const String edit_reach_out_txt = "For changes to your policy, reach out to us";
const String gmc_endorsement_contact_txt =
    "Please reach out to your company’s HR to make changes to your policy";
const String edit_reach_details_txt =
    "To edit your policy details, please call our customer support team on ";
const String edit_reach_write_txt = " or write to us at";
const String old_terms =
    "All the non-insurance services listed on this app are facilitated by ACKO Tech. Acko GI is not connected or responsible for any of these services offered by Acko Tech in any manner.\n\nThese non insurance services include informative services which  are provided for your convenience only. Neither ACKO Tech nor ACKO GI charges you for these services in any manner.\n\nThe services/information thereunder are procured through third party websites/technology or content platform and are not directly sourced or provided by ACKO Tech. ACKO Tech shall not be responsible in any manner for facilitating any of these services.";
const String terms =
    "The information and technology services including the non-insurance products listed on this ACKO app are provided / facilitated by ACKO Technology and Services Private Limited (“ACKO Tech”) for your general information and convenience only.\n\nACKO General Insurance Limited (“ACKO GI”) and ACKO Life Insurance Limited (“ACKO LI”) are insurers and do not provide any services with respect to any non-insurance products. ACKO GI and ACKO LI shall have no liability or obligation for the services and information provided by ACKO Tech.\n\nThis ACKO App also contains links to third party websites, and neither ACKO Tech nor ACKO GI or ACKO LI shall be responsible or liable for any services provided by, or links to such third parties.\n";
const terms_and_conditions = 'Learn more about ACKO Services';

const about_acko_services = 'About ACKO services';
const about_this_app = 'About this app';

const String health_insurance_without = "without";

const String get_offer = 'Get up to 85% off on car insurance';
const String enter_car_number = 'Enter your car number';
const String get_quote = 'Get quote';
const String or = 'OR';
const String new_car = 'Looking to insure your new car?';
const String get_a_quote = 'Get a quote';
const String onwards = 'onwards';
const String other_acko_product = 'Other ACKO products';
const String taxi_insurance = 'Taxi insurance';
const String bike_insurance = 'Bike insurance';
const String testimonial_title = 'Our customers love the\nAcko experience';
const String off_on_your_car = 'Get up to 85% off on your car insurance';
const String whats_not_covered = 'What’s not covered';
const String talk_to_expert = 'Talk to an expert';
const String experts_help =
    'Need help with something? Our experts\nwill help you out';
const String enter_mobile_num = 'Enter mobile number';
const String call_us_on = 'or call our support team at 1860-266-2256';
const String request_a_callback = 'Request a callback';
const String thank_you = 'Thank you';
const String you_will_receive_callback =
    'You will receive a callback from\none of our executives';
const String get_off_on_taxi = 'Get upto 85% off on your\nTaxi insurance';
const String get_quote_quickly = 'Get a quote in the blink of an eye';
const String welcome_back = 'Welcome back!';
const String already_has_policy =
    'Looks like you already have a policy\ninsured with Acko';
const already_has_policy_with_other_account =
    'Looks like you already have a policy\ninsured with Acko. Login to access your policy';
const access_your_policy = 'Access your policy';
const zero_dep_warning = 'This plan is invalid for cars aged above 5 years.';
const car_plan_eligibility = 'This plan eligibility depends on car age';
const claim_not_filled_txt = 'You haven’t filed a claim yet';
const claim_not_found_txt = 'No claims found!';
const explore = 'Explore';
const data_not_available = 'Oops! we are unable to get data at this time';
const do_more_with_acko = 'Do more with ACKO';
const other_services = 'Other services';
const check_rto_records = 'Check RTO records instantly';
const check_challan_records = 'Check challans instantly';
const get_instant_results =
    'Continue with your vehicle number to get instant results';
const no_challans_found = 'No challans found for this vehicle';
const paid_your_challan = 'Paid your challan ?';
const tap_refresh_get_updated_challans =
    'Refresh to get updated challan(s) \n for your vehicle';
const check_another_vehicle_no = 'Check another vehicle number';
const get_rto_recent_subtitle =
    'Continue with your vehicle number to get instant results';
const no_rto_records_found = 'Vehicle records not available';
const vehicle_data_not_found = 'Vehicle records not found';
const no_rto_records_found_subtitle =
    'Sometimes new vehicle records take up to 4\nweeks, and recently sold vehicles take up to\n2 weeks to be updated.';
const unable_to_fetch_details =
    'Uh-oh. Unable to fetch vehicle details at this time';
const unable_to_fetch_fastag = 'Uh-oh. Unable to fetch fastag details.';
const try_another_vehicle = 'Try another vehicle';
const retry = 'Retry';
const something_went_wrong = 'Oops... Something went wrong';
const no_fastag_found = 'No fastag found';
const refresh_to_update = "Refresh to update";
const api_something_went_wrong_sory =
    "We're sorry. This usually never happens.";
const api_something_went_wrong_subtitle =
    'Not able to fetch the data at moment. Please try again';
const rto_car_details_title = 'Car details';
const rto_bike_details_title = 'Bike details';
const rto_vehicle_details_title = 'Registration details';
const no_location_permission = 'Location permission is off';
const no_location_permission_subtitle =
    'Grant location permission to ensure accurate address and results, or choose your desired location.';
const check_valuation_instantly = 'Check valuation instantly';
const get_vehicle_description = 'Get vehicle info with registration number';
const get_challan_description = 'Check if your vehicle has pending challans';
const get_puc_validity_description =
    'Check your vehicle\'s pollution certificate expiry';
const get_valuation_description = 'Get instant valuation for any used car';
const get_car_bike_valuation =
    'Continue with your car number to get instant results';
const get_vehicle_valuation_hint = 'Enter your car number';
const car_bike_valuation = 'Car valuation';
const rto_info_not_found =
    'We could not find your vehicle records from the RTO';
const enter_missing_info = 'Enter the missing information';
const try_with_vehicle_details = 'Try with vehicle details';
const registration_year = 'Registration year';
const brand = 'Brand';
const String search_other_brands = 'Search other brands..';
const String popular_brands = 'Popular brands';
const model = 'Model';
const city = 'City';
const variant = 'Variant';
const verify_details = 'Verify vehicle information';
const vehicle_not_found = 'Vehicle not found';
const missingInformation = 'Missing information';
const select_reg_yr = 'Select registration year';
const select_your_car = 'Select car';
const select_your_model = 'Select model';
const select_your_variant = 'Select variant';
const select_your_city = 'Select city';
const search_reg_yr = 'Search registration year';
const search_car = 'Search car...';
const search_your_model = 'Search model';
const search_your_variant = 'Search variant';
const search_your_city = 'Search city';
const not_your_car = 'Not your car?';
const check_another_car = 'Check another car?';
const resale_value_of_car = 'Approximate resale value of your car';
const min_price = 'Min price';
const max_price = 'Max price';
const transmission = 'Transmission';
const reg_yr = 'Reg year';
const pending_challan = 'Pending challans';
const String asset_header_id = "headerWidget";
const challan_disclaimer_strings = [
  'Things to note:',
  'Payments will be made on the RTO website and may take up to 48 hours to reflect on the app.',
  'For payment delays or refunds, please contact the RTO directly.'
];
const show_less = 'Show Less';
const show_more = 'Show more';
const fuel_type = 'Fuel type';
const owner_name = 'Owner name';
const reg_date = 'Registration date';
const select_year_error = 'Please select year';
const select_brand_error = 'Please select brand';
const select_model_error = 'Please select model';
const select_variant_error = 'Please select variant';
const select_city_error = 'Please select city';
const error_fetch_valuation =
    "Sorry... We're unable to get valuation at the moment.";
const could_not_load_page = 'Sorry, Could not load the page';
const try_different_car = 'Try Different Car';
const welcome = 'Welcome';
const view_all_services = 'View all services';
const get_car_insured = 'Get upto 85% off on car insurance';
const get_bike_insured = 'Bike insurance starting at ₹555';
const enter_bike_number = 'Enter your bike number';
const looking_for_new_car = 'Looking for new car insurance?';
const looking_for_new_bike = 'Looking for new bike insurance?';
const claim_title = 'Choose the policy to continue with claim';
const edit_title = 'Choose the policy to edit';
const view_all = 'View all';
const payment_pending = 'Payment pending';
const payment_successful = 'Payment successful';
const payment_pending_for = '$payment_pending for';
const pay_now = 'Pay now';
const reactivate_now = 'Reactivate now';
const view_details = 'View details';
const special_price_for_your = 'Special price for your';
const required_policy_update_for = 'Required policy update for';
const policies_not_available_for_edit =
    'Policies not available for editing at the moment';
const login_to_continue = 'Login to Continue';

const payment_status_txt = 'See bill details';
const view_breakup_txt = 'View payment breakup';
const view_settlement_txt = 'See settlement letter';
const payment_breakup_txt = 'Payment breakup';
const settlement_details = 'Settlement Details';
const billed_amount_txt = 'Billed amount';
const paid_amount_txt = 'Paid amount';
const tds_txt = 'TDS';
const paid_by_acko = 'Paid by ACKO';
const acko_pays_txt = 'ACKO pays';
const acko_partner = 'Acko Partner';
const total_bill = 'Total bill';
const contact_txt =
    'If you feel you paid more at the hospital, reach out to us at ';

const payout_breakup_txt = 'Payout breakup';
const payout_to_you = 'Payout to you';
const you_pay_txt = 'You pay';
const transaction_detail_txt = 'Transaction details';
const try_again = 'Try again';
const continue_with_recent_search = 'Continue your insurance purchase';
const String claim_status_error_screen = 'Error claim status';
const String more_details = 'More details';
const String claim_part_status = 'Claim part %s status';
const String pdf_txt = "pdf";
const String timeline_txt = 'Timeline';
const String other_documents_optional = 'other documents (optional)';
const String part_amount = 'Part %s amount';
const String claim_part_details = 'Claim part %s details';
const String claim_type_on = "%s on";
const String claim_submitted_on = "Claim submitted on";
const String query_txt = 'query';
const String dash_symbol = '-';
const String payment_made_txt = 'Payment to be made by you';
const String total_txt = 'Total';
const paid_by_you = 'Paid by you';

//SOS ambulance booking Strings
const book_another_ambulance = 'Book another ambulance';
const book_now_for_emergency = 'Book now for\nan emergency';
const book_for_later = 'Book for\nlater';
const live_tracking = 'Live tracking';
const ambulance_trip_completed = 'Ambulance reached\non time';
const advance_life_support = 'Advance life support';
const arriving_in = 'Arriving in ';
const mins = 'Mins';
const add_details_for = 'Add details for ';
const we_are_calling_you_in_10_sec =
    'We are calling you in the\nnext 10 seconds';
const book_an_ambulance = 'Book an ambulance';
const view_all_bookings = "View all Bookings";
const review_your_booking = 'Review your booking';
const confirm_booking = 'Confirm booking';
const location_details = 'Location details';
const booking_confirmed = 'Booking confirmed';
const pickup_location = 'Pick up location';
const dropoff_location = 'Drop off location';
const pickup_date_time = 'Pick up date and time';
const select_the_blood_group = "Select the blood group";

String getCancellationDate(String date, String time) =>
    'You can cancel your ambulance booking  anytime before $date, $time for free. If you cancel after that, you will be charged a cancellation fee of ₹500. ';
const String cancellation_percentage_description =
    'We will need to deduct 5% of the refund amount as payment gateway charges. The remaining refund amount will be processed within 10 working days.';
const dont_have_these_details_book_anyway =
    'Don’t have these details? Book anyway';
const optional = 'optional';
const we_need_this_information_for_hospital =
    'We need this information to pass on to the hospital in case of an emergency';
const we_will_pass_on_this_information =
    'We will pass on this information to the hospital in case of an emergency';
const pickup_address = "Pickup address";
const dropoff_address = 'Dropoff address';
const booked_for = 'Booked for';
const cancellation_policy = 'Cancellation policy';
const how_to_pay_desc =
    'Red Health will send a payment link to your registered mobile number via SMS. You can pay using UPI, debit card, credit card or pay cash directly to the ambulance driver. ';
const how_to_pay = 'How to pay';
const red_health_agents_are_one_call_away =
    'Red Health agents are just one call away';
const want_to_talk_to_us = "Want to talk to us?";
const need_any_assistance = 'Need any assistance?';
const all_bookings = 'All bookings';
const talk_to_us = 'Talk to us';
const have_not_received_a_call_yet = 'Haven’t received a call yet?';
const ongoing_trip = 'Ongoing trip';
const upcoming_trip = 'Upcoming trip';
const you_have_no_upcoming_bookings = 'You have no upcoming bookings';
const past_bookings = "Past bookings";
const who_do_you_need_ambulance_for = 'Who do you need the ambulance for?';
const booking_for_someone_else = 'Booking for someone else?';
//Cashless Claim Strings
const String hospitalization = 'Hospitalization';
const String date_of_admission = 'Date of admission';
const String date_of_discharge = 'Date of discharge';
const String payment_details = 'Bill details';
const String settlement_letter = 'Settlement letter';
const String reimbursement_claim_for = 'Reimbursement claim for';
const String doc_at_helpdesk = 'Documents required at hospital helpdesk';
const String if_claim_more_than =
    'If the claim amount is more than ₹1L, we will also require your ';
const String hospital = 'Hospital';
const String hospital_address = 'Hospital address';
const String room_category = 'Room category';
const String policy = 'Policy';
const String policy_number_txt = 'Policy number';
const String topup_policy_no = 'Top-up policy number';
const String organization = 'Organization';
const String employee_code_txt = 'Employee code';
const String your_contact = 'Your contact';
const String cashless_txt = 'cashless';
const String cashless = 'Cashless';
const String blacklisted = 'EXCLUDED';
const String acko_cashless = 'ACKO cashless';

const String welcome_home = 'Welcome home';
const String welcome_home_desc =
    'Get daily updates, payments alerts,\nBuy new policy and services from Acko';
const String manage_your_policies = 'Manage your policies';
const String manage_your_policies_desc =
    'Edit, claim and download policies\non the go';
const String explore_services = 'Explore services';
const String explore_service_desc =
    'Do more with ACKO. Explore our various\nservices here';
const String get_support = 'Get support';
const String get_support_desc =
    'We are always just a click away.\nEmail/Call support and FAQ’s';
const String next = 'Next';
const String got_it = 'Got it';
const String insurer = 'Insurer';
const String policy_expiry_date = 'Policy expiry date';
const String engine_number = 'Engine number';
const String chassis_number = 'Chassis number';
const String previous_insurer = 'Previous insurer';
const String vehicle_type = 'Vehicle type';
const String previous_policy_expiry = 'Previous policy expired';
const String previous_policy_expiry_date = 'Previous policy expiry date';
const String connection_failed =
    'Connection to API server failed due to internet connection';
const String off_name = 'Off name';
const String complaint = 'Complaint';
const String blackListData = 'Blacklist Status';

//Page Name for screen tracking
const String health_home_page = 'Health home';
const String policy_benefits_page = 'Health policy benefits';
const String acko_exclusive = 'ACKO exclusive offer';
const String policy_card_page = 'Health policy cards';
const String claims_landing_page = 'Claims landing';
const String reimbursement_intro_page = 'Reimbursement intro';
const String cashless_claim_details_title = 'Cashless Claim Details';
const String claim_success_page = 'Claim success';
const String cashless_static_detail_page = 'Cashless details';
const String cashless_treatment_page = 'Cashless treatment';
const String cash_approved_page = 'Cash approved';
const String raise_claim_education_page = 'Raise claim';
const String claim_declaration_page = 'Claim declarations';
const String document_upload_page = 'Document upload';
const String edit_Endorsement_page = 'Edit endorsement intro';
const String payment_breakup_page = 'Claims payment breakup';
const String reimbursement_txt = 'reimbursement';
const String accept_txt = 'Accept';
const String select_option = 'Please select an option';
const String paid_challans = 'Paid challans';
const String no_challan_title = 'No pending challans';
const String no_challan_subtitle = 'Proof of your safe driving skills!';
const String nothing_yet = 'Nothing yet.';
const String nothing_to_show_yet = 'Nothing to show yet';
const String all_paid_challans = 'All paid challans will be shown here';
const String all_challans_history =
    'All your challan payments will be shown here';

//Aarogya Sanjeevni title
const String aarogya_page =
    'To register a claim on your Aarogya Sanjeevani Policy';
const String fnol_cover_page_title = 'What are you claiming for?';
const String traffic_violation_photos = 'Traffic violation photos';
const String photos = 'Photos';

const String start_date = 'Start date';
const String end_date = 'End date';
const String policy_holder = 'Policy holder';
const String total_insured_value = 'Insured value (IDV)';
const String claim = 'Claim';
const String not_blackListed_note = 'This vehicle is not blacklisted';
const String information_not_available = 'Information not available';
const String download_policy = 'Download policy';
const String pdf_doc = 'PDF document';
const String policy_emailed = 'Policy document sent to email';
const String policy_whats_apped = 'Policy document sent to Whatsapp';
const String save_to_digilocker = 'Save to Digilocker. ';
const String read_more = 'Read more';
const String log_out = 'Log out';
const String full_name = 'Full name';
const edit_info = 'Edit info';
const bullet = '\u00b7';
const four_bullets = '••••';
const saved_cards = 'Saved cards';
const transfer_ownership = 'Transfer ownership';
const kyc_members = 'KYC members';
const no_saved_card = 'You have no saved cards';
const confirm_delete_saved_card =
    'Are you sure you want to remove the saved card?';
const saved_card_delete_success = 'Successfully deleted wallet details';
const customer_call_request_already_created =
    'Customer call request is already created';
const deactivate_account = 'Deactivate account';
const matching_your_vehicle_hsrp = 'Matching your vehicle number with the right HSRP provider...';
const splash_screen_text = 'Get 100% hospital\nbills covered';
const not_access_active_expired_policies =
    'You’ll not be able to access your active or expired policies';
const not_raise_claim = 'You’ll not be able to raise a claim';
const not_get_discounts_offers =
    'You’ll not get any discounts or offers on the apps';
const not_get_alerts =
    'You’ll not get any alerts about your challans, FASTag and other services';
const view_your_active_policies = 'View your active or previous policies';
const raise_a_claim_for_eligible_policy =
    'Raise a claim for any eligible policy';
const protect_with_lower_premiums =
    'Protect your car, bike or family by getting insurance at lowest premiums';
const get_service_informations =
    'Get useful information about your vehicle, traffic challans, nearby services etc.';
const deactivation_success = 'Your account has been successfully deactivated.';
const deactivate_account_desc =
    'Deactivating your account will log you out of the ACKO app';
const deactivate_account_footer =
    'If you log in to the ACKO app anytime in the future, we’ll restore all your existing data and automatically activate your account your account for easy access to secure what matters to you.';
const delete_account_message =
    'Reach out to us at $acko_customer_service to\ndelete your account';
const want_to_delete_account = 'Want to delete your account?';
const edit_personal_info = 'Edit personal info';
const first_name = 'First name';
const last_name = 'Last name';
const acko_customer_service = '<EMAIL>';
const String enter_pincode_to_continue = 'Enter your pincode to continue';
const String ensure_pincode_is_correct = 'Ensure the pincode is correct';
const String enter_a_valid_vehicle_number =
    "Please enter a valid vehicle number ";
const String invalid_email_id = 'Invalid email ID';
const String required = 'Required';
const String invalid_upi_id = "Invalid UPI ID";
const String invalid_ifsc = "Invalid IFSC";
const String invalid_name = "Invalid Name";
const String invalid_phone = "Invalid phone number";
const String invalid_account = "Invalid account number";
const update_mobile_number =
    'To update mobile number in a policy document, reach out to us at ';
const update_personal_detail_success = 'Successfully updated personal details';
const String pay_amount = 'Pay amount to';
const search = 'Search';
const time_slot = 'Available: 7:00 AM - 7:00 PM';
const time_24x7 = 'Available 24x7';
const recovery_otp_title = 'Enter verification code';
const recover_my_account = 'Recover my account';
const recover_desc = 'Enter your email or mobile number linked to your account';
const tap_btn_acko_partnered_mobile_centers =
    'tap_btn_acko_partnered_mobile_centers';
const tap_btn_all_mobile_centers = 'tap_btn_all_mobile_centers';
const tab_btn_all_ev_centers = 'tab_btn_all_ev_centers';
const tab_btn_ev_brand_selected = 'tab_btn_ev_brand_selected';
const puc_center_title = 'Emission check centers nearby';
const mobile_repairs_title = 'Service centers for';
const you_have_active_policy = 'You have an active policy';
const active_policy_desc =
    'Please raise a claim on your ACKO plan before visiting a service center.';
const raise_a_claim = 'Raise a claim';
const claim_banner_text =
    'Raise a claim on your active ACKO plan before visiting a service center.';
const show_nearby_device_centers = 'Show nearby service centres';
const location_permission_disabled = 'Location permission is disabled';
const choose_location_manually = 'Choose location manually';
const location_disabled_desc =
    'Location permission is required to ensure accurate results';
const allow_location_permission = 'Allow location permission';
const enable_location_services = 'Enable Location Services';
const enable_location_services_desc =
    'Please go to Settings and enable location service for Acko';
const enable_android_location_permission =
    'It looks like you have turned off permissions. It can be enabled under Phone settings > Apps > ACKO > Permissions';
const try_changing_the_location = 'Try changing the location';
const String message_us = 'Message Us';
const String enable_permission = 'Enable Permissions';
const enable_permissions =
    'It looks like you have turned off necessary permissions. It can be enabled under Phone settings > Apps > ACKO > Permissions';
const recommended_for_u = 'Recommended for you';
const String your_mails_will_appear_here =
    'Your mails will appear here. Click the below button to start new conversation.';
const String start_new_mail = 'Start new mail';
const String acko_support = 'ACKO support';
const String mail_us = 'Mail us';
const String inbox = 'Inbox';
const String from = 'From';
const String view_more = 'View more';
const String view_less = 'View less';
const String last_updated = 'Last updated';

//JusPay Strings
const event = 'event';
const payLoad = "payload";
const value = 'value';
const methodName = "method_name";
const pigName = 'pig_name';
const screenName = "screen_name";
const status = "status";
const user_aborted = "user_aborted";
const backpressed = "backpressed";
const authorizing = "authorizing";
const pending_vbv = "pending_vbv";
const error = 'error';
const paymentInstrument = 'paymentInstrument';
const errorMessage = 'errorMessage';

//Health Plan-
const selectPlan = 'Select Plan';
const treatedFor = 'Treated for ';
const choosePlan = 'Choose a plan';
const reviewPlan = 'Review your plan limits';
const knowMore = 'Know more';
//Error Strings
const String some_error = 'some error';
const enter_valid_otp = 'Enter a valid OTP';
const otp_has_expired = 'OTP has expired. Click resend to generate a new OTP';
const no_access_to_email = 'I don’t have access to my email';
const String tapHere = 'Cant find claim? Tap here';

const String treatmentNotCoveredLabel = "Treatment not covered";
const String continueWithClaim = "Continue with claim";
const String treatmentNotCoveredDesc =
    "%s are not covered by %s’s health policy. Your claim will be rejected";
const String corporate_health_plan = 'Corporate health plan';
const String select_your_policy = 'Select your policy';
const String slowInternetError = "Slow internet. Might impact app performance.";
const String viewPlansFor = "View plans for";
const String whenWasTheVisitDescription =
    "Date of hospital admission, health checkup, doctor consultation, etc";
const String sampleCarRegNumber = "Eg: KA02MU0001";
const String carCoupon = "ACKOAGAIN";
const String carCouponSubtitle = "₹200 off on car insurance";
const String viewCarInsurancePlans = "View car insurance plans";
const String carCouponDescription =
    "This coupon code will be available the next time you purchase ACKO car insurance";
const String carCouponAdditionFailed =
    "Unable to process the request. Please try again later";
const String carCouponValidationFailed =
    "Please enter valid vehicle registration number";
const String find_out_how_acko_claims_work = "Find out how ACKO claims work";
const String treatment_costs_insurance_pay_for =
    "How much of my treatment costs will insurance pay for?";
const String list_of_documents_needed = "List of documents I need for";
const String pan_id_prpof_photo = "PAN, ID proof, photo";
const String how_to_raise_claim = "How to raise a claim?";
const String go_for_cashless_hospitalisation =
    "Go for cashless hospitalisation";
const String get_reimbursed = "Get reimbursed";
const String planning_for_treatment = "Planning for a treatment";
const String treatment_already_done = "Treatment already done";
const String recommended = "Recommended";
const String how_acko_claims_work = "How ACKO claims work";
const String learn_documents_needed =
    "Learn what documents you'll need, how to submit a claim, and more";
const String looking_for_new_claim = "Looking for a new claim?";
const String upload_these_documents =
    "Upload these documents to process your claim";
const String add_comments = "Add comments";
const String any_comment = "Any comments or questions can be added here";
const String write_here = "Write here";

const String newString = 'New';
const String healthPolicyCardSubtitle =
    'Complete coverage for hospitalization bills up to';
const String youAndYourFamilyCovered = 'You and your family is covered';
const String activePolicies = "ACTIVE POLICIES";
const String expiredPolicies = "EXPIRED POLICIES";
const String seeAllBenefits = "See all benefits";
const String youAndYourFamilyHealth = "You & your family’s health";
const String lifeInsurance = "Your life cover";

const String getHelp = "Get help";
const String needMoreHelp = 'Need more help?';
const String findAnswersToYourQuestions =
    'Find answers to your questions and get help';
const String biggerDot = '●';
const String smallerDot = '•';
const String learnMore = 'Learn more';
const String cantFindCashlessHospital = 'Can’t find a cashless hospital?';
const String getAdvanceCashLabel =
    'Get advance cash for your treatment at any hospital';
const String unlimited = 'Unlimited';
const String coversAllHospitalBills =
    'coverage for all your hospitalisation bills';

const String letsMakeSure = "Let's make sure";
const String everythingInOrder = 'everything is in order';
const String bookingDetails = 'Booking details';
const String pincode = 'Pincode';
const String address = 'Address';
const String slot = 'Slot';
const String bookPpmcTests = 'Book %s tests';
const String get_directions = "Get directions";
const String ppmcTestBookingFor = 'Who are you booking this test for?';
const String ppmcPickMembersForAssessment =
    "Pick everyone who'll be at the same address at the same time. For the rest, you can get another slot later!";
const String ppmcLabTest =
    "Select those living in the same area to go for the lab test together.";
const String add_address_lab =
    "What’s your preferred address?\nWe will find the nearest labs.";
const String noAssessments = 'No assessments to show';
const String selectMembers = "Select member/'s to continue..";
const String selectAddress = "Next: Select address";
const String noAddedAddress = 'No pre-added addresses?';
const String whatTheAddress = "What's the address?";
const String selectAddressAndProceed = 'Select an address to proceed';
const String selectSlot = "Next: Select slot";
const String enterPincodeToProceed = "Enter pincode to proceed...";
const String preferredPinCode =
    "What’s your preferred pin code?\nWe will find labs nearby.";
const String whichSlotWorks = "Which slot works for you?";
const String testsAreUsuallyCompleted =
    "Tests are usually completed in 30 minutes.";
const String selectSlotToContinue = "Select a slot to continue...";
const String morning = 'Morning';
const String afternoon = 'Afternoon';
const String evening = 'Evening';
const String noSlotsAvailable = 'No slots available';
const String allSlotsAreBooked =
    'Looks like all the slots are booked for selected date. Try other date';
const String scheduleSlotsAreBooked =
    "Looks like all the slots are booked for this %s.";
const String slotNotAvailable = "Slot not available";
const String bookingThingsToKnow = "Things to know before booking";
const String dateNotAvailable = "Date not available";
const String viewBookingDetails = "View booking details";
const String bookAnotherTest = 'Book another test';
const String enterPincode = "Enter pincode";
const String pucNotFound = 'PUC not found';
const String rateLimitExceeded = 'Rate Limit Exceeded';

class PayoutStrings {
  static const String titleAccountListPage = "How would you like to be paid?";
  static const String titleAccountListPageFromProfile =
      "Add your account details here";
  static const String addAccountDetails =
      "Add account details that belongs to ";
  static const String upi = "UPI";
  static const String titleAddBankAccountPage = "Share your bank details";
  static const String depositInfoMessage =
      "For verification we will deposit ₹ 1 to your bank account.";
  static const String titleAddUPIAccountPage = "Share your UPI details";
  static const String accountNameHintText = "Enter name as per bank records";
  static const String bankAccount = "Bank account";
  static const String add = "Add";
  static const String placeholderUPI = "UPI ID";
  static const String validUPIValidationMessage = "Please Enter Valid UPI";
  static const String emptyUPIIDMessage = "UPI Id cannot be empty";
  static const String accountHolderNamePlaceholder = "Account holder's name";
  static const String recipientName = "Recipient's name";
  static const String accountNumberPlaceHolder = "Account number";
  static const String confirmAccountNumberPlaceholder =
      "Confirm account number";
  static const String ifscCodePlaceHolder = "IFSC code";
  static const String verifyButtonTitle = "Verify now";

  static const String account_verification_page = 'Account Verification';
  static const String payee_name = 'Payee name';
  static const String enter_details_txt = 'Re-enter %s details';
  static const String contact_us = 'Contact us';
  static const String bank_account_name = 'Bank account name';

  static const String health_here_to_help = "We are here to help";
  static const String health_call_us = "Call us";
  static const String health_email_us = "Email us";

  static const verification_success = 'success';
  static const verification_rejected = 'rejected';

  static const String payout_cta_text = 'Submit account details';
  static const String redirecting_txt = 'Redirecting. Please wait!';
  static const String account_added_successfully = 'Account added successfully';
  static const String update_bank_details_cta_txt = 'Update bank details';
  static const String add_bank_details_cta_txt = 'Add bank details';
  static const String update_upi_details_txt = 'Update UPI details';
  static const String add_upi_details_txt = 'Add UPI details';
  static const String fetching_ifsc_code = "Fetching IFSC details...";

  static const String select_member = 'Select member';
}

const String familyText1 = "7.5 cr+";
const String familyText2 = "Happy smiles";
const String ratingText1 = "4.7";
const String ratingText2 = "Google rating";
const String ratingText3 = "/5";
const String claimText1 = "400 cr+";
const String claimText2 = "Claims settled";

const manage_vehicle = 'Manage vehicles';
const add_new_vehicle = 'Add new vehicle';
const add_vehicle = 'Add vehicle';
const add_your_vehicle = 'Add your vehicle';
const String how_to_transfer = "How to transfer my car insurance?";
const String ot_desc_1 =
    "1. Enter your new vehicle\'s policy number and we\'ll validate the details.";
const String ot_desc_2 =
    '2. After successful validation, you can proceed to the next step.';
const String know_more_about_ot = "Know more about ownership transfer";
const add_your_vehicle_desc = 'Get updates, reminders, offers & many more';
const vehicle_not_found_err_title = 'Vehicle records not found';
const vehicle_not_found_err_desc =
    'Sometimes new vehicle records take up to 4 weeks, and recently sold vehicles take up to 2 weeks to be updated.';
const vehicle_has_been_added = 'This vehicle has already\nbeen added';
const go_to_vehicle_details = 'Go to vehicle details';
const add_another_vehicle = 'Add another vehicle?';

//Challan Automation

const cred = 'Cred';
const phone_pe = 'PhonePe';
const google_pay = 'Google Pay';
const ok = 'OK';
const open_generic_upi_app = 'Open your UPI app and complete the payment request by the RTO';


String vehicleConfirmationTitle(bool isCar) =>
    'Here’s your ${isCar ? 'car' : 'bike'}!\nConfirm and add your ${isCar ? 'car' : 'bike'}';
const continue_string = 'Continue';
const not_your_vehicle = 'Not your vehicle?';
const succesfully_deleted_vehicles = "Successfully removed vehicle details";
const for_your = 'For your';
const upload_docs_and_access_anytime = 'Upload documents and access it anytime';
const your_car_docs = 'Your car documents';
const is_this_your_vehicle = 'Is this your vehicle?';
const is_this_your_vehicle_desc = 'Get updates, reminders, offers & many more';
const are_you_sure_you_want_to_delete_vehicle =
    'Are you sure you want to remove the vehicle details';
const manage_vehicles_one_place = 'All your vehicles in one place';
const your_vehicle_added_from_policy =
    "We've added your vehicle details as per your ACKO policy";
const introducing = 'Introducing';
const intro_10L_plan = '$introducing ₹10L Platinum health plan';

const kyc_consent =
    "I agree that I do not belong to the UN/EU sanctions list and I am not politically exposed";
const start_kyc = 'Start KYC';
const kyc_skip_modal_title =
    'Are you not ready with your PAN & Aadhaar number?';
const kyc_skip_modal_desc =
    'We strongly recommend you to complete your KYC to access your policy. You can complete your KYC from "My policies" section later';
const kyc_skip_not_recommended = 'Skip (Not recommended)';
const make_sure_same_as_govt_id =
    'Lets make sure your name is same as your government ID';
const lets_start_with_your_name = 'Lets start with your name';
const as_per_your_govt_id = 'As per your government ID';
const app_update_available = "App update available";

const exlusive_price_for_acko_users = 'Exclusive price for ACKO users';
const acko_10l_plan_zero_waiting = '₹10L plans with\nzero waiting period';
const waiting_period = "Waiting period";
const exclusions_text = "Exclusions";
const for_specific_illness = "for specific illnesses";
const save_taxes = "Want to save taxes this year?";
const tax_explanation =
    "Get tax deductions up to ₹75k and 100% hospital bill payment with ACKO health plans. ";

///New home String Constants
const check_for_a_different = "Check for a different ";
const view_all_acko_plans = "View all ACKO plans";
const launching_soon = "Launching soon";
const travel_desc = "International travel insurance like no other";
const corporate_plan = "Corporate health policy from ACKO?";
const access_benefits = "Access your benefits now";
const corporate_plan_when_expired =
    "Have another corporate health policy from ACKO?";
const link_email = "Link your new email id now";
const i_am_interested = "I am interested";
const recent_search_text = 'Get upto 75% off';
const get_insurance = 'Get insurance';
const travel_coming_soon = 'International travel insurance\nComing soon!';
const get_notified = 'Get notified';
const buying_new_car = 'Buying a new car?';
const planning_travel_soon = 'Planning to travel soon?';
const buying_new_bike = 'Buying a new bike?';
const maintain_your_car = 'Maintain your car';
const maintain_your_bike = 'Maintain your bike';
const for_your_family_health = "For your family's health";
const disclaimer_heading =
    'All the non-insurance services listed on this app are facilitated by ACKO Tech. ';
const rated_by_mint = "India’s #1 health plan";
const prefetched_asset_card_type = "prefetched_asset_card";
const recent_searches_type = "recent_searches";
const trending_at_acko = "Trending now @ACKO";

/// Acko Drive String constants
const acko_drive_city_selection_tittle = 'Select your city';
const banner_header_text = "LIMITED TIME OFFER";
const banner_body_text = 'Upgrade to an SUV for\na better and safer ride';
const banner_cta_text = "Explore new SUVs";
const alt_banner_body_text = 'Upgrade to a bigger\nand safer SUV today';
// const alt_banner_cta_text = "Book Now";
const powered_by = 'Powered by';
const taking_you_to_acko_drive = 'Taking you to ACKO Drive';
const taking_to_acko_drive_desc =
    'Your contact information will be used on ACKO Drive for a seamless experience';
const go_to_acko_drive = 'Go to ACKO Drive';

/// Challan Payments String constants
const payment_breakup_bottomsheet_title = 'Acko’s got your back';
const payment_breakup_bottomsheet_subtitle =
    'We take-care of your pending challan\npayment';
const payment_breakup_bottomsheet_challan_breakup_text = 'Challan breakup';
const payment_breakup_bottomsheet_button_text = 'To pay';
const payment_details_title = "Challan payment details";
const payment_detials_support_button_text = 'Support';
const violator = "Violator:";
const challan_no = "Challan no:";
const payment_date = "Payment date:";
const payment_mode = "Payment mode:";
const amount_paid = 'Amount paid:';
const transaction_id = "Transaction Id:";
const violation_date = 'Violation date:';
const location = 'Location:';
const paid = 'Paid:';
const transaction_history_title = 'Transactions History';
const help_and_support_bottomsheet_title = 'Help & Support';
const help_and_support_bottomsheet_mail_widget_title = 'Write to us';
const help_and_support_bottomsheet_mail_widget_subtitle =
    'We will reply as soon as we can';
const help_and_support_bottomsheet_call_widget_title = 'Call us';
const help_and_support_bottomsheet_call_widget_subtitle =
    'Talk to us, we are here to help';
const payment_completion_title = 'Well done!';
const payment_completion_received_subtitle = 'We’ve received your payment.';
const payment_completion_notification_subtitle =
    'You will be notified in 1-3 working\ndays once challan is cleared';
const payment_completion_button_text = 'Check challan status';
const check_challan_status_text = 'To check the status of challan, visit';
const challan_payment_history = 'Challan payment history';
const check_pending_traffic_challan_text =
    'Check for pending traffic challans on your vehicle';
const pay_challan_dues_text = 'Pay dues to clear your challans';
const clear_challan_with_rto_notification_text =
    'We will clear the challan with the RTO and notify you';
const challan_payments_intro_bottomsheet_title =
    'Pay traffic challans for\nyour vehicles!';
const challan_payments_guide_card_heading = 'Here’s how it works';
const beta_feature_text = 'Beta feature';
const payment_breakup_guide_card_status_1 =
    'Pay challan dues & we will clear it with the RTO';
const payment_breakup_guide_card_status_2 =
    'Challans are usually cleared within 1 to 3 working days';
const challan_payments_header_titile = 'Pay traffic challans';
const challan_payments_header_subtitile = 'Exclusive only for Maharashtra RTO';
const challan_payment_breakup_text = 'Challan payment breakup';
const amount_to_pay_text = 'Amount to pay';
const proceed_to_pay_text = 'Proceed to pay';
const violations_list_title = 'Violations list';
const copied_challan_no_to_clipboard = 'Copied Challan No. to your Clipboard';
const copied_abha_address_to_clipboard =
    'Copied ABHA Address to your Clipboard';
const eChallan = "E-challan";
const challan_disclaimer =
    'Data might be missing or incorrect.\nOur servers update once in 24 hours.';
const challan_rating_title = 'Found this useful?';
const challan_rating_subtitle =
    'Take a minute to rate the\nACKO app & spread the word';
const play_store_review_url =
    'https://play.google.com/store/apps/details?id=com.acko.android';
const app_store_review_url =
    'https://apps.apple.com/app/id1621244359?action=write-review';
const store_url_error = 'Unable to open store';
const submit_rating = 'Submit rating';

//Advance cash
const advance_cash_title = "Get cash in advance to pay hospital bills";
const advance_cash_request_review = "Our claims team will review your request.";
const advance_cash_go_ahead_plan =
    "Once we get a go ahead, we will pre-load your ACKO app with the treatment money which you can use to pay your hospital bills";
const advance_cash_balance_warning =
    "Since your wallet balance is “0”, you need to submit all the hospitals documents for verification.";
const advance_cash_document_review =
    "Our experts will review your documents and contact you if more information is needed about the hospitalization.";
const advance_cash_submit_documents =
    "You need to submit all the hospitals documents for reimbursement.";
const advance_cash_for_treatment = "Get $advance_cash for treatment";
const String advance_cash_balance = '$advance_cash wallet';
const advance_cash_sub_title =
    "Say goodbye to insurance worries with $advance_cash. Just admit, recover and leave the hospital with peace of mind.";
const advance_cash_ready_to_use = '$advance_cash ready to use';
const advance_cash_ready_desc =
    "The requested advance cash has already been credited. Use the outstanding amount before requesting another cash advance.";
const advance_cash_rejected = '$advance_cash request not approved';
const advance_cash_rejected_desc =
    "Unfortunately, we are unable to approve your advance cash request as your policy doesn’t cover the treatment you mentioned. We apologise for the inconvenience.";
const String advance_cash_cancelled_page = '$advance_cash cancelled';
const String advance_cash_status = '$advance_cash status';
const String advance_cash_customer_education =
    '$advance_cash customer education';
const String advance_cash_customer_education_on_docs =
    '$advance_cash customer education docs';
const String advance_cash_vs_cashless = '$advance_cash vs cashless';
const String get_advance_cash = "Get $advance_cash for treatment";
const String advance_cash_cancel_warning =
    'The $advance_cash will no longer be available to make a payment at the hospital. Your sum insured will be reset back to original.';
const String advance_cash_expiry_warning =
    'You’ve some approved amount balance in your wallet,use it before expiry or exit if no longer needed.';
const request_manual_verification = "Request manual verification";
const hospital_name = "Hospital name";
const get_started = "Get started";
const understood = 'Understood';
const yours_advance_cash_for_treatment = "Your advance cash for treatment";
const requested_amount = 'Requested amount';
const approved_amount = 'Approved amount';
const utilized_amount = 'Utilized amount';
const approved_date = 'Approved date';
const valid_till = 'Valid till';
const invalid_qr_code = 'Invalid QR code';
const invalid_qr_code_message =
    'The QR code scanned doesn’t match the expected hospital name. Please request manual verification and try scanning again.';
const ac_expired_on = 'Expired on';
const cash_approved = '$advance_cash approved';
const cash_approved_description =
    'You can use the maximum amount for your treatment without any paperwork.';
const speedy_recovery = 'Wishing you a speedy recovery & Get well soon ✌️';
const keep_these_documents_handy =
    "To get approval for Advance Cash, you will need the following documents";
const this_feature_is_only = "This feature is only applicable for your ";
const go_back_to_home = 'Go back to home page';
const balance_amount_must_be_paid =
    'Please pay the remaining amount and get it reimbursed after treatment';
const treatment_over = 'Time to rest, relax and recover.';
const paid_to = 'Paid to';
const paid_at = 'Paid at';
const acr_existent_for_member =
    "There is already an advance cash request initiated for different member. If you want to submit claim for any other member, please delete this draft and create a new claim.";
const acr_existent_for_date =
    "There is already an advance cash request initiated for a different date. If you want to submit claim for any other date, please delete this draft and create a new claim.";
const change_member = "Change member";
const change_date = "Change date";
const cash_balance = 'Cash balance';
const transferred_amount = 'Transferred amount';
const txn_id = 'Transaction ID';
const cancelled_on = 'Cancelled on';
const failure = "FAILURE";
const cancellation_reason = 'Cancelled by user';
const success = "Success";
const pending = "Pending";
const payoutInitiatedStatus = 'hosp-payout-initiated';
const will_do_later = 'I will do it later';
const doctors_note = 'Doctor\'s note';
const hospital_and_doctors =
    'Hospital and doctor\'s name with hospital doctor stamp';
const details_of_diagnosis = 'Details of diagnosis';
const proposed_line_of_treatment = 'Proposed line of treatment';
const estimated_cost_of_treatment =
    'Estimated cost of treatment including room type and rent';
const doctors_note_sample = 'Doctor\'s note sample:';
const estimated_cost_sample = 'Estimated cost sample';
const track_status = 'Track status';
const remaining_payment = 'Remaining payment';
const advance_cash_canceled = '$advance_cash cancelled';
const how_acr_works = 'How does $advance_cash work?';
const recipient_details = 'Enter recipient bank details';
const acr_detail_note = 'Note: ';
const acr_detail_note_desc =
    'This amount can be used only for the above member.';
const acr_detail_upload_doc_note =
    'Submitting your documents will help us process and close your claim at the earliest';
const im_done_with_treatment = 'I’m done with treatment';
const faqs = 'Frequently asked questions';
const what_happens_next = "What happens next?";
const cancel_request = 'Cancel request ->';
const claim_settled = 'Your claim is being processed';
const requested_on = 'Requested on';
const why_is_advance_cash_different =
    'How is Advance Cash different from Cashless?';
const see_why = "See why";
const watch_now = "Watch now";
const how_does_advance_cash_work = 'How does $advance_cash work?';
const learn_more_about_advance_cash = "Learn more about Advance Cash ->";
const additional_info = 'Additional info';
const view_sample = 'View sample ->';
const doctors_note_desc =
    'A doctor’s note must have the treatment details and approximate cost on the hospital’s letterhead.';
const additional_documents_desc =
    'All other treatment-related reports like X-rays, blood tests, etc. These reports will help us process your Advance Cash request quickly. ';
const request_under_review =
    'Your request is under review. \n We will credit the approved amount to your Advance Cash wallet. ';
const why_choose_advance_cash = 'Why choose Advance Cash?';
const estimated_amount = 'Estimated amount';
const camera_and_storage_permission = 'Camera access disabled';
const camera_and_storage_permission_desc =
    'Please enable camera access to make QR code payments';
const pay_later = 'Pay later';
const dont_reactivate = 'I don’t want to reactivate my policy';
const subscribe_for_alerts = 'Subscribe for alerts';
const subscribe_for_puc_alerts = 'Subscribe for emission check alerts';
const timely_alert_for_expiring_puc =
    'Get timely alerts for expiring emission checks'; // PUCs
const informed_about_expiration_date =
    'Keep yourself informed about expiration dates';
const here_how_it_works = 'Here\'s how it works';
const puc_expiry_alerts = 'Emission check expiry alert';

const setup = 'Set up';
const manage = 'Manage';
const setup_puc_reminders = 'Set up emission check expiry reminder';
const manage_reminders = 'Manage reminders';
const puc_expiry_date = 'Emission check expiry date';
const remind_me_before = 'Remind me before';
const setup_now = 'Set up now';
const select_vehicle = 'Select vehicle';
const acko_fetch_dates_and_remind =
    'Acko will automatically fetch the expiry details and will send reminders as these dates approach.';
const track_all_vehicle_expiry_dates =
    'Track of all their vehicle document expiry dates';
const avoid_fine_for_expired_documents = 'Avoid fines for expired documents';
const refresh_for_updated_data = 'Refresh for updated data';
const refresh = 'Refresh';
const puc_expiry = 'Emission check expiry';
const is_puc_renewed = 'Is your emission check renewed?';
const set_up_alert = 'Set up alert';
const subscribe_for_puc_alert = 'Subscribe for emission check alert';
const puc_is_expired = 'Your emission check is expired';
const puc_expires_on = 'Emission check expires on';
const expires_on = 'Expires on';

const unable_to_get_updated_puc_details =
    'We are unable to fetch your\nupdated emission check details';
const unable_to_get_latest_puc_details =
    'Unable to get latest emission check certificate. Please try later';
const updated_puc_details = 'Emission check certificate updated';
const no_vehicle_added_yet = 'No vehicle added yet';
const get_timely_alert_expiring_puc_for_vehicle =
    'Get timely alerts for expiring emission check & access more features for your vehicle';
const get_notified_when_puc_expires =
    'Get notified when emission check expires';
const get_notified_when_fastag_balance =
    'Get notified when balance goes below ${rupeeSymbol}';
const get_notified_vehicle_documents =
    'Get notified on time so your vehicle documents are always up to date';
const first_alert_instruction = 'Sign up for alerts you need';
const second_alert_instruction =
    'Challan alerts will be sent to you every 15 days';
const third_alert_instruction =
    'Get notified for expiring emission certificate or low balance on FASTag';

const no_pushNotification_permission = 'Notifcation permission is off';
const no_pushNotification_permission_subtitle =
    'Grant notification permission to allow us to notify you before your vehicle document expires';
const try_again_in_24_hours = 'Please try again in 24 hours';
const get_timely_reminders_for_your_vehicle =
    'Get timely reminders for your vehicle';
const discover = 'Discover';
const new_tag_title = 'New';

///CMS constants
const we_have_problem = 'Houston, We have a problem';
const could_not_load = 'Could not load the page';
const reload = 'Reload';
const video_not_feeling_well = 'This video is not feeling well today!';
const explore_healthy_videos =
    'Explore our other healthy video options for now';
const exclusive = 'Exclusive for you';
const recommended_videos = 'Recommended Videos';
const String active = 'Active';
const String on_hold = 'On hold';
const String deactivated = 'Deactivated';
const String under_review = 'Under review';
const String proceed_anyway = 'Okay, proceed anyway';
const String see_more = 'See more';
const String see_less = 'See less';

const vechicleStringConstants = (
  inputValidationError: (
    bike: 'Enter a valid bike registration number',
    car: 'Enter a valid car registration number',
    vehicle: 'Enter a valid vehicle registration number'
  ),
);

//Fastag string
const fastag_recharge = 'FASTag recharge';
const recharge_fastag_for_your_vehicle = "Recharge fastag for your vehicle";
const enter_your_vehicle_number = 'Enter your vehicle number';
const entered_bike_number = "You've entered a bike number";
const entered_commercial_number = "You've entered a commerical number";
const commercial_fastag_unsupported =
    "Fastag recharge not supported for commerical vehicles";
const no_toll_required_for_bike =
    "No toll payments required for your bike's\nadventures!";
const change_vehicle_number = "Change vehicle number";
const fastag_not_found = "FASTag not found";
const fastag_not_linked = 'FASTag not linked';
const fastag_recharge_limits = 'FASTag recharge limits';
const fastag_recharge_amount_times_limit =
    'There are limits on the recharge amount and the number of recharges you can do per day and/or month';
const learn_about_fastag_limits =
    'Learn more about recharge limits on your FASTag ->';
const try_later = 'Try later';
const not_supported = 'Not supported';
const commercial_vehicles_not_supported =
    'We do not support recharging FASTag\naccounts for commercial vehicles';
const fastag_not_found_subtitle =
    'You could have selected the wrong FASTag\nprovider. Check your FASTag issuing bank and\ntry again';
const fastag_not_linked_subtitle =
    'You must link your vehicle number with the\nFASTag provider to recharge.';
const check_vehicle_registration_number_correctly =
    "You could have selected the wrong FASTag\nprovider. Check your FASTag issuing bank and\ntry again";
const check_fastag_bank = "Finding your FASTag provider";
const look_bank_logo_fastag_sticker =
    "Check the FASTag sticker for a bank name";
const remove_fastag = "Remove FASTag";
const fastag_balance = "FASTag balance";
const tag_status = "Tag status";
const view_more_details = 'View more details';
const how_to_find_fastag_bank = 'How to find FASTag provider?';
const popular_issuing_banks = 'Popular issuing banks';
const cancel_auto_recharge_question = 'Cancel auto recharge?';
const cancel_auto_recharge = 'Cancel auto recharge';
const update_setting = 'Update settings';
const you_are_about_to_remove_desc =
    'You are about to remove FASTag auto recharge facility.\n\nYour FASTag can be blacklisted by a toll plaza if found with insufficient balance.\n\nDo you want to update auto recharge settings instead?';
const current_balance = 'Current balance';
const whitespace_entered_in_numeric =
    'whitespace entered in an numeric keyboard';
const enter_a_valid_amount = 'Please enter a valid amount';
const view_transaction_history = 'View transaction history';
const setup_alert = 'Set up alert';
const when_fastag_balance_goes_below = "When FASTag balance goes below";
const get_notified_on_time_fastag =
    'Get notified on time so your FASTag is always up to date';
const view_current_balance = 'View current balance';
const details_text = 'Details';
const recharge_text = "Recharge";
const recharge_now_text = "Recharge now";
const fastag_details = "FASTag details";
const is_this_your_car =
    'Is this your car? Add to your profile to recharge FASTag instantly';
const continue_to_pay = 'Continue to pay';
const max_possible_recharge = 'Maximum permissible recharge amount';
const unable_to_download_pdf = 'Unable to download receipt pdf';
const pay_details = 'Payment details';
const recharge_amount = 'Recharge amount:';
const customer_convenience_fee = 'Customer convenience fee:';
const download_receipt = 'Download receipt';
const total_amount = 'Total amount';
const amt_paid = 'Amount paid';
const payment_processing = 'Payment processing';
const fastag_payment_processing_text =
    'Awaiting response from your\nFASTag provider. This usually takes\n1-2 minutes';
const view_receipt = 'View receipt';
const check_status = 'Check status';
const payment_failed = 'Payment failed';
const fastag_payment_failed_text =
    'Could not recharge FASTag right\nnow. Any amount debited will be\nrefunded within 7 working days';
const fastag_recharge_success = 'Recharge successful';
const fastag_trnasaction_history = 'FASTag transaction history';
const receipt_not_available = 'Receipt not available';
const paid_on = 'Paid on';
const unable_to_delete_fastag_recharge = 'Unable to delete fastag recharge';
const fastag_car_owners = '10L+ car\nowners';
const fastag_recharge_steps = '2 step\nrecharge';
const fastag_supported_banks = '20+ banks\nsupported';
const enter_your_car_number = "Enter your car number";
const recharge_different_car = 'Recharge different car';
const select_fastag_provider = 'Select your FASTag provider';
const search_your_provider = 'Search your provider';
const popular_fastag_providers = 'Popular FASTag providers';
const recharge_deletion_failed = 'Recharge deletion failed';
const recharge_deleted_successfully = 'Recharge deleted successfully';
const unable_to_fetch_settlement_status = 'Unable to fetch settlement status';
const copied_transaction_id_to_your_clipboard =
    'Copied transaction id to your clipboard';
const bbps_transaction_reference_id = 'BBPS transaction reference id';
const all_fastag_history = 'All your fastag payments will be shown here';
const payment_status_loading = 'Payment status...';
const can_not_delete_fastag = 'Unable to delete FASTag recharge';
const fastag_auto_recharge_setup = 'FASTag auto recharge\nset up';
const if_fastag_balance_is_below = "If FASTag balance is below";
const recharge_for = "Recharge for";
const auto_recharge_fastag = 'Auto recharge FASTag';
const easy_way_to_maintain_balance = "An easy way to maintain FASTag balance";
const cant_find_asset_info = 'Unable to find asset info';
const unable_to_refresh_details = 'Unable to fetch details';
const fastag = "FASTag";
const fastag_payment_details_pdf = "fastagPaymentDetails.pdf";
const recharge_on_the_go = "Recharge on the go";
const String vehicle_no = 'Vehicle no';
const String customer_name = 'Customer name';
const String min_top_up_recharge = 'Minimum top up amount';
const String max_permissible_recharge = 'Maximum permissible\nrecharge amount';
const String your_vehicles = 'Your vehicles';
const String more_actions = 'More actions';
const String fastag_payment_history = 'FASTag payment history';
const String fastag_low_balance_alert = 'FASTag low balance alert';
const String get_alerts_for_puc_and_challan =
    'Get alerts for Challans & emission check';
const String get_alerts_for_challan = 'Get alerts for Challans';
const String get_alerts_for_puc = 'Get alerts for emission check';
const String DAILY_RECHARGE = 'daily recharge';
const String okay = 'Okay';

String getAllThreeReminders(isCar) =>
    'Challans${isCar ? ', FASTag' : ''} & emission check';

String getFastagAndPuc(isCar) =>
    'Get alerts for${isCar ? ' FASTag &' : ''} emission check';

String getFastagAndChallan(isCar) =>
    'Get alerts for${isCar ? ' FASTag &' : ''} Challans';

String getFastag(isCar) => isCar ? 'Get alerts for FASTag' : '';

///ppmc constants
const address_list_title = "Select the address for sample collection";
const add_address_title = "What's the address for sample collection?";
const assessment = "assessment";
const faq_text = "FAQs";
const no_vendor_title = "Thank you for the details";
const no_vendor_text_home =
    'We will get in touch with you shortly to schedule your home tests.';
const no_vendor_text_lab = 'We’ll share the details via email and WhatsApp.';
const home_scheduled_text = "Home tests scheduled successfully";
const lab_scheduled_text = "Lab visit scheduled successfully";
const home_rescheduled_text = "Lab visit rescheduled successfully";
const lab_rescheduled_text = "Lab visit rescheduled successfully";
const test_reminder_text = "Important reminders for accurate results ->";
const how_to_reschedule_test = "How to reschedule tests?";
const call_us_on_our = "Call us on our toll-free number";
const share_your_booking = "Share your booking ID with us";
const let_us_know_your = "Let us know your preferred slot";
const pick_any_slot = "Pick any slot in the next 7 days";
const lab_professional_details = "Lab professional details";
const appointment_details = "Appointment details";
const String errorFetchingLocationDetails = "Error Fetching Location Details";

const puc_emission_check = 'emission check';
const puc_emission_check_sentence_case = 'Emission check';
const puc_emission_checks = 'emission checks'; // puc emission
const emission = 'emission';

///Life vmer and ppmc
const schedule = "Schedule";
const video_medical_evaluation = "Video medical evaluation";
const how_does_this_work = "How does this work";
const vmer_schedule_success_message =
    "Video evaluation\nscheduled successfully";
const vmer_reschedule_success_message =
    "Video evaluation\nrescheduled successfully";

const life_policy_cancel_title = "Are you sure you want to cancel?";
const life_policy_cancel_desc =
    "Any reimbursement claim and cashless claim won’t be honoured once the policy is cancelled.";
const life_cancel_cta_text = "Yes, I want to cancel";
const life_cancel_talk_expert = "Talk to an insurance expert";
const policy_cancellation = "Policy cancellation";
const why_cancel_desc = "Why are you cancelling your policy?";
const refund_details = "Refund details";
const refund_details_desc =
    "As you’re canceling your policy within 30 days of purchase, you’re eligible for full refund.";
const cancel_my_policy = "Cancel my policy";
const go_to_my_account = "Go to my account";
const life_support_email = "<EMAIL>";
const health_support_email = "<EMAIL>";
const life_contact = "1800 210 1992";
const health_contact = "1800 266 2256";
const String life_tab_banner_title = "Beyond Basic Term Coverage";
const String life_tab_banner_desc =
    "ACKO's Flexi Life adapts to life's changing needs";

const String confirm_my_slot = "Confirm my slot";
const String pick_another_slot = "Pick another slot";
const String join_call = "Join call";
const String timeline = "Timeline";
const String lifeBookingDetailsNoteText =
    "We will give you an update after the health evaluation is completed.";
const String healthBookingDetailsNoteText =
    "We will give you an update after the health evaluation is completed for all the members.";
const String vmerSlotNoteText =
    "We will send you a reminder with all the details an hour before your scheduled slot";

/// Manage Alerts constants
const String challans = 'challans';
const String puc_reminders = 'puc_reminders';
const String puc_reminder = 'puc_reminder';
const String challan_reminder = 'challan_reminder';
const String add_puc_reminder_succcess = 'Emission check expiry alert active';
const String add_fastag_reminder_succcess = 'Fastag low balance alert active';
const String reminder_add_success = 'Successfully added reminder';
const String challan_alerts_active = 'Challan alerts active';
const String challan_alerts_active_desc =
    'Alerts will be sent to you every 15 days';
const String removed_reminder = 'Successfully removed reminder';
const String removed_puc_reminder =
    'Successfully removed emission check reminder';
const String removed_challan_reminder = 'Successfully removed challan reminder';
const String auto_check_challan =
    'Get regular challan alerts for\nyour vehicle';
const String traffic_challan_alert = 'Traffic challan alert';
const String last_checked = 'Last checked';
const String alerts_for_your = 'Alerts for your';
const String subscribe_traffic_challan = 'Subscribe for traffic challan';
const String traffic_challan_and_puc_expiry =
    'Traffic challans & emission check expiry';
const String check_now = 'Check now';
const String alert_bottomsheet_title =
    'Get alerts for emission check\nexpiry & traffic challans';
const String alert_bottomsheet_subtitle =
    'Set up alerts and keep your vehicle\nup to date';
const get_puc_alerts = 'Get emission check alerts';
const get_traffic_challan_alerts = 'Get traffic challan alerts';
const vehicle_added_successfully = 'Vehicle added successfully';
const view_challan_details = 'View challan details';
const no_pending_challans_found = 'Looks good. No pending\nchallans found';
const back_home = 'Back home';

const add_new_family_member = 'Add new family member';
const add_members = 'Add members';
const create_link_text = 'Create/Link';
const lets_setup_abha_number = "Lets set up your ABHA number";
const edit_info_abha_details =
    "Updates made here won\'t affect any policy related information or ABHA records";
const self_text = "Self";
const no_abha_id_available = 'No ABHA ID available';
const abha_linked_text = "ABHA linked";
const abha_address = "ABHA Address";
const setup_your_abha_number = "Setup your ABHA number";

const add_date = 'Add date';
const no_policy_link_error =
    'Uh-oh! Seems like we are unable to fetch your policy document at the moment. Please try once again. If the issue persists, write to <NAME_EMAIL> and we will share your document separately.';

/// Email login
const String landing_page_heading =
    "Simple. Fast. Paperless.\nMeet ACKO Insurance. ";
const String landing_insurance_simple =
    'Insurance that’s simple... and super fast';
const String email_login_title = 'Can we have\nyour work email ID?';
const String email_login_input_hint = 'Enter your work email ID';
const String email_login_verify = 'Verify your email';
const String please_wait = 'Please wait...';
const String try_email_again = 'Try again with email ID';
const String not_on_acko_corp = 'Not on the ACKO corporate health plan?';
const String login_with_phone = 'Log in with phone number';
const String email_otp_on_the_way = 'Your OTP is on its way ';
const String email_otp_sent_to = 'Enter the OTP sent to ';
const String email_not_found_title = 'Oops! We couldn\'t find\nthat email ID';
const String email_not_found_sub_title =
    'If you have the ACKO corporate health policy, please check your work email and try again';
const String have_corporate_policy = 'Have a corporate health policy?';
const String login_with_work_email = 'Log in with work email';
const String cant_access = "Can't access?";
const String didnot_receive_code = "Didn’t receive an access code?";
const String company_policy =
    "Depending on your company policy, access code may take up to 30 minutes to reach your inbox.";
const String resend_otp_in = "Resend in 00:%s";
const String otp_on_the_way = "Your OTP is on its way";
const String enter_otp_sent_to = "Enter the OTP sent to %s";
const String otp_sent_successfully = "OTP sent successfully";
const String fetching_details = "Fetching your data ...";
const String do_later = "I'll do this later";
const String go_to_home_page = "Go to home page";
const String setup_policy = "Set up your policy";
const String review_now = "Review now";
const String welcome_to_acko = "Welcome to ACKO, ";
const String facing_issue = "It seems we are facing an issue";
const String please_login_with_phone = "Please log in with your phone number";

/// Help and Support constants
const String write_to_us = 'Write to us';
const String call_support = 'Call support';
const String guides = 'Guides';
const String guides_description = 'Read our guides on insurance.';
const String articles = 'Articles';
const String articles_description = 'Helpful articles to keep you informed.';
const String ebooks = 'Ebooks';
const String ebooks_description =
    'Grow your knowledge on all things insurance.';
const String help_and_support = 'Help & Support';
const String for_all_lobs = 'For car, bike, health & others';
const String for_life = 'For life insurance';
const String explore_more = 'Explore more';
const acko_general_insurance = 'Acko General Insurance';
const acko_life_insurance = 'Acko Life Insurance';
const the_acko_story = 'The ACKO story';

const vehicle_emission_check = 'Vehicle emission check';
const please_check_internet =
    'Something went wrong. Please check your internet connection.';
const please_try_again_later = 'Please try again later';
const emission_puc_not_available =
    'Emission certificate (PUC) data not available';
const expiry_date = 'Expiry Date';
const certificate_number = 'Certificate number';
const certificate_derails = 'Certificate details';
const pollution_certificate_is = 'Pollution certificate is ';
const unable_to_fetch_emission_data =
    'We are unable to fetch emission data for this vehicle right now. Please try later';
const date_nudge =
    'Don’t have the exact dates? You can change\nthe dates later';

/// new ABHA constants
const remove_member_msg =
    'Removing this member will hide their ABHA card from your account. It won\'t affect your shared insurance policies, if any.';
const proceed_confirmation = 'Are you sure you want to proceed?';
const yes_remove = 'Yes, Remove';
const no_keep_them = 'No, keep them';
const abha_note_text =
    'Keeping your family listed means you can see their ABHA cards and health records all in one place';
const abha_share_text =
    'I have joined the next big revolution in health care in India. You should join it as well. Get your ABHA card now in ACKO App';
const your_abha_card = "Your ABHA card";
const for_you = "For you";
const you_n_your_family = "You and your family";
const no_abha_card_error = 'Unable to view ABHA card';
const add_family_members = 'Add family members';
const view_more_family_members = 'View more family members';
const your_friends = "Your friends";
const abha_invite_friend_text =
    'Your friends will receive invitations to get their ABHA, but this will not be visible on your profile.';
const invite_string = 'Invite';
const friend_text = 'Friend';
const String seeDetailedAnalysis = 'See detailed analysis';

/// GMC upsell sell default config strings
const gmc_upsell_card_title = "One major treatment will exhaust your %s cover";
const gmc_upsell_card_subtitle = "See how hospital costs are rising";
const gmc_upsell_sheet_title =
    "Your coverage may <b>not be enough</b> for your family";
const gmc_upsell_sheet_subtitle =
    "It can run out quickly during a hospital stay of over 10 days.";
const gmc_upsell_sheet_desc =
    "Increase your coverage to manage higher healthcare costs.";
const gmc_upsell_sheet_cta_title = "Explore health plans";
const gmc_upsell_sheet_cta_subtitle = "I’m okay with less coverage";

const please_answer_all_questions_to_proceed =
    'Please answer all questions to proceed';

const name_can_only_have_characters =
    'The Name field must be filled and should contain only letters!';

const api_logs = 'Api logs';
const no_api_logs = 'No API logs available.';
const curl_copied = 'CURL copied to clipboard';
const curl = 'CURL';
const headers_string = 'Headers';
const response_string = 'Response';
const error_string = 'Error';
const search_request_hint = 'Search using method, response, status code';
const vehicle_error_constants = (
  empty: "Enter your vehicle number to continue",
  invalid: (
    car: "Ensure the car number is correct",
    vehicle: "Ensure the vehicle number is correct"
  )
);

class TravelString {
  static const claimDocumentUploadLimit = 'You have reached to maximum limit';
  static const countryUpdateErrorTitle = 'Country update error';
  static const countryUpdateErrorDescription =
      "We're unable to update the countries in your policy because your selected plan is not available for the new countries";
  static const reachToTravel =
      'Reach <NAME_EMAIL> for further queries ';
}

///ppmc v2 constants

const test_info_screen_title = "Difficult for a member to complete some tests?";
const test_info_screen_subtitle =
    "No problem. Just let us know which members will be unable to take the tests and and we will suggest alternate tests.";
const HOME_TEST = "HOME_TEST";
const LAB_TEST = "LAB_TEST";
const confirming_your_booking = "Confirming your booking...";
const booking_details_title = "Medical <b>test bookings</b>";
const test_selection_title = "Book slots for the tests";
const home_test_desc =
    "A phlebotomist (lab professional) will visit your address and collect the required samples.";
const lab_test_desc =
    "required to visit a lab for the medical tests. You can pick slots based on the location and availability.";
const status_pending = "pending";
const lab_str = "lab";
const lab_ack_select_member =
    "Select members who cannot\nvisit the lab tomorrow";
const will_send_details_to_whatsapp =
    'We will send all the details on WhatsApp';

//Please try again
const please_try_again = 'Please try again';
const invalid_data_format = 'Invalid data format';
//Payment URL not found
const payment_url_not_found = 'Payment URL not found';

/// life reinstate
const life_reinstate_in_grace_period = 'life_reinstate_in_grace_period';
const life_reinstate = 'life_reinstate';
const WEB_PAGE_STRING = "WEB_PAGE";
const DEEP_LINK_STRING = "DEEP_LINK";
const under_writing = 'under_writing';
const activate_it_now = 'Activate it now';
const complete_pending_tasks = 'Complete pending tasks';
const lifeLockTitle = 'Your family is not protected yet';
const lifeReinstateLockTitle = 'Your family is not protected right now';
const lifeReinstateLock =
    'Secure their financial future through your term life insurance';
const lifeUserActionLock =
    'Complete pending actions to unlock your care centre and life locker';
const lifeNoUserActionLock =
    'Your life care centre and locker will be unlocked after the policy is issued';

/// html text & css parser
const linear_gradient = "linear-gradient";
const radial_gradient = "radial-gradient";
const unsupported_gradient_error =
    "Only linear-gradient and radial-gradient are supported.";
const invalid_linear_gradient_error = 'Invalid linear-gradient syntax';
const invalid_radial_gradient_error = 'Invalid radial-gradient syntax';
const invalid_color_format_error = 'Invalid color format:';

/// PPE
const changes_requested = "Changes requested";
const premium_details = "Premium details";
const old_premium = "Old premium";
const new_premium = "New premium";

/// health VAS
const String healthians = 'healthians';
const String oneMgTnC = '1mg/tnc';
const String medicine = 'medicine';
const String oneMg = '1mg';
const mFineCta = "mfine_cta";
const policyBenefit = "policy_benefit";
const user_ekey_error = "User ekey is required";
const service_type_error = "Invalid service type";
const vas_url_error = "VAS url wasn't generated properly";
const no_partner = "no partner";
const vas_nav = "VAS Navigation";
const redir_url_not_found = 'redirection url not found';
const upsell_card_event = 'upsell_card';

const acko_corporate_policy = "ACKO corporate policy";
const view_your_profile = "View your profile";