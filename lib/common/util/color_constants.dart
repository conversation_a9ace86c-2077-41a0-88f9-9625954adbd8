import 'package:flutter/material.dart';

const Color color454F59 = Color(0xFF454F59);
const Color color414A53 = Color(0xFF414A53);
const Color colorD9F6E7 = Color(0xFFD9F6E7);
const Color color32B572 = Color(0xFF32B572);
const Color color8CE99A = Color(0xFF8CE99A);
const Color color8D99A7 = Color(0xFF8D99A7);
const Color colorE5EAF0 = Color(0xFFE5EAF0);
const Color color2871EA = Color(0xFF2871EA);
const Color colorFFEFEE = Color(0xFFFFEFEE);
const Color colorFD6055 = Color(0xFFFD6055);
const Color colorF6F7FB = Color(0xFFF6F7FB);
const Color color5A68E7 = Color(0xFF5A68E7);
const Color colorF4F5F7 = Color(0xFFF4F5F7);
const Color color1C2476 = Color(0xFF1C2476);
const Color color151619 = Color(0xFF151619);
const Color colorBDBDBD = Color(0xFFBDBDBD);
const Color colorEBEBEB = Color(0xFFEBEBEB);
const Color color4F4F4F = Color(0xFF4F4F4F);
const Color color7C7C7C = Color(0xFF7C7C7C);
const Color color4857E2 = Color(0xFF4857E2);
const Color colorEDEDED = Color(0xFFEDEDED);
const Color color575757 = Color(0xFF575757);
const Color color010101 = Color(0xFF010101);
const Color color5E5E5E = Color(0xFF5E5E5E);
const Color colorC9D0DA = Color(0xFFC9D0DA);
const Color color737F8B = Color(0xFF737F8B);
const Color color4F34D2 = Color(0xFF4F34D2);
const Color color5E27C9 = Color(0xFF5E27C9);
const Color colorFFDA12 = Color(0xFFFFDA12);
const Color color37C87E = Color(0xFF37C87E);
const Color color0FA457 = Color(0xFF0FA457);
const Color color0895AA = Color(0xFF0895AA);
const Color color909090 = Color(0xFF909090);
const Color color555A68 = Color(0xff555A68);
const Color color3EB753 = Color(0xff3EB753);
const Color colorB9AFD0 = Color(0xffB9AFD0);
const Color color7C47E1 = Color(0xff7C47E1);
const Color colorFEE2D2 = Color(0xffFEE2D2);
const Color colorF4F0FD = Color(0xffF4F0FD);
const Color colorF4F0FC = Color(0xffF4F0FC);
const Color colorCCCCCC = Color(0xFFCCCCCC);
const Color color7E6DA5 = Color(0xff7E6DA5);
const Color colorD0BDF4 = Color(0xffD0BDF4);
const Color colorA9DDFE = Color(0xffA9DDFE);
const Color colorE2F5FF = Color(0xffE2F5FF);
const Color colorF2994A = Color(0xffF2994A);
const Color colorD0C2ED = Color(0xffD0C2ED);
const Color color2F80ED = Color(0xff2F80ED);
const Color color453960 = Color(0xff453960);
const Color color74CE63 = Color(0xff74CE63);
const Color color6CCAFF = Color(0xff6CCAFF);
const Color color8990A1 = Color(0xff8990A1);
const Color color9364ED = Color(0xFF9364ED);
const Color color434F5A = Color(0xFF434F5A);
const Color colorF58500 = Color(0xFFF58500);
const Color color707F8C = Color(0xFF707F8C);
const Color color0B0E34 = Color(0xFF0B0E34);
const Color color5E5E5EFF = Color(0x1A000000);
const Color color232323 = Color(0xFF232323);
const Color color0091DD = Color(0xFF0091DD);
const Color colorE4EAF1 = Color(0xFFE4EAF1);
const Color colorE4F9E5 = Color(0xFFE4F9E5);
const Color color282E33 = Color(0xFF282E33);
const Color color333333 = Color(0xFF333333);
const Color color343758 = Color(0xFF343758);
const Color colorFFE9EE = Color(0xFFFFE9EE);
const Color colorD1C0FF = Color(0xFFD1C0FF);
const Color colorFFBECD = Color(0xFFFFBECD);
const Color colorFFD9AD = Color(0xFFFFD9AD);
const Color colorFFF1E2 = Color(0xFFFFF1E2);
const Color colorF75276 = Color(0xFFF75276);
const Color colorF2AA00 = Color(0xFFF2AA00);
const Color colorFFF9E4 = Color(0xFFFFF9E4);
const Color colorECE4F9 = Color(0xFFECE4F9);
const Color color8A99A9 = Color(0xFF8A99A9);
const Color color5D607C = Color(0xFF5D607C);
const Color colorC8D0DB = Color(0xFFC8D0DB);
const Color color44CB86 = Color(0xFF44CB86);
const Color colorEB9D08 = Color(0xFFEB9D08);
const Color colorFEEFD2 = Color(0xFFFEEFD2);
const Color color60BAEA = Color(0xFF60BAEA);
const Color colorBBF9C5 = Color(0xFFBBF9C5);
const Color color00A871 = Color(0xFF00A871);
const Color colorD760BAE0 = Color(0x7260BAE0);
const Color colorFFE46E = Color(0xFFFFE46E);
const Color colorFFA52F = Color(0xFFFFA52F);
const Color colorB0B1C4 = Color(0xFFB0B1C4);
const Color colorE4F6FE = Color(0xFFE4F6FE);
const Color color16C2F8 = Color(0xFF16C2F8);
const Color colorC9EEFD = Color(0xFFC9EEFD);
const Color color2E2E33_72 = Color(0xB72E2E33);
const Color color2E2E33_90 = Color(0xD7000000);
const Color colorE50031 = Color(0xFFE50031);
const Color color018CFF = Color(0xFF018CFF);
const Color colorF4D324 = Color(0xFFF4D324);
const Color color18B665 = Color(0xFF18B665);
const Color colorF1F2F6 = Color(0xFFF1F2F6);
const Color color626B74 = Color(0xFF626B74);
const Color colorD6D9E0 = Color(0xFFD6D9E0);
const Color color27D883 = Color(0xFF27D883);
const Color colorFD4B44 = Color(0xFFFD4B44);
const Color color8788A0 = Color(0xFF8788A0);
const Color color474747 = Color(0xFF474747);
const Color colorF3F7FE = Color(0xFFF3F7FE);
const Color color4C1C00 = Color(0xFF4C1C00);
const Color colorF3F7FF = Color(0xFFF3F7FF);
const Color colorC4C4C4 = Color(0xFFC4C4C4);
const Color colorD9DAE8 = Color(0xFFD9DAE8);
const Color colorDADADE = Color(0xFFDADADE);
const Color colorFAFAFA = Color(0xFFFAFAFA);
const Color colorF4F4F4 = Color(0xFFF4F4F4);
const Color colorFFFFFF = Color(0xFFFFFFFF);
const Color color000000 = Color(0xFF000000);
const Color color282828 = Color(0xFF282828);
const Color colorE8E8E8 = Color(0xFFE8E8E8);
const Color colorE5E5E7 = Color(0xFFE5E5E7);
const Color colorD8D8D8 = Color(0xFFD8D8D8);
const Color color828282 = Color(0xFF828282);
const Color colorA6B1C6 = Color(0xFFA6B1C6);
const Color colorF0F0F0 = Color(0xFFF0F0F0);
const Color color2673DD = Color(0xFF2673DD);
const Color color0096ff = Color(0xFF0096ff);
const Color colorD5D5D5 = Color(0xFFD5D5D5);
const Color colorDDD4F8 = Color(0xFFDDD4F8);
const Color color0E1244 = Color(0xFF0E1244);
const Color color787393 = Color(0xFF787393);
const Color colorCED1DB = Color(0xFFCED1DB);
const Color colorD9DDF0 = Color(0xFFD9DDF0);
const Color colorDAD9FF = Color(0xFFDAD9FF);
const Color colorF1F3F8 = Color(0xFFF1F3F8);
const Color color5BDB89 = Color(0xFF5BDB89);
const Color colorf5f6fc = Color(0xFFf5f6fc);
const Color colorE0E0E8 = Color(0xFFE0E0E8);
const Color colorC28A05 = Color(0xFFC28A05);
const Color color21202A = Color(0xFF21202A);
const Color colorFBEAEA = Color(0xFFFBEAEA);
const Color color95221D = Color(0xFFAC2B2B);
const Color colorEAEEFF = Color(0xFFEAEEFF);
const Color colorFEF1FB = Color(0xFFFEF1FB);
const Color colorE8F1FD = Color(0xFFE8F1FD);
const Color color0085FF = Color(0xFF0085FF);
const Color color652ECE = Color(0xFF652ECE);
const Color color320981 = Color(0xFF320981);
const Color color57DB97 = Color(0xFF57DB97);
const Color color878787 = Color(0xFF878787);
const Color color192434 = Color(0xFF192434);
const Color colorABE5FC = Color(0xFFABE5FC);
const Color color292E32 = Color(0xFF292E32);
const Color color1B73E8 = Color(0xFF1B73E8);
const Color colorFFEEEC = Color(0xFFFFEEEC);
const Color color219653 = Color(0xFF219653);
const Color color0B753E = Color(0xFF0B753E);
const Color color47465E = Color(0xFF47465E);
const Color color4B4B4B = Color(0xFF4B4B4B);
const Color colorFCC2D7 = Color(0xFFFCC2D7);
const Color colorFFE380 = Color(0xFFFFE380);
const Color colorB9F4F9 = Color(0xFFB9F4F9);
const Color colorFFC400 = Color(0xFFFFC400);
const Color colorFFFBF2 = Color(0xFFFFFBF2);


//IA Colours
const Color colorFFF7E5 = Color(0xFFFFF7E5);
const Color colorEB8000 = Color(0xFFEB8000);
const Color colorD83D37 = Color(0xFFD83D37);
const Color color040222 = Color(0xFF040222);
const Color color121212 = Color(0xFF121212);
const Color color541AC3 = Color(0xFF541AC3);
const Color colorEE1520 = Color(0xFFEE1520);
const Color color7B18C9 = Color(0xFF7B18C9);
const Color color32144A = Color(0xFF32144A);
const Color colorB5B5B5 = Color(0xFFB5B5B5);
const Color colorF8F7FC = Color(0xFFF8F7FC);
const Color color5B5675 = Color(0xFF5B5675);
const Color colorDADEF1 = Color(0xFFDADEF1);
const Color colorFFAB00 = Color(0xFFFFAB00);
const Color colorE7E7F0 = Color(0xFFE7E7F0);
const Color color36354C = Color(0xFF36354C);
const Color colorEFE9FB = Color(0xFFEFE9FB);
const Color colorF0F0F6 = Color(0xFFF0F0F6);
const Color colorF3FBF7 = Color(0xFFF3FBF7);
const Color color1C73E8 = Color(0xFF1C73E8);
const Color colorB5B5B9 = Color(0xFFB5B5B9);
const Color color5920C5 = Color(0xFF5920C5);
const Color colorE5E5E5 = Color(0xffe5e5e5);
const Color colorCBCBDB = Color(0xFFCBCBDB);
const Color colorE05752 = Color(0xFFE05752);
const Color color15132F = Color(0xFF15132F);
const Color color5B457B = Color(0xFF5B457B);
const Color colorFDECC6 = Color(0xFFFDECC6);
const Color colorFFF9EC = Color(0xFFFFF9EC);
const Color colorE1E1EA = Color(0xFFE1E1EA);
const Color color6E6A82 = Color(0xFF6E6A82);
const Color colorE8FDF2 = Color(0xFFE8FDF2);
const Color colorD83939 = Color(0xFFD83939);
const Color color0A7AFF = Color(0xFF0A7AFF);
const Color colorD16900 = Color(0xFFD16900);
const Color colorAD87F4 = Color(0xFFAD87F4);
const Color colorA57EF0 = Color(0xFFA57EF0);
const Color color6028C9 = Color(0xFF6028C9);
const Color color7745D6 = Color(0xFF7745D6);
const Color color8E61E3 = Color(0xFF8E61E3);
const Color colorF58700 = Color(0xFFF58700);
const Color colorFFC800 = Color(0xFFFFC800);
const Color colorA9A5BE = Color(0xFFA9A5BE);
const Color colorBBB9C6 = Color(0xFFBBB9C6);
const Color color7A7690 = Color(0xFF7A7690);
const Color colorE3FAFC = Color(0xFFE3FAFC);
const Color colorDFD4F5 = Color(0xFFDFD4F5);
const Color colorB9B9BC = Color(0xFFB9B9BC);
const Color color4EA3F9 = Color(0xFF4EA3F9);
const Color color69C696 = Color(0xFF69C696);
const Color colorEBFBEE = Color(0xFFEBFBEE);
const Color colorA3A1AE = Color(0xFFA3A1AE);
const Color colorBBBAC3 = Color(0xFFBBBAC3);
const Color colorFFF2F2 = Color(0xFFFFF2F2);
const Color colorFFEEEE = Color(0xFFFFEEEE);
const Color colorF3EDFF = Color(0xFFF3EDFF);
const Color colorECFEFF = Color(0xFFECFEFF);
const Color colorFFFBF1 = Color(0xFFFFFBF1);
const Color colorFDF0F9 = Color(0xFFFDF0F9);
const Color colorC1F5C8 = Color(0xFFC1F5C8);
const Color colorDBCEF4 = Color(0xFFDBCEF4);
const Color color0B3874 = Color(0xFF0B3874);
const Color colorF5F5F5 = Color(0xFFF5F5F5);
const Color colorFBE9F6 = Color(0xFFFBE9F6);
const Color colorFFCFF2 = Color(0xFFFFCFF2);
const Color colorFFE9E9 = Color(0xFFFFE9E9);
const Color colorE9DEFF = Color(0xFFE9DEFF);
const Color colorFFF3F3 = Color(0xFFFFF3F3);
const Color color1DB6CD = Color(0xff1DB6CD);
const Color colorFAF8FE = Color(0xFFFAF8FE);
const Color colorECE5FA = Color(0xFFECE5FA);
const Color colorE1E2F = Color(0xFF1E1E2F);
const Color color7B7692 = Color(0XFF7B7692);
const Color color807E8A = Color(0XFF807E8A);
const Color colorE9ECF0 = Color(0XFFE9ECF0);
const Color colorF9F9F9 = Color(0XFFF9F9F9);
const Color color09001B = Color(0XFF09001B);
const Color color37117E = Color(0XFF37117E);
const Color color2A356C = Color(0XFF2A356C);
const Color color26595A = Color(0XFF26595A);
const Color color774859 = Color(0XFF774859);
const Color color774848 = Color(0XFF774848);
const Color color8A52F5 = Color(0XFF8A52F5);
const Color color2F1265 = Color(0XFF2F1265);
const Color color180D2F = Color(0XFF180D2F);
const Color color104EA3 = Color(0XFF104EA3);
const Color color387980 = Color(0XFF387980);
const Color colorE2E8FD = Color(0xFFE2E8FD);
const Color colorF7EDF3 = Color(0xFFF7EDF3);
const Color colorF6F6F6 = Color(0xFFF6F6F6);
const Color color706F87 = Color(0xFF706F87);

const Color color064625 = Color(0xFF064625);
const Color colorF7FFF1 = Color(0xFFF7FFF1);
const Color colorFF6B6B = Color(0xFFFF6B6B);
const Color colorA9C1FE = Color(0xFFA9C1FE);

Color getColorByHex(String? hexColorValue) {
  return hexColorValue == null
      ? Colors.transparent
      : Color(int.parse('0xFF' + hexColorValue.replaceAll('#', '')));
}

const Color colorE6EAF0 = Color(0xFFE6EAF0);
const Color colorE8E7EC = Color(0xFFE8E7EC);
const Color color262351 = Color(0xFF262351);
const Color colorD9D9D9 = Color(0xFFD9D9D9);
const Color colorF16574 = Color(0xFFF16574);
const Color colorE3F4FF = Color(0xFFE3F4FF);
const Color color9AF6AA = Color(0xFF9AF6AA);
const Color color6CB7FF = Color(0xFF6CB7FF);
const Color color79758F = Color(0xFF79758F);
const Color color1B2123 = Color(0xFF1B2123);
const Color color00A0A8 = Color(0xFF00A0A8);
const Color color6AE4EF = Color(0xFF6AE4EF);
const Color color4C6AE9 = Color(0xFF4C6AE9);
const Color color7014E9 = Color(0xFF7014E9);
const Color colorB48CFF = Color(0xFFB48CFF);
const Color color4F4D6A = Color(0xFF4F4D6A);
const Color colorEFE9FA = Color(0xFFEFE9FA);
const Color colorBBA9CA = Color(0xFFBBA9CA);
const Color colorF8FDFF = Color(0xFFF8FDFF);
const Color colorFFE388 = Color(0xFFFFE388);
const Color colorE09F2D = Color(0xFFE09F2D);
const Color colorF3C0BE = Color(0xFFF3C0BE);
const Color colorFE6B6D = Color(0xFFFE6B6D);
const Color color5A21C6 = Color(0xFF5A21C6);
const Color colorF3F3F3 = Color(0xFFF3F3F3);
const Color colorD7D7D7 = Color(0xFFD7D7D7);
const Color colorFFE9A1 = Color(0xFFFFE9A1);
const Color colorE8C44F = Color(0xFFE8C44F);
const Color colorDEDEDE = Color(0xFFDEDEDE);
const Color color76003D = Color(0xFF76003D);
const Color color757575 = Color(0xFF757575);
const Color color5820C5 = Color(0xFF5820C5);

const Color colorF9F6FF = Color(0xFFF9F6FF);
const Color colorF07975 = Color(0xFFF07975);
const Color colorFBF2FF = Color(0xFFFBF2FF);
const Color colorE4EFFF = Color(0xFFE4EFFF);
const Color colorB191ED = Color(0xFFB191ED);
const Color colorEAEAEA = Color(0xFFEAEAEA);
const Color color230B32 = Color(0xFF230B32);
const Color colorA6A6A6 = Color(0xFFA6A6A6);
const Color color53288D = Color(0xFF53288D);
const Color color13002C = Color(0xFF13002C);
const Color color451999 = Color(0xFF451999);

const Color colorF3FAF7 = Color(0xFFF3FAF7);
const Color colorF5F0FF = Color(0xFFF5F0FF);
const Color colorF44949 = Color(0xFFF44949);
const Color colorF0F4F9 = Color(0xFFF0F4F9);
const Color colorF488EE = Color(0xFFF488EE);
const Color colorFF44BD = Color(0xFFFF44BD);
const Color colorFFF9F6 = Color(0xFFFFF9F6);
const Color colorF5F1FF = Color(0xFFF5F1FF);
const Color colorFFD2D2 = Color(0xFFFFD2D2);
const Color color054599 = Color(0xFF054599);
const Color color2D0F44 = Color(0xFF2D0F44);
const Color color060606 = Color(0xFF060606);
const Color color070707 = Color(0xFF070707);
const Color color101010 = Color(0xff101010);


//PDP Health+Life
const Color color380F57 = Color(0xFF380F57);
const Color colorFF6363 = Color(0xFFFF6363);
const Color color32134A = Color(0xFF32134A);
const Color colorDE6D2D = Color(0xFFDE6D2D);
const Color colorE157D3 = Color(0xFFE157D3);


const Color colorFFE9F9 = Color(0xFFFFE9F9);
const Color colorFDF7F7 = Color(0xFFFDF7F7);

const Color colorEADFFF = Color(0xFFEADFFF);
const Color colorC6E9FF = Color(0xFFC6E9FF);
const Color color323247 = Color(0xFF323247);
const Color color0C1A4B = Color(0xFF0C1A4B);
const Color colorEEEEEE = Color(0xFFEEEEEE);
const Color colorDFDFDF = Color(0xFFDFDFDF);
const Color color560098 = Color(0xFF560098);
const Color colorFCFBFF = Color(0xFFFCFBFF);
