import 'package:analytics/analytics.dart';
import 'package:analytics/events/tap_events.dart';
import 'package:flutter/material.dart';

class BackPressRouteObserver extends NavigatorObserver {
  String _getModalType(Route<dynamic> route) {
    return switch (route.runtimeType) {
      DialogRoute => 'dialog',
      ModalBottomSheetRoute => 'bottom_sheet',
      PopupRoute => 'popup',
      _ => 'unknown',
    };
  }

  String _getRouteName(Route<dynamic> route) {
    if (route is PopupRoute) {
      return _getModalType(route);
    }
    return route.settings.name ?? 'unnamed_route';
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    AnalyticsTrackerManager.instance
        .sendEvent(event: TapConstants.TAP_BACK_NAVIGATION, properties: {
      'from_page': _getRouteName(route),
      'to_page':
          previousRoute != null ? _getRouteName(previousRoute) : 'unknown',
    });
  }
}
