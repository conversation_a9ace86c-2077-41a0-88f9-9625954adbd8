import 'dart:async';
import 'dart:io';

import 'package:acko_core_utilities/deeplink_handler/DeeplinkHandler.dart';
import 'package:acko_flutter/common/bloc/ApplicationState.dart';
import 'package:acko_flutter/common/native_to_dart_handler/segment_native_to_dart_handler.dart';
import 'package:acko_flutter/common/util/PreferenceHelper.dart';
import 'package:acko_logger/acko_logger.dart';
import 'package:acko_logger/events/info/app_debug_info.dart';
import 'package:acko_logger/model/info_model.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/card_loaded_events.dart';
import 'package:analytics/events/events_base.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:sdui/sdui.dart';
import 'package:session_manager_module/session_manager.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/core/string_extensions.dart';
import 'package:utilities/remote_config/remote_config.dart';
import 'package:utilities/state_provider/StateProvider.dart';
import 'package:utilities/widgets/acko_safe_cubit.dart';

import '../../r2d2/events.dart';
import '../../util/Utility.dart';
import 'device_info_repo.dart';

class ApplicationBloc extends AckoSafeCubit<ApplicationSate> {
  ApplicationBloc() : super(InitialState()) {
    initApplication();
  }

  void initApplication() {
    ///calls running in background, await not needed.
    Constants.PLATFORM_CHANNEL.setMethodCallHandler(_methodCallHandler);
    Constants.PLATFORM_CHANNEL
        .invokeMethod("update_native_to_dart_handler_loc", {
      "native_to_dart_init_handler_loc":
          getNativeToDartCommunicationInitMethodLocation()
    });
  }

  bool? enableSDUIDeeplink;

  Future<void> _methodCallHandler(MethodCall call) async {
    switch (call.method) {
      case Constants.AF_LINK:
        AckoLoggerManager.instance.logInfo(
            event: AppDebugInfoEvent(
          page: 'application_bloc',
          infoMessage: 'methodcallhandler invoked',
          journey: 'AF_LINK',
          data: {'args': call.arguments?.toString()},
        ));
        if (call.arguments is Map) {
          R2D2Events.instance.trackAppsFlyerEvent(call.arguments);
        }
        StateProvider().notify(ObserverState.AF_CALLBACK, data: call.arguments);
        break;
      case "open_page":
        String map = call.arguments;
        enableSDUIDeeplink ??=
            await RemoteConfigInstance.instance.getGbBoolAsync(
          RemoteConfigKeysSet.ENABLE_SDUI_DEEPLINK,
          defaultValue: false,
        );

        await triggerDeeplink(map, enableSDUIDeeplink!);
        break;

      case "app_rating_prefs":
        var showHomeRatingView = call.arguments["should_display_home_ratings"];
        var userRatingDone = call.arguments["is_user_rating_done"];
        setBoolPrefs(
            BoolDataSharedPreferenceKeys.PREFS_SHOULD_SHOW_RATING_ON_HOME,
            showHomeRatingView);
        setBoolPrefs(
            BoolDataSharedPreferenceKeys.PREFS_SHOULD_SHOW_APP_RATING_VIEW,
            userRatingDone);
        break;

      case "app_rating_event":
        var event = call.arguments["event"];
        var pageTitle = call.arguments["page_title"];
        var feedback = call.arguments["user_feedback"];
        R2D2Events.instance
            .trackHomeInAppRatingEvents(event, pageTitle, feedback);
        break;
      case Constants.IS_APP_UPDATE:
        R2D2Events.instance.trackAppUpdateEvent(call.arguments);
        break;
      case "show_app_update":
        Future.delayed(Duration(seconds: 5), () {
          StateProvider()
              .notify(ObserverState.SHOW_FLEXI_UPDATE, data: call.arguments);
        });
        break;
      case "communication_service_notification_status_update":
        final _args = Map<String, dynamic>.from(call.arguments);
        _args.removeWhere(
            (key, value) => value == null || (value.toString()).isNullOrEmpty);
        DeviceInfoRepository()
            .updateCommunicationServiceNotificationStatus(_args);
        break;
      case "send_events":
        debugPrint("send events");
        String eventName = call.arguments["event_name"];
        debugPrint("send events $eventName ");

        Map eventParams = call.arguments["parameters"];

        Map<String, dynamic> params = Map();
        eventParams.entries.forEach((element) {
          params.putIfAbsent(element.key, () => element.value);
          debugPrint("notification $eventName params key $element");
        });

        await R2D2Events.instance
            .trackPushNotificationEvent(eventName, extraInfo: params);

        ///Whenever passing a new event, it needs to be registred in the Analyitcal module
        BaseEventsClass? event =
            AnalyticsTrackerManager.instance.nativeEventsParser(eventName);
        if (event != null) {
          AnalyticsTrackerManager.instance
              .sendEvent(event: event, properties: params);
        }
        break;
    }
  }

  Future<void> triggerDeeplink(String url, bool enableSDUIDeeplink) async {
    Util.isPageLinkRequired = true;

    final Uri uri = Uri.parse(url);
    Map<String, dynamic>? deepLinkMap;

    AckoLoggerManager.instance.logInfo(
        event: AppDebugInfoEvent(
            page: 'splash_screen',
            infoMessage: 'DEEPLINK_OPEN_PAGE invoked',
            journey: 'deeplink_handling',
            data: {"source": "intent_url"}));

    // Get SDUI deeplink only if link contains https scheme.
    if (enableSDUIDeeplink && uri.scheme.equalsIgnoreCase("https")) {
      // Intialising only for the lifecyle of this call
      final BaseRepository _baseRepository = BaseRepository();

      final deeplinkResponse = await _baseRepository.getResponse(
        Urls.sduiDeeplinkPath,
        queryParams: {
          'url': url,
        },
      );

      //  make an api call so that the server can derive the deeplink from a web url.
      if (deeplinkResponse.error == null &&
          deeplinkResponse.data?['url'] != null &&
          deeplinkResponse.data?['url'] is String) {
        final _url = deeplinkResponse.data!['url'] as String;
        deepLinkMap = _url.getRoute();
        AckoLoggerManager.instance.logInfo(
            event: AppDebugInfoEvent(
                page: 'splash_screen',
                infoMessage: 'DEEPLINK_OPEN_PAGE invoked',
                journey: 'deeplink_handling',
                data: {
              "source": "sdui_deeplink_url",
              "id": _url,
            }));
      } else {
        // Fallback in case of a server failure.
        deepLinkMap = url.getRoute();
        AckoLoggerManager.instance.logInfo(
            event: AppDebugInfoEvent(
                page: 'splash_screen',
                infoMessage: 'DEEPLINK_OPEN_PAGE invoked',
                journey: 'deeplink_handling',
                data: {
              "source": "app_parsed_url",
              "id": deepLinkMap?['route'],
            }));
      }
    } else {
      // Let the App handle the deeplink in case the the scheme is anything apart form https://
      deepLinkMap = url.getRoute();
    }

    debugPrint("Sdui ApplicationBloc url ${deepLinkMap?['url']}");
    debugPrint("Sdui ApplicationBloc route ${deepLinkMap?['route']}");
    if (deepLinkMap != null) {
      emit(DeepLinkReceivedState(deepLinkMap));
    }
  }
}
