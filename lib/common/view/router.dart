import 'dart:async';
import 'dart:convert';

import 'package:acko_flutter/common/bloc/ApplicatioBloc.dart';
import 'package:acko_flutter/common/bloc/ApplicationState.dart';
import 'package:acko_flutter/common/model/drawer_item.dart';
import 'package:acko_flutter/common/model/utm_parameters.dart';
import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/common/view/ErrorWidget.dart';
import 'package:acko_flutter/common/view/acko_scaffold.dart';
import 'package:acko_flutter/common/view/toast.dart';
import 'package:acko_flutter/feature/abha-journey/analytics/abha_journey_property_store.dart';
import 'package:acko_flutter/feature/abha-journey/cubit/abha_journey_cubit.dart';
import 'package:acko_flutter/feature/abha-journey/domain/repository/abha_journey_repository.dart';
import 'package:acko_flutter/feature/abha-journey/view/pages/abha-detail-page/abha_detail_page.dart';
import 'package:acko_flutter/feature/abha-journey/view/pages/abha-journey-landing-page/abha_journey_landing_page.dart';
import 'package:acko_flutter/feature/account_recovery/bloc/account_recovery_bloc.dart';
import 'package:acko_flutter/feature/account_recovery/bloc/account_recovery_otp_verification_bloc.dart';
import 'package:acko_flutter/feature/account_recovery/view/account_recovery_page.dart';
import 'package:acko_flutter/feature/account_recovery/view/recovery_otp_page.dart';
import 'package:acko_flutter/feature/account_recovery/view/recovery_verify_screen.dart';
import 'package:acko_flutter/feature/acko_drive/bloc/acko_drive_cubit.dart';
import 'package:acko_flutter/feature/acko_drive/view/acko_drive_view.dart';
import 'package:acko_flutter/feature/acko_services/bloc/brand_screen_bloc.dart';
import 'package:acko_flutter/feature/acko_services/bloc/map_location_bloc.dart';
import 'package:acko_flutter/feature/acko_services/bloc/map_location_search_bloc.dart';
import 'package:acko_flutter/feature/acko_services/bloc/rto_detail_bloc.dart';
import 'package:acko_flutter/feature/acko_services/repository/acko_service_repository.dart';
import 'package:acko_flutter/feature/acko_services/ui/acko_service_area_search.dart';
import 'package:acko_flutter/feature/acko_services/ui/acko_service_common_header.dart';
import 'package:acko_flutter/feature/acko_services/ui/acko_service_rto_detail_screen.dart';
import 'package:acko_flutter/feature/acko_services/ui/brand_screen.dart';
import 'package:acko_flutter/feature/acko_services/ui/brand_search_list_screen.dart';
import 'package:acko_flutter/feature/acko_services/ui/map_view_service.dart';
import 'package:acko_flutter/feature/ahc/analytics/ahc_property_store.dart';
import 'package:acko_flutter/feature/ahc/cubit/ahc_cubit.dart';
import 'package:acko_flutter/feature/ahc/domain/models/ahc_landing_model.dart';
import 'package:acko_flutter/feature/ahc/domain/models/ahc_schedule_arguments_model.dart';
import 'package:acko_flutter/feature/ahc/domain/repository/ahc_repository.dart';
import 'package:acko_flutter/feature/ahc/view/pages/ahc_landing_page/ahc_landing_page.dart';
import 'package:acko_flutter/feature/ahc/view/pages/ahc_schedule_page/ahc_schedule_page.dart';
import 'package:acko_flutter/feature/ahc/view/pages/ahc_test_selection_page/ahc_test_selection_page.dart';
import 'package:acko_flutter/feature/api_logger/bloc/cubit.dart';
import 'package:acko_flutter/feature/app_home_page/model/services_response.dart';
import 'package:acko_flutter/feature/app_policy_page/utils/CommonDataStore.dart';
import 'package:acko_flutter/feature/auto_assets/bloc/auto_assets_controller_bloc.dart';
import 'package:acko_flutter/feature/auto_assets/bloc/create_asset_bloc.dart';
import 'package:acko_flutter/feature/auto_assets/bloc/single_asset_management_bloc.dart';
import 'package:acko_flutter/feature/auto_assets/model/detail_vehicle_asset_model.dart';
import 'package:acko_flutter/feature/auto_assets/view/create_asset/create_vehicle_asset.dart';
import 'package:acko_flutter/feature/auto_assets/view/manage_assets/manage_assets.dart';
import 'package:acko_flutter/feature/car_journey/bloc/car_landing_bloc.dart';
import 'package:acko_flutter/feature/car_journey/view/car_landing_page.dart';
import 'package:acko_flutter/feature/car_journey/view/plan_info_page.dart';
import 'package:acko_flutter/feature/car_journey/view/taxi_insurance_page.dart';
import 'package:acko_flutter/feature/central_webview/bloc/bloc.dart';
import 'package:acko_flutter/feature/challan/bloc/pending_challan_bloc.dart';
import 'package:acko_flutter/feature/challan/view/acko_service_lookup_screen.dart';
import 'package:acko_flutter/feature/challan/view/pending_challan_screen.dart';
import 'package:acko_flutter/feature/claim/advance_cash/advance_cash_info/bloc/advance_cash_details_bloc.dart';
import 'package:acko_flutter/feature/claim/advance_cash/advance_cash_info/view/advance_cash_details_page.dart';
import 'package:acko_flutter/feature/claim/advance_cash/onboarding/bloc/advance_cash_intro_bloc.dart';
import 'package:acko_flutter/feature/claim/advance_cash/onboarding/view/advance_cash_customer_education_page.dart';
import 'package:acko_flutter/feature/claim/advance_cash/onboarding/view/advance_cash_intro_page.dart';
import 'package:acko_flutter/feature/claim/claims_new_home/claims_member_wise_lists_page.dart';
import 'package:acko_flutter/feature/claim/my_claims_home/bloc/my_claims_bloc.dart';
import 'package:acko_flutter/feature/claim/my_claims_home/views/my_claim_home_page.dart';
import 'package:acko_flutter/feature/claim/payment_breakup/bloc/payment_breakup_bloc.dart';
import 'package:acko_flutter/feature/claim/payment_breakup/view/claim_payment_breakup_page.dart';
import 'package:acko_flutter/feature/claim/post_claim/common/bloc/claim_success_cubit.dart';
import 'package:acko_flutter/feature/claim/post_claim/common/view/claim_status_page.dart';
import 'package:acko_flutter/feature/claim/pre_claim/common/bloc/claim_intro_bloc.dart';
import 'package:acko_flutter/feature/claims_education/view/health_claims_option_selection_screen.dart';
import 'package:acko_flutter/feature/coin_history/coin_history_bloc.dart';
import 'package:acko_flutter/feature/coin_history/view/coin_history_page.dart';
import 'package:acko_flutter/feature/content/video/bloc/video_with_list/video_list_page_cubit.dart';
import 'package:acko_flutter/feature/content/video/views/video_list_page.dart';
import 'package:acko_flutter/feature/content/video/views/video_popup_view.dart';
import 'package:acko_flutter/feature/coverages_sdui/bloc/coverages_sdui_bloc.dart';
import 'package:acko_flutter/feature/coverages_sdui/view/coverages_sdui.dart';
import 'package:acko_flutter/feature/discover_sdui/bloc/discover_sdui_bloc.dart';
import 'package:acko_flutter/feature/discover_sdui/view/discover_sdui.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_constants.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_repo.dart';
import 'package:acko_flutter/feature/endorsement/health/health_renewal/analytics/renewal_property_store.dart';
import 'package:acko_flutter/feature/endorsement/health/health_renewal/cubit/renewal-edit-cubit/renewal_edit_cubit.dart';
import 'package:acko_flutter/feature/endorsement/health/health_renewal/cubit/renewal-home-cubit/renewal_home_cubit.dart';
import 'package:acko_flutter/feature/endorsement/health/health_renewal/cubit/renewal-review-cubit/renewal_review_cubit.dart';
import 'package:acko_flutter/feature/endorsement/health/health_renewal/view/pages/renewal-edit/renewal_edit_page.dart';
import 'package:acko_flutter/feature/endorsement/health/health_renewal/view/pages/renewal-edit/renewal_review_page.dart';
import 'package:acko_flutter/feature/endorsement/health/health_renewal/view/pages/renewal-home/health_renewal_home_page.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/bloc/checkout_bloc/ppe_checkout_bloc.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/bloc/edit_bloc/ppe_edit_bloc.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/bloc/overview_bloc/ppe_overview_bloc.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/bloc/review_bloc/ppe_review_bloc.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/bloc/tracking_bloc/ppe_tracking_bloc.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/view/screens/ppe_changes_submitted_screen.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/view/screens/ppe_checkout_screen.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/view/screens/ppe_edit_screen.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/view/screens/ppe_edit_summary_screen.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/view/screens/ppe_edit_tracking_screen.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/view/screens/ppe_mandate_cancellation_screen.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/view/screens/ppe_overview_screen.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/view/screens/ppe_review_changes_screen.dart';
import 'package:acko_flutter/feature/endorsement/life/domain/repository/life_jm_repository.dart';
import 'package:acko_flutter/feature/endorsement/life/life_renewal/analytics/life_renewal_property_store.dart';
import 'package:acko_flutter/feature/endorsement/life/life_renewal/cubit/life_renewal_cubit.dart';
import 'package:acko_flutter/feature/endorsement/life/life_renewal/view/pages/life-reactivate-page/life_reactivate_page.dart';
import 'package:acko_flutter/feature/endorsement/life/life_renewal/view/pages/life-renewal-page/life_renewal_page.dart';
import 'package:acko_flutter/feature/endorsement/shared/add_member/bloc/add_member_cubit.dart';
import 'package:acko_flutter/feature/endorsement/shared/add_member/view/add_member_screen.dart';
import 'package:acko_flutter/feature/fastag/bloc/fastag_decision_screen_cubit.dart';
import 'package:acko_flutter/feature/fastag/bloc/fastag_fetch_balance_cubit.dart';
import 'package:acko_flutter/feature/fastag/bloc/fastag_link_delink_management_cubit.dart';
import 'package:acko_flutter/feature/fastag/bloc/fastag_settlement_cubit.dart';
import 'package:acko_flutter/feature/fastag/enums/fastag_recharge_flow_enums.dart';
import 'package:acko_flutter/feature/fastag/view/pages/biller_recharge_receipt/fastag_recharge_receipt_page.dart';
import 'package:acko_flutter/feature/fastag/view/pages/billers_transaction_history/fastag_transaction_history_page.dart';
import 'package:acko_flutter/feature/fastag/view/pages/fastag_decision_page/fastag_decision_screen.dart';
import 'package:acko_flutter/feature/fastag/view/pages/fastag_link_delink_management_page/fastag_link_delink_management_page.dart';
import 'package:acko_flutter/feature/fastag/view/pages/fastag_view_details_page/fastag_view_details_page.dart';
import 'package:acko_flutter/feature/health_commons/health_navigator_page.dart';
import 'package:acko_flutter/feature/health_home/bloc/health_home_bloc.dart';
import 'package:acko_flutter/feature/health_home/views/health_home_page.dart';
import 'package:acko_flutter/feature/health_policy/bloc/health_policy_bloc.dart';
import 'package:acko_flutter/feature/health_policy/repository/health_policy_repository.dart';
import 'package:acko_flutter/feature/health_policy/view/edit_endorsement_page.dart';
import 'package:acko_flutter/feature/health_policy/view/health_policy_benefits.dart';
import 'package:acko_flutter/feature/hsrp/bloc/hsrp_detail_page_bloc.dart';
import 'package:acko_flutter/feature/hsrp/ui/hsrp_detail_page.dart';
import 'package:acko_flutter/feature/login/view/login_landing_page.dart';
import 'package:acko_flutter/feature/login/view/web_login_flow.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/cubit/booking_details/booking_details_cubit.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/domain/repo/booking_details_repository.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/view/screens/booking_details_screen.dart';
import 'package:acko_flutter/feature/medical_test_booking/common/utils/booking_constants.dart';
import 'package:acko_flutter/feature/medical_test_booking/schedule_booking/core/screens/ppmc_booking_screen.dart';
import 'package:acko_flutter/feature/medical_test_booking/schedule_booking/core/screens/test_booking_selection_screen.dart';
import 'package:acko_flutter/feature/medical_test_booking/schedule_booking/cubit/medical_tests_booking/medical_test_booking_cubit.dart';
import 'package:acko_flutter/feature/medical_test_booking/schedule_booking/cubit/medical_tests_selection/medical_test_selection_cubit.dart';
import 'package:acko_flutter/feature/nw_hospitals/cubit/nw_hospitals_cubit.dart';
import 'package:acko_flutter/feature/nw_hospitals/domain/repository/nw_hospitals_repository.dart';
import 'package:acko_flutter/feature/nw_hospitals/view/pages/nw_hospitals_page.dart';
import 'package:acko_flutter/feature/onboardingSDUI/initialPage/onboarding_inital_page_cubit.dart';
import 'package:acko_flutter/feature/onboardingSDUI/initialPage/onboarding_initial_page.dart';
import 'package:acko_flutter/feature/onboardingSDUI/onboardingCompletePages/app_ready_page.dart';
import 'package:acko_flutter/feature/onboardingSDUI/onboardingCompletePages/personalising_app_page.dart';
import 'package:acko_flutter/feature/onboardingSDUI/onboarding_profile_page/onboarding_profile_page.dart';
import 'package:acko_flutter/feature/onboardingSDUI/onboarding_welcome/onboarding_welcome_screen.dart';
import 'package:acko_flutter/feature/p2I/health/add_info/domain/repo/add_info_repo.dart';
import 'package:acko_flutter/feature/p2I/health/member_exclusion/domain/repo/member_exclusion_repo.dart';
import 'package:acko_flutter/feature/p2I/health/status_tracking/domain/repo/status_tracking_repo.dart';
import 'package:acko_flutter/feature/p2I/health/telemer/cubit/telemer_cubit.dart';
import 'package:acko_flutter/feature/p2I/health/telemer/domain/repo/telemer_repo.dart';
import 'package:acko_flutter/feature/payment/bloc/payment_page_bloc.dart';
import 'package:acko_flutter/feature/payment/pay_ext/acko_ext_payment_bloc.dart';
import 'package:acko_flutter/feature/payment/pay_ext/acko_pay_ext_constants.dart';
import 'package:acko_flutter/feature/payment/pay_ext/view/acko_pay_ext_page.dart';
import 'package:acko_flutter/feature/payment/payout/acko_payout_constants.dart';
import 'package:acko_flutter/feature/payment/payout/acko_payouts_bloc.dart';
import 'package:acko_flutter/feature/payment/payout/ui/acko_payout_page.dart';
import 'package:acko_flutter/feature/payment/view/payment_page.dart';
import 'package:acko_flutter/feature/policy_details/bloc/policy_details_bloc.dart';
import 'package:acko_flutter/feature/policy_success/view/policy_success_page.dart';
import 'package:acko_flutter/feature/profile/bloc/UserProfileBloc.dart';
import 'package:acko_flutter/feature/profile/bloc/add_wallet_bloc.dart';
import 'package:acko_flutter/feature/profile/bloc/edit_details_bloc.dart';
import 'package:acko_flutter/feature/profile/bloc/edit_personal_info_bloc.dart';
import 'package:acko_flutter/feature/profile/bloc/kyc_information_cubit.dart';
import 'package:acko_flutter/feature/profile/bloc/payout_preference_bloc.dart';
import 'package:acko_flutter/feature/profile/bloc/personal_detail_bloc.dart';
import 'package:acko_flutter/feature/profile/bloc/whatsapp_pref_bloc.dart';
import 'package:acko_flutter/feature/profile/view/ProfilePage.dart';
import 'package:acko_flutter/feature/profile/view/bank_details_screen.dart';
import 'package:acko_flutter/feature/profile/view/deactivate_account_page.dart';
import 'package:acko_flutter/feature/profile/view/deactivation_success_page.dart';
import 'package:acko_flutter/feature/profile/view/edit_detail_page.dart';
import 'package:acko_flutter/feature/profile/view/edit_personal_info_screen.dart';
import 'package:acko_flutter/feature/profile/view/payout_preference.dart';
import 'package:acko_flutter/feature/profile/view/personal_details_page.dart';
import 'package:acko_flutter/feature/profile/view/saved_cards_screen.dart';
import 'package:acko_flutter/feature/profile/view/wallet_page.dart';
import 'package:acko_flutter/feature/profile/view/whatsapp_pref_page.dart';
import 'package:acko_flutter/feature/profile_completion/bloc/bloc_singleton_instance.dart';
import 'package:acko_flutter/feature/profile_completion/model/profile_completion_model.dart';
import 'package:acko_flutter/feature/profile_completion/util/profile_completion_util.dart';
import 'package:acko_flutter/feature/profile_completion/view/profile_completion_main_page.dart';
import 'package:acko_flutter/feature/rapid_response/bloc/rapid_response_cubit.dart';
import 'package:acko_flutter/feature/rapid_response/bloc/rapid_response_tracking_cubit.dart';
import 'package:acko_flutter/feature/rapid_response/model/rapid_response_enums.dart';
import 'package:acko_flutter/feature/rapid_response/model/schedule_booking_params.dart';
import 'package:acko_flutter/feature/rapid_response/view/booking_history/booking_history_page.dart';
import 'package:acko_flutter/feature/rapid_response/view/home/<USER>';
import 'package:acko_flutter/feature/rapid_response/view/home/<USER>';
import 'package:acko_flutter/feature/rapid_response/view/ongoing_booking/ongoing_booking_tracking_page.dart';
import 'package:acko_flutter/feature/rapid_response/view/schedule_booking/list_family_page.dart';
import 'package:acko_flutter/feature/rapid_response/view/schedule_booking/location_picker_page.dart';
import 'package:acko_flutter/feature/rapid_response/view/schedule_booking/schedule_booking_review_page.dart';
import 'package:acko_flutter/feature/recurring_payment/ui/recurring_payment_overlays.dart';
import 'package:acko_flutter/feature/spalsh/bloc/SplashScreenBloc.dart';
import 'package:acko_flutter/feature/spalsh/view/SplashScreen.dart';
import 'package:acko_flutter/feature/tab_view_sdui/bloc/bloc.dart'
    as TabViewSdui;
import 'package:acko_flutter/feature/vas-sdui/bloc/vas_cubit.dart';
import 'package:acko_flutter/feature/challan_sdui/challan_journey.dart';
import 'package:acko_flutter/feature/challan_sdui/transactions/challan_transactions_listing_page.dart';
import 'package:acko_flutter/feature/challan_sdui/transactions/challan_transactions_listing_cubit.dart';
import 'package:acko_flutter/feature/vas-sdui/view/vas_landing.dart';
import 'package:acko_flutter/framework/pdp/health/bloc/documents_download_cubit.dart';
import 'package:acko_flutter/framework/pdp/health/view/screens/download_documents.dart';
import 'package:acko_flutter/framework/pdp/health/view/screens/support_page.dart';
import 'package:acko_flutter/life_insurance/benefits_and_coverage/bloc/life_cover_benefits_bloc.dart';
import 'package:acko_flutter/life_insurance/benefits_and_coverage/view/life_benefits_page.dart';
import 'package:acko_flutter/life_insurance/common/life_navigator_page.dart';
import 'package:acko_flutter/life_insurance/medical_evaluation/medical_evaluation_page.dart';
import 'package:acko_flutter/life_insurance/medical_evaluation/vmer/view/vmer_evaluation_page.dart';
import 'package:acko_flutter/life_insurance/medical_evaluation/vmer/view/vmer_secret_schedule_page.dart';
import 'package:acko_flutter/life_insurance/medical_evaluation/vmer/view/vmer_slot_schedule_success_page.dart';
import 'package:acko_flutter/life_insurance/medical_evaluation/vmer/view/vmer_timer_page.dart';
import 'package:acko_flutter/life_insurance/policy_updates/bloc/policy_updates_bloc.dart';
import 'package:acko_flutter/life_insurance/policy_updates/view/life_policy_updates_page.dart';
import 'package:acko_flutter/travel_insurance/claim/claim_state/view/page/claim_state_page.dart';
import 'package:acko_flutter/travel_insurance/claim/damages/view/page/claim_damages_page.dart';
import 'package:acko_flutter/travel_insurance/claim/expectation/bloc/claim_expectation_cubit.dart';
import 'package:acko_flutter/travel_insurance/claim/expectation/view/page/claim_expectation_page.dart';
import 'package:acko_flutter/travel_insurance/claim/policies/view/page/policy_list_page.dart';
import 'package:acko_flutter/travel_insurance/claim/tracking/view/page/claim_tracking_page.dart';
import 'package:acko_flutter/travel_insurance/flight_pass/add_flight/bloc/add_flight_bloc.dart';
import 'package:acko_flutter/travel_insurance/flight_pass/add_flight/view/page/add_flight_page.dart';
import 'package:acko_flutter/travel_insurance/flight_pass/add_flight/view/page/add_flight_result.dart';
import 'package:acko_flutter/travel_insurance/flight_pass/claim_expectation/bloc/flight_pass_claim_expectation_bloc.dart';
import 'package:acko_flutter/travel_insurance/flight_pass/claim_expectation/view/page/flight_pass_expectation_page.dart';
import 'package:acko_flutter/travel_insurance/flight_pass/coupons/bloc/coupons_bloc.dart';
import 'package:acko_flutter/travel_insurance/flight_pass/coupons/view/page/flight_pass_coupon_detail_page.dart';
import 'package:acko_flutter/travel_insurance/flight_pass/coupons/view/page/flight_pass_coupons_page.dart';
import 'package:acko_flutter/travel_insurance/flight_pass/coverage_details/bloc/coverage_bloc.dart';
import 'package:acko_flutter/travel_insurance/flight_pass/coverage_details/view/page/flight_pass_coverage_details_page.dart';
import 'package:acko_flutter/travel_insurance/flight_pass/create_claim/view/page/create_claim_page.dart';
import 'package:acko_flutter/travel_insurance/flight_pass/pdp/bloc/flight_pass_pdp_bloc.dart';
import 'package:acko_flutter/travel_insurance/flight_pass/pdp/view/page/flight_pass_pdp_page.dart';
import 'package:acko_flutter/travel_insurance/flight_pass/purchase_success/view/page/flight_pass_purchase_success_page.dart';
import 'package:acko_flutter/travel_insurance/flight_pass/user_flights/bloc/user_flights_bloc.dart';
import 'package:acko_flutter/travel_insurance/flight_pass/user_flights/view/page/user_flight_page.dart';
import 'package:acko_flutter/travel_insurance/flight_pass/your_claims/bloc/your_claims_bloc.dart';
import 'package:acko_flutter/travel_insurance/flight_pass/your_claims/view/page/flight_pass_claims_page.dart';
import 'package:acko_flutter/travel_insurance/my_trips/bloc/my_trip_cubit.dart';
import 'package:acko_flutter/travel_insurance/my_trips/view/page/my_trips_page.dart';
import 'package:acko_flutter/travel_insurance/pdp/cancel_policy/bloc/policy_cancel_cubit.dart';
import 'package:acko_flutter/travel_insurance/pdp/cancel_policy/view/page/policy_cancelled_page.dart';
import 'package:acko_flutter/travel_insurance/pdp/cancel_policy/view/page/request_cancellation_page.dart';
import 'package:acko_flutter/travel_insurance/pdp/cashless_hospitals/bloc/cashless_hospitals_bloc.dart';
import 'package:acko_flutter/travel_insurance/pdp/cashless_hospitals/bloc/cashless_hospitals_by_country_data_bloc.dart';
import 'package:acko_flutter/travel_insurance/pdp/cashless_hospitals/view/page/cashless_hospitals_page.dart';
import 'package:acko_flutter/travel_insurance/pdp/draft_policy/view/page/compliance_pending_page.dart';
import 'package:acko_flutter/travel_insurance/pdp/draft_policy/view/page/payment_pending_page.dart';
import 'package:acko_flutter/travel_insurance/pdp/draft_policy/view/page/policy_ready_page.dart';
import 'package:acko_flutter/travel_insurance/pdp/help/view/page/help_page.dart';
import 'package:acko_flutter/travel_insurance/pdp/home/<USER>/pdp_model.dart';
import 'package:acko_flutter/travel_insurance/pdp/home/<USER>/page/pdp_home_page.dart';
import 'package:acko_flutter/travel_insurance/pdp/nominee/view/page/edit_nominee_page.dart';
import 'package:acko_flutter/travel_insurance/pdp/nominee/view/page/view_nominee_page.dart';
import 'package:acko_flutter/travel_insurance/pdp/peril/view/page/peril_page.dart';
import 'package:acko_flutter/travel_insurance/pdp/plan_benefit/view/page/plan_benefit_page.dart';
import 'package:acko_flutter/travel_insurance/pdp/policy_detail/view/page/edit_policy_page.dart';
import 'package:acko_flutter/travel_insurance/pdp/story/view/page/story_page.dart';
import 'package:acko_flutter/travel_insurance/shared/view/page/logger/travel_logger_page.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_web_view_module/bloc/bloc.dart';
import 'package:acko_web_view_module/common/enums.dart';
import 'package:acko_web_view_module/view/acko_web_view_module.dart';
import 'package:analytics/analytics.dart';
import 'package:analytics/events/health_life/page_events/health_life_page_events.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:policy_details/policy_details.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/remote_config/remote_config.dart';
import 'package:utilities/utilities.dart';

import '../../feature/acko_services/ui/acko_service_loading_screen.dart';
import '../../feature/adaptive_sdui_page/bloc/adaptive_sdui_bloc.dart';
import '../../feature/adaptive_sdui_page/view/screen.dart';
import '../../feature/all_policies_page_sdui/bloc/all_policies_sdui_bloc.dart';
import '../../feature/all_policies_page_sdui/view/all_policies_sdui.dart';
import '../../feature/api_logger/api_logger.dart';
import '../../feature/asset_sdui/bloc/asset_overview_bloc.dart';
import '../../feature/asset_sdui/bloc/asset_sdui_bloc.dart';
import '../../feature/asset_sdui/view/asset_overview_page.dart';
import '../../feature/asset_sdui/view/asset_sdui.dart';
import '../../feature/assets_tab_view/ui/assets_tab_view.dart';
import '../../feature/auto_assets/bloc/document_viewer_bloc.dart';
import '../../feature/auto_assets/bloc/manage_subscriptions_bloc.dart';
import '../../feature/auto_assets/bloc/single_asset_management_events.dart';
import '../../feature/auto_assets/view/common/document_viewer.dart';
import '../../feature/auto_assets/view/manage_subscription/alert_subscription_page.dart';
import '../../feature/challan/bloc/acko_service_lookup_bloc.dart';
import '../../feature/challan/bloc/challan_payment_details_bloc.dart';
import '../../feature/challan/bloc/challan_payment_order_id_bloc.dart';
import '../../feature/challan/bloc/challan_transaction_history_bloc.dart';
import '../../feature/challan/view/challan_payment_breakup_screen.dart';
import '../../feature/challan/view/challan_payment_details.dart';
import '../../feature/challan/view/challan_transactions_history_page.dart';
import '../../feature/challan/view/payment_completion_screen.dart';
import '../../feature/challan_webview/challan_transaction_detail_bloc.dart';
import '../../feature/challan_webview/challan_transactions_detail_page.dart';
import '../../feature/challan_webview/challan_web_view_bloc.dart';
import '../../feature/challan_webview/challan_webview.dart';
import '../../feature/challan_webview/models/challan_automation_misc_models.dart';
import '../../feature/claim/claim_intro/view/claim_intro_page.dart';
import '../../feature/claim/payment_breakup/view/categorywise_deductions_page.dart';
import '../../feature/claim/payment_breakup/view/miscellanous_deduction_page.dart';
import '../../feature/claim/post_claim/common/view/claim_detail_page.dart';
import '../../feature/claim/pre_claim/common/bloc/file_claim_bloc.dart';
import '../../feature/claim/pre_claim/common/view/file_claim.dart';
import '../../feature/claim/pre_claim/fnol_treatment_selection/bloc/treatment_category_bloc.dart';
import '../../feature/claim/pre_claim/fnol_treatment_selection/views/fnol_treatment_categories_widget.dart';
import '../../feature/fastag/bloc/fastag_recharge_receipt_cubit.dart';
import '../../feature/fastag/bloc/pay_biller_cubit.dart';
import '../../feature/fastag/view/pages/biller_enter_amount_page/fastag_recharge_enter_amount_page.dart';
import '../../feature/fastag/view/pages/biller_payment_status/fastag_payment_status.dart';
import '../../feature/fastag/view/pages/fastag_recharge_limits_info_page/fastag_recharge_limits_info_page.dart';
import '../../feature/fnol/bloc/claims_bloc.dart';
import '../../feature/fnol/view/claims_screen.dart';
import '../../feature/health_home/views/arogya_sanjeevni_page.dart';
import '../../feature/login/bloc/web_login_bloc.dart';
import '../../feature/login/gmc_merging_and_handover/bloc/gmc_merging_bloc.dart';
import '../../feature/login/gmc_merging_and_handover/view/gmc_account_connecting_progress.dart';
import '../../feature/login/gmc_merging_and_handover/view/gmc_handover_page.dart';
import '../../feature/my_account/bloc/internet_policies_by_group_bloc.dart';
import '../../feature/my_account/view/internet_policy_list_page.dart';
import '../../feature/onboardingSDUI/inputPage/onboarding_input_page.dart';
import '../../feature/onboardingSDUI/inputPage/onboarding_input_page_cubit.dart';
import '../../feature/onboardingSDUI/onboardingCompletePages/app_ready_page_cubit.dart';
import '../../feature/onboardingSDUI/onboarding_model.dart';
import '../../feature/p2I/health/add_info/cubit/add_info_cubit.dart';
import '../../feature/p2I/health/add_info/view/screens/medical_documents_screen.dart';
import '../../feature/p2I/health/member_exclusion/cubit/member_exclusion_cubit.dart';
import '../../feature/p2I/health/member_exclusion/view/screens/member_exclusion_screen.dart';
import '../../feature/p2I/health/status_tracking/cubit/status_tracking_cubit.dart';
import '../../feature/p2I/health/status_tracking/view/screens/status_tracking_screen.dart';
import '../../feature/p2I/health/telemer/view/screens/telemer_screen.dart';
import '../../feature/payment/payout/models/payout_models.dart';
import '../../feature/pdp_sdui/bloc/policy_bloc.dart';
import '../../feature/pdp_sdui/policy_screen.dart';
import '../../feature/policy_coverage_sdui/bloc/policy_coverage_bloc.dart';
import '../../feature/policy_coverage_sdui/policy_coverage.dart';
import '../../feature/profile/bloc/saved_card_bloc.dart';
import '../../feature/profile/view/kyc_information.dart';
import '../../feature/profile/view/self_family_members_list_page.dart';
import '../../feature/profile_completion/bloc/address_bloc.dart';
import '../../feature/profile_completion/bloc/family_members_bloc.dart';
import '../../feature/profile_completion/bloc/health_coverage_cubit.dart';
import '../../feature/profile_completion/bloc/profile_completion_bloc.dart';
import '../../feature/profile_completion/view/address/address_detailed_view.dart';
import '../../feature/profile_completion/view/address/profile_address.dart';
import '../../feature/profile_completion/view/members/family_members_detailed_view.dart';
import '../../feature/profile_completion/view/members/profile_members.dart';
import '../../feature/puc_validity/puc_validity.dart';
import '../../feature/support_sdui/bloc/support_sdui_bloc.dart';
import '../../feature/support_sdui/view/support_sdui.dart';
import '../../feature/tab_view_sdui/view/home_tab_view.dart';
import '../../feature/challan_sdui/cubit/challan_cubit.dart';
import '../../framework/pdp/health/bloc/health_cards_cubit.dart';
import '../../framework/pdp/health/bloc/policy_view_cubit.dart';
import '../../framework/pdp/health/models/health_policies_basic_details.dart';
import '../../framework/pdp/health/models/ui_models/policy_card_model.dart';
import '../../framework/pdp/health/view/screens/health_cards_page.dart';
import '../../framework/pdp/health/view/screens/health_coverages_page.dart';
import '../../framework/pdp/health/view/screens/health_policies_sheet.dart';
import '../../framework/pdp/health/view/screens/policy_view.dart';
import '../../life_insurance/medical_evaluation/vmer/bloc/vmer_slot_schedule_bloc.dart';
import '../../life_insurance/medical_evaluation/vmer/view/vmer_slot_selection_page.dart';
import '../../travel_insurance/claim/claim_state/bloc/claim_state_bloc.dart';
import '../../travel_insurance/claim/damages/bloc/claim_damage_cubit.dart';
import '../../travel_insurance/claim/policies/bloc/claim_policies_cubit.dart';
import '../../travel_insurance/claim/tracking/bloc/claim_tracking_bloc.dart';
import '../../travel_insurance/pdp/draft_policy/bloc/draft_policy_cubit.dart';
import '../../travel_insurance/pdp/fnol/view/page/fnol_page.dart';
import '../../travel_insurance/pdp/help/bloc/help_cubit.dart';
import '../../travel_insurance/pdp/home/<USER>/pdp_home_bloc_singleton.dart';
import '../../travel_insurance/pdp/plan_benefit/bloc/plan_benefit_bloc.dart';
import '../../travel_insurance/pdp/policy_detail/bloc/policy_detail_bloc.dart';
import '../../travel_insurance/pdp/policy_detail/view/page/payment_verify_page.dart';
import '../../travel_insurance/pdp/policy_detail/view/page/policy_detail_page.dart';
import '../util/app_update_page.dart';

CommonDataStoreBloc? globalCommonDataStoreBloc;

enum RouteAnimationType {
  default_transition(name: 'default_transition'),
  custom_slide(name: 'custom_slide'),
  fade_anim(name: 'fade_anim'),
  transparent(name: 'transparent');

  final String name;

  const RouteAnimationType({required this.name});

  static RouteAnimationType fromJson(String type) {
    return switch (type) {
      'custom_slide' => custom_slide,
      'fade_anim' => fade_anim,
      'transparent' => transparent,
      _ => default_transition
    };
  }
}

class AppRouter {
  Route route(RouteSettings routeSettings) {
    final animationType = _getRouteAnimationType(routeSettings: routeSettings);

    return _getWrapperRouter(
      routeSettings,
      builder: (context) {
        return Scaffold(
          resizeToAvoidBottomInset: false,
          backgroundColor: animationType == RouteAnimationType.transparent
              ? Colors.white.withOpacity(0)
              : null,
          body: BlocConsumer<ApplicationBloc, ApplicationSate>(
            listenWhen: (previous, current) {
              return current is DeepLinkReceivedState;
            },
            listener: (context, state) {
              debugPrint("deepLink listener");
              if (state is DeepLinkReceivedState) {
                debugPrint("deeplink received");

                if (Util.isPageLinkRequired && state.params.isNotEmpty) {
                  if (state.params["route"]
                      .toString()
                      .equalIgnoreCase("web_page")) {
                    state.params["is_permission_required"] = false;
                    state.params["source"] = "deep_link";
                    if (state.params['url'].contains('central-kyc')) {
                      state.params['hide_app_bar'] = true;
                    }
                  }

                  if (state.params["route"] == Routes.APP_HOME) {
                    int tabIndex = 0;
                    if (state.params.containsKey("tab")) {
                      tabIndex = int.tryParse(state.params["tab"]) ?? 0;
                      StateProvider().notify(
                        ObserverState.CHANGE_TAB,
                        data: {"index": tabIndex},
                      );
                    }

                    if (state.params.containsKey("home_tab")) {
                      String newTab = state.params["home_tab"];
                      int index = _getTabIndex(newTab);

                      StateProvider().notify(
                        ObserverState.CHANGE_SDUI_TAB,
                        data: {
                          "index": index,
                          "tab_id": "insurance_tabs",
                          "from": "application_bloc"
                        },
                      );
                    }
                  } else {
                    _checkForExistingRouteAndNavigate(context, state.params);
                  }
                  Util.isPageLinkRequired = false;
                }
              }
            },
            buildWhen: (previous, current) {
              return (current is! DeepLinkReceivedState);
            },
            builder: (context, state) {
              return GetNavigationWidget(
                  context: context,
                  route: routeSettings.name ?? '',
                  params: routeSettings.arguments);
            },
          ),
        );
      },
    );
  }

  _getTabIndex(String key) {
    switch (key.toLowerCase().trim()) {
      case "car":
        return 0;
      case "bike":
        return 1;
      case "health":
        return 2;
      case "life":
        return 3;
      case "travel":
        return 4;
    }
    return 0;
  }

  void _checkForExistingRouteAndNavigate(BuildContext context, Map params) {
    String newRoute = params["route"] ?? Routes.APP_HOME;
    if (doesRouteNeedsReplacement(newRoute)) {
      Navigator.pushNamedAndRemoveUntil(
          context, newRoute, (route) => (route.isFirst),
          arguments: params);
    } else {
      Navigator.pushNamed(context, newRoute, arguments: params);
    }
  }

  bool doesRouteNeedsReplacement(String newRoute) =>
      (newRoute == Routes.CMS_VIDEO_PAGE);

  RouteAnimationType _getRouteAnimationType({
    required RouteSettings routeSettings,
  }) {
    Map<String, dynamic>? arguments =
        routeSettings.arguments as Map<String, dynamic>?;
    RouteAnimationType routeAnimationType =
        RouteAnimationType.default_transition;
    if (arguments?.containsKey("route_animation_type") ?? false) {
      if (arguments?['route_animation_type'] is String) {
        routeAnimationType =
            RouteAnimationType.fromJson(arguments?['route_animation_type']);
      } else {
        routeAnimationType =
            arguments?['route_animation_type'] as RouteAnimationType;
      }
    }
    return routeAnimationType;
  }

  _getWrapperRouter(RouteSettings routeSettings, {builder}) {
    RouteAnimationType routeAnimationType = _getRouteAnimationType(
      routeSettings: routeSettings,
    );

    switch (routeAnimationType) {
      case RouteAnimationType.transparent:
        return TransparentRoute(
          builder: builder,
          settings: routeSettings,
        );
      default:
        return CupertinoPageRoute(
          builder: builder,
          settings: routeSettings,
        );
    }
  }

  static Future<PaymentResponse?> loadPaymentPage(
      BuildContext context, PaymentConfig paymentConfig,
      {PaymentScreen page = PaymentScreen.SCAN_QR_CODE}) async {
    PaymentResponse? response = await Navigator.of(context).pushNamed(
            Routes.ACKO_PAY_EXT,
            arguments: {"config": paymentConfig, "page_to_load": page})
        as PaymentResponse?;
    return Future.value(response);
  }

  static Future<PayoutResponse?> loadPayoutPage(
      BuildContext context, PayoutConfig payoutConfig,
      {PayoutPage page = PayoutPage.ACCOUNT_LIST}) async {
    PayoutResponse? response = await Navigator.of(context).pushNamed(
            Routes.ACKO_PAYOUT,
            arguments: {"config": payoutConfig, "page_to_load": page})
        as PayoutResponse?;
    return Future.value(response);
  }
}

// ignore: must_be_immutable
class GetNavigationWidget extends StatelessWidget {
  GetNavigationWidget(
      {Key? key, required this.context, this.route, this.params})
      : super(key: key);

  final BuildContext context;
  final String? route;
  dynamic params;

  @override
  Widget build(BuildContext context) {
    if (params == null) params = {};
    StateProvider _stateProvider = StateProvider();
    _triggerScreenName(route, params);
    switch (route) {
      case Routes.APPLICATION:
        return BlocProvider<SplashScreenBloc>(
            key: UniqueKey(),
            create: (context) => SplashScreenBloc(),
            child: SplashScreen());
      case Routes.CLAIMS:
        {
          String? policyNumber;
          if (params['policyNumber'] != null) {
            policyNumber = params['policyNumber'];
          }
          return BlocProvider<ClaimsBloc>(
            create: (context) => ClaimsBloc(policyNumber ?? ''),
            child: ClaimsScreen(policyNumber),
          );
        }
      case Routes.API_LOGGER:
        return BlocProvider<ApiLoggerCubit>(
            create: (context) => ApiLoggerCubit(params['logger']),
            child: ApiLoggerUI());
      case Routes.WEB_PAGE:
      case Routes.WEB_PAGE_V2:
        Util.initializeWebViewSingletonClasses();
        return BlocProvider<AckoWebViewBloc>(
          create: (context) =>
              AckoWebViewBloc({WebViewLOBs.CENTRAL: AckoWebViewCentralBloc()}),
          child: AckoWebView(
            url: params['url'],
          ),
        );
      case Routes.CHALLAN_WEB_PAGE:
        final ChallanAutomationDetailArgs args = (params != null &&
                params['challanTransactionArgs'] != null)
            ? params['challanTransactionArgs'] as ChallanAutomationDetailArgs
            : ChallanAutomationDetailArgs.fromJson(params);

        return MultiBlocProvider(
            providers: [
              BlocProvider<ChallanWebViewBloc>(
                  create: (context) => ChallanWebViewBloc(
                        searchSource: args.violationLocation!,
                        violationNumbers: args.noticeNumbers!,
                        registrationNumber: args.registrationNumber!,
                        phoneNumber: args.phoneNumber!,
                      )),
              BlocProvider<AckoWebViewBloc>(
                  create: (context) => AckoWebViewBloc(
                      {WebViewLOBs.CENTRAL: AckoWebViewCentralBloc()}))
            ],
            child: ChallanWebView(
              sentUrl: params['url'],
            ));
      case Routes.SDUI_ASSET_OVERVIEW_PAGE:
        return BlocProvider(
          create: (context) => SduiAssetOverviewBloc(params),
          child: SduiAssetOverviewPage(),
        );
      case Routes.ASSET_TAB_VIEW:
      final bool showBackButton = params != null && params!.containsKey('showBackButton')
          ? params['showBackButton'].toString().equalsIgnoreCase('true')
          : false;
      final int defaultTab = params != null && params!.containsKey('defaultTab')
          ? int.parse(params['defaultTab'].toString())
          : 0;
        return AssetsTabView(defaultTab: defaultTab, showBackButton: showBackButton);
      case Routes.SINGLE_ASSET_VIEW:
      case Routes.SDUI_ASSET_DETAIL_PAGE:
        return FutureBuilder(future: RemoteConfigInstance.instance.getGbAsyncData(RemoteConfigKeysSet.APP_IA_VERSION), builder: (context, snapshot){
          if(!snapshot.hasData) return AckoServiceLoadingScreen();
          bool isV10 = (snapshot.data).toString().containsIgnoreCase("v10");
          if(isV10){
            return AssetsTabView(defaultTab: 0, showBackButton: true);
          }

          return MultiBlocProvider(providers: [
            BlocProvider<AssetSDUIBloc>(
              create: (context) =>
                  AssetSDUIBloc(params["assetId"], params["regNumber"]),
            ),
            BlocProvider<CommonDataStoreBloc>.value(
              value: globalCommonDataStoreBloc!,
            )
          ], child: AssetSduiView());
        });

      case Routes.ALL_POLICIES:
        return MultiBlocProvider(providers: [
          BlocProvider<AllPoliciesSDUIBloc>(
            create: (context) => AllPoliciesSDUIBloc(params['url']),
          ),
          BlocProvider<CommonDataStoreBloc>.value(
            value: globalCommonDataStoreBloc!,
          )
        ], child: AllPoliciesSduiScreen());
      case Routes.PROFILE:
        return BlocProvider(
          create: (context) => UserProfileBloc(),
          child: ProfilePage(
            updateProfileDataCallback: params['updateProfileDataCallback'],
            params: params,
          ),
        );
      case Routes.APP_HOME:
        if (globalCommonDataStoreBloc == null) {
          globalCommonDataStoreBloc = CommonDataStoreBloc();
        }
        return getAppRefreshHomeView();
      case Routes.COVERAGES_PAGE_SDUI:
        String endpoint = (params != null && params['endpoint'] != null)
            ? params['endpoint']
            : '';
        return MultiBlocProvider(
          providers: [
            BlocProvider<CoveragesSDUIBloc>(
              create: (context) => CoveragesSDUIBloc(endpoint, true),
            ),
            BlocProvider<CommonDataStoreBloc>.value(
              value: globalCommonDataStoreBloc!,
            )
          ],
          child: CoverageSduiScreen(),
        );
      case Routes.DISCOVER:
        {
          if (!(params != null && params.containsKey('uri'))) {
            AckoToast.show('Url not found');
            return CommonScaffold.setScaffold(const SizedBox.shrink());
          }

          return MultiBlocProvider(providers: [
            BlocProvider<DiscoverSDUIBloc>(
              create: (context) => DiscoverSDUIBloc(params['uri']),
            ),
            BlocProvider<CommonDataStoreBloc>.value(
              value: globalCommonDataStoreBloc!,
            )
          ], child: DiscoverSduiScreen());
        }
      case Routes.DEACTIVATE_ACCOUNT:
        return DeactivateAccountPage();
      case Routes.DEACTIVATION_SUCCESS:
        return DeactivationSuccessPage();
      case Routes.ACCOUNT_RECOVERY:
        return BlocProvider<AccountRecoveryBloc>(
          create: (context) => AccountRecoveryBloc(),
          child: AccountRecoveryPage(params['nextUrl']),
        );
      case Routes.RECOVERY_VERIFY:
        return BlocProvider<AutoRecoveryOtpVerificationBloc>(
            create: (context) => AutoRecoveryOtpVerificationBloc(),
            child: RecoveryVerifyScreen(params['accountRecoveryResponse'],
                params['nextUrl'], params['email']));
      case Routes.PROFILE_FAMILY_MEMBER_DETAILED_VIEW:
        ProfileFamilyMembers? memberDetails = params['member_details'] != null
            ? (params['member_details'] as ProfileFamilyMembers)
            : null;
        bool isSelf = memberDetails?.relation == 'Self';
        if (isSelf) {
          EditDetails userDetails = EditDetails(
              name: memberDetails?.name,
              phoneNumber: memberDetails?.phone,
              dateOfBirth: memberDetails?.dob,
              gender: memberDetails?.gender);
          return BlocProvider(
              create: (context) =>
                  EditPersonalInfoBloc(userDetails: userDetails),
              child: EditPersonalInfoScreen(
                showDeactivateAccount: false,
              ));
        } else {
          FamilyMembersConfig? membersConfig = params['member_config'];
          return BlocProvider<ProfileFamilyMembersBloc>(
            create: (context) => ProfileFamilyMembersBloc(),
            child: FamilyMembersDetailedView(
                memberDetails: params['member_details'],
                familyMembersConfig: membersConfig),
          );
        }
      case Routes.PROFILE_COMPLETION:
        dynamic arguments = params;
        return BlocProvider<ProfileCompletionBloc>.value(
          value: ProfileCompeltionBlocSingletonInstance.instance.blocInstance,
          child: ProfileCompletionMainPage(arguments: params),
        );
      case Routes.PROFILE_ADDRESS:
        return BlocProvider<ProfileAddressBloc>(
          create: (context) => ProfileAddressBloc(),
          child: ManageProfileAddress(
            popResultBack: params['pop_result_back'] ?? false,
            manageAddressPageHeader: params['manage_address_page_header'],
            addAddressPageHeader: params['add_address_page_header'],
            deeplinkUrl: params['deeplink_url'],
            selectAddressNavigateRouteCtaText:
                params['select_address_navigate_route_cta_text'] ??
                    txt_continue,
          ),
        );

      case Routes.PROFILE_ADDRESS_DETAILED_VIEW:
        return BlocProvider<ProfileAddressBloc>.value(
          value: params['bloc'],
          child: AddressDetailedView(
            address: params['address'],
          ),
        );
      case Routes.MANAGE_PROFILE_MEMBERS:
        return MultiBlocProvider(
          providers: [
            BlocProvider<ProfileCompletionBloc>.value(
              value: ProfileCompeltionBlocSingletonInstance.instance.blocInstance,
            ),
            BlocProvider<HealthCoverageCubit>(
              create: (_) => HealthCoverageCubit(),
            ),
          ],
          child: ManageProfileMembers(params: params),
        );

      case Routes.HEALTH_PDP_COVERAGES_SCREEN:
        String? policyId = params['policyId'];
        String? policyNumber = params['policy_number'];
        String? proposalId = params['proposalId'];
        String? policyName = params['policyName'];
        String? policyStatus = params['policyStatus'];
        String? policyStartDate = params['policyStartDate'];
        List<CardCoverModel> coveragesModel = params['policyKeyCovers'];
        bool isExpired = params['isExpired'] ?? false;
        WaitingInfoModel? waitingInfo = params['waitingInfo'];
        WaitingInfoModel? noWaitingInfo = params['noWaitingInfo'];
        Map<String, String>? policyInfoMap = params['policyInfoMap'];
        String upcomingStatus = params['upcomingStatus'] ?? "";
        HealthPolicyHolder? person = params['selectedPolicyHolder'];
        String productInfo = params['productInfo'];

        return CoveragesPage(
            policyId: policyId,
            policyNumber: policyNumber,
            proposalId: proposalId,
            policyName: policyName,
            policyStartDate: policyStartDate,
            policyStatus: policyStatus,
            coveragesModel: coveragesModel,
            isExpired: isExpired,
            waitingInfo: waitingInfo,
            noWaitingInfo: noWaitingInfo,
            policyInfoMap: policyInfoMap,
            upcomingStatus: upcomingStatus,
            person: person,
            productInfo: productInfo);

      case Routes.POLICY_COVERAGE_SCREEN:
        String regNumber = (params != null && params['regNumber'] != null)
            ? params['regNumber']
            : '';
        return MultiBlocProvider(providers: [
          BlocProvider<PolicyCoverageBloc>(
            create: (context) => PolicyCoverageBloc(regNumber),
          ),
          BlocProvider<CommonDataStoreBloc>.value(
            value: globalCommonDataStoreBloc!,
          )
        ], child: PolicyCoverage());
      case Routes.RECOVERY_OTP:
        String userEmailOrMobileEntered =
            (params != null && params['enteredText'] != null)
                ? params['enteredText']
                : '';
        return BlocProvider<AutoRecoveryOtpVerificationBloc>.value(
            value: params['bloc'],
            child: RecoveryOTPPage(userEmailOrMobileEntered));
      case Routes.LOGIN_PAGE:
        return BlocProvider<WebLoginBloc>(
          create: (context) => WebLoginBloc(
            email: params['email'],
            isUserLoggedOut: params['is_user_logged_out'],
          ),
          child: WebLoginFlow(
            nextUrl: params["next"],
            phoneNumber: params["phone_number"],
          ),
        );
      case Routes.ONBOARDING_APP_READY_PAGE:
        OnBoardingUserInfo? onBoardingUserInfo = params['onBoardingUserInfo'];
        return BlocProvider<AppReadyPageCubit>(
            create: (context) =>
                AppReadyPageCubit(onBoardingUserInfo: onBoardingUserInfo),
            child: AppReadyPage());
      case Routes.ONBOARDING_PERSONALISING_PAGE:
        OnBoardingUserInfo? onBoardingUserInfo = params['onBoardingUserInfo'];
        return PersonalisingAppPage(onBoardingUserInfo: onBoardingUserInfo);
      case Routes.ONBOARDING_INPUT_PAGE:
        OnboardingContentModel onboardingContent =
            params['onboardingContentModel'];
        OnBoardingUserInfo? onBoardingUserInfo = params['onBoardingUserInfo'];
        return BlocProvider<OnboardingInputPageCubit>(
          create: (context) =>
              OnboardingInputPageCubit(onBoardingUserInfo: onBoardingUserInfo),
          child: OnBoardingInputPage(onboardingContent: onboardingContent),
        );

      case Routes.ONBOARDING_PROFILE_PAGE:
        final Pages page = params['profilePage'];
        final String? firstName = params['firstName'];
        final String? lastName = params['lastName'];
        return OnboardingProfilePage(
          page: page,
          firstName: firstName,
          lastName: lastName,
        );

      case Routes.ONBOARDING_WELCOME_PAGE:
        final Pages page = params['welcomePage'];
        final String? firstName = params['firstName'];
        final String? lastName = params['lastName'];
        final OnboardingInitialPageCubit onboardingCubit = params['cubit'];
        return OnboardingWelcomeScreen(
          page: page,
          cubit: onboardingCubit,
          firstName: firstName,
          lastName: lastName,
        );

      case Routes.ONBOARDING_INITIAL_PAGE:
        OnBoardingUserInfo? onBoardingUserInfo = params['onboardingUserInfo'];
        return BlocProvider<OnboardingInitialPageCubit>(
          create: (context) => OnboardingInitialPageCubit(onBoardingUserInfo),
          child: OnboardingInitialPage(),
        );

      case Routes.LOGIN_LANDING_PAGE:
        return LoginLandingPage(
            isUserLoggedOut: params['is_user_logged_out'],
            nextUrl: params["next_url"],
            isPhoneNumberEditable: params["is_phone_number_editable"] ?? true,
            phoneNumber: params.containsKey("phone_number")
                ? params["phone_number"]
                : null);
      case Routes.MERGE_ACCOUNTS_LOADER_PAGE:
        return BlocProvider<GmcMergingHandoverCubit>(
            create: (context) => GmcMergingHandoverCubit(
                email: params['email'],
                isUserLoggedOut: params['is_user_logged_out'],
                phoneNumber: params.containsKey("phone_number")
                    ? params["phone_number"]
                    : null),
            child: GmcAccountConnectingLoader(
              fromPage: params["from_page"],
            ));
      case Routes.GMC_LOGIN_HANDOVER_PAGE:
        return BlocProvider<GmcMergingHandoverCubit>(
            create: (context) => GmcMergingHandoverCubit(
                isUserLoggedOut: params['is_user_logged_out'],
                phoneNumber: params.containsKey("phone_number")
                    ? params["phone_number"]
                    : null,
                fromPage: params["from_page"] ?? null),
            child: GmcHandoverPage(params["next_url"]));
      case Routes.CHALLAN_PAYMENT_DETAILS:
        return BlocProvider<ChallanPaymentDetailsCubit>(
            create: (context) =>
                ChallanPaymentDetailsCubit(params['challan_id'] ?? ""),
            child: ChallanPaymentDetailsWidget());
      case Routes.YOU_AND_YOUR_FAMILY:
        return BlocProvider<ProfileCompletionBloc>.value(
          value: ProfileCompeltionBlocSingletonInstance.instance.blocInstance,
          child: SelfAndFamilyMembersListPage(),
        );
      case Routes.CHALLAN_TRANSACTION_HISTORY:
        return BlocProvider<ChallanTransactionHistoryCubit>(
            create: (context) => ChallanTransactionHistoryCubit(),
            child: ChallanTransactionHistoryPage());
      case Routes.CHALLAN_PROCEED_TO_PAY_SCREEN:
        return BlocProvider<ChallanPaymentOrderIdCubit>(
          create: (context) => ChallanPaymentOrderIdCubit(
              amountDetails: params['amount_details'],
              challanNumber: params['challan_number'],
              violationDetails: params['violation_details'],
              registrationNumber: params['registration_number']),
          child: ChallanPaymentBreakupScreen(),
        );
      case Routes.CHALLAN_PAYMENT_COMPLETION_SCREEN:
        String challanID = params['challan_id'] ?? "";
        String previousScreenName = params['previous_screen_name'] ?? "";
        return PaymentCompletionScreen(
            challanID: challanID, previousScreenName: previousScreenName);
      case Routes.POLICY_DETAILS_PAGE:
        return MultiBlocProvider(providers: [
          BlocProvider<PolicyBloc>(
            create: (context) => PolicyBloc(params),
          ),
          BlocProvider<CommonDataStoreBloc>.value(
            value: globalCommonDataStoreBloc!,
          )
        ], child: SduiPolicyScreen());
      case Routes.POLICY_SUCCESS:
        return BlocProvider(
            create: (context) => PolicyDetailsBloc(
                params["policy_id"], true, params['policy_number']),
            child: PolicySuccessPage());
      case Routes.SUPPORT:
        String url = '/pages/support?showAppBar=true';
        if (params != null && params['url'] != null) {
          url = params['url'];
        }

        return MultiBlocProvider(providers: [
          BlocProvider<SupportSDUIBloc>(
            create: (context) => SupportSDUIBloc(url),
          ),
          BlocProvider<CommonDataStoreBloc>.value(
            value: globalCommonDataStoreBloc!,
          )
        ], child: SupportSduiScreen());
      case Routes.PERSONAL_DETAILS:
        return BlocProvider(
            create: (context) =>
                PersonalDetailsBloc(userDetails: params['user_details']),
            child: PersonalDetailPage(
              showDeactivateAccount: params['show_deactivate_account'] ?? true,
            ));
      case Routes.EDIT_DETAIL_PAGE:
        return BlocProvider(
            create: (context) => EditDetailsBloc(
                editableField: params['editable_field'],
                details: params['user_details']),
            child: EditDetailPage());
      case Routes.EDIT_PERSONAL_DETAILS:
        return BlocProvider(
            create: (context) =>
                EditPersonalInfoBloc(userDetails: params['user_details']),
            child: EditPersonalInfoScreen(
              showDeactivateAccount: params['show_deactivate_account'] ?? true,
            ));
      case Routes.WHATSAPP_OPT_IN:
        return BlocProvider(
            create: (context) => WhatsappPreferenceBloc(),
            child: WhatsappPreference(
                params['phone'], params['is_whatsapp_true']));
      case Routes.LIFE_CANCEL_POLICY_PAGE:
        return BlocProvider<LifeCancelPolicyCubit>(
            create: (context) => LifeCancelPolicyCubit(
                data: params['data'], policyNumber: params['policy_number']),
            child: LifePolicyCancelPage());
      case Routes.HEALTH_CLAIMS_LIST_PAGE:
        String? fromPage =
            params.containsKey('from_page') ? params['from_page'] : null;
        HealthAssetDetailsResponseInsured? selectedAsset =
            params['selectedAsset'];
        if (fromPage != null &&
            fromPage.equalsIgnoreCase('Asset_View') &&
            selectedAsset != null) {
          return BlocProvider<MyClaimsBloc>(
              create: (context) => MyClaimsBloc(fromPage: fromPage),
              child: MemberWiseClaimsListPage(selectedAsset: selectedAsset));
        } else {
          return BlocProvider<MyClaimsBloc>(
              create: (context) => MyClaimsBloc(fromPage: fromPage),
              child: MyClaimHomePage());
        }
      case Routes.NETWORK_HOSPITALS_LIST:
        return BlocProvider<NwHospitalsCubit>(
          create: (context) => NwHospitalsCubit(
            NwHospitalsRepository(),
            AckoServiceRepository(),
            HealthHeaderApiRepo.instance,
            params['plan_name'],
          ),
          child: NwHospitalsPage(
            latlng: params['place_latlong'],
            fromPage: params['from_screen'],
          ),
        );

      case Routes.PPE_EDIT_OVERVIEW_SCREEN:
        {
          return BlocProvider(
            child: PrePolicyEditsOverviewScreen(),
            create: (context) =>
                PPEOverviewCubit(proposalId: params['proposal_id']),
          );
        }

      case Routes.PPE_EDIT_SCREEN:
        {
          return BlocProvider(
            create: (context) => PPEEditCubit(
                editType: params['edit_type'] ?? EditType.NONE,
                proposalId: params['proposal_id'],
                prePolicyEditingResponse: params['data'],
                scrollToBottom: params['scroll_to_bottom'] ?? false),
            child: PrePolicyEditsScreen(),
          );
        }

      case Routes.PPE_EDIT_REVIEW_SCREEN:
        {
          return BlocProvider(
            create: (context) => PPEReviewCubit(
                proposalId: params['proposal_id'],
                prePolicyEditingResponse: params['data'],
                mandateCreationFlow: params['create_mandate'] ?? false),
            child: ReviewChangesScreen(),
          );
        }

      case Routes.PPE_CHECKOUT_SCREEN:
        {
          return BlocProvider(
            create: (context) => PPECheckoutCubit(
                proposalId: params['proposal_id'],
                prePolicyEditingResponse: params['data']),
            child: PPECheckoutScreen(),
          );
        }

      case Routes.PPE_PAYMENT_SUCCESS_SCREEN:
        {
          return PPEChangesSubmittedScreen(
              proposalId: params['proposal_id'],
              prePolicyEditingResponse: params['data'],
              nextNode: params['next_node'],
              isBasba: params['basba_proposal'],
              isMandateEnabled: params['is_mandate_enabled'],
          );
        }

      case Routes.PPE_ADD_MEMBER_SCREEN:
        {
          return BlocProvider(
            create: (context) => AddMemberCubit(
                proposalId: params['proposal_id'],
                journeyType: params['journey_type'],
                healthJMResponse: params['data'],
                policyNumber: params['policy_number']),
            child: AddNewMemberScreen(
              isRenewalFlow: params['journey_type'] == JourneyType.RENEWAL,
              isAspPolicy: params['is_asp_policy'] ?? false,
            ),
          );
        }

      case Routes.PPE_EDIT_SUMMARY_SCREEN:
        {
          return BlocProvider(
            create: (context) => PPETrackingCubit(
                ppeTrackingState: PPETrackingStates.CHANGES_SUBMITTED,
                proposalId: params['proposal_id'],
                prePolicyEditingResponse: params['data']),
            child: PolicyUpdateSubmittedScreen(),
          );
        }

      case Routes.PPE_EDIT_TRACKING_SCREEN:
        {
          return BlocProvider(
            create: (context) => PPETrackingCubit(
                ppeTrackingState: PPETrackingStates.CHANGES_TRACKING,
                proposalId: params['proposal_id'],
                prePolicyEditingResponse: params['data'],
                refreshHome: params['refresh_home']),
            child: PolicyUpdateTrackingScreen(),
          );
        }
      case Routes.PPE_MANDATE_CANCELLED_SCREEN:
        {
          return PPEMandateCancellationInfoScreen(
            orderId: params['order_id'],
          );
        }
      case Routes.PPMC_BOOKING_SCREEN:
        {
          final referenceId = params['ref_id'] ?? params['referenceId'];
          final testCategory =
              params['test_category'] ?? params['testCategory'];
          // todo: check if we can make it only as boolean single check
          final bool? rescheduleOnly = params.containsKey('rescheduleOnly')
              ? (params['rescheduleOnly'] is String)
                  ? (params['rescheduleOnly'] as String)
                      .containsIgnoreCase('true')
                  : (params['rescheduleOnly'] is bool)
                      ? params['rescheduleOnly'] as bool
                      : null
              : null;
          // todo: connect with BE on this and check what do they send it as.
          final policyType = (params.containsKey('policyType') ||
                  params.containsKey('policy_type'))
              ? (params['policyType'] is String)
                  ? (params['policyType'] as String).containsIgnoreCase('life')
                      ? AssessmentPolicyType.life
                      : AssessmentPolicyType.health
                  : (params['policyType'] is AssessmentPolicyType)
                      ? params['policyType']
                      : (params['policy_type'] is String)
                          ? (params['policy_type'] as String)
                                  .containsIgnoreCase('life')
                              ? AssessmentPolicyType.life
                              : AssessmentPolicyType.health
                          : (params['policy_type'] is AssessmentPolicyType)
                              ? params['policy_type']
                              : null
              : null;
          bool preSelectAll = false;
          if (params.containsKey('pre_select_all')) {
            if (params['pre_select_all'] is bool) {
              preSelectAll = params['pre_select_all'];
            } else if (params['pre_select_all'] is String) {
              preSelectAll = bool.tryParse(params['pre_select_all']) ?? false;
            }
          }

          return BlocProvider<MedicalTestBookingBloc>(
            create: (context) => MedicalTestBookingBloc(
                fromScreen: params['from_screen'],
                referenceId: referenceId,
                policyType: policyType,
                source: params['source'],
                assessmentId: params['assessment_id'],
                testCategory: testCategory,
                assessmentGroupId: params['assessmentGroupId'],
                ahc: params['ahc'] ?? false,
                rescheduleOnly: rescheduleOnly,
                preSelectAll: preSelectAll),
            child: PpmcBookingScreen(),
          );
        }

      case Routes.PPMC_BOOKING_DETAILS_SCREEN:
        return BlocProvider<BookingDetailsCubit>(
          create: (context) => BookingDetailsCubit(
            repo: BookingDetailsRepository(
              ahc: (params['ahc'] ?? false) ||
                  (params['xSource'] == 'HEALTH_AHC'),
            ),
            referenceId: params['referenceId'],
            ahcBookingDetail: params['ahcBookingDetail'] as BookingDetail?,
          ),
          child: BookingDetailsScreen(),
        );

      case Routes.PPMC_TESTS_LANDING_SCREEN:
        return BlocProvider<MedicalTestSelectionCubit>(
          create: (context) => MedicalTestSelectionCubit(
            referenceId: params['referenceId'],
          ),
          child: TestBookingSelectionScreen(),
        );

      case Routes.P2I_STATUS_TRACKING_SCREEN:
        {
          final String? referenceId = params['referenceId'];
          if (referenceId.isNullOrEmpty) {
            AckoToast.show('Reference id not found');
            return CommonScaffold.setScaffold(SizedBox.shrink());
          }

          return BlocProvider<StatusTrackingCubit>(
              create: (context) => StatusTrackingCubit(
                    referenceId: referenceId,
                    repo: StatusTrackingRepo(),
                    addInfoRepo: AddInfoRepo(),
                  ),
              child: StatusTrackingScreen());
        }

      case Routes.P2I_TELEMER_SCREEN:
        {
          final String? referenceId = params['referenceId'];
          if (referenceId.isNullOrEmpty) {
            AckoToast.show('Reference id not found');
            return CommonScaffold.setScaffold(SizedBox.shrink());
          }
          return BlocProvider<TelemerCubit>(
              create: (context) =>
                  TelemerCubit(referenceId: referenceId, repo: TelemerRepo()),
              child: TelemerScreen());
        }
      case Routes.P2I_ADD_INFO_SCREEN:
        {
          final String? referenceId = params['referenceId'];
          if (referenceId.isNullOrEmpty) {
            AckoToast.show('Reference id not found');
            return CommonScaffold.setScaffold(SizedBox.shrink());
          }
          return BlocProvider<MedicalDocumentsCubit>(
              create: (context) => MedicalDocumentsCubit(
                  repo: AddInfoRepo(),
                  referenceId: referenceId,
                  addInfoUrl: params['addInfoUrl']),
              child: MedicalDocumentsScreen());
        }
      case Routes.P2I_MEMBER_EXCLUSION_SCREEN:
        {
          final String? referenceId = params['referenceId'];
          if (referenceId.isNullOrEmpty) {
            AckoToast.show('Reference id not found');
            return CommonScaffold.setScaffold(SizedBox.shrink());
          }
          return BlocProvider<MemberExclusionCubit>(
              create: (context) => MemberExclusionCubit(
                  referenceId: referenceId, repo: MemberExclusionRepo()),
              child: ExcludeMemberScreen());
        }
      case Routes.IPD_TRACK_STATUS_PAGE:
        return BlocProvider<ClaimSuccessCubit>(
            create: (context) => ClaimSuccessCubit(
                ahecClaimId: params['claimId']?.toString(),
                policyId: params['policyId'],
                subClaimId: params['subClaimId'],
                parentClaimsUrl: params['claimsUrl'],
                claimType: params['claimType'],
                acrId: params['acrId'],
                allSubClaimsInfo: params['allSubClaimInfo']
            ),
            child: ClaimStatusPage());
      case Routes.LIFE_BENEFITS_PAGE:
        return BlocProvider<LifeBenefitsCubit>(
            create: (context) => LifeBenefitsCubit(
                isPostPolicy: params["isPostPolicy"],
                proposalId: params["proposal_id"],
                policyNumber: params["policy_number"]),
            child: LifePolicyBenefitsPage());
      case Routes.LIFE_RAISE_CLAIM_PAGE:
        return LifeClaimHomePage(
          policyNumber: params['policy_number'],
        );

      case Routes.LIFE_POLICY_UPDATES_PAGE:
        return BlocProvider<LifePolicyUpdatesCubit>(
            create: (context) =>
                LifePolicyUpdatesCubit(policyNumber: params["policy_number"]),
            child: LifePolicyUpdatesPage());
      case Routes.LIFE_DOCUMENTS_DOWNLOAD_PAGE:
        return BlocProvider<LifeDocumentsCubit>(
            create: (context) =>
                LifeDocumentsCubit(policyNumber: params["policy_number"]),
            child: LifeDocumentsDownloadPage());
      case Routes.HEALTH_CLAIM_DETAIL_PAGE:
        return BlocProvider<ClaimSuccessCubit>(
            create: (context) => params["claim_bloc"],
            child: ClaimDetailPage(params["is_from_partial_claim"] ?? false));
      case Routes.HEALTH_EDIT_ENDORSEMENT:
        return EditEndorsementPage(params["product_type"]);
      case Routes.HSRP_DETAILS_PAGE:
        return BlocProvider<HsrpDetailPageBloc>(
            create: (context) => HsrpDetailPageBloc(params: params),
            child: HsrpDetailPage());
      case Routes.ADAPTIVE_SDUI_SCREEN:
        return BlocProvider<AckoAdaptiveSduiBloc>(
            create: (context) => AckoAdaptiveSduiBloc(params['url']),
            child: AckoAdaptiveSduiScreen());
      case Routes.COIN_HISTORY:
        return BlocProvider<CoinHistoryBloc>(
            create: (context) => CoinHistoryBloc(), child: CoinHistoryPage());
      case Routes.PAYMENT:
        Constants.PLATFORM_CHANNEL.invokeMethod("start_payment");
        return BlocProvider<PaymentPageBloc>(
          create: (context) =>
              PaymentPageBloc(params["ekey"], params["utm_source"]),
          child: PaymentPage(),
        );

      case Routes.PAYOUT_PREFERENCE:
        {
          return BlocProvider<PayoutPreferenceBloc>(
            create: (context) => PayoutPreferenceBloc(true),
            child: PayoutPreferenceScreen(),
          );
        }
      case Routes.KYC_INFORMATION:
        {
          return BlocProvider<KycInformationCubit>(
              create: (context) => KycInformationCubit(),
              child: KycInformation());
        }
      case Routes.ADD_WALLET:
        {
          return BlocProvider<AddWalletBloc>(
            create: (context) => AddWalletBloc(),
            child: AddWalletPage(),
          );
        }
      case Routes.HEALTH_POLICY_BENEFITS:
        return handlePolicyBenefitsNavigation();
      case Routes.HEALTH_POLICY_CARDS:
        return handlePolicyCardsNavigation();
      case Routes.BANK_DETAILS:
        return BlocProvider<PayoutPreferenceBloc>(
            create: (context) => PayoutPreferenceBloc(false),
            child: BankDetailsScreen(
                bankDetails: params['bank_details'],
                editableField: params['editable_field']));
      case Routes.HEALTH_CLAIM_PAYMENT_BREAKUP_PAGE:
        return BlocProvider<PaymentBreakupBloc>(
            create: (context) =>
                PaymentBreakupBloc(claimDetailData: params['claimDetailData']),
            child: ClaimPaymentBreakupPage(isFromSettlement: false));

      case Routes.HEALTH_SETTLEMENT_PAGE:
        return BlocProvider<PaymentBreakupBloc>(
            create: (context) =>
                PaymentBreakupBloc(claimDetailData: params['claimDetailData']),
            child: ClaimPaymentBreakupPage(isFromSettlement: true));

      case Routes.HEALTH_CATEGORY_DEDUCTION_PAGE:
        return BlocProvider<PaymentBreakupBloc>(
            create: (context) =>
                PaymentBreakupBloc(claimDetailData: params['claimDetailData']),
            child: CategoryWiseDeductionPage(
              isFromSettlement: params['isFromSettlement'],
            ));

      case Routes.HEALTH_MISC_DEDUCTION_PAGE:
        return BlocProvider<PaymentBreakupBloc>(
            create: (context) =>
                PaymentBreakupBloc(claimDetailData: params['claimDetailData']),
            child: MiscellaneousDeductionsPage());

      case Routes.CAR_LANDING_PAGE:
        {
          return BlocProvider<CarLandingBloc>(
            create: (context) => CarLandingBloc(null),
            child: CarLandingPage(),
          );
        }
      case Routes.CAR_PLAN_INFO_PAGE:
        {
          return BlocProvider<CarLandingBloc>(
            create: (context) => CarLandingBloc(params['plan']),
            child: PlanInfoPage(),
          );
        }
      case Routes.TAXI_INSURANCE_PAGE:
        {
          return BlocProvider<CarLandingBloc>(
            create: (context) => CarLandingBloc(null),
            child: TaxiInsurance(),
          );
        }
      case Routes.ABHA_DETAIL_PAGE:
        return BlocProvider(
          create: (context) => AbhaJourneyCubit(
            UtmParameters(
              utmSource: params['utm_source'],
              utmCampaign: params['utm_campaign'],
              referrer: params['referrer'],
              utmMedium: params['utm_medium'],
              utmTerm: params['utm_term'],
            ),
            AbhaJourneyRepository(),
            HlAnalyticsManager(
              AbhaJourneyPropertyStore(),
            ),
          ),
          child: AbhaDetailPage(
            abhaNumber: params['abhaNumber'],
          ),
        );
      case Routes.ABHA_LANDING_PAGE:
        return BlocProvider(
          create: (context) => AbhaJourneyCubit(
            UtmParameters(
              utmSource: params['utm_source'] ?? 'Web Flow',
              utmCampaign: params['utm_campaign'],
              referrer: params['referrer'],
              utmMedium: params['utm_medium'],
              utmTerm: params['utm_term'],
            ),
            AbhaJourneyRepository(),
            HlAnalyticsManager(
              AbhaJourneyPropertyStore(),
            ),
          ),
          child: AbhaJourneyLandingPage(),
        );
      // }

      case Routes.PUC_VALIDITY:
        {
          String registrationNumber =
              (params['reg_num'] != null) ? params['reg_num'] : "";

          return MultiBlocProvider(
            providers: [
              BlocProvider<SingleAssetManagementBloc>(
                create: (context) =>
                    SingleAssetManagementBloc(SingleAssetInitState()),
              ),
              BlocProvider<UserAssetsControllerBloc>(
                create: (context) => UserAssetsControllerBloc(
                    CommonDataStore.sharedInstance.assetApiRepo),
              ),
              BlocProvider<MapLocationBloc>(
                create: (context) =>
                    MapLocationBloc(ServiceType.PUC_VALIDITY, callEvent: true),
              ),
            ],
            child: PUCValidityScreen(
              registrationNumber: registrationNumber,
            ),
          );
        }
      case Routes.FASTAG_LIMITS_INFO_PAGE:
        return FastagRechargeLimitsInfoPage();
      case Routes.PENDING_CHALLAN:
        Map<String, dynamic>? json = params['json'];
        String? registrationNumber = params['reg_num'];
        String? flowType =
            registrationNumber != null ? 'vehicle_search' : params['flow_type'];

        return BlocProvider<SduiParentCubit>(
          create: (_) => ChallanCubit(
            challaDataStoreJson: json,
            registrationNumber: registrationNumber,
          ) as SduiParentCubit,
          child: ChallanJourney(
            flowType: flowType,
          ),
        );
      // return BlocProvider<SduiParentCubit>(
      //   create: (_) => VasCubit(usecase: 'CHALLAN') as SduiParentCubit,
      //   child: VasLandingPage(),
      // );

      // {
      //   String registrationNumber;
      //   if (params['reg_num'] != null) {
      //     registrationNumber = params['reg_num'];
      //   } else {
      //     return Container();
      //   }

      //   return MultiBlocProvider(
      //     providers: [
      //       BlocProvider<PendingChallanBloc>(
      //         create: (context) => PendingChallanBloc(registrationNumber),
      //       ),
      //       BlocProvider<SingleAssetManagementBloc>(
      //         create: (context) =>
      //             SingleAssetManagementBloc(SingleAssetInitState()),
      //       ),
      //       BlocProvider<UserAssetsControllerBloc>(
      //         create: (context) => UserAssetsControllerBloc(
      //             CommonDataStore.sharedInstance.assetApiRepo),
      //       ),
      //     ],
      //     child: PendingChallanScreen(
      //       registrationNumber: registrationNumber,
      //     ),
      //   );
      // }
      case Routes.PUC_VALIDITY_LOOKUP:
        {
          List<ServiceList>? services;
          if (params['list'] != null) {
            services = params['list'];
          }
          return MultiBlocProvider(
              providers: [
                BlocProvider<AckoServiceLookUpBloc>(
                  create: (context) =>
                      AckoServiceLookUpBloc(services, ServiceType.PUC_VALIDITY),
                ),
                BlocProvider<UserAssetsControllerBloc>(
                  create: (context) => UserAssetsControllerBloc(
                      CommonDataStore.sharedInstance.assetApiRepo),
                ),
              ],
              child: AckoServiceLookUpScreen(
                ServiceType.PUC_VALIDITY,
                prefillInfo: params,
              ));
        }
      case 'challan_transaction_listing':
        Map<String, dynamic>? assetStoreMapping = params['assetStoreMapping'];
        return BlocProvider<ChallanTransactionsListingCubit>(
          create: (_) => ChallanTransactionsListingCubit(
            assetStoreMapping: assetStoreMapping,
          ),
          child: ChallanTransactionsListingView(),
        );

      case Routes.CHALLAN_LOOKUP:
        String? registrationNumber = params['reg_num'];
        if (registrationNumber.isNotNullOrEmpty) {
          String? flowType = 'vehicle_search';
          return BlocProvider<SduiParentCubit>(
            create: (_) => ChallanCubit(
              registrationNumber: registrationNumber,
            ) as SduiParentCubit,
            child: ChallanJourney(
              flowType: flowType,
            ),
          );
        }

        return BlocProvider<SduiParentCubit>(
          create: (_) => VasCubit(usecase: 'CHALLAN') as SduiParentCubit,
          child: VasLandingPage(),
        );

      case Routes.HSRP_LOOKUP:
        return BlocProvider<SduiParentCubit>(
          create: (_) => VasCubit(usecase: 'HSRP') as SduiParentCubit,
          child: VasLandingPage(),
        );
      case Routes.CHALLAN_TRANSACTION_PAYMENT_DETAILS:
        ChallanHistoryArgs historyArgs =
            (params != null && params['challanHistoryArgs'] != null)
                ? params['challanHistoryArgs'] as ChallanHistoryArgs
                : ChallanHistoryArgs.fromJson(params);
        return BlocProvider(
            create: (context) =>
                ChallanTransactionDetailBloc(historyArgs: historyArgs),
            child: ChallanTransactionsDetailPage());
      case Routes.VEHICLE_VALUATION_HOME:
      case Routes.VEHICAL_VALUATION_HOME:
        {
          List<ServiceList>? services;
          if (params['list'] != null) {
            services = params['list'];
          }

          return MultiBlocProvider(
              providers: [
                BlocProvider<AckoServiceLookUpBloc>(
                  create: (context) =>
                      AckoServiceLookUpBloc(services, ServiceType.VALUATION),
                ),
                BlocProvider<UserAssetsControllerBloc>(
                  create: (context) => UserAssetsControllerBloc(
                      CommonDataStore.sharedInstance.assetApiRepo),
                )
              ],
              child: AckoServiceLookUpScreen(
                ServiceType.VALUATION,
                prefillInfo: params,
              ));
        }
      case Routes.FASTAG_RECHARGE_ENTER_AMOUNT_PAGE:
        String billerDetailsID = params['biller_id'];
        String registrationNumber = params['reg_num'];
        return MultiBlocProvider(
          providers: [
            BlocProvider<FastagPayBillerCubit>(
              create: (context) => FastagPayBillerCubit(),
            ),
            BlocProvider<FastagFetchBalanceCubit>(
                create: (context) => FastagFetchBalanceCubit(
                      registrationNumber: registrationNumber,
                      billerID: billerDetailsID,
                      source: FastagSources.fastagRechargePage.name,
                    )),
          ],
          child: BillerEnterAmountPage(),
        );
      case Routes.FASTAG_TRANSACTION_HISTORY_PAGE:
        String? regNo = params?['registration_number'];
        return FastagTransactionHistoryPage(
          regNo: regNo,
        );
      case Routes.FASTAG_DETAILS_PAGE:
        PostCloseAction postCloseAction = params['post_close_action'];
        FastagFetchBalanceCubit? fastagBalanceCubit =
            params?['fastag_balance_cubit'];

        if (fastagBalanceCubit == null) {
          return ErrorPage();
        }
        return BlocProvider.value(
            value: fastagBalanceCubit,
            child: FastagViewDetailsPage(
              postCloseAction: postCloseAction,
            ));
      case Routes.FASTAG_RECHARGE_STATUS_PAGE:
        {
          String? sourceScreen = params['sourceScreen'];
          return MultiBlocProvider(
              providers: [
                BlocProvider<FastagSettlementCubit>(
                    create: (context) => FastagSettlementCubit(
                          paymentId: params['bill_recharge_payment_id'],
                          source: FastagSources.fastagSettlementPage.name,
                        )),
                BlocProvider<SearchAssetBloc>(
                    create: (context) => SearchAssetBloc()),
                BlocProvider<UserAssetsControllerBloc>(
                  create: (context) => UserAssetsControllerBloc(
                      CommonDataStore.sharedInstance.assetApiRepo),
                )
              ],
              child: FastagPaymentStatus(
                sourceScreen: sourceScreen,
              ));
        }
      case Routes.FASTAG_RECHARGE_RECEIPT_PAGE:
        {
          return BlocProvider<FastagRechargeReceiptCubit>(
              create: (context) => FastagRechargeReceiptCubit(
                  settlementId: params['settlement_id']),
              child: FastagRechargeReceiptPage(
                  billRechargeReceiptData: params['bill_recharge_receipt_data'],
                  regNo: params['reg_no']));
        }
      case Routes.FASTAG_LINK_DELINK_MANAGE_PAGE:
        {
          bool linkingOnly = params['linking_only'] ?? true;
          bool popAfterLinking = params['pop_after_linking'] ?? false;
          FastagFetchBalanceCubit? fastagBalanceCubit =
              params?['fastag_balance_cubit'];
          return MultiBlocProvider(
            providers: [
              BlocProvider<LinkDelinkFastagManagementCubit>(
                create: (context) => LinkDelinkFastagManagementCubit(
                  userFastagAccountId: params?['user_account_id'],
                  registrationNumber: params['registration_number'],
                  source: FastagSources.fastagLinkDelinkPage.name,
                ),
              ),
              BlocProvider<UserAssetsControllerBloc>(
                create: (context) => UserAssetsControllerBloc(
                    CommonDataStore.sharedInstance.assetApiRepo),
              ),
              if (fastagBalanceCubit != null)
                BlocProvider.value(
                  value: fastagBalanceCubit,
                ),
            ],
            child: DelinkLinkFastagPage(
              linkingOnly: linkingOnly,
              popAfterLinking: popAfterLinking,
            ),
          );
        }

      case Routes.FASTAG_DECISION_PAGE:
        bool isLinked = (params?['is_linked'] ?? 'false') == 'true';
        String regNum = params?['reg_num'];
        return BlocProvider<FastagDecisionScreenCubit>(
          create: (context) =>
              FastagDecisionScreenCubit(regNumber: regNum, isLinked: isLinked),
          child: FastagDecisionScreen(
            isLinked: isLinked,
            billerId: params?['biller_id'],
          ),
        );

      case Routes.FASTAG_LOOKUP:
        final List<ServiceList>? services = params['list'];

        return MultiBlocProvider(
          providers: [
            BlocProvider<AckoServiceLookUpBloc>(
              create: (context) =>
                  AckoServiceLookUpBloc(services, ServiceType.FASTAG_RECHARGE),
            ),
            BlocProvider<UserAssetsControllerBloc>(
              create: (context) => UserAssetsControllerBloc(
                  CommonDataStore.sharedInstance.assetApiRepo),
            )
          ],
          child: AckoServiceLookUpScreen(ServiceType.FASTAG_RECHARGE),
        );

      case Routes.RAPID_RESPONSE_HOME:
        bool showQuickBookingBottomSheet =
            ((params["quick_booking"]) == "true");
        return BlocProvider<RapidResponseCubit>(
          create: (BuildContext context) => RapidResponseCubit(
              fromPage: RapidResponsePageUseCase.home,
              showQuickBooking: showQuickBookingBottomSheet),
          child: RapidResponseView(),
        );

      case Routes.RAPID_RESPONSE_ONGOING_BOOKING_TRACKING_PAGE:
        String sosId = params["sosId"];
        TrackingStatusUseCase? trackingUseCase;
        if (params['trackingStatusUseCase'] != null &&
            params['trackingStatusUseCase'] is String) {
          trackingUseCase =
              TrackingStatusUseCase.fromString(params['trackingStatusUseCase']);
        } else {
          trackingUseCase = params['trackingStatusUseCase'];
        }
        bool? showCallerScreen = params['showCallerScreen'];

        return BlocProvider<RapidResponseTrackingCubit>(
          create: (BuildContext context) => RapidResponseTrackingCubit(
            sosId: sosId,
            trackingUseCase: trackingUseCase,
            showCallerScreen: showCallerScreen,
          ),
          child: OnGoingBookingTrackingPage(),
        );
      case Routes.RAPID_RESPONSE_TRANSACTION_COMPLETE_PAGE:
        String sosId = params['sosId'];
        RapidResponseBookingUseCase? useCase;

        if (params['useCase'] != null && params['useCase'] is String) {
          useCase = RapidResponseBookingUseCase.fromString(params['useCase']);
        } else {
          useCase = params['useCase'];
        }

        return BlocProvider<RapidResponseTrackingCubit>(
          create: (BuildContext context) =>
              RapidResponseTrackingCubit(sosId: sosId, useCase: useCase),
          child: RapidResponseTransactionCompletePage(),
        );

      case Routes.RAPID_RESPONSE_BOOKING_HISTORY_LIST_PAGE:
        final bookingHistory = params['bookingHistory'] ?? null;
        return BlocProvider<RapidResponseCubit>(
          create: (BuildContext context) =>
              RapidResponseCubit(fromPage: RapidResponsePageUseCase.history),
          child: RapidResponseBookingHistoryPage(),
        );

      case Routes.RAPID_RESPONSE_FAMILY_DETAIL_VIEW:
        return BlocProvider<RapidResponseCubit>(
          create: (BuildContext context) =>
              RapidResponseCubit(fromPage: RapidResponsePageUseCase.home),
          child: RapidResponseListFamilyPage(),
        );
      case Routes.RAPID_RESPONSE_LOCATION_PICKER_PAGE:
        ScheduleBookingParams dataModel = params['params'];
        return MultiBlocProvider(
          providers: [
            ///bloc to search location
            BlocProvider<MapLocationSearchBloc>(
              create: (context) => MapLocationSearchBloc(),
            ),

            ///bloc to get current location
            BlocProvider<MapLocationBloc>(
              create: (context) => MapLocationBloc(
                ServiceType.RAPID_RESPONSE,
                callEvent: false,
              ),
            )
          ],
          child: SOSLocationPickerPage(params: dataModel),
        );

      case Routes.RAPID_RESPONSE_SCHEDULE_BOOKING_REVIEW_PAGE:
        ScheduleBookingParams dataModel = params['params'];
        return BlocProvider<RapidResponseCubit>(
          create: (context) =>
              RapidResponseCubit(fromPage: RapidResponsePageUseCase.home),
          child: BookForLaterReviewPage(params: dataModel),
        );

      case Routes.RTO_LOOKUP:
        final List<ServiceList>? services = params['list'];

        return MultiBlocProvider(
          providers: [
            BlocProvider<AckoServiceLookUpBloc>(
              create: (context) =>
                  AckoServiceLookUpBloc(services, ServiceType.RTO),
            ),
            BlocProvider<UserAssetsControllerBloc>(
              create: (context) => UserAssetsControllerBloc(
                  CommonDataStore.sharedInstance.assetApiRepo),
            )
          ],
          child: AckoServiceLookUpScreen(ServiceType.RTO),
        );

      case Routes.RTO_DETAILS:
        if (params['reg_num'] != null) {
          final registrationNumber = params['reg_num'];
          return BlocProvider<RtoDetailBloc>(
            create: (context) => RtoDetailBloc(registrationNumber),
            child: AckoServiceRTODetailScreen(),
          );
        }
        return Container();

      case Routes.SERVICE_MAP_VIEW:
        final serviceType =
            (params['serviceType'] != null && params['serviceType'] is String)
                ? _getServiceType(params['serviceType'])
                : params['serviceType']; //params['serviceType'];
        final brandId = params['brandId'] ?? '0';
        return BlocProvider<MapLocationBloc>(
          create: (context) => MapLocationBloc(serviceType,
              brandId: brandId, title: params['brandName'] ?? ''),
          child: MapViewService(
            serviceType: serviceType,
          ),
        );

      case Routes.SELECT_BRAND_SCREEN:
        if (params['serviceType'] != null && params['serviceType'] is String) {
          params['serviceType'] = _getServiceType(params['serviceType']);
        }
        return BlocProvider<BrandsSearchBloc>(
          create: (context) => BrandsSearchBloc(),
          child: BrandScreen(
            serviceType: params['serviceType'],
            showClaimBottomSheet: (params['showClaimBottomSheet'] ?? 'false')
                .toString()
                .equalsIgnoreCase('true'),
          ),
        );
      case Routes.BRAND_SEARCH_LIST_SCREEN:
        return BlocProvider(
          create: (context) => BrandsSearchBloc(),
          child: BrandSearchListScreen(
            serviceType: params['serviceType'],
          ),
        );
      case Routes.EV_CENTERS_MAP_VIEW:
        final serviceType = params['serviceType'];
        final brandId = params['brandId'] ?? '0';
        return BlocProvider<MapLocationBloc>(
          create: (context) => MapLocationBloc(serviceType,
              brandId: brandId, title: params['brandName'] ?? ''),
          child: MapViewService(
            serviceType: serviceType,
          ),
        );
      case Routes.PUC_CENTER:
        final serviceType =
            (params['serviceType'] != null && params['serviceType'] is String)
                ? _getServiceType(params['serviceType'])
                : params['serviceType']; //params['serviceType'];
        final brandId = params['brandId'] ?? '0';
        return BlocProvider<MapLocationBloc>(
          create: (context) => MapLocationBloc(serviceType, brandId: brandId),
          child: MapViewService(
            serviceType: serviceType,
          ),
        );
      case Routes.MAP_AREA_SEARCH:
        final serviceType = params['serviceType'];
        return BlocProvider<MapLocationSearchBloc>(
          create: (context) => MapLocationSearchBloc(),
          child: AckoServiceAreaSearch(
            serviceType: serviceType,
          ),
        );

      case Routes.APP_UPDATE_PAGE:
        return AppUpdatePage();
      case Routes.SAVED_CARD:
        return BlocProvider<SavedCardBloc>(
          create: (context) => SavedCardBloc(),
          child: SavedCards(),
        );

      case Routes.MANAGE_ASSETS:
        String? source = (params != null && params.containsKey('source'))
            ? params['source']!
            : 'asset_management';
        return BlocProvider<UserAssetsControllerBloc>(
          create: (context) => UserAssetsControllerBloc(
              CommonDataStore.sharedInstance.assetApiRepo),
          child: ManageAssets(source: source),
        );

      case Routes.CREATE_VEHICLE_ASSET:
        String? source = (params != null && params.containsKey('source'))
            ? params['source']!
            : 'asset_management';
        String? regNumber;
        if (params != null) {
          regNumber = params['regNumber'];
        }
        return MultiBlocProvider(providers: [
          BlocProvider<UserAssetsControllerBloc>(
              create: (context) => UserAssetsControllerBloc(
                  CommonDataStore.sharedInstance.assetApiRepo)),
          BlocProvider<SearchAssetBloc>(create: (context) => SearchAssetBloc())
        ], child: CreateVehicleAsset(regNumber: regNumber, source: source));
      case Routes.INTERNET_POLICY:
        return BlocProvider<InternetPoliciesByGroupBloc>(
            create: (context) => InternetPoliciesByGroupBloc(params['policy']),
            child: InternetPolicyListPage(params['display_name']));

      case Routes.ACKO_PAYOUT:
        return BlocProvider.value(
            value: commonAckoPayoutBloc,
            child: AckoPayoutPage(
                config: params['config'], payoutPage: params["page_to_load"]));

      case Routes.ACKO_PAY_EXT:
        return BlocProvider.value(
            value: commonPayExtBloc,
            child: AckoPayExtPage(
                config: params['config'],
                paymentScreen: params["page_to_load"]));

      case Routes.TREATMENT_CATEGORIES:
        Map<String, dynamic> paramsData =
            (params is Map<String, dynamic>) ? params : Map();
        return BlocProvider<TreatmentCategoryBloc>(
            child: TreatmentCategoriesPage(),
            create: (ctx) => TreatmentCategoryBloc(
                paramsData['memberName'], paramsData['claimId'],
                treatmentPath: paramsData.containsKey('treatmentPath')
                    ? paramsData['treatmentPath']
                    : null));

      case Routes.ADVANCE_CASH_DETAIL:
        Map<String, dynamic> paramsData =
            (params is Map<String, dynamic>) ? params : Map();
        return BlocProvider(
            create: (context) => AdvanceCashDetailsBloc(params['acrId']),
            child: AdvanceCashDetailsPage());

      case Routes.ADVANCE_CASH_PAYMENT:
        Map<String, dynamic> paramsData =
            (params is Map<String, dynamic>) ? params : Map();
        return BlocProvider(
            create: (context) => AckoExtPaymentBloc(),
            child: AckoPayExtPage(
                config: PaymentConfig(
                    paymentLOB: PaymentLOB.health,
                    supportContactNumber: contact_no,
                    supportEmail: health_acko_email_id,
                    paymentJourney: PaymentJourney.advancedCash,
                    paymentSource: PaymentSource.health,
                    entityId: params['acr-id'],
                    paymentStatus: paramsData['status'],
                    amount: paramsData.containsKey('amount')
                        ? (double.tryParse("${paramsData['amount']}") ?? 0)
                        : 0),
                paymentScreen: PaymentScreen.PAYMENT_STATUS));

      case Routes.ASSET_DOCUMENT_VIEWER:
        return BlocProvider<DocumentViewerCubit>(
          create: (context) => DocumentViewerCubit(params['asset_doc']),
          child: DocumentViewer(
            detailVehicleAssetModel: params['vehicle_model'],
          ),
        );
      case Routes.FNOL_INTRO:
        return BlocProvider(
            create: (context) => ClaimIntroBloc(),
            child: ClaimIntroduction(loadFromDraft: params['load_from_draft']));
      case Routes.FILE_HEALTH_FNOL:
        return BlocProvider(
            create: (context) => FileClaimBloc(
                acrId: params['acr_id'] ?? null,
                loadFromDraft: params['load_from_draft'] ?? false,
                incidentReason: params['incident_reason'],
                hasPendingPayment: params['has_pending_payment'] ?? false,
                isAdvanceCashFlow: params['is_advance_cash_flow'] ?? false,
                isClaimForAdvanceCash:
                    params['is_claim_for_advance_cash'] ?? false),
            child: FileClaimPage());

      case Routes.ACKO_DRIVE_VIEW:
        return BlocProvider(
          create: (_) => AckoDriveCubit(
            params['login_use_case'],
            redirectUrl: (params['url'] ?? params['redirect_url']),
          ),
          child: AckoDriveView(
            pageSource: params['page_source'],
          ),
        );

      ///TRAVEL
      case Routes.PDP_HOME:
        return MultiBlocProvider(
          providers: [
            BlocProvider.value(
              value: PdpHomeBlocSingleton.blocInstance,
            ),
            BlocProvider(
              create: (context) => PolicyDetailBloc(),
            ),
            BlocProvider(
              create: (context) => ClaimDamageCubit(),
            ),
            BlocProvider(
              create: (context) => PlanBenefitBloc(),
            ),
          ],
          child: PdpHomePage(
            policyId: params['policyId'],
            utmParam: params['utmParam'],
            toClaim: params['toClaim'] == '1',
            toEdit: params['toEdit'] == '1',
          ),
        );
      case Routes.PERIL:
        return MultiBlocProvider(
          providers: [
            BlocProvider(
              create: (context) => ClaimDamageCubit(),
            ),
            BlocProvider.value(
              value: PdpHomeBlocSingleton.blocInstance,
            ),
          ],
          child: PerilPage(
            perilDataList: params['perilList'] as List<PerilListRefresh>,
            policyId: params['policyId'],
          ),
        );
      case Routes.FNOL:
        return FnolPage(
          model: params['fnolData'] as ClaimModeList,
        );
      case Routes.COVER_STORY:
        return CoverStoryPage(
          storyList: params['storyList'] as List<StoryList>,
        );
      case Routes.TRAVEL_HELP:
        return BlocProvider(
          create: (context) => HelpCubit(),
          child: HelpPage(
            isFlightPass: bool.tryParse(params['isFlightPass'] ?? '') ?? false,
            planType: params['planType'],
          ),
        );
      case Routes.POLICY_DETAIL:
        return PolicyDetailPage(
          model: params['policyDetail'] as PdpModel,
        );
      case Routes.COVER_BENEFIT:
        return BlocProvider(
          create: (context) => PlanBenefitBloc(),
          child: PlanBenefitPage(policyId: params['policyId']),
        );
      case Routes.REQUEST_CANCELLATION:
        return MultiBlocProvider(
          providers: [
            BlocProvider.value(
              value: PdpHomeBlocSingleton.blocInstance,
            ),
            BlocProvider(create: (context) => PolicyCancelCubit())
          ],
          child: RequestCancellationPage(
              requestPolicyCancel: params['requestPolicyCancel']),
        );
      case Routes.CANCELLED_POLICY:
        return PolicyCancelledPage(nudge: params['nudge']);
      case Routes.EDIT_POLICY:
        return MultiBlocProvider(
          providers: [
            BlocProvider.value(
              value: PdpHomeBlocSingleton.blocInstance,
            ),
            BlocProvider(
              create: (context) => PolicyDetailBloc(),
            ),
          ],
          child: EditPolicyPage(
            insuredId: params['insuredId'],
          ),
        );

      case Routes.VIEW_NOMINEE:
        return ViewNomineePage(
          travellerList: params['nominee'],
        );
      case Routes.TRAVEL_CASHLESS_HOSPITALS:
        return MultiBlocProvider(
          providers: [
            BlocProvider(create: (context) => CashlessHospitalsBloc()),
            BlocProvider(
                create: (context) => CashlessHospitalsByCountryDataBloc())
          ],
          child: CashlessHospitalsPage(
            countryList: params['country_list'],
          ),
        );
      case Routes.PAYMENT_PENDING:
        return BlocProvider(
          create: (context) =>
              DraftPolicyCubit(proposalId: params['proposalId']),
          child: PaymentPendingPage(utmParam: params['utmParam']),
        );
      case Routes.EDIT_NOMINEE:
        return MultiBlocProvider(
          providers: [
            BlocProvider.value(
              value: PdpHomeBlocSingleton.blocInstance,
            ),
            BlocProvider(
              create: (context) => PolicyDetailBloc(),
            ),
          ],
          child: EditNomineePage(travellerDetail: params['travellerDetail']),
        );
      case Routes.TRAVEL_LOGGER:
        return TravelLoggerPage();
      case Routes.PAYMENT_VERIFY:
        return BlocProvider(
          create: (context) => PolicyDetailBloc(),
          child: PaymentVerifyPage(orderId: params['orderId']),
        );
      case Routes.COMPLIANCE_PENDING:
        return CompliancePendingPage(
            compliancePending: params['compliancePending']);
      case Routes.POLICY_READY:
        return PolicyReadyPage(
          paymentData: params['paymentData'],
        );
      case Routes.CLAIM_DAMAGES:
        return BlocProvider(
          create: (context) => ClaimDamageCubit(),
          child: ClaimDamagesPage(
            claimModeList: params['claimModeList'],
            title: params['title'],
            subTitle: params['subTitle'],
            iconUrl: params['iconUrl'],
            policyId: params['policyId'],
            perilId: params['perilId'],
            policyNumber: params['policyNumber'],
          ),
        );
      case Routes.CLAIM_EXPECTATION:
        return BlocProvider(
          create: (context) => ClaimExpectationCubit(),
          child: ClaimExpectationPage(
            policyId: params['policyId'],
            policyNumber: params['policyNumber'],
            claimType: params['claimType'],
            damageId: params['damageId'],
            perilId: params['perilId'],
          ),
        );
      case Routes.TRAVEL_CLAIM_STATE:
        return BlocProvider(
          create: (context) => ClaimStateBloc(),
          child: ClaimStatePage(
            machineId: params['machineId'],
          ),
        );
      case Routes.TRAVEL_CLAIM_TRACKING:
        return BlocProvider(
          create: (context) => ClaimTrackingBloc(),
          child: ClaimTrackingPage(claimNumber: params['claimNumber']),
        );
      case Routes.POLICY_LIST_PAGE:
        return BlocProvider(
          create: (context) => ClaimPoliciesCubit(params['claimNumber']),
          child: PolicyListPage(),
        );

      case Routes.TRAVEL_MY_TRIPS:
        return BlocProvider(
          create: (context) => MyTripsCubit(),
          child: MyTripsPage(),
        );

      case Routes.FLIGHT_PASS_PURCHASE_SUCCESS:
        return PurchaseSuccessPage(policyId: params['policyId']);

      case Routes.FLIGHT_PASS_PDP:
        return MultiBlocProvider(
          providers: [
            BlocProvider(create: (context) => FlightPassPdpBloc()),
            BlocProvider(create: (context) => CouponsBloc())
          ],
          child: FlightPassPDPPage(policyId: params['policyId']),
        );

      case Routes.USER_FLIGHTS:
        return BlocProvider(
          create: (context) => UserFlightsBloc(),
          child: UserFlightPage(
            policyId: params['policy_id'],
            policyNumber: params['policy_number'],
            perilId: params['peril_id'],
            claimModeId: params['claim_mode_id'],
            damageId: params['damage_id'],
            passStartDate: params['pass_start_date'],
            passEndDate: params['pass_end_date'],
          ),
        );

      case Routes.FLIGHT_PASS_COUPONS:
        return BlocProvider(
          create: (context) => CouponsBloc(),
          child: FlightPassCouponsPage(
            policyId: params['policy_id'],
            flightPassCouponData: params['flight_pass_coupon_data'],
          ),
        );

      case Routes.FLIGHT_PASS_COUPON_DETAIL:
        return BlocProvider(
          create: (context) => CouponsBloc(),
          child: FlightPassCouponDetailPage(
            coupon: params['coupon'],
            policyId: params['policy_id'],
            tagIndex: params['tag_index'],
          ),
        );

      case Routes.FLIGHT_PASS_COVERAGE_DETAIL:
        return BlocProvider(
          create: (context) => CoverageBloc(),
          child: FlightPassCoverageDetailsPage(
            policyId: params['policy_id'],
            coverageType: params['coverage_type'],
          ),
        );

      case Routes.ADD_FLIGHT:
        return BlocProvider(
          create: (context) => AddFlightBloc(),
          child: AddFlightPage(
            policyId: params['policy_id'],
            policyNumber: params['policy_number'],
            perilId: params['peril_id'],
            claimModeId: params['claim_mode_id'],
            damageId: params['damage_id'],
            passStartDate: params['pass_start_date'],
            passEndDate: params['pass_end_date'],
          ),
        );
      case Routes.ADD_FLIGHT_RESULT:
        return AddFlightResult(
          policyId: params['policy_id'],
          policyNumber: params['policy_number'],
          perilId: params['peril_id'],
          claimModeId: params['claim_mode_id'],
          damageId: params['damage_id'],
          flightObject: params['flight_object'],
        );

      case Routes.FLIGHT_PASS_EXPECTATION:
        return BlocProvider(
          create: (context) => FlightPassClaimBloc(),
          child: FlightPassExpectationPage(
            policyId: params['policy_id'],
            policyNumber: params['policy_number'],
            perilId: params['peril_id'],
            claimModeId: params['claim_mode_id'],
            damageId: params['damage_id'],
            flightId: params['flight_id'],
          ),
        );

      case Routes.FLIGHT_PASS_CREATE_CLAIM:
        return BlocProvider(
          create: (context) => ClaimStateBloc(),
          child: CreateClaimPage(
            machineId: params['machineId'],
          ),
        );

      case Routes.FLIGHT_PASS_CLAIMS:
        return BlocProvider(
          create: (context) => YourClaimsBloc(),
          child: YourClaims(policyNumber: params['policy_number']),
        );

      /////Travel routes end here
      case Routes.ADVANCE_CASH_INTRO:
        return BlocProvider(
            create: (context) => AdvanceCashIntroBloc(),
            child: AdvanceCashIntroPage());

      case Routes.ADVANCE_CASH_LEARN_MORE:
        return BlocProvider(
            create: (context) => AdvanceCashIntroBloc(),
            child:
                AdvanceCashCustomerEducationPage(params['isChild'] ?? false));
      case Routes.ALERT_SUBSCRIPTION_PAGE:
        // return BlocProvider(
        //     create: (context) => ManageSubscriptionsBloc(
        //         assetModel: params['asset_model'],
        //         pucModel: params['puc'],
        //         subscriptionsList: params['subscriptions']),
        //     child: ManageSubscriptionPage());
        if (params['asset_model'] == null) {
          SduiAssetOverviewBloc bloc = SduiAssetOverviewBloc(params);
          params['asset_model'] = bloc.model;
        }
        String? regNum = ((params['asset_model'] ?? DetailVehicleAssetModel())
                as DetailVehicleAssetModel)
            .vehicle
            ?.assetNumber;
        return MultiBlocProvider(providers: [
          BlocProvider(
            create: (context) => ManageSubscriptionsBloc(
                assetModel: params['asset_model'],
                pucModel: params['puc'],
                subscriptionsList: params['subscriptions']),
          ),
          BlocProvider<FastagDecisionScreenCubit>(
            create: (context) =>
                FastagDecisionScreenCubit(regNumber: regNum ?? ''),
          )
        ], child: ManageSubscriptionPage());

      case Routes.MEDICAL_EVALUATION_DETAILS:
        return BlocProvider(
          create: (context) => MedicalEvaluationBloc(
              referenceId: params.containsKey("proposal_id")
                  ? params["proposal_id"]
                  : null,
              bookingId: params.containsKey("booking_id")
                  ? params["booking_id"]
                  : null),
          child: MedicalEvaluationPage(),
        );
      case Routes.VMER_EVALUATION_DETAILS:
        return BlocProvider(
          create: (context) => MedicalEvaluationBloc(
              referenceId: params.containsKey("proposal_id")
                  ? params["proposal_id"]
                  : null,
              bookingId: params.containsKey("booking_id")
                  ? params["booking_id"]
                  : null),
          child: VMEREvaluationPage(),
        );
      case Routes.VMER_SECRET_SCHEDULE_PAGE:
        return BlocProvider(
          create: (context) => MedicalEvaluationBloc(
            referenceId: params.containsKey("proposal_id")
                ? params["proposal_id"]
                : null,
          ),
          child: VMERSecretSchedulePage(),
        );
      case Routes.VMER_TIMER_PAGE:
        return BlocProvider(
          create: (context) => MedicalEvaluationBloc(
              referenceId: params.containsKey("referenceId")
                  ? params["referenceId"]
                  : null,
              scheduleSuccess: params.containsKey("scheduleSuccess")
                  ? params["scheduleSuccess"]
                  : null,
              vmerVideoLink: params.containsKey("vmerVideoLink")
                  ? params["vmerVideoLink"]
                  : null,
              countDownSeconds: params.containsKey("countDownSeconds")
                  ? params["countDownSeconds"]
                  : null,
              vmerSlotScheduleResponse:
                  params.containsKey("vmerSlotScheduleResponse")
                      ? params["vmerSlotScheduleResponse"]
                      : null),
          child: VMERTimerPage(),
        );
      case Routes.VMER_SLOT_SELECTION:
        return BlocProvider(
          create: (context) => MedicalEvaluationBloc(
              referenceId: params.containsKey("referenceId")
                  ? params["referenceId"]
                  : null,
              bookingId:
                  params.containsKey("bookingId") ? params["bookingId"] : null),
          child: VMERSlotSelectionPage(),
        );
      case Routes.VMER_SCHEDULE_SUCCESS:
        return BlocProvider(
          create: (context) => MedicalEvaluationBloc(
            fromScreen: params['fromScreen'],
            vmerSlotScheduleResponse: params['vmerSlotScheduleResponse'],
          ),
          child: VMERScheduleSuccessPage(),
        );
      case Routes.CMS_VIDEO_PAGE:
        return BlocProvider<VideoListPageCubit>(
            create: (context) => VideoListPageCubit(
                  allContents: params['all_contents'],
                  selectedVideoId: params['video_id'],
                ),
            child: VideoListPage());

      case Routes.CMS_VIDEO_POPUP_PAGE:
        return VideoViewPopupView(
            videoId: params['video_id'],
            orientation: params["orientation"] ?? 'portrait');

      // case Routes.PPMC_SCHEDULE_REVIEW:
      //   return BlocProvider<PpmcScheduleReviewCubit>(
      //     create: (context) => PpmcScheduleReviewCubit(
      //         reviewData: params["reviewData"],
      //         referenceId: params["referenceId"],
      //         testCategory: params["testCategory"],
      //         source: params["source"]),
      //     child: PpmcScheduleReviewPage(),
      //   );

      case Routes.ASSET_POLICY_HEALTH:
        Map policyValue = Map();
        policyValue["name"] = params['name'];
        policyValue["dob"] = params['dob'] != null && params['dob'] != 'null'
            ? params['dob']
            : '';

        /// Only name will be available
        return MultiBlocProvider(providers: [
          BlocProvider<HealthAssetDetailsCubit>(
            create: (context) => HealthAssetDetailsCubit(filter: policyValue),
          ),
          BlocProvider<CommonDataStoreBloc>.value(
            value: globalCommonDataStoreBloc!,
          ),
          BlocProvider<AbhaJourneyCubit>.value(
            value: AbhaJourneyCubit(
              UtmParameters(
                utmSource: params['utm_source'],
                utmCampaign: params['utm_campaign'],
                referrer: params['referrer'],
                utmMedium: params['utm_medium'],
                utmTerm: params['utm_term'],
              ),
              AbhaJourneyRepository(),
              HlAnalyticsManager(
                AbhaJourneyPropertyStore(),
              ),
            ),
          )
        ], child: HealthAssetDetailsView());

      case Routes.HEALTH_HOME_PAGE:
        debugPrint("======> HEALTH_HOME_PAGE ${jsonEncode(params)}");
        // https://www.acko.com/health/home?policy_id=id&register_claim=true&edit_policy_url=url&is_edit_policy=true&e-cards=true
        // Edit policy URL : https://www.acko.com/health/home?policy_id=id&edit_policy_url=url&is_edit_policy=true
        // Register claim URL :  https://www.acko.com/health/home?register_claim=true
        // New home page : https://www.acko.com/health/home?policy_id=id
        // old home page : https://www.acko.com/health/home
        if ((params.containsKey('policy_number') ||
            params.containsKey('policy_id') ||
            params.containsKey('proposal_id'))) {
          int defaultTab = int.tryParse(params['defaultTab'] ?? "") ?? 0;
          HealthPolicyHolder? person = params['selectedPolicyHolder'];
          if (params.containsKey('policy_number') ||
              params.containsKey('policy_id')) {
            return BlocProvider<HealthPolicyDetailsCubit>(
                create: (context) {
                  return HealthPolicyDetailsCubit(
                      params.containsKey('policy_id') &&
                              params['policy_id'] != null
                          ? {"policy_id": params['policy_id']}
                          : params.containsKey('policy_number')
                              ? {"policy_number": params['policy_number']}
                              : params.containsKey('proposal_id')
                                  ? {"proposalId": params['proposal_id']}
                                  : {},
                      defaultTab,
                      person);
                },
                child: HealthPolicyView());
          } else {
            return SizedBox.shrink();
          }
        } else {
          return BlocProvider<HealthHomeBloc>(
              key: UniqueKey(),
              create: (context) =>
                  HealthHomeBloc(params?["is_expired"] ?? false),
              child: HealthHomePage(product: params?['product_type']));
        }

      case Routes.HEALTH_CLAIMS_OPTION_SELECT_SCREEN:
        return HealthClaimsOptionSelectionScreen(policyNumber: params['policy_number']);

      case Routes.HEALTH_RENEWAL_HOME_PAGE:
        return BlocProvider(
          create: (context) => RenewalHomeCubit(
            JourneyManagerRepository(),
            HlAnalyticsManager(RenewalPropertyStore()),
          ),
          child: HealthRenewalHomePage(queryParam: {
            'policy_number': params["policy_number"],
            'member_details': params['member_details'],
            'show_modal': params['show_modal'] ?? true,
          }),
        );
      case Routes.HEALTH_RENEWAL_EDIT_PAGE:
        return BlocProvider(
          create: (context) => RenewalEditCubit(
            JourneyManagerRepository(),
          ),
          child: RenewalEditPage(
            proposalDetails: params['proposal_details'],
          ),
        );
      case Routes.HEALTH_RENEWAL_REVIEW_PAGE:
        return BlocProvider(
          create: (context) => RenewalReviewCubit(
            journeyManagerRepository: JourneyManagerRepository(),
            renewalPolicyEditingResponse: params['member_details'],
          ),
          child: RenewalReviewPage(),
        );
      case Routes.HEALTH_SUPPORT_PAGE:
        return SupportPage(
          supportUrl: params?['support_url'],
        );
      case Routes.ASP_CLAIMS_CONTACT_PAGE:
        return AarogyaSanjeevaniPage();
      case Routes.LIFE_HOME_PAGE:
        return LifePolicyViewPage(
          policyNumber: params['policy_number'],
        );
      case Routes.AHC_LANDING:
        final policyNumber = params['policy_number'];
        return BlocProvider(
          create: (context) => AhcCubit(
            AhcRepository(),
            HlAnalyticsManager(
              AhcPropertyStore(),
            ),
            policyNumber ?? '',
          ),
          child: AhcLandingPage(),
        );
      case Routes.AHC_SCHEDULE:
        return BlocProvider(
          create: (context) => MedicalTestBookingBloc(
            ahcScheduleArguments:
                params['ahcScheduleArguments'] as AhcScheduleArgumentsModel,
          ),
          child: AhcSchedulePage(),
        );
      case Routes.AHC_TEST_SELECTION_PAGE:
        return AhcTestSelectionPage(
          ahcLandingModel: params['ahcLandingModel'],
          policyNumber: params['policyNumber'],
        );
      case Routes.LIFE_RENEWAL_PAGE:
        final policyNumber = params['policy_number'];
        if (policyNumber == null) {
          AckoToast.show('Policy number not found');
          return CommonScaffold.setScaffold(const SizedBox.shrink());
        }

        final journeyId =
            params['status'] ?? params['card_status'] ?? 'life_renewal';

        return BlocProvider(
          create: (context) => LifeRenewalCubit(
            LifeJmRepository(),
            policyNumber,
            journeyId,
            HlAnalyticsManager(
              LifeRenewalPropertyStore(),
            ),
            UtmParameters(
              utmCampaign: params['utm_campaign'],
              utmSource: params['utm_source'],
              utmMedium: params['utm_medium'],
              utmTerm: params['utm_term'],
            ),
          ),
          child: LifeRenewalPage(),
        );
      case Routes.LIFE_REACTIVATE_PAGE:
        {
          final policyNumber = params['policy_number'];
          final journeyId = params['card_status'] ?? 'life_renewal';
          if (policyNumber == null) {
            AckoToast.show('Policy number not found');
            return CommonScaffold.setScaffold(const SizedBox.shrink());
          }

          return BlocProvider(
            create: (context) => LifeRenewalCubit(
              LifeJmRepository(),
              policyNumber,
              journeyId,
              HlAnalyticsManager(
                LifeRenewalPropertyStore(),
              ),
              UtmParameters(
                utmCampaign: params['utm_campaign'],
                utmSource: params['utm_source'],
                utmMedium: params['utm_medium'],
                utmTerm: params['utm_term'],
              ),
            ),
            child: LifeReactivatePage(),
          );
        }
      case Routes.HEALTH_NAVIGATOR:
        return HealthNavigatorPage(
            path: params['path'],
            assetId: params['asset_id'] ?? params['asset-id'],
            policyId: params['policy_id'] ?? params['policy-id'],
            fromPage: params['from_page'] ?? params['from-page'],
            name: params['name'],
            gender: params['gender'],
            relationship: params['relationship'],
            edit: params['edit'] == true,
            url: params['url']);
      case Routes.LIFE_NAVIGATOR:
        return LifeNavigatorPage(
            path: params['path'],
            policyNo: params['policy_number'] ?? params['policy-number'],
            url: params['url'],
            fromPage: params['from_page'] ?? params['from-page']);
      case Routes.POLICY_DOCUMENTS_SCREEN:
        final fromPage =
            params.containsKey('from_page') ? params['from_page'] : null;
        final documents =
            params.containsKey('documents') ? params['documents'] : null;
        final actionUrl =
            params.containsKey('action_url') ? params['action_url'] : null;
        final policyId =
            params.containsKey('policy_id') ? params['policy_id'] : null;
        final policyNumber = params.containsKey('policy_number')
            ? params['policy_number']
            : null;
        return BlocProvider(
          create: (ctx) => DocumentsDownloadCubit(documents,
              policyId: policyId, policyNumber: policyNumber),
          child: DownloadPolicyDocuments(
            fromPage: fromPage,
            onHelpPressed: () {
              RecurringPaymentClickHandler.handlePaymentNavigation(
                  context, actionUrl);
            },
          ),
        );
      default:
        return getAppRefreshHomeView();
    }
  }

  handleNavigationForPolicyBenefitsBasedOnGB(BuildContext? context) async {
    await healthPoliciesBottomSheet(context);
  }

  handleNavigationForPolicyCardsBasedOnGB(BuildContext? context) async {
    if (context != null) healthAssetsBottomSheet(context);
  }

  Widget handlePolicyBenefitsNavigation() {
    HealthPolicyHolder? selectedAsset = params?['selectedAsset'];
    if ((params.containsKey('policy_id') ||
        params.containsKey('policy_number'))) {
      return BlocProvider<HealthPolicyViewCubit>(
          create: (context) => HealthPolicyViewCubit(
              {"policyId": params['policy_id']},
              params['defaultTab'],
              selectedAsset),
          child: HealthPolicyViewPage());
    } else {
      return BlocProvider<HealthPolicyBloc>(
          create: (context) => HealthPolicyBloc(
              selectedUserHealthPolicy: params?['selectedMember'],
              selectedAsset: selectedAsset,
              selectedHealthPolicyInfo: params?['selectedHealthPolicyInfo']),
          child: HealthPolicyBenefits());
    }
  }

  Widget handlePolicyCardsNavigation() {
    var policyDetails = Map();
    policyDetails["name"] = params['name'] ?? null;
    policyDetails["gender"] = params['gender'] ?? null;
    policyDetails["relationship"] = params['relationship'] ?? null;
    policyDetails["policy_id"] = params['policy_id'] ?? null;
    policyDetails["policy_number"] = params['policy_number'] ?? null;

    var isFromCoveragesPage = params['isFromCoveragesPage'];
    if (isFromCoveragesPage is String) {
      isFromCoveragesPage = isFromCoveragesPage.toLowerCase() == 'true';
    }

    return BlocProvider<HealthCardsCubit>.value(
      value: HealthCardsCubit(
          filter: policyDetails,
          isFromCoveragesPage: isFromCoveragesPage ?? false,
          allPolicies: params['allPolicies'] ?? []),
      child: HealthCardsScreen(),
    );
  }

  Widget getAppRefreshHomeView() {
    Constants.PLATFORM_CHANNEL.invokeMethod("home_page_loaded");
    AnalyticsTrackerManager.instance
        .sendEvent(event: HLPageEvents.VIEW_MY_ACCOUNT_PAGE, properties: {});
    return MultiBlocProvider(
      providers: [
        BlocProvider<TabViewSdui.HomeTabBarViewBloc>(
          create: (context) => TabViewSdui.HomeTabBarViewBloc(),
        ),
        BlocProvider<CommonDataStoreBloc>.value(
          value: globalCommonDataStoreBloc!,
        )
      ],
      child: HomeTabBarView(),
    );
  }
}

Future<void> _triggerScreenName(String? route, dynamic params) async {
  ///For pages like adaptive sdui page / specific use cases,
  ///route name can be passed a param for screen load analytics
  String analyticalRouteName =
      (params != null && params.containsKey('analytical_route_name'))
          ? params['analytical_route_name']
          : route;
  await AnalyticsTrackerManager.instance
      .sendScreenViewEvent(analyticalRouteName, Util.getPlatform());
}

ServiceType? _getServiceType(String key) {
  ServiceType? _serviceType;
  switch (key.toLowerCase()) {
    case 'rto':
      _serviceType = ServiceType.RTO;
      break;
    case 'puc_validity':
      _serviceType = ServiceType.PUC_VALIDITY;
      break;
    case 'challan':
      _serviceType = ServiceType.CHALLAN;
      break;
    case 'vehicle_valuation':
      _serviceType = ServiceType.VALUATION;
      break;
    case 'mobile_repairs':
      _serviceType = ServiceType.MOBILE_REPAIR;
      break;
    case 'puc_center':
      _serviceType = ServiceType.PUC_CENTER;
      break;
    case 'network_hospitals':
      _serviceType = ServiceType.NETWORK_HOSPITALS;
      break;
  }
  return _serviceType;
}

class CustomRoute<T> extends MaterialPageRoute<T> {
  CustomRoute({required WidgetBuilder builder, required RouteSettings settings})
      : super(builder: builder, settings: settings);

  @override
  Duration get transitionDuration => Duration(milliseconds: 150);

  @override
  Widget buildTransitions(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation, Widget currentPage) {
    return FadeTransition(opacity: animation, child: currentPage);
  }
}

class CustomSlideRoute<T> extends MaterialPageRoute<T> {
  CustomSlideRoute(
      {required WidgetBuilder builder, required RouteSettings settings})
      : super(builder: builder, settings: settings);

  @override
  Duration get transitionDuration => Duration(milliseconds: 300);

  @override
  Widget buildTransitions(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation, Widget currentPage) {
    const begin = Offset(0.0, 1.0);

    /// Start at the bottom
    const end = Offset.zero;

    /// End at the top
    var tween = Tween(begin: begin, end: end);

    var offsetAnimation = animation.drive(tween);

    return SlideTransition(
      position: offsetAnimation,
      child: currentPage,
    );
  }
}

class TransparentRoute extends PageRoute {
  TransparentRoute({
    required this.builder,
    RouteSettings? settings,
  }) : super(settings: settings, fullscreenDialog: false);

  final WidgetBuilder builder;

  @override
  bool get opaque => false;

  @override
  Color? get barrierColor => null;

  @override
  String? get barrierLabel => null;

  @override
  bool get maintainState => true;

  @override
  Duration get transitionDuration => const Duration(milliseconds: 350);

  @override
  Widget buildPage(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation) {
    final result = builder(context);
    return Material(
      child: FadeTransition(
        opacity: Tween<double>(begin: 0, end: 1).animate(animation),
        child: Semantics(
          scopesRoute: true,
          explicitChildNodes: true,
          child: result,
        ),
      ),
    );
  }
}
