import 'package:acko_flutter/common/bloc/ApplicatioBloc.dart';
import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/util/route_observer.dart';
import 'package:acko_flutter/common/view/router.dart';
import 'package:acko_flutter/r2d2/events.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:design_module/design_module.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/utilities.dart';

import '../../feature/api_logger/api_logger_wrapper.dart';
import '../util/back_press_route_observer.dart';

BuildContext? mainPageContext;

class Application extends StatefulWidget {
  @override
  _AppSate createState() {
    return _AppSate();
  }
}

class _AppSate extends State<Application> with WidgetsBindingObserver {
  bool _initialRoutePushed = false;

  ///kDebugMode ensure logger is removed during tree shaking
  ///and is only available during debug mode
  final ApiLogger? logger = kDebugMode ? ApiLogger() : null;
  final AppRouter appRouter = AppRouter();
  @override
  void initState() {
    super.initState();
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.light
        .copyWith(statusBarColor: Colors.black.withOpacity(0.15)));
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      preCacheImages();
    });
  }

  void preCacheImages() async {
    if (await ConnectionStatus.instance.isNetworkAvaialable()) {
      Util.preCacheImages(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return routes();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    ConnectionStatus.instance.close();
    super.dispose();
  }

  Widget routes() {
    return BlocProvider<ApplicationBloc>(
        create: (context) => ApplicationBloc(),
        child: MaterialApp(
            builder: (ctx, child) {
              mainPageContext = ctx;
              ScreenSizeHelper.instance.init(ctx);
              return ApiLoggerWrapper(
                widget: child!,
                navigatorKey: navigatorKey,
                apiLogger: logger,
              );
            },
            initialRoute: Routes.APPLICATION,
            navigatorKey: navigatorKey,
            navigatorObservers: [
              AckoRouteObserver.instance,
              BackPressRouteObserver()
            ],
            onGenerateRoute: appRouter.route,
            theme: getApplicationTheme()));
  }

  ThemeData getApplicationTheme() {
    return Theme.of(context).copyWith(
        appBarTheme: Theme.of(context).appBarTheme.copyWith(
              systemOverlayStyle: SystemUiOverlayStyle.dark,
            ),
        primaryColor: color7C47E1,
        scaffoldBackgroundColor: Colors.white,
        bottomSheetTheme: BottomSheetThemeData(
            backgroundColor: Colors.white, surfaceTintColor: Colors.white),
        primaryColorDark: color5E27C9,
        textSelectionTheme: Theme.of(context)
            .textSelectionTheme
            .copyWith(cursorColor: color5B5675),
        colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: color7C47E1,
            ));
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      R2D2Events.instance.trackAppOpenEvent("resume");
    }
  }
}

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
