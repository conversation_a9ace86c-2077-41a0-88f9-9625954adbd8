import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/state_provider/StateProvider.dart';

import '../widgets/asset_view/AssetCircularWidget.dart';

class AssetMemberSelectionCarouselView extends SDUIStatefulWidget {
  AssetMemberSelectionCarouselView({
    required this.circularWidgets,
    required this.onAssetSelection,
    required this.defaultId,
    super.key,
  });

  final List<AssetCircularWidget> circularWidgets;
  final Function(String selectedAsset) onAssetSelection;
  final String defaultId;

  @override
  State<AssetMemberSelectionCarouselView> createState() =>
      _AssetMemberSelectionCarouselViewState();
}

class _AssetMemberSelectionCarouselViewState
    extends State<AssetMemberSelectionCarouselView> implements StateListener {
  late PageController _pageController;
  List<AssetCircularWidget> circularWidgets = List.empty(growable: true);
  String selectedId = "";
  StateProvider _stateProvider = StateProvider();

  @override
  void initState() {
    _stateProvider.subscribe(this);
    selectedId = widget.defaultId;
    circularWidgets = widget.circularWidgets;

    _pageController = PageController(
      initialPage: 0,
      viewportFraction: 0.2,
    );

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _centerListWithSelectedItem();
    });
    super.initState();
  }

  void _centerListWithSelectedItem() {
    int index = circularWidgets.indexWhere((e) => e.id == selectedId);
    if (index > 0) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_pageController.hasClients) {
          _pageController.animateToPage(
            index - 1,
            duration: const Duration(milliseconds: 300),
            curve: Curves.ease,
          );
        }
      });
    }
  }

  void setSelectedId(String id) {
    setState(() {
      selectedId = id;
    });
    _centerListWithSelectedItem();
  }

  void onTapped(String id, BuildContext context) {
    setSelectedId(id);
    widget.onWidgetTapped(context, properties: {"id": id});
  }

  @override
  Widget build(BuildContext context) {
    if (circularWidgets.isEmpty) {
      return const SizedBox.shrink();
    }

    bool isSingleItem = circularWidgets.length == 1;

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: isSingleItem ? null : checkDouble(0.68, context: context),
          child: Stack(
            children: [
              Container(
                height: checkDouble(52, context: context, isHeight: true),
                child: Center(
                  child: isSingleItem
                      ? _buildCircularItem(context, 0)
                      : PageView.builder(
                    controller: _pageController,
                    scrollDirection: Axis.horizontal,
                    itemCount: circularWidgets.length,
                    itemBuilder: (context, index) {
                      return _buildCircularItem(context, index);
                    },
                  ),
                ),
              ),
              if (!isSingleItem && circularWidgets.length > 3)
                Positioned(
                  left: 0,
                  child: IgnorePointer(
                    child: Container(
                      width: 50,
                      height: 48,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Color(0xff101010),
                            Color(0xff101010).withOpacity(0),
                          ],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                      ),
                    ),
                  ),
                ),
              if (!isSingleItem && circularWidgets.length > 3)
                Positioned(
                  right: 0,
                  child: IgnorePointer(
                    child: Container(
                      width: 50,
                      height: 48,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Color(0xff101010),
                            Color(0xff101010).withOpacity(0),
                          ],
                          begin: Alignment.centerRight,
                          end: Alignment.centerLeft,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCircularItem(BuildContext context, int index) {
    bool isSelected = circularWidgets[index].id.toLowerCase() == selectedId.toLowerCase();

    return InkWell(
      onTap: () {
        setSelectedId(circularWidgets[index].id);
        widget.onAssetSelection(circularWidgets[index].id);
      },
      child: Center(
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: isSelected
                ? Border.all(width: 2.0, color: const Color(0xff6bcb79))
                : null,
            color: isSelected ? color121212 : const Color(0xff24202a),
          ),
          height: isSelected ? 48 : 40,
          width: isSelected ? 48 : 40,
          child: circularWidgets[index],
        ),
      ),
    );
  }

  @override
  void onStateChanged(ObserverState state, {data}) {
    if (state == ObserverState.REFRESH_HEALTH_MEMBER_CAROUSEL) {
      selectedId = data['name'];
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        _centerListWithSelectedItem();
      });
    }
  }
}
