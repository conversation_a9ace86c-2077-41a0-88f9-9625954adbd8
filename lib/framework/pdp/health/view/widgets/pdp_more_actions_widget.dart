import 'package:acko_flutter/framework/pdp/health/models/health_assets_details.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/pdp_claim_tile.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:policy_details/policy_details.dart';
import 'package:sdui/sdui.dart';

import '../../../../../common/util/color_constants.dart';
import '../../models/members_list_model.dart';
import '../../models/ui_models/more_actions_model.dart';

class MoreActionsWidget extends StatelessWidget {
  List<MoreActions>? moreActionsList;
  HealthAssetsResponse? assetsResponse;
  HealthAssetDetailsResponseInsured? insured;
  bool? hasLifePolicy;
  Function(String)? tapEvent;
  String? fromPage;
  MemberData? memberData;

  MoreActionsWidget(
      {this.moreActionsList,
      this.assetsResponse,
      this.insured,
      this.hasLifePolicy,
      this.tapEvent,
      this.fromPage = null,
      this.memberData = null});

  @override
  Widget build(BuildContext context) {

    final _bloc = BlocProvider.of<HealthAssetDetailsCubit>(context);

    return Container(
      padding: EdgeInsets.fromLTRB(16, 32, 16, 32),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SDUIText(
            padding: EdgeInsets.only(bottom: 24),
            value: 'More actions',
            textStyle: 'hSmall',
            textColor: Color(0xFF121212),
          ),
          ListView.separated(
            padding: EdgeInsets.zero,
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              return ClaimTile(
                assetResponse: assetsResponse,
                insured: insured,
                moreAction: moreActionsList?[index],
                icon: Icons.arrow_forward_rounded,
                hasLifePolicy: hasLifePolicy,
                tapEvent: tapEvent,
                fromPage: fromPage,
                memberData: memberData,
                advanceCashData: _bloc.advanceCashDetailsData?.advanceCashData,
                isAdvanceCashEnabled: _bloc.isAdvanceCashEnabled,
              );
            },
            separatorBuilder: (context, index) {
              return Padding(
                padding: EdgeInsets.fromLTRB(56, 20, 0, 20),
                child: Divider(
                  color: colorE8E8E8,
                ),
              );
            },
            itemCount: moreActionsList?.length ?? 0,
          )
        ],
      ),
    );
  }
}
