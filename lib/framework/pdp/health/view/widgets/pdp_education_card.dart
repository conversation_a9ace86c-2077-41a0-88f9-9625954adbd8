import 'package:acko_flutter/feature/claims_education/models/claims_education_parameter_config.dart';
import 'package:acko_flutter/framework/pdp/health/models/ui_models/pdp_education_card_model.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/claims_emergency_guide_banner.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:flutter/material.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/constants/constants.dart';

import '../../../../../common/util/color_constants.dart';
import '../../../../../feature/claim/advance_cash/onboarding/data/AdvanceCashState.dart';
import '../../common/tap_handler.dart';

class HealthEducationCard extends StatelessWidget {
  final HealthEducationCardData educationCard;
  final bool? isAdvanceCashEnabled;
  final AdvanceCashData? advanceCashData;
  final Function(String)? tapEvent;

  const HealthEducationCard({
    Key? key,
    required this.educationCard,
    required this.isAdvanceCashEnabled,
    required this.advanceCashData,
    required this.tapEvent,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final _borderContainerStyling = BoxDecoration(
      border: Border.all(
        width: 1,
        color: Colors.transparent,
      ),
      borderRadius: BorderRadius.circular(16),
      gradient: LinearGradient(
        begin: Alignment.centerLeft,
        end: Alignment.centerRight,
        colors: [
          colorEADFFF,
          colorC6E9FF,
        ],
      ),
    );

    return InkWell(
      onTap: () {
        if (tapEvent != null) {
          tapEvent!(educationCard.title ?? '');
        }
        if (educationCard.onTap.isNotNullOrEmpty) {
          HealthTapHandler.educationalCardNavigaton(educationCard.onTap!,
              context, isAdvanceCashEnabled, advanceCashData);
        } else if (educationCard.redirectUrl.isNotNullOrEmpty) {
          final ClaimsEducationParameterConfig parameterConfig =
              ClaimsEducationParameterConfig(
            isAcrEnabled: isAdvanceCashEnabled,
            hasAcrData: advanceCashData != null,
            fromPage: 'health_asset_view',
          );
          final url = parameterConfig.generateUrl(educationCard.redirectUrl);
          if (url.isNullOrEmpty) return;
          Navigator.pushNamed(context, Routes.WEB_PAGE_V2,
              arguments: {"url": url});
        }
      },
      child: Container(
        decoration: _borderContainerStyling,
        clipBehavior: Clip.hardEdge,
        child: Container(
          margin: EdgeInsets.zero,
          decoration: BoxDecoration(
              color: colorFFFFFF, borderRadius: BorderRadius.circular(15)),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    SDUIImage(
                      imageUrl: educationCard.imageUrl,
                      width: 80,
                      height: 80,
                    ),
                    const SizedBox(
                      width: 12,
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: SDUIText(
                                  value: educationCard.title,
                                  alignment: TextAlign.start,
                                  textColor: color451999,
                                  textStyle: 'lSmall',
                                ),
                              ),
                              Icon(
                                Icons.arrow_forward_ios_rounded,
                                size: 20,
                                color: color36354C,
                              )
                            ],
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.center,
                          ),
                          SDUIText(
                            padding: const EdgeInsets.only(top: 12),
                            value: educationCard.description,
                            maxLines: 3,
                            alignment: TextAlign.start,
                            textColor: color4B4B4B,
                            textStyle: 'p2',
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              if (educationCard.emergencyBanner != null)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: EmergencyBannerWidget(
                    emergencyBanner: educationCard.emergencyBanner!,
                    isAcrEnabled: isAdvanceCashEnabled,
                    hasAcrData: advanceCashData != null,
                  ),
                ),
              const SizedBox(
                height: 20,
              )
            ],
          ),
        ),
      ),
    );
  }
}
