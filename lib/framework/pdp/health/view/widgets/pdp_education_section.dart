import 'package:acko_flutter/feature/claim/advance_cash/onboarding/data/AdvanceCashState.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/pdp_education_card.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:policy_details/policy_details.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/utilities.dart';

import '../../../../../common/util/color_constants.dart';
import '../../models/ui_models/pdp_education_card_model.dart';

class HealthEducationSection extends StatelessWidget {
  final HealthEducationData? educationData;
  final bool? isAdvanceCashEnabled;
  final AdvanceCashData? advanceCashData;
  final Function(String)? tapEvent;

  const HealthEducationSection({
    Key? key,
    this.educationData,
    this.isAdvanceCashEnabled,
    this.advanceCashData,
    this.tapEvent,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<HealthAssetDetailsCubit?>();
    return Container(
      color: colorF5F5F5,
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 32),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SDUIText(
            value: 'Not sure how to raise a claim?\nLet us help you. ',
            alignment: TextAlign.start,
            textColor: color040222,
            textStyle: 'hSmall',
            maxLines: 5,
          ),
          if (educationData != null)
            ListView.separated(
              padding: EdgeInsets.only(top: 24),
              itemCount: educationData!.cards.length,
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                HealthEducationCardData card = educationData!.cards[index];
                return HealthEducationCard(
                  educationCard: card,
                  isAdvanceCashEnabled: isAdvanceCashEnabled,
                  advanceCashData: advanceCashData,
                  tapEvent: tapEvent,
                );
              },
              separatorBuilder: (context, index) => SizedBox(height: 16),
            ),
          if (cubit != null && cubit.lifeEducationalSectionList.isNotEmpty)
            ListView.separated(
              itemCount: cubit.lifeEducationalSectionList.length,
              shrinkWrap: true,
              padding: EdgeInsets.only(top: 16),
              physics: NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                HealthEducationCardData card =
                    cubit.lifeEducationalSectionList[index];
                return HealthEducationCard(
                  educationCard: card,
                  isAdvanceCashEnabled: false,
                  advanceCashData: null,
                  tapEvent: (val) {
                    final claimService = cubit.concatenatedSubServices
                        .firstWhereOrNullIterable((element) =>
                            element.serviceId.equalsIgnoreCase('Raise Claim'));
                    final claimTypeIds = claimService?.context?.claimsQuery
                        ?.map((e) => e.id)
                        .toList();
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LifeClaimDocumentsPage(
                          claimTypeIds: List<String>.from(
                            claimTypeIds ?? [],
                          ),
                          selectedClaimId: card.id,
                        ),
                      ),
                    );
                    cubit.handleAssetPageTapEvent((card.id ?? '') + '_claim');
                  },
                );
              },
              separatorBuilder: (context, index) => SizedBox(height: 16),
            ),
        ],
      ),
    );
  }
}
