import 'package:acko_flutter/common/widgets/shimmer.dart';
import 'package:design_module/utilities/color_constants.dart';
import 'package:flutter/material.dart';

class HealthCoveragesShimmer extends StatelessWidget {
  HealthCoveragesShimmer({super.key, this.showV10UI = false});
  bool showV10UI = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const SizedBox(
          height: 12,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
          ),
          child: Shimmer.fromColors(
            child: Container(
              height: 240,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                color: colorFFFFFF,
              ),
            ),
            baseColor: showV10UI ? colorF3F3F3 : color1B1B1B,
            highlightColor: showV10UI ? colorD7D7D7 : color272727,
          ),
        ),
      ],
    );
  }
}
