import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/feature/claims_education/models/claims_education_parameter_config.dart';
import 'package:acko_flutter/framework/pdp/health/models/ui_models/pdp_education_card_model.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:flutter/material.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/constants/constants.dart';

class EmergencyBannerWidget extends StatelessWidget {
  final EmergencyBanner emergencyBanner;
  final bool? isAcrEnabled;
  final bool? hasAcrData;
  final String? uhids;
  final String? name;

  const EmergencyBannerWidget({
    Key? key,
    required this.emergencyBanner,
    this.isAcrEnabled,
    this.hasAcrData,
    this.uhids,
    this.name,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final _emergencyBannerBorderStyling = BoxDecoration(
      border: Border.all(
        width: 1,
        color: Colors.transparent,
      ),
      borderRadius: BorderRadius.circular(12),
      gradient: LinearGradient(
        begin: Alignment.centerLeft,
        end: Alignment.centerRight,
        colors: [
          colorF3C0BE.withOpacity(0.6),
          colorFBE9F6.withOpacity(0.6),
        ],
      ),
    );

    final _emergencyBannerStyling = BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.centerLeft,
        end: Alignment.centerRight,
        colors: [colorFFE9F9, colorFDF7F7],
      ),
      borderRadius: BorderRadius.circular(11),
    );

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 8),
          child: Divider(
            thickness: 1.0,
            height: 1.0,
            color: colorE7E7F0,
          ),
        ),
        InkWell(
          onTap: () {
            final ClaimsEducationParameterConfig parameterConfig =
                ClaimsEducationParameterConfig(
              isAcrEnabled: isAcrEnabled,
              hasAcrData: hasAcrData,
              uhids: uhids,
              name: name,
              fromPage: 'health_asset_view',
            );
            final url =
                parameterConfig.generateUrl(emergencyBanner.redirectUrl);
            if (url.isNullOrEmpty) return;
            Navigator.pushNamed(context, Routes.WEB_PAGE_V2, arguments: {
              "url": url,
            });
          },
          child: Container(
            decoration: _emergencyBannerBorderStyling,
            padding: EdgeInsets.all(1), // Border thickness
            child: Container(
              decoration: _emergencyBannerStyling,
              padding: EdgeInsets.all(12), // Inner padding
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  if(emergencyBanner.imageUrl.isNotNullOrEmpty)
                  SDUIImage(
                    imageUrl: emergencyBanner.imageUrl,
                    width: 24,
                    height: 24,
                  ),
                  SizedBox(width: 10),
                  if(emergencyBanner.text.isNotNullOrEmpty)
                  Expanded(
                    child: SDUIText(
                      value: emergencyBanner.text,
                      alignment: TextAlign.start,
                      textColor: color121212,
                      textStyle: 'p2',
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_rounded,
                    size: 20,
                    color: color36354C,
                  )
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}