import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/framework/pdp/health/models/ui_models/pdp_education_card_model.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/pdp_education_card.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:policy_details/policy_details.dart';
import 'package:sdui/sdui.dart';

class LifeOnlyAllTheWaysToClaim extends StatelessWidget {
  const LifeOnlyAllTheWaysToClaim({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<HealthAssetDetailsCubit>();
    return Container(
      color: colorF5F5F5,
      width: MediaQuery.sizeOf(context).width,
      padding: EdgeInsets.symmetric(
        horizontal: 16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(
            height: 40,
          ),
          SDUIText(
            value: 'All the ways to claim',
            alignment: TextAlign.start,
            textColor: color040222,
            textStyle: 'hSmall',
          ),
          ListView.separated(
            itemCount: cubit.lifeEducationalSectionList.length,
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              HealthEducationCardData card =
                  cubit.lifeEducationalSectionList[index];
              return HealthEducationCard(
                educationCard: card,
                isAdvanceCashEnabled: false,
                advanceCashData: null,
                tapEvent: (val) {
                  final claimService = cubit.concatenatedSubServices
                      .firstWhereOrNullIterable((element) =>
                          element.serviceId.equalsIgnoreCase('Raise Claim'));
                  final claimTypeIds = claimService?.context?.claimsQuery
                      ?.map((e) => e.id)
                      .toList();
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => LifeClaimDocumentsPage(
                        claimTypeIds: List<String>.from(
                          claimTypeIds ?? [],
                        ),
                        selectedClaimId: card.id,
                      ),
                    ),
                  );
                  cubit.handleAssetPageTapEvent((card.id ?? '') + '_claim');
                },
              );
            },
            separatorBuilder: (context, index) => SizedBox(height: 16),
          ),
          const SizedBox(
            height: 40,
          ),
        ],
      ),
    );
  }
}
