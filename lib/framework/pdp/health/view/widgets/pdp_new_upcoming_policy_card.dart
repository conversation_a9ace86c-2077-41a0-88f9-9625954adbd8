import 'package:flutter/material.dart';
import 'package:policy_details/policy_details.dart';
import 'package:sdui/sdui.dart';

import '../../../../../common/util/color_constants.dart';
import '../../../../../util/Utility.dart';

class UpcomingPolicyCardUI extends StatelessWidget {
  HealthPolicyDetailsModel? policyData;
  Function(HealthPolicyDetailsModel?)? onTap;
  bool showAppv10UI = false;

  UpcomingPolicyCardUI(
      {this.policyData, this.onTap, this.showAppv10UI = false});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (onTap != null) {
          onTap!(policyData);
        }
      },
      child: showAppv10UI
          ? Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: LinearGradient(
                  colors: [
                    color5920C5,
                    color5920C5.withOpacity(0.6),
                    const Color(0xFFDFDFDF),
                    const Color(0xFFDFDFDF),
                    const Color(0xFFDFDFDF),
                    const Color(0xFFDFDFDF),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomCenter,
                ),
              ),
              padding: const EdgeInsets.all(2), // thickness of border
              child: UpcomingPolicyCardContentUI(
                policyData: policyData,
              ),
            )
          : UpcomingPolicyCardContentUI(
              policyData: policyData,
            ),
    );
  }
}

class UpcomingPolicyCardContentUI extends StatelessWidget {
  const UpcomingPolicyCardContentUI({
    super.key,
    required this.policyData,
  });

  final HealthPolicyDetailsModel? policyData;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 16, horizontal: 12),
      margin: EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: [
          Color(0xFFFFFFFF).withOpacity(0.024),
          Color(0xFFFFFFFF).withOpacity(0.12)
        ], begin: Alignment.topLeft, end: Alignment.bottomRight),
        border: Border(
            left: BorderSide(
                width: 1, color: Color(0xFFFFFFFF).withOpacity(0.16)),
            top: BorderSide(
                width: 1, color: Color(0xFFFFFFFF).withOpacity(0.16))),
        borderRadius: BorderRadius.all(Radius.circular(16.0)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SDUIText(
                value: policyData?.planName,
                textStyle: 'lSmall',
                maxLines: 3,
                textColor: Color(0xFFFFFFFF),
              ),
              SizedBox(
                height: 4,
              ),
              SDUIText(
                value: policyData?.getProtectionStartsMessage,
                textStyle: 'pXSmall',
                maxLines: 3,
                textColor: Color(0xFFFFFFFF),
              ),
            ],
          )),
          SDUIText(
            value:
                "Starts on ${Util.fullFormatDate(policyData?.formattedStartDateDDMMMYY ?? '')}",
            textStyle: 'lXXSmall',
            textConfiguration: TextConfiguration.colored,
            margin: const EdgeInsets.only(left: 2),
            padding: const EdgeInsets.all(4),
            border: WidgetBorder(cornerRadius: 4),
            alignment: TextAlign.center,
            textColor: Color(0xff5920C5),
            background: WidgetBackground(color: Color(0xffEFE9FB)),
          ),
          SizedBox(width: 10),
          SDUIImage(
            imageUrl: 'assets/images/Arrow_right.svg',
            width: 20,
            height: 20,
            fit: BoxFit.fill,
          )
        ],
      ),
    );
  }
}
