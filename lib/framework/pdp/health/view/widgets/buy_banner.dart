import 'package:acko_core_utilities/acko_core_utilities.dart';
import 'package:acko_flutter/feature/vas/gmc_upsell/model/gmc_upsell_dto.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:flutter/material.dart';
import 'package:sdui/sdui.dart';
import 'package:sprintf/sprintf.dart';

import '../../../../../common/util/color_constants.dart';
import '../../../../../common/util/strings.dart';
import '../../../../../feature/vas/gmc_upsell/view/gmc_upsell_bottomsheet.dart';

class BuyBanner extends StatelessWidget {
  final String? title;
  final String? subtitle;
  final String? imageUrl;
  final String? sumInsured;
  final Function? analyticalEvent;
  final String? tapEvent;
  final String? viewEvent;
  final String? fromPage;
  final BuyType buyType;
  final String? redirectionUrl;
  const BuyBanner({
    super.key,
    this.title,
    this.subtitle,
    this.imageUrl,
    this.sumInsured,
    this.analyticalEvent,
    this.tapEvent,
    this.viewEvent,
    this.fromPage,
    this.buyType = BuyType.gmc_up_sell,
    this.redirectionUrl
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        analyticalEvent?.call();
        if(redirectionUrl.isNotNullOrEmpty) {
           var map = redirectionUrl!.getRoute();
           if (map != null) {
             Navigator.pushNamed(context, map["route"], arguments: map);
           }
        } else {
          HealthGMCUpsellBottomSheet(
            context,
            tapEvent: tapEvent,
            viewEvent: viewEvent,
            fromPage: fromPage,
            buyType: buyType,
          ).showBottomSheet();
        }
      },
      child: Container(
          margin: EdgeInsets.fromLTRB(16, 0, 16, 0),
          padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            image: DecorationImage(
                image: NetworkImage(
                    "https://marketing.ackoassets.com/images/health/upsell/gmc/upsell_asset_card_bg.png"),
                fit: BoxFit.cover),
            border: Border(
                left: BorderSide(
                    width: 1, color: Color(0xFFFFFFFF).withOpacity(0.16)),
                top: BorderSide(
                    width: 1, color: Color(0xFFFFFFFF).withOpacity(0.16))),
            borderRadius: BorderRadius.all(Radius.circular(16.0)),
          ),
          child: _getWidget()),
    );
  }

  _getWidget() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        SDUIImage(
          imageUrl: imageUrl ??
              "https://marketing.ackoassets.com/images/health/upsell/gmc/upsell_bill_illustration.png",
          width: 88.0,
          height: 88.0,
        ),
        SizedBox(width: 20),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SDUIText(
                value: sprintf(title ?? gmc_upsell_card_title, [sumInsured]),
                textColor: colorFFFFFF,
                maxLines: 5,
                textStyle: "lSmall",
              ),
              SizedBox(
                height: 2,
              ),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Flexible(
                      child: SDUIText(
                          value: subtitle ?? gmc_upsell_card_subtitle,
                          textColor: colorFFFFFF,
                          maxLines: 5,
                          textStyle: "pXSmall")),
                  SizedBox(
                    width: 4,
                  ),
                  Icon(
                    Icons.arrow_forward_rounded,
                    color: colorFFFFFF,
                    size: 16.0,
                  )
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}
