class HealthEducationData {
  final List<HealthEducationCardData> cards;

  HealthEducationData({required this.cards});

  factory HealthEducationData.fromJson(List<dynamic> jsonList) {
    return HealthEducationData(
      cards: jsonList
          .map((json) => HealthEducationCardData.fromJson(json))
          .toList(),
    );
  }
}

class HealthEducationCardData {
  String? title;
  String? imageUrl;
  String? description;
  String? onTap;
  String? id;
  String? redirectUrl;
  EmergencyBanner? emergencyBanner;

  HealthEducationCardData({
    this.title,
    this.imageUrl,
    this.description,
    this.onTap,
    this.id,
    this.redirectUrl,
    this.emergencyBanner,
  });

  HealthEducationCardData.fromJson(Map<String, dynamic> json) {
    title = json['title'] ?? '';
    imageUrl = json['imageUrl'] ?? '';
    description = json['description'] ?? '';
    onTap = json['onTap'] ?? '';
    id = json['id'] ?? '';
    redirectUrl = json['redirectUrl'] ?? '';
    emergencyBanner = json['emergencyBanner'] != null
        ? EmergencyBanner.fromJson(json['emergencyBanner'])
        : null;
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'imageUrl': imageUrl,
      'description': description,
      'onTap': onTap,
      'id': id,
      'redirectUrl': redirectUrl,
      if (emergencyBanner != null)
        'emergencyBanner': emergencyBanner!.toJson(),
    };
  }
}

class EmergencyBanner {
  String? imageUrl;
  String? text;
  String? redirectUrl;

  EmergencyBanner({this.imageUrl, this.text, this.redirectUrl});

  EmergencyBanner.fromJson(Map<String, dynamic> json) {
    imageUrl = json['imageUrl'] ?? '';
    text = json['text'] ?? '';
    redirectUrl = json['redirectUrl'] ?? '';
  }

  Map<String, dynamic> toJson() {
    return {
      'imageUrl': imageUrl,
      'text': text,
      'redirectUrl': redirectUrl,
    };
  }
}
