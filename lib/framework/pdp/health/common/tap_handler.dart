import 'package:acko_flutter/feature/claims_education/cubit/claims_education_cubit.dart';
import 'package:acko_flutter/feature/claims_education/repo/claims_education_repo.dart';
import 'package:acko_flutter/feature/claims_education/view/widgets/health_policy_claims_entry_widget.dart';
import 'package:acko_flutter/feature/health_home/models/health_header_data_model.dart';
import 'package:acko_flutter/feature/health_home/models/health_header_policy_details_model.dart';
import 'package:acko_flutter/feature/health_home/models/health_policy_header_response.dart';
import 'package:acko_flutter/framework/pdp/health/common/constants.dart';
import 'package:acko_flutter/framework/pdp/health/repo/health_repo.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/ahc_policy_list_bottom_sheet.dart';
import 'package:acko_flutter/main.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/health_life/page_events/health_life_page_events.dart';
import 'package:analytics/events/health_life/tap_events/health_life_tap_events.dart';
import 'package:design_module/design_module.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:policy_details/policy_details.dart';
import 'package:session_manager_module/session_manager.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/remote_config/remote_config.dart';
import 'package:utilities/state_provider/StateProvider.dart';

import '../../../../common/util/PreferenceHelper.dart';
import '../../../../common/util/strings.dart';
import '../../../../common/view/layout_header.dart';
import '../../../../common/view/toast.dart';
import '../../../../feature/claim/advance_cash/common/advance_cash_navigator.dart';
import '../../../../feature/claim/advance_cash/common/view/advance_cash_ready_reject_page.dart';
import '../../../../feature/claim/advance_cash/onboarding/data/AdvanceCashState.dart';
import '../../../../feature/claim/advance_cash/onboarding/view/advance_cash_ce_on_documents_page.dart';
import '../../../../feature/claim/advance_cash/onboarding/view/advanced_cash_story_page.dart';
import '../../../../feature/claim/claim_intro/customer_education/raise_claim_page_ce.dart';
import '../../../../feature/claim/my_claims_home/bloc/my_claims_bloc.dart';
import '../../../../feature/claim/pre_claim/common/view/cashless_treatment_page.dart';
import '../../../../feature/health_home/bloc/health_home_bloc.dart';
import '../../../../feature/health_policy/repository/health_policy_repository.dart';
import '../../../../feature/profile_completion/bloc/bloc_singleton_instance.dart';
import '../../../../feature/profile_completion/bloc/profile_completion_bloc.dart';
import '../../../../feature/profile_completion/model/profile_completion_model.dart';
import '../../../../feature/recurring_payment/ui/recurring_payment_overlays.dart';
import '../../../../feature/vas/vas_bottomsheet.dart';
import '../../../../util/health/health_constants.dart';
import '../models/health_assets_details.dart';
import '../models/health_ecards_model.dart';
import '../models/health_policies_basic_details.dart';
import '../models/members_list_model.dart';
import '../view/widgets/pdp_asset_document_download_sheet.dart';
import '../view/widgets/pdp_help_widget.dart';

class HealthTapHandler {
  static void gridOnTapHandler(
      String? action, String? actionUrl, BuildContext context,
      {bool? isACEnabled,
      AdvanceCashData? advanceCashData,
      HealthAssetsResponse? assetResponse,
      HealthAssetDetailsResponseInsured? insured,
      HealthPolicyDetailsModel? policyDetails,
      HealthPolicyBasicInfo? policyInfo,
      List<HealthPolicyDocuments>? documents,
      List<AssetPolicyDocumentModel>? assetPolicyDocumentModel,
      HealthEcardModel? eCardsModel,
      PolicyInfo? policy,
      PolicyAction? service,
      String? ctaType,
      Map? pdpFilterMap,
      bool? hasLifePolicy,
      String? fromPage = null,
      MemberData? memberData = null,
      bool isAsp = false,
      bool isGmcPolicy = false,
      String? productType = null}) {
    bool isArogyaSanjeevaniPolicy = insured?.policies
            ?.any((element) => element.isAarogyaSanjeevaniPolicy) ??
        false;
    bool isASP = policy?.isAarogyaSanjeevaniPolicy ?? isAsp;
    bool isGMCP = policy?.isGMCPolicy ?? isGmcPolicy;

    if (action.isNotNullAndEmpty) {
      switch (action) {
        case "cashless_learning":
          navigateToRaiseClaimPage(context, isArogyaSanjeevaniPolicy);
          break;
        case 'network_hospitals':
          loadNetworkHospitalsModule(
              context, 'health_asset_view', pdpFilterMap);
          break;
        case 'claim_sheet':
          context.showAckoAnimatedBottomSheet(
            child: BlocProvider(
              create: (context) => ClaimsEducationCubit(selectedAsset: insured, policyNumber: policyDetails?.policyNumber, fromPage: fromPage ?? 'health_policy_view', page: 'Claims bottom sheet', claimesEducationRepo: ClaimsEducationRepository(), healthRepo: HealthRepo(AggregatedPageMode.policy)),
              child: HealthPolicyClaimsEntryWidget(popOnAction: true,),
            ),
          );
          break;
        case 'load_claims_list':
        case 'load_claims':
          Navigator.pushNamed(context, Routes.HEALTH_CLAIMS_LIST_PAGE,
              arguments: {"from_page": fromPage, "selectedAsset": insured});
          break;
        case "view_all_claims_v2":
          var (uhids, name) = insured?.getUhidsAndName() ?? (null, null);
          var jsonData = RemoteConfigInstance.instance.getData(RemoteConfigKeysSet.CLAIMS_ENTRY_CONFIG);
          final assetViewClaim = jsonData['asset_view_claim'] as String? ?? '${Constants.BASE_URL}gi/p/health/claim/view-claims?hide_app_bar=true';
          Navigator.pushNamed(context, Routes.WEB_PAGE_V2,
              arguments: {'url': "$assetViewClaim&uhids=$uhids&name=$name&isAcrEnabled=$isACEnabled&hasAcrData=${advanceCashData != null}"});
          break;
        case "register_reimbursement_claim":
          getReimbursedNavigation(context, fromPage, insured);
          break;
        case "cashless_treatment":
          cashlessTreatmentNavigation(context);
          break;
        case "load_advanced_cash":
          getAdvancedCashCta(context, insured, isACEnabled, advanceCashData);
          break;
        case 'load_order_medicine':
        case 'load_book_lab_test':
        case 'load_doctor_on_call':
          if (action.isNotNullAndEmpty)
            handleBottomSheetViewEvent(
                insured, hasLifePolicy ?? false, getBottomSheetType(action!));
          HealthVASBottomSheet(context, getVAS(action!)!, "asset_page",
              tapEvent: () => handleBottomSheetTapEvent(
                  insured,
                  hasLifePolicy ?? false,
                  getBottomSheetType(action),
                  getTapCTA(action))).showBottomSheet();
          break;
        case 'download_ecard':
          handleDownloadEcard(context, pdpFilterMap, ctaType: ctaType);
          break;
        case 'do_endorsements':
        case 'see_payment_schedule':
          if (action.isNotNullAndEmpty)
            handleEndorsementsAndPaymentSchedule(
                context, action!, actionUrl, isASP, isGMCP, productType);
          break;
        case 'get_help':
          RecurringPaymentClickHandler.handlePaymentNavigation(
              context, actionUrl);
          break;
        case 'do_cancel':
          AckoToast.show("Feature currently not supported");
          break;
        case 'download_documents':
          if (ctaType.equalsIgnoreCase('asset_view')) {
            handleBottomSheetViewEvent(
                insured, hasLifePolicy ?? false, getBottomSheetType(action!));

            /// remove this from here we don't want to call UI in here
            context.showAckoModalBottomSheet(
                child: DownloadDocumentSheet(
                    assetPolicyDocumentModel: assetPolicyDocumentModel,
                    tapEvent: () => handleBottomSheetTapEvent(
                        insured,
                        hasLifePolicy ?? false,
                        getBottomSheetType(action),
                        getTapCTA(action))));
          } else
            handleDownloadDocuments(context, documents, fromPage, actionUrl);
          break;
        case 'get_help_health':
          CommonHeaders.handleHealthCta(context);
          break;
        case 'get_new_health_help':
          handleHelp(context,
              hasLifePolicy: hasLifePolicy ?? false,
              healthProposals: insured?.proposals,
              selectedAsset: insured);
          break;
        case 'ac_education':
          getAdvancedCashStoryNavigation(context);
          break;
        case 'ac_documents_page':
          handleAdvancedCashNavigation(
              context, insured, isACEnabled, advanceCashData);
          break;
        case 'view_member_info':
          handleViewMemberInfoNavigation(context, memberData);
        case 'support':
          Navigator.pushNamed(context, Routes.SUPPORT,
              arguments: {'needAppBar': true});
        case 'ahc_landing_page':
          if (service != null)
            handleAhcNavigation(context: context, service: service);
        default:
          AckoToast.show(something_went_wrong);
      }
    }
  }

  static void handleAhcNavigation({
    required BuildContext context,
    required PolicyAction service,
  }) {
    if (service.applicablePolicies.isNullOrEmpty) return;
    if (service.applicablePolicies!.length == 1) {
      Navigator.of(context).pushNamed(
        Routes.AHC_LANDING,
        arguments: {
          'policy_number': service.applicablePolicies![0].policyNumber,
        },
      );
      return;
    }

    // if multiple policies are available, show the policy selection screen
    context.showAckoModalBottomSheet(
      child: AhcPolicyListBottomSheet(
        policies: service.applicablePolicies,
      ),
    );
  }

  static String getTapCTA(String action) {
    String tapCTA = '';
    switch (action) {
      case 'load_order_medicine':
        tapCTA = 'order_medicine';
      case 'load_book_lab_test':
        tapCTA = 'lab_tests';
      case 'load_doctor_on_call':
        tapCTA = 'teleconsults';
      case 'download_documents':
      case 'get_new_health_help':
        tapCTA = 'policy_card';
      case 'ahc_landing_page':
        tapCTA = 'annual_health_checkup';
    }
    return tapCTA;
  }

  static String getBottomSheetType(String action) {
    String bottomSheetType = '';
    switch (action) {
      case 'load_order_medicine':
        bottomSheetType = 'order_medicine_banner';
      case 'load_book_lab_test':
        bottomSheetType = 'lab_tests_banner';
      case 'load_doctor_on_call':
        bottomSheetType = 'teleconsults_banner';
      case 'download_documents':
        bottomSheetType = 'doc_download_select_policy';
      case 'get_new_health_help':
        bottomSheetType = 'get_help_select_policy';
      case 'ahc_landing_page':
        bottomSheetType = 'annual_health_checkup';
    }
    return bottomSheetType;
  }

  static void handleViewMemberInfoNavigation(
      BuildContext context, MemberData? memberData) async {
    StateProvider _stateProvider = StateProvider();
    ProfileCompletionBloc? profileCompletionBloc =
        ProfileCompeltionBlocSingletonInstance.instance.blocInstance;
    AbhaDetails? abhaDetails = profileCompletionBloc
        .profileCompletionModel?.familyMembers
        ?.where((element) =>
            element.name.equalsIgnoreCase(memberData?.title) &&
            element.dob == memberData?.dob &&
            element.relation.equalsIgnoreCase(memberData?.subtitle))
        .firstOrNull
        ?.abhaDetails;
    String? phoneNo = memberData?.subtitle == 'Self'
        ? await getStringPrefs(StringDataSharedPreferenceKeys.MOBILE_NUMBER)
        : null;
    ProfileFamilyMembers? familyMember = ProfileFamilyMembers(
        ekey: memberData?.ekey ?? "",
        name: memberData?.title ?? "",
        phone: phoneNo,
        relation: memberData?.subtitle ?? "",
        dob: memberData?.dob ?? "",
        gender: memberData?.gender ?? "",
        abhaDetails: abhaDetails);
    Navigator.pushNamed(context, Routes.PROFILE_FAMILY_MEMBER_DETAILED_VIEW,
        arguments: {
          "member_details": familyMember, //familyMemberData,
        }).then((value) {
      if (value is String) {
        if (value.equalsIgnoreCase("Successfully removed family member")) {
          handleNavigation(context, _stateProvider);
        } else {
          _stateProvider.notify(ObserverState.REFRESH_HEALTH_ASSET_VIEW);
        }
      } else if (abhaDetails?.abhaNumber == null) {
        //Added to update abha details once we come back from profile screen else abha wont be updated
        _stateProvider.notify(ObserverState.REFRESH_HEALTH_ASSET_VIEW);
      }
    });
  }

  static Future<void> handleNavigation(
      BuildContext context, StateProvider stateProvider) async {
    try {
      await Future.delayed(Duration(seconds: 5));
      Navigator.of(context).popUntil(ModalRoute.withName(Routes.APP_HOME));
      stateProvider.notify(ObserverState.CHANGE_TAB, data: {"index": 1});
    } catch (e) {
      logException(Exception("Error in navigating to home page"));
    }
  }

  static void handleBottomSheetViewEvent(
      HealthAssetDetailsResponseInsured? selectedAsset,
      bool hasLifePolicy,
      String bottomSheetType) {
    String product = getProductInfo(selectedAsset, hasLifePolicy);
    String memberType = selectedAsset?.relationship ?? '';
    AnalyticsTrackerManager.instance
        .sendEvent(event: HLPageEvents.VIEW_BOTTOM_SHEET, properties: {
      'product': product,
      'member_type': memberType,
      'bottom_sheet_type': bottomSheetType,
      'page_name': "l1_asset_view_page"
    });
  }

  static void handleBottomSheetTapEvent(
      HealthAssetDetailsResponseInsured? selectedAsset,
      bool hasLifePolicy,
      String bottomSheetType,
      String tapCTA) {
    String product = getProductInfo(selectedAsset, hasLifePolicy);
    String memberType = selectedAsset?.relationship ?? '';
    AnalyticsTrackerManager.instance
        .sendEvent(event: HLTrackEvents.TAP_ASSET_BOTTOM_SHEET, properties: {
      'product': product,
      'member_type': memberType,
      'tap_cta': tapCTA,
      'bottom_sheet_type': bottomSheetType,
      'page_name': "l1_asset_view_page"
    });
  }

  static String getProductInfo(
      HealthAssetDetailsResponseInsured? selectedAsset, bool hasLifePolicy) {
    List<String> products = [
      if ((selectedAsset?.retailPoliciesCount ?? 0) > 0) 'retail_health',
      if ((selectedAsset?.gmcPoliciesCount ?? 0) > 0) 'gmc',
      if (hasLifePolicy) 'retail_life',
    ];

    if (products.length > 1) {
      return products.join(',');
    } else {
      return products.isEmpty ? 'none' : products.first;
    }
  }

  static void handleReimbursementNavigation(BuildContext context) {
    List<List<String>> _createArray() {
      final _list = [
        'Provide treatment information',
        'Upload documents',
        'Share account details',
        'Get the settlement amount',
      ];
      final _clickableList = ['', 'See the list of documents', '', ''];
      final arr = [_list, _clickableList];
      return arr;
    }

    final subtitleList = [
      'Enter the patient’s information, admission date, claim amount and treatment details.',
      'Upload the documents related to your treatment',
      'Enter the account number and IFSC code',
      'We will transfer the approved settlement amount to your account'
    ];

    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => CashlessTreatmentPage(
                  treatmentDetails: _createArray(),
                  subTitleList: subtitleList,
                  title: 'Reimbursement',
                  subtitle:
                      'Get treated at any hospital and we will send you the amount after the treatment is completed',
                  buttonText: 'Submit a claim',
                )));
  }

  static void handleACNavigation(BuildContext context,
      bool? isEligibleForAdvanceCash, AdvanceCashData? advanceCashData) {
    List<List<String>> _createArray() {
      final _list = [
        'Apply for Advance Cash on the ACKO app',
        'Get quick approval',
        'Scan the hospital’s QR code to pay or pay via account transfer',
        'Get discharged and submit medical documents',
      ];
      final _clickableList = ['', '', '', ''];
      final arr = [_list, _clickableList];
      return arr;
    }

    final subtitleList = [
      'Enter the patient’s information, treatment details and expected cost.',
      'We will send the approved cash to your ACKO app',
      'You can do this from the ACKO app',
      'Upload final bills and start the claim settlement process at your convenience'
    ];

    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => CashlessTreatmentPage(
                treatmentDetails: _createArray(),
                subTitleList: subtitleList,
                title: 'Advance cash',
                subtitle:
                    'Get money transferred to your ACKO app in advance to pay for your planned treatment',
                buttonText: 'Get advance cash',
                onACTapped: () {
                  handleACRNavigation(
                      context, isEligibleForAdvanceCash, advanceCashData);
                })));
  }

  static handleACRNavigation(BuildContext context,
      bool? isEligibleForAdvanceCash, AdvanceCashData? advanceCashData) async {
    if (isEligibleForAdvanceCash != null) {
      if (advanceCashData != null) {
        Navigator.of(context).push(MaterialPageRoute(
            builder: (_) =>
                AdvanceCashReadyRejectPage('ac_ready', "Health PDP")));
      } else {
        final result = await Navigator.pushNamed(
            context, Routes.FILE_HEALTH_FNOL, arguments: {
          "load_from_draft": false,
          "is_advance_cash_flow": true
        });
        if (result is MaterialPageRoute) Navigator.of(context).push(result);
      }
    }
  }

  static void navigateToRaiseClaimPage(
      BuildContext context, bool isArogyaSanjeevaniPolicy,
      {int initialTabSelection = 1}) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RaiseClaimPage(
          initialTabSelection: initialTabSelection,
          isArogyaSanjeevaniPolicy: isArogyaSanjeevaniPolicy,
        ),
      ),
    );
  }

  static void handleDownloadEcard(BuildContext context, Map? pdpFilterMap,
      {String? ctaType}) {
    if (pdpFilterMap.isNotNullAndEmpty) {
      if (ctaType.equalsIgnoreCase('asset_view')) {
        Navigator.pushNamed(context, Routes.HEALTH_POLICY_CARDS, arguments: {
          'name': pdpFilterMap!['name'],
          'relationship': pdpFilterMap['relationship'],
          'gender': pdpFilterMap['gender'],
        });
      } else {
        if (pdpFilterMap.isNotNullOrEmpty &&
            pdpFilterMap!.containsKey('policy_number'))
          Map<String, dynamic> params = {};
        Navigator.pushNamed(context, Routes.HEALTH_POLICY_CARDS, arguments: {
          'policy_number': pdpFilterMap!['policy_number'],
          'allPolicies': pdpFilterMap['allPolicies']
        });
      }
    } else {
      AckoToast.show("Unable to view e-cards");
    }
  }

  static void handleEndorsementsAndPaymentSchedule(
      BuildContext context,
      String action,
      String? actionUrl,
      bool isASP,
      bool isGMCP,
      String? productType) {
    StateProvider _stateProvider = StateProvider();
    if (actionUrl.isNotNullOrEmpty) {
      final headerText =
          'hide_app_bar=false&hide_header=true&hide_back_arrow=true&feature=${HealthConstants.HEALTH_RETAIL}';
      final urlString = actionUrl!.contains('?')
          ? '${actionUrl}&$headerText'
          : '${actionUrl}?$headerText';
      Navigator.pushNamed(context, Routes.WEB_PAGE_V2, arguments: {
        "url": urlString,
      }).then((value) {
        _stateProvider.notify(ObserverState.MyAccount_Refresh);
        _stateProvider.notify(ObserverState.REFRESH_HEALTH_ASSET_VIEW);
      });
    } else if (isASP) {
      Navigator.of(context).pushNamed(Routes.ASP_CLAIMS_CONTACT_PAGE);
    } else if (isGMCP) {
      Navigator.pushNamed(context, Routes.HEALTH_EDIT_ENDORSEMENT,
          arguments: {"product_type": productType});
    } else {
      AckoToast.show("Endorsement not possible");
    }
  }

  static void handleDownloadDocuments(
      BuildContext context,
      List<HealthPolicyDocuments>? documents,
      String? fromPage,
      String? actionUrl) {
    if (documents.isNotNullOrEmpty) {
      Navigator.of(context).pushNamed(Routes.POLICY_DOCUMENTS_SCREEN,
          arguments: {
            "from_page": fromPage,
            "action_url": actionUrl,
            "documents": documents
          });
    } else {
      AckoToast.show("No documents to download...");
    }
  }

  static VAS? getVAS(String action) {
    switch (action) {
      case 'load_order_medicine':
        return VAS.medicine;
      case 'load_book_lab_test':
        return VAS.lab;
      case 'load_doctor_on_call':
        return VAS.doctorOnCall;
    }
    return null;
  }

  static getReimbursedNavigation(context, String? fromPage,
      HealthAssetDetailsResponseInsured? assetResponse) async {
    Map<String, dynamic>? loadFromDraft = await getDraftedClaimFromPrefs();
    final result = await Navigator.pushNamed(context, Routes.FNOL_INTRO,
        arguments: {
          "load_from_draft": (loadFromDraft != null && loadFromDraft.isNotEmpty)
        });
    if (result != null && !(result is bool)) {
      if (result is Function) {
        result.call();
      } else if (result is MaterialPageRoute) {
        Navigator.push(context, result);
      } else {
        Navigator.pushNamed(context, Routes.HEALTH_CLAIMS_LIST_PAGE,
            arguments: {"from_page": fromPage, "selectedAsset": assetResponse});
      }
    }
  }

  static getAdvancedCashStoryNavigation(context) {
    ACRNavigationHandler.handleNavigationForStatus(context,
        page: "Health PDP",
        cta: ACRNavigationHandler.story,
        acrSource: AcLoadedFrom.homePageTutorialCard);
  }

  static getAdvancedCashCta(
      BuildContext context,
      HealthAssetDetailsResponseInsured? insured,
      bool? isEligibleForAdvanceCash,
      AdvanceCashData? advanceCashData) async {
    bool? hasAcrStoryPageShown = await getBoolPrefs(
            BoolDataSharedPreferenceKeys.IS_ACR_STORY_PAGES_SHOWN) ??
        false;
    String status = advanceCashData?.cta?.status ?? "New";
    AnalyticsTrackerManager.instance.sendEvent(
        event: HLTrackEvents.TAP_BTN_ADVCASH_START,
        properties: {"status": status, "page": "Health PDP"});

    if (isEligibleForAdvanceCash != null) {
      if (advanceCashData != null) {
        Navigator.of(context).push(MaterialPageRoute(
            builder: (_) =>
                AdvanceCashReadyRejectPage('ac_ready', "Health PDP")));
      } else if (!hasAcrStoryPageShown) {
        ACRNavigationHandler.handleNavigationForStatus(context,
            page: "Health PDP", cta: ACRNavigationHandler.story);
      } else {
        if (!(hasOnlyRetailPolicies(insured)) && (isEligibleForAdvanceCash)) {
          ACRNavigationHandler.handleNavigationForStatus(context,
              page: "Health PDP", cta: ACRNavigationHandler.intro);
        }
      }
    }
  }

  static bool hasOnlyRetailPolicies(
      HealthAssetDetailsResponseInsured? insured) {
    bool hasEnterprisePolicy = false;

    insured?.policies?.forEach((element) {
      if (element.productType == "enterprise") hasEnterprisePolicy = true;
    });

    return !hasEnterprisePolicy;
  }

  static loadNetworkHospitalsModule(
      context, screenName, Map? pdpFilterMap) async {
    LatLng? locationDetails = (await Permission.location.status.isGranted)
        ? await getLocationLatlng()
        : null;
    return Navigator.pushNamed(context, Routes.NETWORK_HOSPITALS_LIST,
        arguments: {
          "place_latlong": locationDetails,
          "from_screen": screenName,
          "plan_name": pdpFilterMap?['plan_name'],
        });
  }

  static loadHealthBuyJourney(context) {
    Navigator.pushNamed(context, Routes.WEB_PAGE_V2, arguments: {
      "url": Constants.BASE_URL +
          'gi/p/health/segmentV2?UTM_source=asset_view&hide_app_bar=true',
    });
  }

  static cashlessTreatmentNavigation(context) async {
    HealthHeaderDataModel? healthPoliciesResponse =
        await HealthHeaderApiRepo.instance.getHealthHeaderData();
    List<List<String>> _createArray() {
      final _list = [
        'Check coverage details',
        'Pick a cashless hospital',
        'Register for cashless treatment',
        'The hospital will request us to check and approve the payment'
      ];
      final _clickableList = [
        'See policy coverages',
        'See network hospitals',
        'Download e-cards',
        '',
        ''
      ];
      if (healthPoliciesResponse.activePolicies.isNullOrEmpty &&
          healthPoliciesResponse.onHoldPolicies.isNullOrEmpty) {
        _clickableList.removeWhere((element) => element == 'Download e-cards');
      }
      final arr = [_list, _clickableList];
      return arr;
    }

    final subtextList = [
      'Check if your planned treatment is covered before hospitalisation',
      'Search for ACKO network hospitals and pick your preferred hospital',
      'Provide the patient’s e-card at the hospital and register for a cashless claim',
      'We will pay the hospital directly after the bill amount is approved'
    ];
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => CashlessTreatmentPage(
                  treatmentDetails: _createArray(),
                  subTitleList: subtextList,
                )));
  }

  static callHelpPage(BuildContext context, String url, String productType) {
    if (url == Constants.TERM_LIFE_HELP_HOME) {
      Navigator.push(
          context, MaterialPageRoute(builder: (context) => LifeSupportPage()));
      return;
    }
    if (url == Constants.ENTERPRISE_HELP_PAGE ||
        url == Constants.RETAIL_HEALTH_HELP_PAGE) {
      Navigator.pushNamed(
        context,
        Routes.HEALTH_SUPPORT_PAGE,
        arguments: {
          'support_url': url,
        },
      );
      return;
    }

    final headerText =
        'hide_app_bar=false&hide_header=true&hide_back_arrow=true';
    url += url.contains('?') ? '&$headerText' : '?$headerText';

    Navigator.pushNamed(context, Routes.WEB_PAGE_V2, arguments: {
      'url': url,
    });
  }

  static loadWebJourneys(context, String url, {bool hideAppbar = true}) {
    final headerText = 'hide_app_bar=$hideAppbar';
    url += url.contains('?') ? '&$headerText' : '?$headerText';
    Navigator.pushNamed(context, Routes.WEB_PAGE_V2, arguments: {"url": url});
  }

  // todo: improve
  static handleHelp(BuildContext context,
      {bool hasLifePolicy = false,
      bool hasLifeProposal = false,
      List<HealthPolicyDetailsModel>? healthProposals = null,
      HealthAssetDetailsResponseInsured? selectedAsset}) async {
    List<HealthPolicyDetailsModel> policies = selectedAsset?.policies ?? [];
    //remove policies which are not in selectedAsset.allPolicies by comparing policyNumber
    if (selectedAsset != null &&
        selectedAsset.policies != null &&
        selectedAsset.policies!.isNotEmpty &&
        policies.isNotNullOrEmpty) {
      policies.removeWhere((element) => !selectedAsset.policies!
          .any((policy) => policy.policyNumber == element.policyNumber));
      policies.removeWhere((element) =>
          element.policyId.isNotNullOrEmpty &&
          element.productType == HealthConstants.ENTERPRISE_PRODUCT &&
          ((element.policyType?.equalsIgnoreCase('addon') ?? false) ||
              (element.policyType?.equalsIgnoreCase('topup') ?? false)));
    }

    // todo: not getting any policies update help logic
    if (policies.isNullOrEmpty &&
        hasLifePolicy == true &&
        healthProposals.isNullOrEmpty) {
      callHelpPage(context, Constants.TERM_LIFE_HELP_HOME, "life");
    } else if (policies.isNotNullOrEmpty &&
        policies.length == 1 &&
        healthProposals.isNullOrEmpty &&
        hasLifePolicy == false) {
      String? type = policies[0].productType;
      if (type.isNotNullOrEmpty) {
        callHelpPage(
            context,
            (type == "retail")
                ? Constants.RETAIL_HEALTH_HELP_PAGE
                : (type == Constants.TERM_LIFE_POLICY)
                    ? Constants.TERM_LIFE_HELP_HOME
                    : Constants.ENTERPRISE_HELP_PAGE,
            type!);
      }
    } else if (policies.isNullOrEmpty &&
        (healthProposals?.length ?? 0) <= 1 &&
        hasLifePolicy == false) {
      String? type = healthProposals?.firstOrNull?.productType ?? 'retail';
      callHelpPage(
          context,
          (type == "retail")
              ? Constants.RETAIL_HEALTH_HELP_PAGE
              : (type == Constants.TERM_LIFE_POLICY)
                  ? Constants.TERM_LIFE_HELP_HOME
                  : Constants.ENTERPRISE_HELP_PAGE,
          type!);
    } else {
      handleBottomSheetViewEvent(
          selectedAsset, hasLifePolicy, 'get_help_select_policy');
      context.showAckoModalBottomSheet(
          child: HealthPolicySelectSheet(
        policies: policies,
        healthProposals: healthProposals,
        hasLifePolicy: hasLifePolicy,
        hasLifeProposal: hasLifeProposal,
        tapEvent: () => handleBottomSheetTapEvent(selectedAsset, hasLifePolicy,
            'get_help_select_policy', 'policy_card'),
      ));
    }
  }

  static educationalCardNavigaton(String action, BuildContext context,
      bool? isEligibleForAdvanceCash, AdvanceCashData? advanceCashData,
      {bool hasOnlyAspPolicies = false}) {
    switch (action) {
      case "cashless_treatment":
        HealthTapHandler.gridOnTapHandler('cashless_treatment', null, context);
        break;
      case "reimbursement":
        HealthTapHandler.handleReimbursementNavigation(context);
        break;
      case 'ac_education':
        HealthTapHandler.handleACNavigation(
            context, isEligibleForAdvanceCash, advanceCashData);
        break;
    }
  }

  static handleAdvancedCashNavigation(
      BuildContext context,
      HealthAssetDetailsResponseInsured? policies,
      bool? isEligibleForAdvanceCash,
      AdvanceCashData? advanceCashData) async {
    HealthHeaderDataModel? healthPoliciesResponse =
        await HealthHeaderApiRepo.instance.getHealthHeaderData();
    List<HealthHeaderPolicyDetailsModel> healthPolicies =
        healthPoliciesResponse.actualPolicies(false);
    String? textToShowInNote;
    bool hasGmcAndRetail = false;
    bool hasGmcPolicy = false;
    bool hasRetailPolicy = false;
    healthPolicies.forEach((element) {
      if (element.productType == "enterprise" && !hasGmcPolicy) {
        textToShowInNote = element.planName;
        hasGmcPolicy = true;
      } else if (element.productType == "retail" && !hasRetailPolicy)
        hasRetailPolicy = true;
    });
    hasGmcAndRetail = (hasGmcPolicy && hasRetailPolicy);
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => AdvanceCashCEOnDocumentsPage(
                    hasGmcAndRetail,
                    textToShowInNote,
                    isEligibleForAdvanceCash,
                    advanceCashData, () {
                  HealthTapHandler.educationalCardNavigaton('ac_education',
                      context, isEligibleForAdvanceCash, advanceCashData);
                })));
  }
}
