import 'dart:async';
import 'dart:collection';
import 'dart:convert';

import 'package:acko_flutter/common/util/PreferenceHelper.dart';
import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/feature/claim/advance_cash/onboarding/data/AdvanceCashState.dart';
import 'package:acko_flutter/feature/health_home/models/health_header_data_model.dart';
import 'package:acko_flutter/feature/health_home/models/health_header_policy_details_model.dart';
import 'package:acko_flutter/feature/health_home/models/health_policy_header_response.dart';
import 'package:acko_flutter/feature/health_policy/model/policy_cards_model.dart';
import 'package:acko_flutter/feature/health_policy/repository/health_policy_repository.dart';
import 'package:acko_flutter/framework/pdp/health/common/constants.dart';
import 'package:acko_flutter/framework/pdp/health/models/life_my_account_model.dart';
import 'package:acko_flutter/main.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_flutter/util/health/health_constants.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:intl/intl.dart';
import 'package:networking_module/networking_module.dart';
import 'package:networking_module/util/error/failure.dart';
import 'package:policy_details/policy_details.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/domain/models/hl_cards_data.dart';
import 'package:session_manager_module/SessionManager/session_manager_module.dart';
import 'package:session_manager_module/StorageManager/shared_preferences_storage.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/remote_config/remote_config.dart';
import 'package:utilities/core/base_model.dart' as BaseModel;
import '../../../../feature/app_policy_page/model/policy_list_response.dart';
import '../../../../feature/health_commons/health_network_layer.dart';
import '../../../../feature/vas/gmc_upsell/model/gmc_upsell_dto.dart';
import '../../../../network/ApiResponse.dart';
import '../../../../network/graphql_queries/policies_query.dart';
import '../models/health_assets_details.dart';
import '../models/health_ecards_model.dart';
import '../models/health_page_crosssell_model.dart';
import '../models/health_policies_basic_details.dart';

class HealthRepo {
  HealthRepo(AggregatedPageMode pdpMode) : super();
  HealthPoliciesApiRepo _headerApiRepo = HealthPoliciesApiRepo.instance;
  HealthPolicyRepository policyRepo = HealthPolicyRepository();
  HealthAssetDetailsRepo healthAssetRepo = HealthAssetDetailsRepo();

  HealthHeaderDataModel? _policiesHeaderDetails;
  HealthAssetsResponse? _assetsResponse;

  getPoliciesHeaderDetails() async {
    if (_policiesHeaderDetails == null)
      _policiesHeaderDetails = await _headerApiRepo.getHealthHeaderData();
  }

  clearResponse() {
    _assetsResponse = null;
    _policiesHeaderDetails = null;
    _headerApiRepo.clearPersistedResponse();
  }

  AdvanceCashDiscoveryState? getAdvaceCashDiscoveryState() => _policiesHeaderDetails?.advanceCashDiscoveryState;


  Future<HealthAssetsResponse?> getPoliciesForAsset(Map assetFilter) async {
    if (assetFilter.containsKey("name") &&
        assetFilter.containsKey("gender") &&
        assetFilter.containsKey("relationship")) {
      String? name = assetFilter["name"] ?? null;
      String? gender = assetFilter["gender"] ?? null;
      String? relationship = assetFilter["relationship"] ?? null;
      String? dob = assetFilter["dob"] ?? null;
      await getPoliciesHeaderDetails();
      DateTime? extremeStartDate =
          _policiesHeaderDetails?.getExtremeDatesForAllPolicies().firstOrNull;
      DateTime? extremeEndDate =
          _policiesHeaderDetails?.getExtremeDatesForAllPolicies().lastOrNull;
      if (_policiesHeaderDetails != null) {
        if (extremeStartDate != null && extremeEndDate != null) {
          _assetsResponse = await getHealthAssets(
              extremeStartDate, extremeEndDate,
              name: name, gender: gender, relationship: relationship, dob: dob);
        }
        if (_assetsResponse?.error == null) {
          return Future.value(_assetsResponse);
        } else {
          logException(Exception(
              "API Error : getPoliciesForAsset :: ${_assetsResponse?.error?.errorMessage}"));
          return Future.value(null);
        }
      }
    }
    return Future.value(null);
  }

  dynamic _parsePoliciesResponse(Map? data, String lob) {
    if (data == null) return null;
    final apiData = data['policies'];
    Policies policies = Policies.fromJson(apiData[lob]);
    return policies;
  }

  Future<Map<String, dynamic>?> getLifeInsurancePolicyListAndProposals() async {
    final apiResponse = await NetworkingModule.instance.graphqlService.apiCall(
      GraphQueryDocumentConstants.lifePendingPolicyAndProposals,
    );

    return apiResponse.response.fold((l) {
      logException(Exception(
          "API Error : getLifeInsurancePolicyListAndProposals ${l.error}"));
      return null;
    }, (r) => r);
  }

  Future<LifeMyAccountModel> getLifeMyAccountData() async {
    final userEkey = await SessionManager.instance.storageManager.getUserEkey();
    if (userEkey.isNullOrEmpty) {
      return LifeMyAccountModel.error(
        error:
            Failure(failureMessage: 'Something went wrong, userId not found.'),
      );
    }

    final apiPath = await RemoteConfigInstance.instance
        .getData(RemoteConfigKeysSet.HL_API_PATHS);
    Map<String, dynamic> apiPaths = {};
    if(apiPath != null) {
      apiPaths = json.decode(apiPath);
    }
    String? lifeMyAccountPath = apiPaths["life_my_account"] as String?;

    if (lifeMyAccountPath.isNullOrEmpty) {
      lifeMyAccountPath = PolicyDetailsApiPaths.lifeMyAccount;
    }

    final result = await networkRestService.apiCall<dynamic>(
      ApiRequest(
        apiPath: lifeMyAccountPath!,
        requestType: RequestType.get,
        queryParameters: {
          'userId': userEkey!,
        },
      ),
    );

    return result.response.fold(
      (failure) => LifeMyAccountModel.error(
        error: failure,
      ),
      (success) => LifeMyAccountModel.fromJson(success.data),
    );
  }

  Future<PolicyStatusCardsResponse?> getPolicyStatus(Map assetFilter) async {
    Map<String, dynamic>? lifeProposalsData = null;
    await getPoliciesHeaderDetails();
    if ((assetFilter["relationship"] ?? "") == "Self") {
      lifeProposalsData = await getLifeInsurancePolicyListAndProposals();
    }
    HealthAssetsResponse? healthAssetsResponse =
        await getPoliciesForAsset(assetFilter);
    PolicyStatusCardsResponse? statusResponse;
    List<Map<String, dynamic>?>? policies = [];
    String? id =
        assetFilter.containsKey('policyId') ? assetFilter['policyId'] : null;
    // : assetFilter.containsKey('proposalId') ? assetFilter['proposalId'] : null;
    bool isMandateSubmittedPolicy = assetFilter.containsKey('proposalId');
    if (id.isNotNullOrEmpty) {
      _policiesHeaderDetails?.selectedPolicies(id!)?.forEach((element) {
        policies.add(element?.toJson());
      });
    } else if (!isMandateSubmittedPolicy) {
      _policiesHeaderDetails?.allPolicies?.forEach((key, value) {
        if ((value.isGMCPolicy) &&
            (value.policyType == "topup" || value.policyType == "addon")) {
          return;
        }
        policies.add(value.toJson());
      });
    }
    if (policies.isNotNullOrEmpty ||
        lifeProposalsData != null ||
        (healthAssetsResponse?.selectedAsset?.allProposals.isNotNullOrEmpty ??
            false)) {
      statusResponse = await policyRepo.getPolicyStatusCardsResponse(
          _policiesHeaderDetails?.advanceCashDiscoveryState,
          policies,
          lifeProposalsData,
          healthAssetsResponse: healthAssetsResponse);
      if (statusResponse.error == null) {
        return Future.value(statusResponse);
      }
    }
    return Future.value(null);
  }

  Future<HealthAssetsResponse?> getHealthAssetsData(Map policyFilter,
      {bool isFromClaimsPage = false}) async {
    await getPoliciesHeaderDetails();
    if (_policiesHeaderDetails != null) {
      String? id = policyFilter.containsKey('policyId')
          ? policyFilter["policyId"]
          : policyFilter.containsKey('proposalId')
              ? policyFilter["proposalId"]
              : null;
      DateTime? startDate, endDate;
      if (id.isNotNullOrEmpty) {
        final selectedPoliciesTimelines =
            _policiesHeaderDetails!.getSelectedPolicyTimeLines(id!);
        startDate = selectedPoliciesTimelines[0];
        endDate = selectedPoliciesTimelines[1];
      } else {
        DateTime? extremeStartDate =
            _policiesHeaderDetails?.getExtremeDatesForAllPolicies().firstOrNull;
        DateTime? extremeEndDate =
            _policiesHeaderDetails?.getExtremeDatesForAllPolicies().lastOrNull;
        startDate = extremeStartDate;
        endDate = extremeEndDate;
      }
      if (startDate != null && endDate != null) {
        _assetsResponse = await getHealthAssets(startDate, endDate,
            id: id, isFromClaimsPage: isFromClaimsPage);
        if (_assetsResponse?.error == null)
          return Future.value(_assetsResponse);
      }
    }
    return Future.value(null);
  }

  Future<HealthAssetsResponse?> getHealthAssets(
      DateTime startDate, DateTime endDate,
      {String? id,
      String? name,
      String? gender,
      String? relationship,
      String? dob,
      bool isFromClaimsPage = false}) async {
    await getPoliciesHeaderDetails();

    String assetApiPath;
    assetApiPath = ApiPath.GET_POLICIES_FOR_HEALTH;

    final policyCoversMap = await getPolicyKeyCoversMap();
    Map<String, dynamic> params = {
      'fromYear':
          isFromClaimsPage ? '2018' : DateFormat("yyyy").format(startDate),
      'toYear': DateFormat("yyyy").format(endDate),
      "withProposals": (!isFromClaimsPage) ? true : false,
      "filterRenewed": (!isFromClaimsPage) ? true : false,
      "includeMandateRegisteredProposals": (!isFromClaimsPage) ? true : false
    };
    final response = await HealthNetworkLayer.instance
        .makeApiRequest<AckoResponse>(ApiRequest(
            apiPath:
                assetApiPath, // ApiPath.GET_POLICIES_FOR_HEALTH_MEMBERS, //GET_POLICIES_FOR_HEALTH,
            queryParameters: params,
            requestType: RequestType.get));
    _assetsResponse = await response.response.fold((l) {
      _assetsResponse = HealthAssetsResponse()
        ..error = ErrorHandler(l.failureMessage);
      logException(Exception("API Error : getHealthAssets :: ${l.error}"));
      return _assetsResponse;
    },
        (r) => HealthAssetsResponse.fromJson(r.data,
            id: id,
            name: name,
            gender: gender,
            relationship: relationship,
            dob: dob,
            headerDetails: _policiesHeaderDetails,
            keyCoverMap: policyCoversMap,
            isFromClaimsPage: isFromClaimsPage));
    return Future.value(_assetsResponse);
  }

  Future<List<HealthPolicyBasicInfo?>?> getHealthPolicyBasicDetails(
      Map policyFilter) async {
    final String? id = policyFilter.containsKey("policyId")
        ? policyFilter["policyId"]
        : policyFilter.containsKey("proposalId")
            ? policyFilter["proposalId"]
            : null;
    if (id.isNotNullOrEmpty) {
      await getPoliciesHeaderDetails();
      return Future.value(null);
    }
    return Future.value(null);
  }

  Future<bool?> isFitnessTrackingEnabled(
      Map<dynamic, dynamic> policyFilter) async {
    String? id = policyFilter.containsKey('policy_id')
        ? policyFilter["policy_id"]
        : policyFilter.containsKey('policy_number')
            ? policyFilter["policy_number"]
            : policyFilter.containsKey('proposalId')
                ? policyFilter["proposalId"]
                : null;
    if (id.isNotNullAndEmpty) {
      var healthPolicyHeaderResponse =
          await _headerApiRepo.getHealthHeaderData();
      bool _isFitnessEnabledForRetail = await FirebaseRemoteConfig.instance
          .getBool(HealthRemoteConfigConstants.FEATURE_FLAG_FITNESS_RETAIL);
      final info = healthPolicyHeaderResponse.allPolicies?[id];
      if (!(info?.isExpired ?? false) &&
          !(info?.productType.equalsIgnoreCase(HealthConstants.ASP_PRODUCT) ??
              false)) {
        if (_isFitnessEnabledForRetail ||
            (info?.productType
                    .equalsIgnoreCase(HealthConstants.ENTERPRISE_PRODUCT) ??
                false)) {
          return Future.value(true);
        }
      }
      return Future.value(false);
    }
    return Future.value(false);
  }

  Future<String?> getEnrollmentLinkIfOpen() async {
    var healthPolicyHeaderResponse = await _headerApiRepo.getHealthHeaderData();
    if (healthPolicyHeaderResponse.isEnrolmentOpen) {
      return Future.value(healthPolicyHeaderResponse
              .gmcActivePolicies.firstOrNull?.enrolmentJourneyURL ??
          Constants.HEALTH_ENROLMENT_URL);
    }
    return Future.value();
  }

  Future<bool?> isAdvanceCashEnabled() async {
    await getPoliciesHeaderDetails();
    return Future.value(_policiesHeaderDetails
            ?.advanceCashDiscoveryState?.isEligibleForAdvanceCash ??
        false);
  }

  Future<AdvanceCashData?> getAdvanceCashData() async {
    var healthPolicyHeaderResponse = await _headerApiRepo.getHealthHeaderData();
    return Future.value(
        healthPolicyHeaderResponse.advanceCashDiscoveryState?.advanceCashData);
  }

  Future<AdvanceCashDiscoveryState?> getAdvanceCashDetailsData() async {
    var healthPolicyHeaderResponse = await _headerApiRepo.getHealthHeaderData();
    return Future.value(healthPolicyHeaderResponse.advanceCashDiscoveryState);
  }

  Future<List<HealthPolicyServiceCategory>?> getPolicyServices(
      Map filter) async {
    // var healthPolicyHeaderResponse =
    //     await _headerApiRepo.getHealthHeaderData();
    // final policyId = filter["policyId"];
    // if (policyId != null)
    //   return Future.value(healthPolicyHeaderResponse
    //       .allPolicies?[policyId]?.policyActions?.policyActionCategories);
    return Future.value(null);
  }

  Future<List<HealthPolicyBasicInfo?>?> _getUserPolicies(
      Map assetFilter) async {
    List<HealthPolicyBasicInfo?>? policies = [];
    // if (assetFilter.containsKey("name") &&
    //     assetFilter.containsKey("gender") &&
    //     assetFilter.containsKey("relationship")) {
    //   final name = assetFilter["name"];
    //   final gender = assetFilter["gender"];
    //   final relationship = assetFilter["relationship"];
    //   final dob = assetFilter["dob"];
    //   var healthPolicyHeaderResponse =
    //       await _headerApiRepo.getHealthHeaderData();
    //   final iterable = healthPolicyHeaderResponse.activePolicies?.where(
    //       (policy) => (policy?.fold(
    //               false,
    //               (previousValue, element) =>
    //                   (previousValue ?? false) ||
    //                   (element.person?.name?.equalsIgnoreCase(name) == true &&
    //                       element.person?.gender?.equalsIgnoreCase(gender) ==
    //                           true &&
    //                       element.person?.relationship
    //                               ?.equalsIgnoreCase(relationship) ==
    //                           true)) ??
    //           false));
    //   policies = iterable?.toList();
    // }
    return Future.value(policies);
  }

  Future<List<HealthPolicyServiceCategory>?> getPolicyVasServices(
      HealthAssetsResponse? response) async {
    if (response != null) {
      String data = RemoteConfigInstance.instance
          .getData((response.selectedAsset?.hasGmcPolicy ?? false)
              ? RemoteConfigKeysSet.healthGmcServices
              : (response.selectedAsset?.hasOnlyAspPolicies ?? false)
                  ? RemoteConfigKeysSet.healthAspServices
                  : RemoteConfigKeysSet.healthRetailServices);
      bool canShowDocs = response.selectedAsset?.hasOnlyGmcPolicies ?? false;
      return Future.value((HealthPolicyActions.fromJson(jsonDecode(data),
              canShowDocuments: canShowDocs)
          .assetActionCategories)); // policyActionCategories
    }
    return Future.value(null);
  }

  Future<List<PolicyAction>?> getPolicyFeatures(String? id) async {
    await getPoliciesHeaderDetails();
    if (id.isNotNullAndEmpty && _policiesHeaderDetails != null) {
      HealthHeaderPolicyDetailsModel? info =
          _policiesHeaderDetails!.allPolicies?[id!];
      if (info != null) {
        bool isUpcomingPolicy =
            info.startDateTime.isAfter(DateTime.now()) ?? false;
        bool canEditPolicy =
            !(info.policyStatus.equalsIgnoreCase('lapsed') ?? false) &&
                (info.endDateTime.isAfter(DateTime.now()) ?? false) &&
                !isUpcomingPolicy;
        String? data;
        try {
          await FirebaseRemoteConfig.instance.fetchAndActivate();
          data = RemoteConfigInstance.instance.getData((info.productType
                  .equalsIgnoreCase(HealthConstants.ENTERPRISE_PRODUCT))
              ? RemoteConfigKeysSet.healthGmcServices
              : (info.productType
                      .equalsIgnoreCase(HealthConstants.RETAIL_PRODUCT))
                  ? RemoteConfigKeysSet.healthRetailServices
                  : RemoteConfigKeysSet.healthAspServices);
        } catch (e) {
          logException("Error in fetching policy service config data");
        }
        bool isMonthlyPolicy =
            (info.premiumDetail?.paymentFrequency.equalsIgnoreCase("monthly") ??
                false);
        return Future.value((HealthPolicyActions.fromJson(
                data.isNotNullOrEmpty ? jsonDecode(data!) : null,
                canShowDocuments:
                    (info.policyDocuments?.isNotNullOrEmpty ?? false),
                canEditPolicy: canEditPolicy,
                isMonthlyPolicy: isMonthlyPolicy))
            .policyFeatures);
      }
    }
    return Future.value(null);
  }

  Future<HealthPolicyActions> _policyServices(
      {String? productType,
      bool canEditPolicy = true,
      bool isMonthlyPolicy = false}) async {
    await FirebaseRemoteConfig.instance.fetchAndActivate();
    String data = "";
    if (productType != null)
      data = RemoteConfigInstance.instance.getData(
          productType.equalsIgnoreCase(HealthConstants.ENTERPRISE_PRODUCT)
              ? RemoteConfigKeysSet.healthGmcServices
              : productType.equalsIgnoreCase(HealthConstants.RETAIL_PRODUCT)
                  ? RemoteConfigKeysSet.healthRetailServices
                  : RemoteConfigKeysSet.healthAspServices);
    return Future.value(HealthPolicyActions.fromJson(jsonDecode(data),
        canEditPolicy: canEditPolicy, isMonthlyPolicy: isMonthlyPolicy));
  }

  Future<List<HealthPageCrossSellV3Dao>?> getCrossSellData() async {
    String? data = RemoteConfigInstance.instance
        .getData(RemoteConfigKeysSet.healthPageCrossSellConfigV3);

    /// if data is empty we won't show cross sells
    // if (data.isEmpty) data = RemoteConfigDefaultValues.healthPageCrossSellData;
    return Future.value((data?.isEmpty ?? false)
        ? null
        : healthRevampRedirectionConfigsFromJson(data));
  }

  Future<GMCUpsellSheetDto?> getGMCUpsellData() async {
    String gmcUpsellEntry = await RemoteConfigInstance.instance
        .getData(RemoteConfigKeysSet.GMC_UPSELL_ENTRY);
    GMCUpsellSheetDto? gmcUpsellData;
    if (gmcUpsellEntry.isNotNullOrEmpty) {
      Map<String, dynamic> jsonMap = json.decode(gmcUpsellEntry);
      gmcUpsellData = GMCUpsellSheetDto.fromJson(jsonMap);
    }
    return Future.value(gmcUpsellData);
  }



  Future<PWILOCardsData?> getLifePWILOCardData() async {

    String? phoneNumber = await getStringPrefs(StringDataSharedPreferenceKeys.MOBILE_NUMBER);

    if(phoneNumber.isNullOrEmpty) return PWILOCardsData.error(BaseModel.ErrorHandler("Phone number not found"));

    final result = await networkRestService.apiCall(
      ApiRequest(
          apiPath: ApiPath.lifeConfigV1,
          requestType: RequestType.get,
          queryParameters: {
            'phone_number': phoneNumber,
          }
      ),
    );

    return result.response.fold(
            (failure) =>
            PWILOCardsData.error(BaseModel.ErrorHandler(failure.error)),
            (success) async {
          if (success.data != null && success.data.isNotEmpty) {
            return PWILOCardsData.fromJson(success.data);
          } else {
            return PWILOCardsData.error(BaseModel.ErrorHandler(something_went_wrong));
          }
        });
  }

  Future<PWILOCardsData?> getPWILOCardData() async {
    final result = await networkRestService.apiCall(
      ApiRequest(
          apiPath: ApiPath.healthConfigV1,
          requestType: RequestType.get),
    );

    return result.response.fold(
            (failure) =>
                PWILOCardsData.error(BaseModel.ErrorHandler(failure.error)),
            (success) async {
          if (success.data != null && success.data.isNotEmpty) {
            return PWILOCardsData.fromJson(success.data);
          } else {
            return PWILOCardsData.error(BaseModel.ErrorHandler(something_went_wrong));
          }
        });
  }

  Future<Map<String, HealthPolicyKeyCovers>> getPolicyKeyCoversMap() async {
    String data = RemoteConfigInstance.instance.getData(RemoteConfigKeysSet
        .HEALTH_PDP_KEY_COVERS); // todo: use this key health_page_cross_sell_config_v3
    /// if data is empty we won't show cross sells
    // if (data.isEmpty) data = RemoteConfigDefaultValues.healthPageCrossSellData;
    if (data.isEmpty) {
      logException(
          Exception("Health key covers parsing error HEALTH_PDP_KEY_COVERS"));
      return {};
    }
    Map<String, HealthPolicyKeyCovers> policyMap = {};

    final jsonData = jsonDecode(data);

    for (Map<String, dynamic> jsonDataItem in jsonData) {
      String policyType = jsonDataItem['type'];
      policyMap[policyType] = HealthPolicyKeyCovers.fromJson(jsonDataItem);
    }

    return policyMap;
  }

  Future<HealthEcardModel?> getEcardsDetails(Map assetFilter,
      {bool isFromCoveragesPage = false}) async {
    HealthEcardModel? ecardModel;

    await getPoliciesHeaderDetails();
    if (_policiesHeaderDetails != null) {
      if (_policiesHeaderDetails!.allPolicies != null) {
        String? id = assetFilter.containsKey('policy_id')
            ? assetFilter['policy_id']
            : assetFilter.containsKey('proposal_id')
                ? assetFilter['proposal_id']
                : null;
        if (isFromCoveragesPage || id.isNotNullOrEmpty) {
          // ecardModel = generateEcardsFromHeaderResponse(
          //     _policiesHeaderDetails?.allPolicies,
          //     policyId: id,
          //     isFromCoveragesPage: isFromCoveragesPage);
        } else if (assetFilter.containsKey("name") &&
            assetFilter["name"] != null &&
            assetFilter.containsKey("gender") &&
            assetFilter["gender"] != null &&
            assetFilter.containsKey("relationship") &&
            assetFilter["relationship"] != null) {
          // ecardModel = generateEcardsFromHeaderResponse(
          //   _policiesHeaderDetails?.allPolicies,
          //   name: assetFilter["name"],
          //   relationship: assetFilter["relationship"],
          //   gender: assetFilter["gender"],
          // );
        } else
          Future.value(null);
      } else {
        throw Exception();
      }
    }
    return Future.value(null);
  }

  Future<HealthEcardModel?> getECardsData(
      List<HealthPolicyDetailsModel>? allPolicies,
      String? policyNumber,
      String? name,
      String? gender,
      String? relationship,
      bool? isFromCoveragesPage,
      String? policyId) async {
    HealthEcardModel eCardModel = HealthEcardModel();
    HealthHeaderDataModel? headerModel;
    List<HealthPolicyDetailsModel>? policies = [];
    List<HealthPolicyDetailsModel> topUpPolicies = [];
    List<HealthPolicyDetailsModel> addOnPolicies = [];
    eCardModel.distinctUsers = [];
    headerModel =
        await HealthHeaderApiRepo.instance.getHealthPolicyHeaderDetails();
    headerModel.activePolicies?.forEach((element) {
      policies.add(HealthPolicyDetailsModel.fromJson(element.toJson()));
    });
    headerModel.onHoldPolicies?.forEach((element) {
      policies.add(HealthPolicyDetailsModel.fromJson(element.toJson()));
    });
    if (policies.isNullOrEmpty) return null;
    policies.forEach((policy) {
      if (policy.isUpcoming) return;
      String? currentPolicyNumber =
          policy.policyNumber ?? policy.proposalId ?? null;
      String? currentPolicyId = policy.policyId;
      String? employeeId = policy.users?.first.employeeID;
      policy.insureds?.forEach((insured) {
        String? holderName = insured.name;
        String? uhid = insured.uhid;
        String? validTill = policy.policyEndDate;
        String? policyNum = policy.policyNumber;
        String? url = insured.ecardLink;
        String? gender = insured.gender;
        String? relationship = insured.relationship;
        String? holderId = insured.insuredId;
        String? dob = insured.dob;

        if ((name != null && holderName.notEqualsIgnoreCase(name)) ||
            (gender != null && gender != gender)) return;

        if (policy.isGMCPolicy && policy.policyId.isNotNullOrEmpty) {
          HealthPolicyDetailsModel? alreadyPresentPolicy = topUpPolicies
              .where((element) => element.policyNumber == policy.policyNumber)
              .firstOrNull;
          if (policy.policyType == "topup" && uhid.isNotNullAndEmpty) {
            if (alreadyPresentPolicy == null) {
              topUpPolicies.add(policy);
            }
            return;
          } else if (policy.policyType == "addon" && uhid.isNotNullAndEmpty) {
            if (alreadyPresentPolicy == null) {
              addOnPolicies.add(policy);
            }
            return;
          }
        }

        if ((policyNumber != null && currentPolicyNumber != policyNumber) ||
            (policyId != null && currentPolicyId != policyId)) return;

        HealthEcards eCard = HealthEcards(
            policyName: policy.planName,
            policyNumber: policy.displayPolicyNumber,
            policyId: policy.policyId,
            tpaIconUrl: (policy.tpaLogoUrl?.isNotEmpty ?? true)
                ? policy.tpaLogoUrl
                : null,
            holderName: holderName,
            uhid: uhid,
            validTill: validTill,
            policyNum: policyNum,
            url: url,
            gender: gender,
            relationship: relationship,
            employeeId: policy.productType == HealthConstants.ENTERPRISE_PRODUCT
                ? employeeId
                : '',
            corpIconUrl: (policy.corporateIconUrl?.isNotEmpty ?? true)
                ? policy.corporateIconUrl
                : null,
            dob: dob);

        /// eCardsList and distinctUsers
        eCardModel.eCardsList ??= [];
        eCardModel.distinctUsers ??= [];
        bool eCardsEntryExists = false;
        if (eCardModel.eCardsList != null) {
          eCardsEntryExists = eCardModel.eCardsList!.any((existingEntry) =>
              existingEntry.policyName?.toLowerCase() ==
                  policy.planName?.toLowerCase() &&
              existingEntry.policyNum == policyNum &&
              existingEntry.holderName == holderName &&
              existingEntry.relationship == relationship &&
              existingEntry.gender == gender);
        }
        if (!eCardsEntryExists) eCardModel.eCardsList!.add(eCard);
        AssetDetails asset = AssetDetails(
          id: holderId,
          name: holderName,
          uhid: uhid,
          gender: gender,
          relationship: relationship,
        );

        bool entryExists = false;
        if (eCardModel.distinctUsers != null &&
            (isFromCoveragesPage == true || name == holderName))
          entryExists = eCardModel.distinctUsers!.any((existingEntry) =>
              existingEntry.name?.toLowerCase() == asset.name?.toLowerCase() &&
              existingEntry.gender?.toLowerCase() ==
                  asset.gender?.toLowerCase());

        if (!entryExists) eCardModel.distinctUsers!.add(asset);
      });
    });
    eCardModel.topUpPolicies = topUpPolicies;
    eCardModel.addOnPolicies = addOnPolicies;
    eCardModel.isFromCoveragesPage = isFromCoveragesPage ?? false;
    return eCardModel;
  }
}
