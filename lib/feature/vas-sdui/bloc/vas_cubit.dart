import 'package:sdui/sdui.dart';

import '../models/vas_layout_data.dart';

class VasCubit<T extends VasState> extends SduiParentCubit<VasState> {
  final BaseRepository _repository = BaseRepository();
  String usecase;
  VasCubit({required this.usecase}) : super(VasLoading()) {}

  Future<void> fetchJourneyData({bool backgroundRefresh = false}) async {
    if (!backgroundRefresh) emit(VasLoading());

    final result =
        (await _repository.getResponse('/vas/journey?type=$usecase'));

    if (result.data != null) {
      sduiJson = result.data;
      parseJson();
    } else {
      emit(VasJourneyError(result.error?.error));
    }
  }

  void updateJson(Map<String, dynamic> json) {
    sduiJson = json;
    parseJson();
  }

  void parseJson() {
    final data = VasLayoutData.fromJson(sduiJson?['landing']);
    emit(VasDataLoaded(data));
  }
}

abstract class VasState extends SduiParentState {}

class VasLoading extends VasState {}

class VasLandingRefresh extends VasState {}

class VasDataLoaded extends VasState {
  VasLayoutData data;
  VasDataLoaded(this.data);
}

class VasJourneyLoaded extends VasState {
  VasJourneyLoaded();
}

class VasJourneyError extends VasState {
  Object? error;
  VasJourneyError(this.error);
}
