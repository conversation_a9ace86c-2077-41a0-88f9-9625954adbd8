import 'package:acko_flutter/common/view/ErrorWidget.dart';
import 'package:acko_flutter/feature/vas-sdui/view/vas_loading_view.dart';
import 'package:acko_flutter/network/ApiResponse.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/utilities.dart' as utilites;
import '../bloc/vas_cubit.dart';
import '../vas_analytics.dart';
import 'widgets/vas_journey.dart';

class VasLandingPage extends StatefulWidget {
  const VasLandingPage({Key? key}) : super(key: key);

  @override
  State<VasLandingPage> createState() => _VasLandingPageState();
}

class _VasLandingPageState extends State<VasLandingPage>
    implements utilites.StateListener, ErrorViewRetryCallBack {
  late VasCubit cubit;
  utilites.StateProvider _stateProvider = utilites.StateProvider();

  @override
  void initState() {
    cubit = context.read<SduiParentCubit>() as VasCubit;
    _stateProvider.subscribe(this);
    cubit.fetchJourneyData();

    final analytics = VasAnalytics();
    AnalyticsTrackerManager.instance.sendEvent(
      event: PageLoadedConstants.SERVICES_PAGE_LOADED,
      properties: {
        ...analytics.usecases[cubit.usecase].properties,
      },
    );

    super.initState();
  }

  @override
  void dispose() {
    _stateProvider.dispose(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: () {
          cubit.fetchJourneyData();
          return Future<void>.value();
        },
        child: BlocConsumer<VasCubit, VasState>(
          bloc: cubit,
          listener: (_, state) {
            if (state is VasLandingRefresh || state is UpdateFromParentJson) {
              cubit.parseJson();
            }
          },
          builder: (context, state) {
            if (state is VasLoading || state is VasLandingRefresh) {
              return VasLoadingShimmerView();
            } else if (state is VasDataLoaded) {
              return VasJourney(
                data: state.data,
              );
            }
            if (state is VasJourneyError) {
              return ErrorPage(
                retryCallbackFunc: this,
                errorHandler: ErrorHandler(state.error),
              );
            }
            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }

  @override
  onRetry() {
    cubit.fetchJourneyData();
  }

  @override
  void onStateChanged(utilites.ObserverState state, {data}) {
    if (state == utilites.ObserverState.UPDATE_FROM_PARENT_JSON) {
      if (data['usecase'] == 'CHALLAN_REFRESH') {
        cubit.updateJson(data['sduiJson']);
      } else if (data['usecase'] == 'CHALLAN_ADD_ASSET') {
        cubit.fetchJourneyData(backgroundRefresh: true);
      }
    }
  }
}
