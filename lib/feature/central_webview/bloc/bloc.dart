import 'dart:io';

import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_logger/acko_logger.dart';
import 'package:acko_logger/events/info/webview_info_event.dart';
import 'package:acko_web_view_module/bloc/events.dart';
import 'package:acko_web_view_module/bloc/states.dart';
import 'package:acko_web_view_module/common/enums.dart';
import 'package:acko_web_view_module/lob_contract_classes/bloc_contract.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/card_loaded_events.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:session_manager_module/session_manager.dart';
import 'package:utilities/constants/constants.dart';

import '../../../common/util/PreferenceHelper.dart';
import '../../../util/Utility.dart';

class AckoWebViewCentralBloc extends WebViewBaseBloc {
  AckoWebViewCentralBloc() : super(WebViewLOBs.CENTRAL);

  @override
  handleInput(WebViewEvents event, Emitter<WebViewState> emit) {
    switch (event.runtimeType) {
      case LogoutEvent:
        emit(LogoutState());
        break;

      ///Perform some logic
      /// To emit state for UI emit(RefreshPage())
      /// To emit new event addEvent(eventName)
    }
  }

  @override
  setCookies(
      String url, bool hideWebViewHeader, bool hideWebHeaderBackArrow) async {
    if (!url.containsIgnoreCase(Constants.ACKO)) {
      AckoLoggerManager.instance.logInfo(
          event: WebviewInfoEvent(
              url: url,
              webviewCurrentState: 'webview_cookie_init_deffered',
              page: 'webview'));
      return;
    }
    WebUri uri = WebUri(url);
    final String _domainName = Util.getDomainName(uri);

    final cookieManager = await CookieManager.instance();

    final userSessionModel =
        await SessionManager.instance.storageManager.getUserSessionDetails();

    await _setDeviceLevelCookies(cookieManager, uri, _domainName,
        hideWebViewHeader, url, hideWebHeaderBackArrow, userSessionModel);

    bool isUserLoggedIn = await isLogIn();

    if (isUserLoggedIn) {
      if (userSessionModel.accessToken.isNullOrEmpty ||
          userSessionModel.refreshToken.isNullOrEmpty) {
        handleLogout(url);
        return;
      }

      await _setUserLevelCookies(
          cookieManager, uri, userSessionModel, _domainName);
    }

    final cookies =
        await cookieManager.getCookies(url: WebUri(Constants.BASE_URL));

    String cookieListString = cookies
        .map((e) => "cookie: ${e.name} value: ${e.value}")
        .toList()
        .join('~~~');

    AnalyticsTrackerManager.instance.sendEvent(
      event: CardLoadedConstants.TRACK_EVENT_COMPLETE,
      properties: {
        'product': 'webview_v2',
        'name': cookieListString,
        "platform": Util.getPlatform(),
        'journey': 'webview_cookies_listing'
      },
    );
  }

  Future<void> _setUserLevelCookies(CookieManager cookieManager, WebUri uri,
      UserSessionModel userSessionModel, String _domainName) async {
    await Util.setCookieValue(cookieManager,
        url: uri,
        name: 'user_id',
        value: userSessionModel.accessToken!,
        domain: _getDomainName(uri));

    Util.setCookieValue(cookieManager,
        url: uri,
        name: 'refresh_token',
        value: userSessionModel.refreshToken!,
        domain: _getDomainName(uri));

    String phoneNumber =
        await getStringPrefs(StringDataSharedPreferenceKeys.MOBILE_NUMBER) ??
            "";
    Util.setCookieValue(cookieManager,
        url: uri,
        name: "phone_number",
        value: phoneNumber,
        domain: _getDomainName(uri));

    String? userName = await getUserName();
    if (userName.isNotNullOrEmpty) {
      Util.setCookieValue(cookieManager,
          url: uri, name: "user_name", value: userName, domain: _domainName);
    }
  }

  Future<void> _setDeviceLevelCookies(
      CookieManager cookieManager,
      WebUri uri,
      String _domainName,
      bool hideWebViewHeader,
      String url,
      bool hideWebHeaderBackArrow,
      UserSessionModel userSessionModel) async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();

    if (userSessionModel.trackerId.isNotNullAndEmpty) {
      Util.setCookieValue(cookieManager,
          url: uri,
          name: 'trackerid',
          value: userSessionModel.trackerId!,
          domain: _getDomainName(uri));
    }

    Util.setCookieValue(cookieManager,
        url: uri,
        name: Constants.COOKIE_APP_VERSION,
        value: packageInfo.version,
        domain: _domainName);

    Util.setCookieValue(cookieManager,
        url: uri,
        name: Constants.COOKIE_BUILD_NUMBER,
        value: packageInfo.buildNumber,
        domain: _domainName);

    Util.setCookieValue(cookieManager,
        url: uri, name: "hide_footer", value: "true", domain: _domainName);

    if (Platform.isAndroid) {
      Util.setCookieValue(cookieManager,
          url: uri,
          name: Constants.COOKIE_WEB_VIEW,
          value: Constants.COOKIE_ANDROID_APP,
          domain: _domainName);
    } else {
      Util.setCookieValue(cookieManager,
          url: uri,
          name: Constants.COOKIE_WEB_VIEW,
          value: Constants.COOKIE_IOS_APP,
          domain: _domainName);
    }

    Util.setCookieValue(cookieManager,
        url: uri,
        name: "hide_header",
        value: hideWebViewHeader.toString(),
        domain: _domainName);

    Util.setCookieValue(cookieManager,
        url: uri,
        name: "hide_back",
        value: _getHideBackValue(url, hideWebHeaderBackArrow).toString(),
        domain: _domainName);
  }

  Future<String> getUserName() async {
    return await getStringPrefs(
            StringDataSharedPreferenceKeys.USER_PROFILE_NAME) ??
        "";
  }

  Future<String> getUserPhoneNo() async {
    return await getStringPrefs(StringDataSharedPreferenceKeys.MOBILE_NUMBER) ??
        "";
  }

  handleLogout(String url) async {
    FirebaseCrashlytics.instance.log("userid:: user logged out url - $url");
    await logoutHandling();
    addEvent!(LogoutEvent());
  }

  _getHideBackValue(String url, bool defaultValue) {
    if (url.contains('abha')) {
      return false;
    }
    return defaultValue;
  }

  String _getDomainName(Uri uri) {
    String? domain = uri.host;
    if (domain.startsWith("www.")) {
      return domain.substring(3);
    } else if (!domain.startsWith(".")) {
      return "." + domain;
    } else {
      return domain;
    }
  }
}
