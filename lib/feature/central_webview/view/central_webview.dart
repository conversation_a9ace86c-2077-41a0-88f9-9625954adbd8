import 'dart:convert';
import 'dart:io';

import 'package:acko_flutter/feature/central_webview/webview_actions/actions.dart';
import 'package:acko_flutter/feature/webview/web_view_actions.dart';
import 'package:acko_flutter/feature/webview/web_view_model.dart';
import 'package:acko_flutter/util/download_util.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_flutter/util/health/utils.dart';
import 'package:acko_logger/acko_logger.dart';
import 'package:acko_logger/events/info/app_debug_info.dart';
import 'package:acko_logger/model/info_model.dart';
import 'package:acko_web_view_module/bloc/states.dart';
import 'package:acko_web_view_module/common/custom_dartz.dart';
import 'package:acko_web_view_module/lob_contract_classes/view_contract.dart';
import 'package:acko_web_view_module/view/acko_web_view_module.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:growthbook_sdk_flutter/growthbook_sdk_flutter.dart';
import 'package:session_manager_module/session_manager.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/remote_config/remote_config.dart';
import 'package:utilities/state_provider/StateProvider.dart';

import '../../../common/util/PreferenceHelper.dart';
import '../../../common/util/disable_screenshot_mixin.dart';
import '../../../common/util/strings.dart';
import '../../../common/view/FullPageLoader.dart';
import '../../../util/Utility.dart';
import '../model/webview_challan_response.dart';

class CentralWebViewLogic extends WebViewUIBase
    with DisableScreenshotMixin
    implements StateListener {
  List<dynamic>? _scrollableUrlsMap;
  List<String> _scrollableUrls = [];
  StateProvider _stateProvider = StateProvider();
  String? _paymentRedirectUrl;
  String? redirectUrl;
  WebviewChallanResponse? _challanAutomationResponse;
  ClipboardData? clipboardData;
  ChallanAutomation? stateDetailsToProcess;
  bool _isScreenshotLockEnabled = false;
  late InAppWebViewController centralWebViewController;
  FullPageLoader _fullPageLoader = FullPageLoader.instance;

  CentralWebViewLogic() {}

  runChallanAutomationIfNeeded(currentUrl) async {
    Uri parsedUrl = Uri.parse(currentUrl);

    bool challanAutomationNeeded = (parsedUrl
        .queryParameters['isChallanAutomationNeeded'] ?? "").equalsIgnoreCase(
        "true");

    if (challanAutomationNeeded) {
      Map<String, String> filteredQueryParams = {};
      filteredQueryParams.addAll(parsedUrl.queryParameters);

      filteredQueryParams.removeWhere((key, value) =>
          Constants.challanAutomationKeys.contains(key));

      String updatedUrlString = parsedUrl.origin + parsedUrl.path;
      if (filteredQueryParams.isNotEmpty) {
        updatedUrlString +=
            "?" + Uri(queryParameters: filteredQueryParams).query;
      }

      await centralWebViewController.loadUrl(
        urlRequest: URLRequest(url: WebUri(updatedUrlString)),
      );

      await _fetchChallanAutomationResponseAndPopullateFields(
        currentUrl,
        parsedUrl.queryParameters['searchSource'] ?? "",
      );
    }
  }

  @override
  void addJsHandler(InAppWebViewController controller) {}

  _fetchChallanAutomationResponseAndPopullateFields(currentUrl, searchSource) {
    final response = RemoteConfigInstance.instance
        .getData(RemoteConfigKeysSet.CHALLAN_AUTOMATION);
    _challanAutomationResponse =
        WebviewChallanResponse.fromJson(jsonDecode(response));

    for (var entry in _challanAutomationResponse!.challanAutomation) {
      if (entry.state!.toLowerCase() == searchSource.toLowerCase()) {
        stateDetailsToProcess = entry;
      }
    }
  }

  @override
  Future<Either<NotHandlingMethod, bool>> canGoBack(
      InAppWebViewController controller) async {
    final currentURL = await controller.getUrl();

    if (!(currentURL?.isNull ?? false) &&
        (_isChallanPaymentResponsePage(currentURL) ||
            _isChallanPaymentRedirectFlowURL(currentURL) ||
            _isChallanPaymentPGURL(currentURL))) {
      Navigator.of(pageContext!).pop(true);
      return Right(true);
    } else if (await checkKycUrl(controller)) {
      if (pageContext != null) {
        Navigator.pushNamedAndRemoveUntil(
            pageContext!, Routes.APP_HOME, (route) => route.isFirst,
            arguments: {"tab": "1"});
      }
      return Right(false);
    } else if (await controller.canGoBack()) {
      WebHistory? history = await controller.getCopyBackForwardList();
      if (history != null &&
          !(history.currentIndex! < 1 && Navigator.of(pageContext!).canPop()) &&
          await checkForRedirectionPaymentUr(
              history.currentIndex!, history, controller)) {
        Navigator.of(pageContext!).pop(true);
        return Right(true);
      }
    }
    return Left(NotHandlingMethod());
  }

  Future<bool> checkForRedirectionPaymentUr(int currentIndex,
      WebHistory history, InAppWebViewController controller) async {
    if (currentIndex > 1) {
      Uri previousUrl = history.list![currentIndex - 1].url!;
      if (previousUrl.host.contains(Constants.PAYMENT_REDIRECT_CHECK_HOST) &&
          previousUrl.path.contains(Constants.PAYMENT_REDIRECT_CHECK_URL)) {
        WebUri? newUrl = history.list![currentIndex - 2].url;
        if (newUrl != null) {
          await controller.goBackOrForward(steps: -2);
          controller.loadUrl(urlRequest: URLRequest(url: newUrl));
          pageContext!
              .findAncestorStateOfType<AckoWebViewState>()
              ?.refreshPage();
        }
        return true;
      }
    }
    return false;
  }

  Future<bool> checkKycUrl(InAppWebViewController controller) async {
    bool isKycUrl = false;
    WebHistory? history = await controller.getCopyBackForwardList();
    if (history != null) {
      history.list?.forEach((element) {
        if (element.url?.path.equalsIgnoreCase("/central-kyc") ?? false) {
          isKycUrl = true;
        }
      });
    }
    return isKycUrl;
  }

  @override
  void blocStateListener(WebViewState state) {
    if (state is LogoutState) {
      Navigator.pushNamedAndRemoveUntil(
          pageContext!, Routes.LOGIN_LANDING_PAGE, (route) => false,
          arguments: {"is_user_logged_out": true});
    }
  }

  _downloadPolicy(String url, InAppWebViewController controller) {
    if (url.contains('abha_card')) {
      Downloader.downloadFileAndPreview(url, null, 'abha_card.pdf');
    } else {
      Downloader.download(url, controller: controller);
    }
  }

  @override
  Future<Either<NotHandlingMethod, NavigationActionPolicy>>
  shouldOverrideUrlLoading(InAppWebViewController controller,
      NavigationAction action) async {
    String url = action.request.url.toString();
    _screenShotLockCheck(url);
    if (RemoteConfigInstance.instance
        .getData(RemoteConfigKeysSet.IS_NATIVE_PAYMENT_FLOW) &&
        url.containsIgnoreCase(Constants.PAYMENT_URL)) {
      var paymentUri = Uri.parse(url);
      bool enableMockPayment = RemoteConfigInstance.instance
          .getData(RemoteConfigKeysSet.ENABLE_MOCK_PAYMENT);

      if (enableMockPayment &&
          kDebugMode &&
          !paymentUri.queryParameters.containsKey("status")) {
        String url =
            "https://platform-simulator-frontend-uat.internal.ackodev.com/payments?id=${paymentUri
            .queryParameters["id"]}";
        Navigator.pushNamed(pageContext!, Routes.WEB_PAGE_V2,
            arguments: {"url": url, "payment_new_tab": true});
        return Future.value(Right(NavigationActionPolicy.CANCEL));
      } else {
        var clientName;
        bool showWebPayment = false;

        Map<String, dynamic>? paymentClients = jsonDecode(RemoteConfigInstance
            .instance
            .getData(RemoteConfigKeysSet.PAYMENT_CLIENTS));

        /// check if client is whitelisted
        if (paymentUri.queryParameters.containsKey("client")) {
          clientName = paymentUri.queryParameters["client"];
          if (paymentClients != null) {
            (paymentClients['clients'] as List).forEach((element) {
              if (element == clientName) {
                showWebPayment = true;
              }
            });
          }
        }
        if (showWebPayment) {
          return Future.value(Right(NavigationActionPolicy.ALLOW));
        } else {
          _handlePayment(url);
          return Future.value(Right(NavigationActionPolicy.CANCEL));
        }
      }
    } else if (url.contains(Constants.MY_ACCOUNT_URL) ||
        url.equalIgnoreCase(Constants.webHomeUrl) ||
        url.startsWith(Constants.webHomeUtmUrl) ||
        url.equalIgnoreCase(Constants.BASE_URL) ||
        url.equalIgnoreCase(
            Constants.BASE_URL.substring(0, (Constants.BASE_URL.length - 1)))) {
      if (url.contains("payment_new_tab")) {
        Navigator.pushNamedAndRemoveUntil(
            pageContext!, Routes.APP_HOME, (route) => false);
      } else {
        Navigator.pop(
            pageContext!,
            WebViewResult(
              result: Result.RESULT_OK,
            ));
      }

      return Future.value(Right(NavigationActionPolicy.CANCEL));
    }

    ///handleUrlInterceptions

    Uri urlStr = Uri.parse(url);
    var pid;
    if (urlStr.queryParameters.containsKey("pid")) {
      pid = urlStr.queryParameters["pid"];
    }

    if ((urlStr.host.contains("api.razorpay.com") &&
        urlStr.host.endsWith("api.razorpay.com")) &&
        (urlStr.queryParameters.containsKey("status") &&
            urlStr.queryParameters["status"]!.equalIgnoreCase("failed")) &&
        (urlStr.queryParameters.containsKey("token_recurring_status") &&
            urlStr.queryParameters["token_recurring_status"]!
                .equalIgnoreCase("rejected"))) {
      WebHistory? copyBackForwardList =
      await controller.getCopyBackForwardList();
      debugPrint(
          "web view payment back forward list length ${copyBackForwardList?.list
              ?.toString()}");
      if (copyBackForwardList != null &&
          copyBackForwardList.list != null &&
          copyBackForwardList.list!.length > 0) {
        if (copyBackForwardList.list!.length > 1) {
          if (Platform.isIOS) {
            if (_paymentRedirectUrl != null) {
              controller.goBack();
              controller.loadUrl(
                  urlRequest: URLRequest(
                      url: WebUri(Constants.BASE_URL
                          .substring(0, Constants.BASE_URL.length - 1) +
                          _paymentRedirectUrl!)));
            } else {
              return Future.value(Right(NavigationActionPolicy.ALLOW));
            }
          } else {
            controller.goBack();
          }
        } else {
          Navigator.pop(pageContext!, "refresh_web_window");
        }
        return Future.value(Right(NavigationActionPolicy.CANCEL));
      }
    } else if (urlStr.queryParameters.containsKey("pid") &&
        urlStr.queryParameters.containsKey("success") &&
        urlStr.queryParameters["success"] == "true" &&
        urlStr.queryParameters.containsKey("product")) {
      _getPolicySuccessPage(
          urlStr.queryParameters["pid"], urlStr.queryParameters["pid"]);
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    } else if (pid != null &&
        urlStr.path.contains(Constants.POLICY_DETAILS_CHECK_URL)) {
      if (!(loadingUrl!.contains("endorsement"))) {
        _getPolicyDetailsPage(pid);
      }
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    } else if (urlStr.toString().contains("connect-users") &&
        urlStr.queryParameters.containsKey("email")) {
      setBoolPrefs(BoolDataSharedPreferenceKeys.NOT_HAS_POLICES, false);
    } else if (pid != null &&
        urlStr.toString().equalIgnoreCase(Constants.BASE_URL +
            Constants.POLICY_EDIT_URL +
            pid +
            Constants.POLICY_EDITED)) {
      Navigator.pop(
          pageContext!, WebViewResult(result: Result.RESULT_OK, policyId: pid));
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    } else if (await makePhoneCall(url) ||
        await handleMailTo(url) ||
        WebUtils.getInstance.handleGoogleMapUrl(urlStr)) {
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    } else if (urlStr.scheme.equalIgnoreCase("http")) {
      WebUtils.getInstance.loadChromeSafariTab(
          pageContext!, urlStr.toString(), urlStr.host, () {});
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    }
    // return Future.value(Right(NavigationActionPolicy.CANCEL));

    return Future.value(Left(NotHandlingMethod()));
  }

  Future<bool> makePhoneCall(String phone) async {
    try {
      if (phone.toLowerCase().startsWith("tel:")) {
        await launchUrl(Uri.parse(phone));
        return Future.value(true);
      }
    } on Exception {
      Util.showToast(somethingWentWrong);
      debugPrint('Unable to make a call');
    }
    return Future.value(false);
  }

  Future<bool> handleMailTo(String email) async {
    try {
      Uri? uriEmail = Uri.tryParse(email);
      if (uriEmail != null && email.toLowerCase().startsWith("mailto:")) {
        launchUrl(uriEmail);
        return Future.value(true);
      }
    } on Exception {
      debugPrint('Unable to launch email');
    }
    return Future.value(false);
  }

  _screenShotLockCheck(String url) {
    final bool _isPaymentUrlPresent = url.contains('payment-service-ui-ng') ||
        url.contains('payments.acko.com');

    bool enableMockPayment = RemoteConfigInstance.instance
        .getData(RemoteConfigKeysSet.ENABLE_MOCK_PAYMENT);
    if (kDebugMode && enableMockPayment) {
      debugPrint("Disabled ScreenShot");
      return;
    }
    if (_isPaymentUrlPresent && !_isScreenshotLockEnabled) {
      _isScreenshotLockEnabled = true;
      enableScreenshotLock();
    } else if (!_isPaymentUrlPresent && _isScreenshotLockEnabled) {
      _isScreenshotLockEnabled = false;
      disableScreenshotLock();
    }
  }

  @override
  Future<bool> shouldSubtractKeypadSpacing(String url) async {
    if (_scrollableUrlsMap == null) await _fetchScrollableUrls();

    bool _isScrollableUrl = false;
    for (int index = 0; index < (_scrollableUrls.length); index++) {
      if (url.contains(_scrollableUrls[index])) {
        _isScrollableUrl = true;
        break;
      }
    }
    return _isScrollableUrl;
  }

  _fetchScrollableUrls() async {
    final String? remoteData = RemoteConfigInstance.instance
        .getData(RemoteConfigKeysSet.SCROLLABLE_URLS);
    if (remoteData.isNotNullAndEmpty) {
      Map<String, dynamic>? scrollableURLsFromRemote =
      await jsonDecode(remoteData!.trim());
      _scrollableUrlsMap = scrollableURLsFromRemote?['urls'];
      if (scrollableURLsFromRemote?['urls'] != null) {
        (scrollableURLsFromRemote?['urls'] as List<dynamic>).forEach((element) {
          _scrollableUrls.add(element['url']);
        });
      }
    }
  }

  String? _fetchEncodedScriptForChallanUrl(String inputUrl) {
    if (stateDetailsToProcess?.automationFlowUrls != null) {
      for (var flowUrl in stateDetailsToProcess!.automationFlowUrls!) {
        if (flowUrl.url != null && inputUrl.contains(flowUrl.url!)) {
          // [0] because the value given from firebase config is a list of strings rather than a string
          return flowUrl
              .scripts?.firstOrNull;
        }
      }
    }
    return null;
  }

  String _fetchDecodedScriptString(String url) {
    final String? encodedString = _fetchEncodedScriptForChallanUrl(url);

    if (encodedString.isNullOrEmpty) {
      return "";
    }

    final trimmedEncodedString = encodedString!.replaceAll(RegExp(r'\s+'), '');

    Uint8List decodedBytes = base64Decode(trimmedEncodedString);

    final decodedString = utf8.decode(decodedBytes);

    return decodedString;
  }

  String? getValueFromMapByPartialKey(Map<String, dynamic> map,
      String partialKey) {
    if (map.isNull) {
      return null;
    }

    String? matchingKey = null;

    for (final key in map.keys) {
      if (partialKey.toLowerCase().contains(key.toLowerCase())) {
        matchingKey = key;
        break; // Stop iterating once a match is found
      }
    }

    // If a matching key is found, return the associated value (cast as dynamic)
    return matchingKey != null ? map[matchingKey] as String : null;
  }

  @override
  Either<NotHandlingMethod, HandlingVoidMethod> onDownloadStart(
      InAppWebViewController controller,
      DownloadStartRequest downloadStartRequest) {
    String url = downloadStartRequest.url.toString();
    _downloadPolicy(url, controller);
    return Right(HandlingVoidMethod());
  }

  @override
  void onStateChanged(ObserverState state, {dynamic data}) async {
    if (state == ObserverState.LOGIN_UPDATE && redirectUrl != null) {
      //Removing the front slash "/" from the redirectUrl
      String formatUrl = (redirectUrl!.startsWith('/'))
          ? redirectUrl!.replaceFirst('/', '')
          : redirectUrl!;
      String trimRedirectUrl = Constants.BASE_URL + formatUrl;
      URLRequest? urlRequest = URLRequest(
          url: await blocInstance?.redirectWithNewCookies(trimRedirectUrl));

      if (urlRequest.url != null)
        centralWebViewController.loadUrl(urlRequest: urlRequest);
    } else if (state == ObserverState.showMyPolicies &&
        (pageContext?.mounted ?? false)) {
      await centralWebViewController.reload();
    }
  }

  @override
  Either<NotHandlingMethod, HandlingVoidMethod> onCreateWindow(
      InAppWebViewController controller, CreateWindowAction request) {
    if (!request.request.url.isNull && Platform.isIOS) {
      controller.loadUrl(urlRequest: URLRequest(url: request.request.url));
    } else {
      pushNewWebPage(request.request.url.toString());
    }
    return Right(HandlingVoidMethod());
  }

  pushNewWebPage(String url) async {
    var result = await Navigator.pushNamed(pageContext!, Routes.WEB_PAGE,
        arguments: {'url': url});
    if (result == "refresh_web_window") {
      pageContext!
          .findAncestorStateOfType<AckoWebViewState>()
          ?.setInitialConfig();
    }
  }

  @override
  void onPageCommitVisible(InAppWebViewController controller, Uri? url) {}

  @override
  void onWebViewCreated(InAppWebViewController controller) async {
    centralWebViewController = controller;
    _fetchScrollableUrls();
    await runChallanAutomationIfNeeded(loadingUrl);
    _stateProvider.subscribe(this);
  }

  @override
  Future<Either<NotHandlingMethod, dynamic>> handleWebViewActions(args,
      InAppWebViewController controller) async {
    var result;
    Map<String, dynamic> jsonData = jsonDecode(args.trim());
    WebViewModel model = WebViewModel.fromJson(jsonData);
    switch (model.action) {
      case AckoCentralWebViewActions.REFRESH_HEALTH_PROFILE_QUESTIONS: 
        _stateProvider.notify(ObserverState.REFRESH_HEALTH_PROFILE_QUESTIONS);
        break;
      case AckoCentralWebViewActions.CLAIM_SUCCESS:
        Navigator.pop(pageContext!, WebViewResult(result: Result.RESULT_OK));
        break;
      case AckoCentralWebViewActions.PAYMENT_STATUS:
        if (model.actionValue != null && model.actionValue!.isPaymentSuccess!) {
          _getPolicySuccessPage(
              model.actionValue!.orderId, model.actionValue?.policyNumber);
        }
        break;
      case AckoCentralWebViewActions.PAYMENT_REDIRECT_URl:
        _paymentRedirectUrl = model.actionValue?.url;
        break;
      case AckoCentralWebViewActions.HCAPTCHA:
        var key = model.actionValue!.text;
        Navigator.pop(pageContext!, key);
        break;
      case AckoCentralWebViewActions.NCB_WEB_PAGE:
        Navigator.pushNamed(pageContext!, Routes.WEB_PAGE,
            arguments: {'url': Constants.ncbCertificateUrl});
        break;
      case AckoCentralWebViewActions.OWNERSHIP_TRANSFER_FAQ_LINK:
        Navigator.pushNamed(pageContext!, Routes.WEB_PAGE,
            arguments: {'url': model.actionValue?.url});
        break;
      case AckoCentralWebViewActions.CLOSE_WEB_VIEW:
        Navigator.pop(pageContext!, WebViewResult(result: Result.RESULT_OK));
        break;
      case AckoCentralWebViewActions.OPEN_NATIVE_PAYMENT:

      ///TODO Test this case
        _handlePayment(model.actionValue!.paymentUrl);
        break;
      case AckoCentralWebViewActions.OPEN_POLICY_DETAIL:
        _getPolicyDetailsPage(model.actionValue?.policyId);
        break;
      case AckoCentralWebViewActions.MY_ACCOUNT:
        Navigator.popUntil(pageContext!, ModalRoute.withName(Routes.APP_HOME));
        _stateProvider.notify(ObserverState.MyAccount_Refresh);
        _stateProvider.notify(ObserverState.showMyPolicies);
        FocusManager.instance.primaryFocus?.unfocus();
        break;
      case AckoCentralWebViewActions.REFRESH_CHATBOT_CONVERSATION:
        _stateProvider.notify(ObserverState.REFRESH_SUPPORT_TAB);
        Navigator.pop(pageContext!);
        break;
      default:
        return Future.value(Left(NotHandlingMethod()));
    }

    return Future.value(Right(result));
  }

  _getPolicyDetailsPage(String? policyId) {
    _stateProvider.notify(ObserverState.MyAccount_Refresh);
    if (policyId != null) {
      Navigator.pushNamedAndRemoveUntil(
          pageContext!, Routes.POLICY_DETAILS_PAGE, (route) => route.isFirst,
          arguments: {"policyId": policyId});
    }
  }

  void _handlePayment(String? paymentUrl) {
    if (paymentUrl != null) {
      /// variables
      var paymentUri = Uri.parse(paymentUrl);

      /// remote config data
      bool showNativePayment = RemoteConfigInstance.instance
          .getData(RemoteConfigKeysSet.SHOW_NATIVE_PAYMENT_CAR);

      if (showNativePayment) {
        if (paymentUri.queryParameters.containsKey("id")) {
          String? utmSource = paymentUri.queryParameters["utm_source"] ?? null;
          Navigator.pushNamed(pageContext!, Routes.PAYMENT, arguments: {
            "ekey": paymentUri.queryParameters["id"],
            "utm_source": utmSource
          });
        } else {
          Navigator.pushNamed(pageContext!, Routes.WEB_PAGE,
              arguments: {"url": paymentUrl, "payment_new_tab": true});
        }
      } else {
        Navigator.pushNamed(pageContext!, Routes.WEB_PAGE,
            arguments: {"url": paymentUrl, "payment_new_tab": true});
      }
    }
  }

  _getPolicySuccessPage(String? orderId, String? policyNumber) {
    setBoolPrefs(BoolDataSharedPreferenceKeys.NOT_HAS_POLICES, false);
    _stateProvider.notify(ObserverState.MyAccount_Refresh);
    Navigator.pushNamedAndRemoveUntil(
        pageContext!, Routes.POLICY_SUCCESS, (route) => route.isFirst,
        arguments: {"policy_id": orderId, 'policy_number': policyNumber});
  }

  @override
  Future<void> onLoadStop(InAppWebViewController controller, Uri? url) async {
    Constants.PLATFORM_CHANNEL.invokeMapMethod(
      "page_load_stop",
    );

    if (checkIfTheURLIsPartOfAutomationFlow(url) ||
        _isChallanPaymentResponsePage(url)) {
      clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      await _runChallanPaymentRTOAutomationFlow(url);
    }
  }

  bool checkIfTheURLIsPartOfAutomationFlow(Uri? inputUrl) {
    if (stateDetailsToProcess?.automationFlowUrls == null || inputUrl == null) {
      return false;
    }
    for (var flowUrl in stateDetailsToProcess!.automationFlowUrls!) {
      if (flowUrl.url != null && inputUrl.toString().contains(flowUrl.url!)) {
        return true;
      }
    }
    return false;
  }

  @override
  void onLoadError(InAppWebViewController controller, Uri? url, int code,
      String message) {}

  @override
  void onConsoleMessage(InAppWebViewController controller,
      ConsoleMessage message) {}

  @override
  void onCloseWindow(InAppWebViewController controller) {
    Navigator.pop(pageContext!, "refresh_web_window");
  }

  @override
  void dispose() {
    disableScreenshotLock();
  }

  @override
  Either<NotHandlingMethod, HandlingVoidMethod> onAppBarActionPressed(
      String actionValue) {
    return Left(NotHandlingMethod());
  }

  @override
  void onLoadStart(InAppWebViewController controller, Uri? url) {
    if (checkIfTheURLIsPartOfAutomationFlow(url) ||
        _isChallanPaymentResponsePage(url)) {
      _fullPageLoader.showFullPageLoader(pageContext!, hideBackground: true);
    }
  }

  Future<void> _runChallanPaymentRTOAutomationFlow(Uri? url) async {
    await _runChallanPaymentAutomationScriptFromRemoteConfig(
        centralWebViewController, clipboardData?.text ?? "", url);

    await _isChallanPaymentStatusTextPresentOnLoad(
        centralWebViewController, url);
  }

  Future<void> _runChallanPaymentAutomationScriptFromRemoteConfig(
      InAppWebViewController controller,
      String challanOrRegistrationNum,
      Uri? url) async {
    if (url == null) return;

    var updatedQueryParams = Map<String, String>.from(url.queryParameters);

    updatedQueryParams.remove('searchSource');

    Uri updatedUri = url.replace(queryParameters: updatedQueryParams);

    String currentUrl = updatedUri.toString();

    if (currentUrl.endsWith('?') || currentUrl.endsWith('&')) {
      currentUrl = currentUrl.substring(0, currentUrl.length - 1);
    }

    var decodedAutomationScript = "";
    var keyToCheck = currentUrl;

    decodedAutomationScript = _fetchDecodedScriptString(keyToCheck);
    final scriptWithReplacingSelectedRegNumber =
    decodedAutomationScript.replaceAll(
      "'selectedChallanOrRegNumber'",
      "'$challanOrRegistrationNum'",
    );

    final scriptWithReplacingCheckBoxValue =
    scriptWithReplacingSelectedRegNumber.replaceAll(
      "challanCheckBoxValue",
      "$challanOrRegistrationNum",
    );

    final result = await controller.evaluateJavascript(
        source: scriptWithReplacingCheckBoxValue);

    if (result == AutomationNavigationResult.error_on_page.name) {
      _logChallanAutomationResult(
          keyOfAutomationScript: keyToCheck, isAutomationSuccess: false);
      _fullPageLoader.dismissFullPageLoader(pageContext!);
      Navigator.pop(pageContext!, something_went_wrong);
    } else if (result == AutomationNavigationResult.success_stay_on_page.name) {
      _logChallanAutomationResult(
          keyOfAutomationScript: keyToCheck, isAutomationSuccess: true);
      _fullPageLoader.dismissFullPageLoader(pageContext!);
    } else if (result ==
        AutomationNavigationResult.success_proceed_next_page.name) {
      _logChallanAutomationResult(
          keyOfAutomationScript: keyToCheck, isAutomationSuccess: true);
    }
  }

  ///This contains the function to check if the current page contains the element based on the element selector passed
  Future<bool> _wasElementPresentOnLoad(InAppWebViewController controller,
      String elementSelector, Uri? url) async {
    final checkElementScript = '''
    (function() {
      const element = document.querySelector('$elementSelector');
      return element !== null; // Check if element exists initially
    })();
    ''';

    final isElementPresent =
    await controller.evaluateJavascript(source: checkElementScript);
    return isElementPresent;
  }

  ///This contains the function to check if the current page containts the text to indicate if the challan payment is success or failed
  ///Currently if the challan payment is success , then it can be displayed only in the payment response page
  ///If the challan payment is failure , it can be displayed in either payment resposne page or the challan payment landing page
  Future<void> _isChallanPaymentStatusTextPresentOnLoad(
      InAppWebViewController controller, Uri? url) async {
    ///Check if this is the payment response page received after payment is done
    ///This payment response page can display both success and failure error messages
    final isPaymentResponsePage = _isChallanPaymentResponsePage(url);
    if (isPaymentResponsePage) {
      String successElementSelector = '#success-alert';
      bool wasPresent = await _wasElementPresentOnLoad(
          controller, successElementSelector, url);
      if (wasPresent) {
        AnalyticsTrackerManager.instance.sendEvent(
            event:
            PageLoadedConstants.VIEWED_CHALLAN_PAYMENT_RTO_SUCCESS_SCREEN,
            properties: {
              'platform': Util.getPlatform(),
              'unique_id': clipboardData?.text ?? ""
            });
        _fullPageLoader.dismissFullPageLoader(pageContext!);
        Navigator.of(pageContext!).pop(true);
      } else {
        AnalyticsTrackerManager.instance.sendEvent(
            event: PageLoadedConstants.VIEWED_CHALLAN_PAYMENT_RTO_ERROR_SCREEN,
            properties: {
              'platform': Util.getPlatform(),
              'unique_id': clipboardData?.text ?? ""
            });
        _fullPageLoader.dismissFullPageLoader(pageContext!);
        Navigator.pop(pageContext!, something_went_wrong);
      }

      ///This payment response page can display only failure error messages
    } else if (_isChallanPaymentRedirectFlowURL(url)) {
      String errorElementSelector = '#error-alert';
      bool wasPresent =
      await _wasElementPresentOnLoad(controller, errorElementSelector, url);
      if (wasPresent) {
        AnalyticsTrackerManager.instance.sendEvent(
            event: PageLoadedConstants.VIEWED_CHALLAN_PAYMENT_RTO_ERROR_SCREEN,
            properties: {
              'platform': Util.getPlatform(),
              'unique_id': clipboardData?.text ?? ""
            });
        _fullPageLoader.dismissFullPageLoader(pageContext!);
        Navigator.pop(pageContext!, something_went_wrong);
      }
    }
  }

  ///This function checks if this current page is the redirection url , defined in the firebase remote config as a map
  bool _isChallanPaymentRedirectFlowURL(Uri? url) {
    if (stateDetailsToProcess?.landingPageUrl == null || url == null)
      return false;

    if (stateDetailsToProcess!.landingPageUrl != null &&
        stateDetailsToProcess!.landingPageUrl is String) {
      return stateDetailsToProcess!.landingPageUrl!.contains(url.toString());
    } else {
      return false;
    }
  }

  ///This function checks if this current page is the redirection url , defined in the firebase remote config as a map
  bool _isChallanPaymentPGURL(Uri? url) {
    if (stateDetailsToProcess?.automationPaymentGatewayUrls == null ||
        url == null ||
        url
            .toString()
            .isEmpty) {
      return false;
    }

    // Check if the url contains the payment gateway URL
    final gatewayUrl = stateDetailsToProcess!.automationPaymentGatewayUrls!;
    if (url.toString().contains(gatewayUrl)) {
      return true;
    }

    return false;
  }

  ///This function checks if the current page is the payment response page , after the challan payment is performed
  ///The redirection URLs are defined as a map in the firebase remote config
  bool _isChallanPaymentResponsePage(Uri? url) {
    if (stateDetailsToProcess?.challanPaymentSucessUrl == null || url == null)
      return false;

    final successUrl = stateDetailsToProcess!.challanPaymentSucessUrl!;
    if (url.toString().contains(successUrl)) {
      return true;
    }

    return false;
  }

  @override
  void onPageCompleteLoaded(InAppWebViewController controller) {
    // TODO: implement onPageCompleteLoaded
  }

  ///This is the function which notifies us when the element is not present in screen
  Future<void> _logChallanAutomationResult(
      {required String keyOfAutomationScript,
        required bool isAutomationSuccess}) async {

    AckoLoggerManager.instance.logInfo(
        event: AppDebugInfoEvent(
            page: 'webview',
            infoMessage: isAutomationSuccess
                ? 'challan_payment_automation_success'
                : 'challan_payment_automation_error',
            journey: 'challan_automation',
            data: {"from_page": keyOfAutomationScript}));
  }
}


enum AutomationNavigationResult {
  success_stay_on_page,
  error_on_page,
  success_proceed_next_page
}