import 'dart:io';

import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/events_base.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:session_manager_module/session_manager.dart';
import 'package:utilities/constants/constants.dart';
import 'package:acko_core_utilities/deeplink_handler/DeeplinkHandler.dart';


import '../../../r2d2/events.dart';
import '../../../util/TrackerManager.dart';
import '../../../util/Utility.dart';
import '../../acko_services/ui/acko_service_loading_screen.dart';
import '../../onboardingSDUI/onboarding_model.dart';
import '../bloc/web_login_bloc.dart';

class WebLoginFlow extends StatefulWidget {
  final String? nextUrl;
  final String? phoneNumber;

  const WebLoginFlow({super.key, this.nextUrl, this.phoneNumber});

  @override
  State<WebLoginFlow> createState() => _WebLoginFlowState();
}

class _WebLoginFlowState extends State<WebLoginFlow> {
  late WebLoginBloc _loginBloc;

  @override
  void initState() {
    _loginBloc = BlocProvider.of<WebLoginBloc>(context);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _openAuthPage();
    });
    _pageLoadedAnalytics();
    super.initState();
  }

  _pageLoadedAnalytics() async {
    var trackerIDCookie =
        (await SessionManager.instance.storageManager.getUserSessionDetails())
            .trackerId;
    triggerAmplitudeEvent("enter_mobile_number_page_loaded", {},
        feature: "login");
    if (trackerIDCookie.isNotNullAndEmpty) {
      R2D2Events.instance.trackGetStartedLoading();
    }
    _trackEvents(
        _loginBloc.email.isNotNullAndEmpty
            ? PageLoadedConstants.APP_LOGIN_PHONE_LINK_SCREEN_LOADED
            : PageLoadedConstants.VIEW_MOBILE_NUMBER_PAGE,
        _loginBloc.email.isNotNullAndEmpty
            ? {"email": _loginBloc.email}
            : {"source": "mobile_login"});
  }

  void _trackEvents(BaseEventsClass event, Map<String, dynamic>? properties) {
    AnalyticsTrackerManager.instance
        .sendEvent(event: event, properties: properties ?? {});
  }

  _openAuthPage() async {
    String loginUrl = Constants.MOBILE_AUTH + "${widget.phoneNumber.isNotNullAndEmpty ? '&mobile=${widget.phoneNumber}&disable=true&screen=otp' : ''}";

    final model =
        await Navigator.pushNamed(context, Routes.WEB_PAGE_V2, arguments: {
      "url": loginUrl
    });

    if (!mounted) return;

    if (model != null && model is UserSessionModel) {
      _loginBloc.authVerified(model);
      return;
    }
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else {
      /// In case login is the root page and no page behind it
      /// we will close the app on back pressed
      exit(0);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<WebLoginBloc, WebviewLoginState>(
        builder: (context, state) {
      if (state is LoadingState) {
        return Center(child: AckoServiceLoadingScreen());
      }
      return const SizedBox.shrink();
    }, listener: (context, state) async {
      if (state is AuthSuccessState) {
        if (_loginBloc.email.isNotNullAndEmpty) {
          AnalyticsTrackerManager.instance.sendEvent(
              event: PageLoadedConstants.APP_LOGIN_SUCCESSFUL_HOME_LOADED,
              properties: {
                'platform': Util.getPlatform(),
                'phone': state.userSessionModel.phone
              });
          if (widget.nextUrl.isNotNullAndEmpty) {
            String defaultUrl = widget.nextUrl!.formatLink();
            await Constants.PLATFORM_CHANNEL.invokeMethod(
                "store_deeplink", {"deeplink": widget.nextUrl!});
            Navigator.pushNamedAndRemoveUntil(
                context, Routes.APP_HOME, (_) => false);
          } else {
            Navigator.pushNamedAndRemoveUntil(
                context, Routes.MERGE_ACCOUNTS_LOADER_PAGE, (_) => false,
                arguments: {
                  "phone_number": state.userSessionModel.phone,
                  "email": _loginBloc.email,
                  "is_user_logged_out": _loginBloc.isUserLoggedOut
                });
          }
        } else {
          OnBoardingUserInfo onboardingUserInfo = OnBoardingUserInfo(
            nextUrl: widget.nextUrl,
            phoneNumber: state.userSessionModel.phone,
            email: _loginBloc.email,
            isUserLoggedOut: _loginBloc.isUserLoggedOut,
          );

          Navigator.pushNamedAndRemoveUntil(
            context,
            Routes.ONBOARDING_INITIAL_PAGE,
            (_) => false,
            arguments: {"onboardingUserInfo": onboardingUserInfo},
          );
        }
      }
    });
  }
}
