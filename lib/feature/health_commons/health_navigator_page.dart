import 'dart:convert';

import 'package:acko_flutter/feature/acko_services/ui/acko_service_loading_screen.dart';
import 'package:acko_flutter/feature/app_policy_page/utils/CommonDataStore.dart';
import 'package:acko_flutter/feature/bottomsheet/model/BottomSheetData.dart';
import 'package:acko_flutter/feature/health_home/bloc/health_home_bloc.dart';
import 'package:acko_flutter/feature/health_home/models/health_header_data_model.dart';
import 'package:acko_flutter/feature/health_home/models/health_header_policy_details_model.dart';
import 'package:acko_flutter/feature/health_home/models/health_policy_header_response.dart';
import 'package:acko_flutter/feature/health_policy/repository/health_policy_repository.dart';
import 'package:acko_flutter/feature/vas/gmc_upsell/model/gmc_upsell_dto.dart';
import 'package:acko_flutter/feature/vas/vas_bottomsheet.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_flutter/util/health/health_constants.dart';
import 'package:acko_flutter/util/health/utils.dart';
import 'package:analytics/analytics.dart';
import 'package:analytics/events/health_life/page_events/health_life_page_events.dart';
import 'package:analytics/events/health_life/tap_events/health_life_tap_events.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/remote_config/remote_config.dart';

import '../../common/view/FullPageLoader.dart';
import '../vas/gmc_upsell/view/gmc_upsell_bottomsheet.dart';

class HealthNavigatorPage extends StatelessWidget {
  final String? policyId;
  final String? assetId;
  final String? url;
  final String path;
  final String? fromPage;
  final String? name;
  final String? relationship;
  final String? gender;
  final bool? edit;
  late final HealthNavigationPage _healthNavigationPage;
  late final HealthHeaderPolicyDetailsModel? navigatedHealthData;

  FullPageLoader _fullPageLoader = FullPageLoader.instance;

  HealthNavigatorPage(
      {required this.path,
      this.policyId,
      this.fromPage,
      this.assetId,
      this.url,
      this.edit,
      this.name,
      this.gender,
      this.relationship}) {
    _healthNavigationPage = path.getHealthNavigationPage();
  }

  @override
  Widget build(BuildContext context) {
    _navigateTo(context, _healthNavigationPage,
            policyId: policyId,
            edit: edit,
            sourcePage: fromPage,
            url: url != null ? Uri.decodeFull(url!) : null)
        .then((value) {
      if (value is MaterialPageRoute) {
        Navigator.of(context).pushReplacement(value);
      } else if (value is Map && value.containsKey("route")) {
        Navigator.of(context)
            .pushReplacementNamed(value["route"], arguments: value['args']);
      } else {
        Navigator.of(context).pop(value);
      }
    });
    return Scaffold(
      backgroundColor: Colors.black.withOpacity(0.2),
      body: Center(
        child: AckoServiceLoadingScreen(
          title: 'Loading...',
        ),
      ),
    );
  }

  Future<dynamic> _navigateTo(
      BuildContext context, HealthNavigationPage navigationPage,
      {String? policyId, bool? edit, String? sourcePage, String? url}) async {
    debugPrint(
        "=======> _navigateTo $policyId\n$edit\n$sourcePage\n$url\n$name\n$gender\n$relationship");
    try {
      HealthHeaderDataModel? healthPoliciesResponse =
          HealthHeaderApiRepo.instance.headerDetails ??
              await HealthHeaderApiRepo.instance.getHealthHeaderData();

      if (policyId.isNullOrEmpty) {
        policyId = (healthPoliciesResponse.recentGMCPolicy ??
                healthPoliciesResponse.recentlyExpiredGMCPolicy ??
                ((healthPoliciesResponse.retailPolicies).isNotEmpty
                    ? healthPoliciesResponse.retailPolicies[0]
                    : null))
            ?.policyId;
      }
      navigatedHealthData = (<HealthHeaderPolicyDetailsModel?>[]
            ..addAll(healthPoliciesResponse.retailPolicies)
            ..addAll(healthPoliciesResponse.gmcPolicies))
          .firstWhereOrNullIterable(
        (policy) => policy?.policyId == policyId,
      );
      bool isExpired = false;
      HealthPolicyRepository.clear();
      switch (navigationPage) {
        case HealthNavigationPage.AS_POLICY:
          if (edit == true)
            return Future.value({
              "route": Routes.HEALTH_EDIT_ENDORSEMENT,
              "args": {"product_type": HealthConstants.AS_PRODUCT}
            });
          else if (edit == false)
            return Future.value({"route": Routes.ASP_CLAIMS_CONTACT_PAGE});
          else
            try {
              HealthRedirectionConfig configsJson =
                  _findItem(HealthConstants.AS_PRODUCT);
              final String url =
                  Constants.BASE_URL + (configsJson.productUrl ?? "");

              return _launchWeb(url);
            } catch (ex) {
              throw ("AS Config not found");
            }
        case HealthNavigationPage.HEALTH_RETAIL_PURCHASE:
          try {
            HealthRedirectionConfig configsJson =
                _findItem(HealthConstants.RETAIL_PRODUCT);
            final String url =
                Constants.BASE_URL + (configsJson.productUrl ?? "");
            return _launchWeb(url);
          } catch (ex) {
            throw ("Retail Config not found");
          }

        case HealthNavigationPage.GMC_LINKING:
          _fullPageLoader.showFullPageLoader(context);
          final String? url =
              await CommonDataStore.sharedInstance.getGmcLinkUrl();
          _fullPageLoader.dismissFullPageLoader(context);
          if (url != null) {
            return _launchWeb(url, isGmcLinkUrlLoaded: true);
          } else
            throw ("GMC linking url is null");
        case HealthNavigationPage.ASSET_VIEW:
          final v10 = (await RemoteConfigInstance.instance.getGbAsyncData(RemoteConfigKeysSet.APP_IA_VERSION)).toString().equalsIgnoreCase("app_v10");
          if (v10) {
            return Future.value({
              "route": Routes.ASSET_TAB_VIEW,
              "args": {
                "showBackButton": true,
                "defaultTab": 1,
              }
            });
          }

          return Future.value({
            "route": Routes.ASSET_POLICY_HEALTH,
            "args": {
              "name": name,
              "gender": gender,
              "relationship": relationship
            }
          });
        case HealthNavigationPage.E_CARDS:
        case HealthNavigationPage.HEALTH_HOME:
          return Future.value({
            "route": Routes.HEALTH_HOME_PAGE,
            "args": {
              // todo: we are getting policy_id from home page, the logic should be on the policy_id rather active or expired
              "is_expired": navigatedHealthData?.isExpired ?? isExpired,
              "product_type": "health" +
                  (navigatedHealthData != null
                      ? "_${navigatedHealthData?.productType?.toLowerCase()}"
                      : ""),
              "policy_id": policyId,
              "from_page": fromPage
            }
          });
        case HealthNavigationPage.PHARMACY:
          AnalyticsTrackerManager.instance.sendEvent(
              event: HLTrackEvents.TAP_ORDER_MEDICINE,
              properties: {"from_page": sourcePage});
          return Future.value(HealthVASBottomSheet(
              context, VAS.medicine, (sourcePage ?? "home")));

        case HealthNavigationPage.LABS:
          AnalyticsTrackerManager.instance.sendEvent(
              event: HLTrackEvents.TAP_BOOK_LAB_TEST,
              properties: {"from_page": sourcePage});
          return Future.value(
              HealthVASBottomSheet(context, VAS.lab, (sourcePage ?? "home")));

        case HealthNavigationPage.DOCTOR_ON_CALL:
          if (navigatedHealthData != null) {
            AnalyticsTrackerManager.instance.sendEvent(
                event: HLTrackEvents.TAP_DOCTOR_ON_CALL,
                properties: {
                  "from_page": sourcePage,
                  "product": navigatedHealthData?.productType
                });
            return Future.value(
              HealthVASBottomSheet(
                context,
                VAS.doctorOnCall,
                (sourcePage ?? "home"),
              ),
            );
          } else {
            //TODO(rohan): remove api calls for internal navigation
            Navigator.pop(context);
            Util.showExploreHealthPlansBottomSheet(context, 'doctor_on_call');
            throw ("Health data is required for doctor on call. Name, mobile, email, product type is required");
          }

        case HealthNavigationPage.GMC_UPSELL:
          return HealthGMCUpsellBottomSheet(
            context,
            fromPage: "l0_coverages_tab",
          );
        case HealthNavigationPage.LIFE_CROSS_SELL:
          return HealthGMCUpsellBottomSheet(
            context,
            fromPage: "l0_coverages_tab",
            buyType: BuyType.life_cross_sell,
          );

        case HealthNavigationPage.EDIT_POLICY:
          if (healthPoliciesResponse.error == null) {
            if (healthPoliciesResponse.activePolicies
                    ?.where((element) =>
                        element.stage?.toUpperCase() ==
                        LinkPolicyState.ENDORSEMENT)
                    .toList()
                    .isNotEmpty ==
                true) {
              return Future.value({
                "route": Routes.HEALTH_EDIT_ENDORSEMENT,
                "args": {
                  "product_type": (navigatedHealthData?.productType ?? null)
                }
              });
            } else {
              UiUtils.getInstance
                  .showToast("Policy can't be edited at this stage.");
            }
          }
          return Future.value();

        case HealthNavigationPage.REGISTER_CLAIM:
          return Future.value({
            "route": Routes.HEALTH_CLAIMS_LIST_PAGE,
            "args": {"from_page": fromPage}
          });

        case HealthNavigationPage
              .LOAD_MERGED_PDP: // todo: merged policy card is only for active policies not for expired
          return Future.value({
            "route": Routes.HEALTH_HOME_PAGE,
            "args": {
              "is_expired": navigatedHealthData?.isExpired ?? isExpired,
              "product_type": "health" +
                  (navigatedHealthData != null
                      ? "_${navigatedHealthData?.productType?.toLowerCase()}"
                      : "")
            }
          });

        case HealthNavigationPage.LOAD_WEB_PAGE:
          return Future.value({
            "route": Routes.WEB_PAGE,
            "args": {
              'url': url,
              'hide_header': false,
              "hide_app_bar": true,
              "feature": "health_enrolment_card"
            }
          });
        case HealthNavigationPage.NETWORK_HOSPITALS:
          LatLng? locationDetails = (await Permission.location.status.isGranted)
              ? await getLocationLatlng()
              : null;
          return Future.value({
            "route": Routes.NETWORK_HOSPITALS_LIST,
            "args": {"place_latlong": locationDetails, "from_screen": fromPage}
          });
        case HealthNavigationPage
              .AS_POLICY_V2: // todo: we should merge AS policy with other health policies
          if (edit == true)
            return Future.value({
              "route": Routes.HEALTH_HOME_PAGE,
              "args": {
                "is_expired": navigatedHealthData?.isExpired ?? isExpired,
                "product": "health_as"
              }
            });
          else
            return Future.value({
              "route": Routes.ASP_CLAIMS_CONTACT_PAGE,
            });

        default:
          return Future.value({
            "route": Routes.APP_HOME,
          });
      }
    } catch (ex, stack) {
      debugPrintStack(stackTrace: stack);
      debugPrint(ex.toString());
      return Future.error(ex);
    }
  }

  HealthRedirectionConfig _findItem(String search) {
    return List<HealthRedirectionConfig>.from(json
            .decode((RemoteConfigInstance.instance
                .getData(RemoteConfigKeysSet.healthRedirectionConfig)))
            .map((x) => HealthRedirectionConfig.fromJson(x)))
        .firstWhere((element) => element.product == search);
  }

  void _triggerViewHealthHomePageEvent(HealthPolicy data, String source) {
    try {
      var dataProps = <String, dynamic>{"page": source};
      var corporateName = data.policy?.data?.corporate != null
          ? data.policy?.data?.corporate != null
          : (data.policy?.data?.displayName ?? "empty");
      dataProps = {
        "policy_id": data.user?.policyId ?? "empty",
        "plan_id": data.policy?.planId ?? "empty",
        "policy_number": data.policy?.policyNumber ?? "empty",
        "status": data.policy?.stage?.toLowerCase() ?? "empty",
        "corporate_name": "$corporateName",
        "product": "${data.policy?.productType}"
      };
      AnalyticsTrackerManager.instance
          .sendEvent(event: HLPageEvents.VIEW_HEALTH_HOME_PAGE, properties: {});
    } catch (e, stack) {
      debugPrintStack(stackTrace: stack);
      debugPrint("$e");
    }
  }

  void _triggerRetailPolicyClickEvent(HealthPolicy data, String source) {
    AnalyticsTrackerManager.instance
        .sendEvent(event: HLTrackEvents.TAP_RETAIL_HEALTH_CARD, properties: {
      "product": data.policy?.productType,
      "policy_id": data.policy?.policyId,
      "page": source
    });
    _triggerViewHealthHomePageEvent(data, source);
  }

  void _triggerGMCPolicyClickEvent(HealthPolicy data, String? source) {
    AnalyticsTrackerManager.instance
        .sendEvent(event: HLTrackEvents.TAP_CORPORATE_PLAN, properties: {
      "policy_status": "${data.policy?.status}",
      "page": "$source",
      "product": "${data.policy?.productType}",
      "corporate_name": "${data.policy?.data?.corporate}"
    });
  }

  Future<dynamic> _launchWeb(String url, {bool isGmcLinkUrlLoaded = false}) {
    final args = {
      "url": url,
      "hide_back_arrow": true,
      "hide_header": true,
      "isGmcLinkUrlLoaded": isGmcLinkUrlLoaded
    };
    return Future.value({"route": Routes.WEB_PAGE, "args": args});
  }
}
