import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:analytics/events/tap_events.dart';
import 'package:flutter/widgets.dart';
import 'package:meta/meta.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/widgets/acko_safe_cubit.dart';

import '../view/members/health_coverage_status_enum.dart';
import 'health_coverage_model.dart';
import 'health_coverage_state.dart';

class HealthCoverageCubit extends AckoSafeCubit<HealthCoverageState> {
  final BaseRepository _baseRepository;

  HealthCoverageCubit()
      : _baseRepository = BaseRepository(),
        super(HealthCoverageInitial()){
    fetchHealthCoverageStatus();
  }

  Future<void> fetchHealthCoverageStatus() async {
    emit(HealthCoverageLoading());
    try {
      final response = await _baseRepository.getResponse('/pages/health-profile');
      if (response.error == null) {
        if (response.data != null && response.data!['data'] != null) {
          final model = _normalizeCoverage(HealthCoverageModel.fromJson(response.data!['data']));
          final event_props = getHealthCoverageEventProps(model);
          AnalyticsTrackerManager.instance.sendEvent(
              event: PageLoadedConstants.FAMILY_MEMBERS_SCREEN,
              properties: {
                "acko_insured": event_props['acko_insured'],
                "non_acko_insured": event_props['non_acko_insured'],
              });
          emit(HealthCoverageLoaded(model));
        } else {
          emit(HealthCoverageError('No data received'));
        }
      } else {
        emit(HealthCoverageError('Something went wrong'));
      }
    } catch (e) {
      emit(HealthCoverageError('Unable to load coverage status'));
    }
  }

  HealthCoverageModel _normalizeCoverage(HealthCoverageModel model) {
    final ackoRetail = model.ackoRetail;
    final ackoGmc = model.ackoGmc;

    return model.copyWith(
      otherRetail: ackoRetail ? false : model.otherRetail,
      otherGmc: ackoGmc ? false : model.otherGmc,
    );
  }

  String _getInsuranceType(bool hasGmc, bool hasRetail, {bool isAcko = false}) {
    if (hasGmc && hasRetail) return 'both';
    if (hasGmc) return isAcko ? 'gmc' : 'employee';
    if (hasRetail) return 'personal';
    return 'none';
  }

  Map<String, String> getHealthCoverageEventProps(HealthCoverageModel model) {
    return {
      'acko_insured': _getInsuranceType(model.ackoGmc, model.ackoRetail, isAcko: true),
      'non_acko_insured': _getInsuranceType(model.otherGmc, model.otherRetail),
    };
  }
}
