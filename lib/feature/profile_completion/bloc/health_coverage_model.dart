import '../../../common/model/BaseModel.dart';

class HealthCoverageModel extends BaseModel {
  final bool ackoRetail;
  final bool ackoGmc;
  final bool otherRetail;
  final bool otherGmc;
  final String webviewUrl;

  HealthCoverageModel({
    required this.ackoRetail,
    required this.ackoGmc,
    required this.otherRetail,
    required this.otherGmc,
    required this.webviewUrl,
  });

  HealthCoverageModel.error(error)
      : ackoRetail = false,
        ackoGmc = false,
        otherRetail = false,
        otherGmc = false,
        webviewUrl = '' {
    this.error = error;
  }

  factory HealthCoverageModel.fromJson(Map<String, dynamic> json) {
    final ackoInsured = json['ackoInsured'] as Map<String, dynamic>;
    final otherProvider = json['otherProvider'] as Map<String, dynamic>;

    return HealthCoverageModel(
      ackoRetail: ackoInsured['retail'] as bool,
      ackoGmc: ackoInsured['gmc'] as bool,
      otherRetail: otherProvider['retail'] as bool,
      otherGmc: otherProvider['gmc'] as bool,
      webviewUrl: json['webview_url'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ackoRetail': ackoRetail,
      'ackoGmc': ackoGmc,
      'otherRetail': otherRetail,
      'otherGmc': otherGmc,
      'webview_url': webviewUrl,
    };
  }

  HealthCoverageModel copyWith({
    bool? ackoGmc,
    bool? ackoRetail,
    bool? otherGmc,
    bool? otherRetail,
    String? webviewUrl,
  }) {
    return HealthCoverageModel(
      ackoGmc: ackoGmc ?? this.ackoGmc,
      ackoRetail: ackoRetail ?? this.ackoRetail,
      otherGmc: otherGmc ?? this.otherGmc,
      otherRetail: otherRetail ?? this.otherRetail,
      webviewUrl: webviewUrl ?? this.webviewUrl,
    );
  }
}
