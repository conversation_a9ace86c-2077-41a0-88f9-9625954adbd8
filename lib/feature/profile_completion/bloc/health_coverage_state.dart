import 'package:flutter/cupertino.dart';

import '../view/members/health_coverage_status_enum.dart';
import 'health_coverage_model.dart';

@immutable
abstract class HealthCoverageState {}

class HealthCoverageInitial extends HealthCoverageState {}

class HealthCoverageLoading extends HealthCoverageState {}

class HealthCoverageLoaded extends HealthCoverageState {
  final HealthCoverageModel model;

  HealthCoverageLoaded(this.model);
}

class HealthCoverageError extends HealthCoverageState {
  final String message;

  HealthCoverageError(this.message);
}