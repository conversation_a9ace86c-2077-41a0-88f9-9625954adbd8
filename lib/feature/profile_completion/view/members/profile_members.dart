import 'package:acko_flutter/common/util/screen_size_helper.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:analytics/events/tap_events.dart';
import 'package:design_module/utilities/hybrid_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/state_provider/StateProvider.dart';

import '../../../../common/util/color_constants.dart';
import '../../../../common/view/AckoText.dart';
import '../../../../common/view/FullPageLoader.dart';
import '../../../../common/view/dotted_border.dart';
import 'package:utilities/constants/constants.dart';
import '../../../../util/deeplink/next_url_handling_mixin.dart';
import '../../../car_journey/view/icon_app_bar.dart';
import '../../../home_view_util.dart';
import '../../bloc/health_coverage_cubit.dart';
import '../../bloc/health_coverage_model.dart';
import '../../bloc/health_coverage_state.dart';
import '../../bloc/profile_completion_bloc.dart';
import '../../model/profile_completion_model.dart';
import 'family_member_card.dart';
import 'health_coverage_status_enum.dart';

class ManageProfileMembers extends StatefulWidget {
  final Map<dynamic, dynamic>? params;

  const ManageProfileMembers({super.key, this.params});
  @override
  State<ManageProfileMembers> createState() => _ManageProfileMembersState();
}

class _ManageProfileMembersState extends State<ManageProfileMembers> with NextUrlHandlingMixin implements StateListener  {
  late ProfileCompletionBloc _bloc;
  late FullPageLoader _fullPageLoader;
  StateProvider _stateProvider = StateProvider();
  late String _webviewUrl;
  late HealthCoverageCubit _healthCoverageCubit;


  @override
  initState() {
    _fullPageLoader = FullPageLoader.instance;
    handleNextUrlNavigation(widget.params);
    _healthCoverageCubit = context.read<HealthCoverageCubit>();
    super.initState();
    _stateProvider.subscribe(this);
    AnalyticsTrackerManager.instance.sendEvent(
        event: PageLoadedConstants.FAMILY_MEMBERS_SCREEN,
        properties: {'platform': Util.getPlatform()});
  }

  @override
  void dispose() {
    super.dispose();
    _stateProvider.dispose(this);
  }

  @override
  Widget build(BuildContext context) {
    _bloc = BlocProvider.of<ProfileCompletionBloc>(context);
    return Scaffold(
      appBar:
          IconAppbar.newBackBtnAppBar(context, background: Colors.transparent),
      body: SafeArea(
        child: _bodyContent(),
      ),
    );
  }

  _bodyContent() {
    return BlocBuilder<ProfileCompletionBloc, ProfileCompletionState>(
        builder: (ctx, state) {
      _dismissFullPageLoader();
      List<ProfileFamilyMembers> _addressList =
          _bloc.profileCompletionModel?.familyMembers ?? [];
      return Padding(
        padding: const EdgeInsets.fromLTRB(20.0, 20.0, 10, 20),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextEuclidSemiBold18('My family'),
              SizedBox(
                height: 45,
              ),
              _membersView(_addressList),
              SizedBox(height: 30),
              _healthCoverageWithBloc(),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildHealthCoverageUI(HealthCoverageModel model) {
    final showAckoGmc = model.ackoGmc;
    final showAckoRetail = model.ackoRetail;
    final showOtherGmc = model.otherGmc;
    final showOtherRetail = model.otherRetail;
    final exactlyOneAcko = showAckoGmc ^ showAckoRetail;
    final exactlyOneOther = showOtherGmc ^ showOtherRetail;
    final noAckoCoverage = !showAckoGmc && !showAckoRetail;

    final showOnlyAckoEmployer = showAckoGmc && !showAckoRetail && !showOtherGmc && !showOtherRetail;
    final showOnlyAckoPersonal = showAckoRetail && !showAckoGmc && !showOtherGmc && !showOtherRetail;

    // Case: fallback case for other insurance when only one acko insured is true
    final showFallbackEmployer = exactlyOneAcko && showAckoRetail && !showOtherGmc;
    final showFallbackPersonal = exactlyOneAcko && showAckoGmc && !showOtherRetail;

    // Case: both acko false, but one of other is true
    final showBothOtherDueToPartial = noAckoCoverage && (showOtherGmc ^ showOtherRetail);

    final shouldShowOtherCoverageSection = showOtherGmc ||
        showOtherRetail ||
        showFallbackEmployer ||
        showFallbackPersonal ||
        showBothOtherDueToPartial ||
        (!showAckoGmc && !showAckoRetail && !showOtherGmc && !showOtherRetail);

    final showOnlyOtherHeadingWithNoData =
        !showAckoGmc && !showAckoRetail && !showOtherGmc && !showOtherRetail;

    final shouldShowAddDetails =
        (exactlyOneAcko && !showOtherGmc && !showOtherRetail) ||
            (exactlyOneOther && !showAckoGmc && !showAckoRetail) ||
            (showOnlyOtherHeadingWithNoData);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showAckoGmc || showAckoRetail)
          _ackoSection(
            showEmployer: showAckoGmc,
            showPersonal: showAckoRetail,
          ),

        if (shouldShowOtherCoverageSection)
          Padding(
            padding: noAckoCoverage ? EdgeInsets.zero : const EdgeInsets.only(top: 35.0),
            child: _otherCoverageSection(
              showEmployer: !showOnlyOtherHeadingWithNoData &&
                  (showOtherGmc || showFallbackEmployer || (showBothOtherDueToPartial && !showOtherGmc)),
              employerTick: showOtherGmc,
              showPersonal: !showOnlyOtherHeadingWithNoData &&
                  (showOtherRetail || showFallbackPersonal || (showBothOtherDueToPartial && !showOtherRetail)),
              personalTick: showOtherRetail,
              showOnlyAckoEmployer: showOnlyAckoEmployer,
              showOnlyAckoPersonal: showOnlyAckoPersonal,
            ),
          ),

        if (shouldShowAddDetails) ...[
          SizedBox(height: 12),
          _editDetails(title: HealthCoverageConstants.addDetails),
        ] else if (shouldShowOtherCoverageSection) ...[
          SizedBox(height: 16),
          _editDetails(title: HealthCoverageConstants.editDetails),
        ] else if (!(showAckoGmc || showAckoRetail || showOtherGmc || showOtherRetail)) ...[
          _coveragePlaceholder(HealthCoverageConstants.ahc),
          SizedBox(height: 16),
          _editDetails(title: HealthCoverageConstants.addDetails),
        ],
      ],
    );
  }


  Widget _ackoSection({bool showEmployer = false, bool showPersonal = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _coveragePlaceholder(HealthCoverageConstants.ahc),
        if (showEmployer) ...[
          SizedBox(height: 12),
          _coverageRow(title: HealthCoverageConstants.empProvided),
        ],
        if (showPersonal) ...[
          SizedBox(height: 12),
          _coverageRow(title: HealthCoverageConstants.personalHealth),
        ],
      ],
    );
  }

  Widget _otherCoverageSection({
    bool showEmployer = false,
    bool employerTick = true,
    bool showPersonal = false,
    bool personalTick = true,
    bool showOnlyAckoEmployer = false,
    bool showOnlyAckoPersonal = false
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _coveragePlaceholder(HealthCoverageConstants.yourOtherHealthCoverage),

        if (showEmployer && !showOnlyAckoPersonal && !showOnlyAckoEmployer) ...[
          SizedBox(height: 12),
          _otherCoverageRow(
            title: HealthCoverageConstants.empProvided,
            imageUrl: employerTick
                ? HealthCoverageConstants.greenCorrectTick
                : HealthCoverageConstants.redWrongCross,
          ),
        ],

        if (showPersonal && !showOnlyAckoEmployer && !showOnlyAckoPersonal) ...[
          SizedBox(height: 12),
          _otherCoverageRow(
            title: HealthCoverageConstants.personalHealth,
            imageUrl: personalTick
                ? HealthCoverageConstants.greenCorrectTick
                : HealthCoverageConstants.redWrongCross,
          ),
        ],

        if (showOnlyAckoEmployer && showOnlyAckoPersonal) ...[
          SizedBox(height: 12),
          _otherCoverageRow(
            title: HealthCoverageConstants.empProvided,
            imageUrl: employerTick
                ? HealthCoverageConstants.greenCorrectTick
                : HealthCoverageConstants.redWrongCross,
          ),
          SizedBox(height: 12),
          _otherCoverageRow(
            title: HealthCoverageConstants.personalHealth,
            imageUrl: personalTick
                ? HealthCoverageConstants.greenCorrectTick
                : HealthCoverageConstants.redWrongCross,
          ),
        ],
      ],
    );
  }

  Widget _healthCoverageWithBloc() {
    return BlocBuilder<HealthCoverageCubit, HealthCoverageState>(
    builder: (context, state) {
        if (state is HealthCoverageLoading) {
          return getShimmerLoader();
        } else if (state is HealthCoverageLoaded) {
          _webviewUrl = state.model.webviewUrl;
          return _buildHealthCoverageUI(state.model);
        } else if (state is HealthCoverageError) {
          return const SizedBox.shrink();
        }
        return SizedBox.shrink();
      },
    );
  }


  Widget _coverageRow({
    required String title,
  }) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorF5F5F5,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorE8E8E8,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextEuclidMediumL4(title)
          ),
          HybridImage(
            imageUrl: HealthCoverageConstants.ackoCovered,
            width: 100,
            height: 20,
            fit: BoxFit.contain,
          ),
        ],
      ),
    );
  }

  Widget _coveragePlaceholder(String title) {
    return Column (
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextEuclidBoldL6(title),
        SizedBox(height: 6),
      ],
    );
  }

  Widget _editDetails({required String title}) {
    final isAdd = title.toLowerCase().contains('add');
    final propertiesText = isAdd ? 'add details' : 'edit details';
    return GestureDetector(
      onTap: () {
        AnalyticsTrackerManager.instance.sendEvent(
            event: TapConstants.TAP_BTN_INTERACTION,
            properties: {
              "journey" : propertiesText,
              "from_page" : "family profile",
              "cta_text" : propertiesText
            });
        Navigator.pushNamed(
          context, Routes.WEB_PAGE_V2, arguments: {
            "url": _webviewUrl
        }
        );
      },
      child: TextEuclidMediumL4(title, textColor: color1B73E8),
    );
  }

  Widget _otherCoverageRow({required String title, required String imageUrl}) {
    return Container(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SvgPicture.network(
            imageUrl,
            width: 24,
            height: 24,
            placeholderBuilder: (context) => SizedBox(
              width: 24,
              height: 24,
            ),
          ),
          SizedBox(width: 8),
          Expanded(child: TextEuclidMediumL4(title)),
        ],
      ),
    );
  }

  _membersView(List<ProfileFamilyMembers> membersList) {
    return GridView.builder(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        if (index == 0) {
          return FamilyMemberCard(
            familyMemberData: ProfileFamilyMembers(name: _bloc.userName ?? ""),
            isMyProfile: true,
          );
        }
        if (index == (membersList.length + 1)) {
          return _getAddMemberCard();
        }
        return FamilyMemberCard(familyMemberData: membersList[index - 1]);
      },
      itemCount: membersList.length + 2,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: getScreenAwareWidth(20),
        mainAxisSpacing: getScreenAwareHeight(36),
        mainAxisExtent: getScreenAwareHeight(130),
      ),
    );
  }

  _getAddMemberCard() {
    return InkWell(
      onTap: _goToCreateMemberPage,
      child: Column(
        children: [
          DottedBorder(
            padding: EdgeInsets.only(),
            dashPattern: [3, 2],
            borderType: BorderType.Circle,
            color: color5B5675,
            child: Container(
              height: 80,
              width: 80,
              decoration:
                  BoxDecoration(shape: BoxShape.circle, color: colorF8F7FC),
              child: Center(child: Icon(Icons.add)),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(
              top: 12,
            ),
            child: TextEuclidSemiBold14(
              'Add member',
            ),
          ),
        ],
      ),
    );
  }

  _goToCreateMemberPage() {
    AnalyticsTrackerManager.instance.sendEvent(
        event: TapConstants.TAP_BTN_COMPLETE_PROFILE_ADD_INFO_INITIATE,
        properties: {'platform': Util.getPlatform(), 'type': 'family'});

    AnalyticsTrackerManager.instance.sendEvent(
        event: TapConstants.TAP_BTN_INTERACTION ,
        properties: {'journey': 'add family', 'from_page': 'profile'});

    Navigator.pushNamed(
      context,
      Routes.PROFILE_FAMILY_MEMBER_DETAILED_VIEW,
    ).then((value) {
      if (value != null) {
        HomeViewUtil.sharedInstance.showSnackBar(value.toString(), context);
        StateProvider().notify(ObserverState.PROFILE_REFRESH, data: {});
      }
    });
  }

  _dismissFullPageLoader() => _fullPageLoader.dismissFullPageLoader(context);

  @override
  void onStateChanged(ObserverState state, {data}) {
    if (state == ObserverState.REFRESH_HEALTH_PROFILE_QUESTIONS && mounted) {
      _healthCoverageCubit.fetchHealthCoverageStatus();
    }
  }
}
