enum HealthCoverageStatus {
  bothCovered<PERSON>y<PERSON>ck<PERSON>,
  onlyEmployerCoveredByAck<PERSON>,
  onlyPersonalCoveredByAck<PERSON>,
  bothNotCovered,
  gmcByAckoPersonal<PERSON><PERSON><PERSON><PERSON>,
  employerByAckoPersonal<PERSON>y<PERSON><PERSON>,
  bothCoveredByOther,
  onlyOnePolicyNotCovered
}

class HealthCoverageConstants {
  static const String ackoCovered = "https://auto.ackoassets.com/central-app/features/profile_build_health/acko_covered.png";
  static const String greenCorrectTick = "https://auto.ackoassets.com/central-app/features/profile_build_health/green_correct_tick.svg";
  static const String redWrongCross = "http://auto.ackoassets.com/central-app/features/profile_build_health/red_wrong_cross.svg";

  static const String yourOtherHealthCoverage = "Health coverages from other insurers";
  static const String ahc = "Health coverages from ACKO";
  static const String personalHealth = "Personal health policy";
  static const String empProvided = "Employer-provided policy";

  static const String addDetails = "+Add details";
  static const String editDetails = "Edit details";
}