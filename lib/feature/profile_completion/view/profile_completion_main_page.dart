import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/common/view/AckoText.dart';
import 'package:acko_flutter/common/view/FullPageLoader.dart';
import 'package:acko_flutter/feature/acko_services/ui/acko_services_initial_screen.dart';
import 'package:acko_flutter/feature/car_journey/view/icon_app_bar.dart';
import 'package:acko_flutter/feature/profile_completion/model/profile_completion_main_page_grid_model.dart';
import 'package:acko_flutter/feature/profile_completion/util/profile_completion_util.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/card_loaded_events.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';

import '../../../common/util/screen_size_helper.dart';
import '../../../util/deeplink/next_url_handling_mixin.dart';
import '../bloc/profile_completion_bloc.dart';

class ProfileCompletionMainPage extends StatefulWidget {
  final dynamic arguments;
  ProfileCompletionMainPage({Key? key, this.arguments}) : super(key: key);

  @override
  State<ProfileCompletionMainPage> createState() =>
      _ProfileCompletionMainPageState();
}

class _ProfileCompletionMainPageState extends State<ProfileCompletionMainPage> with NextUrlHandlingMixin {
  ProfileCompletionBloc? _bloc;
  bool isSectionCompleted = false;
  late FullPageLoader _fullPageLoader;
  bool isWhiteAppBarNeeded = false;
  bool? showCelebrationAnim = false;

  void initState() {
    super.initState();
    handleNextUrlNavigation(widget.arguments);
    _bloc = BlocProvider.of<ProfileCompletionBloc>(context);
    _fullPageLoader = FullPageLoader.instance;
    _bloc?.getProfileCompletionData();
    _showCelebration();
    AnalyticsTrackerManager.instance.sendEvent(
        event: CardLoadedConstants.USER_PROFILE_COMPLETION_LEVEL_PERCENTAGE,
        properties: {
          'platform': Util.getPlatform(),
          'count': _bloc?.profileCompletionModel?.completionPercentage,
          'from_page': 'profile_completion',
          'addons':
          ProfileCompletionUtil().getAddOns(_bloc?.profileCompletionModel)
        });
    if (widget.arguments.containsKey('category') == true &&
        widget.arguments.containsKey('route') == true &&
        widget.arguments['route'] != null)
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        _bloc?.navigateToCategory(context, widget.arguments);
      });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(alignment: AlignmentDirectional.topCenter, children: [
      Scaffold(
        backgroundColor: Colors.white,
        appBar: IconAppbar.newBackBtnAppBar(
          context,
          background: (isWhiteAppBarNeeded) ? colorF5F5F5 : colorE2F5FF,
        ),
        body: SafeArea(
          child: _getContents(),
        ),
      ),
      if (showCelebrationAnim ?? false)
        Positioned(
            top: 0,
            child: Lottie.asset(
              'assets/anim/profile_completion_celebrate.json',
              repeat: false,
              width: getScreenAwareWidth(400),
            ))
    ]);
  }

  _getContents() {
    return BlocConsumer<ProfileCompletionBloc, ProfileCompletionState>(
      bloc: _bloc,
      listenWhen: (_, current) {
        return (current is ProfileLoadingState ||
            current is LoadedState ||
            current is RefreshMainPageState);
      },
      listener: (context, state) {
        if (state is ProfileLoadingState)
          _fullPageLoader.showFullPageLoader(context);
        else if (state is RefreshMainPageState) {
          _showCelebration();
        } else {
          _fullPageLoader.dismissFullPageLoader(context);
        }
      },
      buildWhen: (prev, current) {
        return (current is LoadedState || current is ProfileErrorState);
      },
      builder: (context, state) {
        if (state is LoadedState || state is RefreshMainPageState) {
          FullPageLoader.instance.dismissFullPageLoader(context);
          return Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [_getHeader(), _getCompletionGrid()],
          );
        } else if (state is ProfileLoadingState) {
          return Center(child: CircularProgressIndicator());
        } else {
          return AckoServicesIntiailScreen(
            isOutlinedButton: true,
            title: something_went_wrong,
            imgUrl: Util.getAssetImage(assetName: 'ic_bucket_drop.svg'),
            subTitle: api_something_went_wrong_subtitle,
            btnTitle: "Try again",
            onTap: () {
              _bloc?.getProfileCompletionData();
            },
          );
        }
      },
    );
  }

  _getHeader() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
            colors: [colorE2F5FF, Colors.white],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter),
      ),
      child: Column(
        children: [
          SizedBox(
            height: getScreenAwareHeight(70),
            child: ProfileCompletionUtil().getCircularProgressBar(
                context,
                _bloc?.profileCompletionModel?.completionPercentage ?? 0,
                '${_bloc?.profileCompletionModel?.completionPercentage ?? 0}%',
                height: 68),
          ),
          Padding(
            padding: EdgeInsets.only(top: getScreenAwareHeight(25)),
            child: TextEuclidBoldL18(
                ((_bloc?.profileCompletionModel?.completionPercentage ?? 0) ==
                    100)
                    ? profile_setup_complete
                    : almost_there),
          ),
          Padding(
            padding: EdgeInsets.only(
              top: getScreenAwareHeight(8),
              bottom: getScreenAwareHeight(36),
            ),
            child: TextEuclidMedium14(
              ((_bloc?.profileCompletionModel?.completionPercentage ?? 0) ==
                  100)
                  ? your_profile_is_ready
                  : profile_from_good_to_great,
              textColor: color5B5675,
              textAlign: TextAlign.center,
            ),
          )
        ],
      ),
    );
  }

  _getCompletionGrid() {
    List<ProfileCompletionGrid>? gridList =
    _bloc?.getProfileCompletionMainPageGrid(_bloc?.profileCompletionModel);
    // return SizedBox.shrink();
    return Expanded(
      child: GridView.builder(
        physics: NeverScrollableScrollPhysics(),
        padding: EdgeInsets.symmetric(horizontal: getScreenAwareWidth(20)),
        itemBuilder: (context, index) => _getCard(gridList![index]),
        itemCount: gridList?.length,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: getScreenAwareWidth(16),
          mainAxisSpacing: getScreenAwareHeight(16),
        ),
      ),
    );
  }

  _getCard(ProfileCompletionGrid card) {
    return InkWell(
      onTap: () {
        _bloc?.onGridClick(context, card);
      },
      child: Container(
        decoration: BoxDecoration(
            border: Border.all(color: colorE7E7F0),
            borderRadius: BorderRadius.circular(12.0)),
        child: Stack(children: [
          Center(
            child: Column(
              children: [
                Padding(
                  padding: EdgeInsets.only(top: getScreenAwareHeight(28)),
                  child: Stack(children: [
                    SvgPicture.asset(Util.getAssetImage(
                        assetName: 'ic_profile_grey_circle.svg')),
                    Align(
                        widthFactor: 1.35,
                        heightFactor: 1.35,
                        child: SvgPicture.asset(
                            Util.getAssetImage(assetName: card.imageUrl))),
                  ]),
                ),
                Padding(
                  padding: EdgeInsets.only(top: getScreenAwareHeight(10)),
                  child: TextEuclidSemiBold16(card.categoryName!),
                ),
                Padding(
                    padding: EdgeInsets.only(
                      top: getScreenAwareHeight(6),
                    ),
                    child: TextEuclidMedium14(
                      _bloc?.getNoOfItems(card),
                      textColor: color7A7690,
                    )),
              ],
            ),
          ),
          if (card.isCategoryCompleted)
            Padding(
              padding: EdgeInsets.only(
                top: getScreenAwareHeight(10),
                right: getScreenAwareWidth(10),
              ),
              child: Align(
                child: SvgPicture.asset(
                  Util.getAssetImage(
                      assetName: 'ic_profile_completion_check_tick.svg'),
                  height: getScreenAwareHeight(24),
                  width: getScreenAwareWidth(24),
                ),
                alignment: Alignment.topRight,
              ),
            ),
        ]),
      ),
    );
  }

  _showCelebration() async {
    showCelebrationAnim = await _bloc?.canShowProfileCompletionGraffiti();
    if ((showCelebrationAnim ?? false) && this.mounted) {
      setState(() {});
      await Future.delayed(Duration(milliseconds: 1500));
      setState(() {
        showCelebrationAnim = false;
      });
    }
  }
}