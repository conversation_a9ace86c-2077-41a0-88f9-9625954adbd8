import 'package:acko_flutter/feature/homeSDUI/parser/home_header.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';

class HomeParser extends SDUIBaseParser {
  @override
  fromJson(
    Map<String, dynamic> json, {
    SDUIBaseParser? parser,
    int? widgetPosition,
    String? key,
    Map<String, dynamic>? homePageDataStore,
  }) {
    List<Widget> homeWidgets = [];
    if (json["widgets"] != null) {
      final widgets = json["widgets"];
      widgets.forEach((v) {
        if (v['\$type'] == 'HomeView.Header') {
          homeWidgets.add(HomeHeader.fromJson(v, homePageDataStore!));
        } else if (v['\$type'] == 'HomeView.Body') {
          final body = v;
          if (body['buyCards'] != null) {
            body['buyCards'].forEach((w) {
              Widget buyCardWidget = SDUIParser.getInstance().fromJson(w);
              if (buyCardWidget != SDUINull()) homeWidgets.add(buyCardWidget);
            });
          }
          if (body['services'] != null) {
            body['services'].forEach((w) {
              Widget serviceGrid = SDUIParser.getInstance().fromJson(w);
              if (serviceGrid != SDUINull()) homeWidgets.add(serviceGrid);
            });
          }
          if (body['contentView'] != null) {
            body['contentView'].forEach((v) {
              String type = v['\$type'];
              Widget? contentWidget;
              if (type == 'AlertWidget') {
                contentWidget = BlocProvider(
                  create: (context) => AlertsBloc(id: v['id']),
                  child: AlertsWidget(dataStore: homePageDataStore),
                );
              } else {
                contentWidget = SDUIParser.getInstance().fromJson(v);
              }
              if (contentWidget != SDUINull()) homeWidgets.add(contentWidget);
            });
          }
          if (body['footerItems'] != null) {
            body['footerItems'].forEach((v) {
              Widget footerWidget = SDUIParser.getInstance().fromJson(v);
              if (footerWidget != SDUINull()) homeWidgets.add(footerWidget);
            });
          }
        }
      });
    }
    return homeWidgets;
  }
}
