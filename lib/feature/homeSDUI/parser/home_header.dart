import 'package:acko_flutter/feature/homeSDUI/components/buy_card_tabbar/async_tab_bar.dart';
import 'package:acko_flutter/feature/homeSDUI/components/home_buy_cards/buy_card_sdui.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:flutter/material.dart';
import 'package:sdui/sdui.dart';

import '../components/active_policy_card/active_policy_card_ui.dart';
import 'package:acko_flutter/feature/homeSDUI/components/home_status_card_wrapper.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../components/buy_card_tabbar/async_tab_bar_cubit.dart';

class HomeHeader extends SDUIStatelessWidget {
  Widget? appBar;
  WidgetBackground? background;
  Widget? tabBar;
  EdgeInsets? padding;
  List<Widget>? headingItems = [];

  HomeHeader.fromJson(
      Map<String, dynamic> json, Map<String, dynamic> homePageDataStore) {
    if (json['background'] != null) {
      background = WidgetBackground.fromJson(json['background']);
    }
    if (json['padding'] != null) {
      padding = getEdgeInsets(json['padding']);
    }
    if (json['appbar'] != null) {
      appBar = SDUIParser.getInstance().fromJson(json['appbar']);
    }
    if (json['tabBar'] != null) {
      var type = json['tabBar']["\$type"];
      if (type == asyncWidget) {
        tabBar = BlocProvider(
          key: ValueKey(json['tabBar']['attributes']['url']),
          create: (_) => BuyCardsAsyncTabbarCubit(),
          child: BuyCardsAsyncTabbar().fromJson(json['tabBar']),
        );
      } else if (type == "component.tab.TabBar") {
        SduiTabBar? widget =
            SDUIParser.getInstance().fromJson(json['tabBar']) as SduiTabBar;
        List<SDUITabChildrenAbstract>? tabChildrenWidgets;
        if (widget.tabChild.isNotNullOrEmpty) {
          tabChildrenWidgets = [];
          for (SDUITabChildrenAbstract children in widget.tabChild!) {
            tabChildrenWidgets.add(BuyCardSDUI(
              childWidgets: children.childWidgets,
            ));
          }
        }
        tabBar = TabBarParser().parseTabBarView(widget, json['tabBar'],
            tabChildrenWidgets: tabChildrenWidgets);
      } else {
        tabBar = SDUIParser.getInstance().fromJson(json['tabBar']);
      }
    }
    if (json['headingItems'] != null) {
      for (var element in json['headingItems']) {
        if (element['\$type'] == 'HomeView.ActivePolicyCard') {
          ///TODO : check if we can templetize this in a generic card
          headingItems?.add(SDUIActivePolicyCard.fromJson(element));
        } else if (element['\$type'] == 'AlertWidget') {
          Widget widget = BlocProvider(
            create: (context) => AlertsBloc(id: element['id']),
            child: AlertsWidget(dataStore: homePageDataStore),
          );
          headingItems?.add(widget);
        } else if (element['\$type'] == 'HomeView.StatusCardWrapper') {
          Widget widget = BlocProvider(
              create: (context) => CommonDataStoreViewBloc(),
              child: HomeStatusCardWrapper().fromJson(element));
          headingItems?.add(widget);
        } else {
          headingItems?.add(SDUIParser.getInstance().fromJson(element));
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: getBoxDecoration(background, null),
      padding: padding,
      child: Column(
        children: [
          appBar ?? SizedBox.shrink(),
          Column(children: headingItems ?? []),
          tabBar ?? SizedBox.shrink(),
        ],
      ),
    );
  }
}
