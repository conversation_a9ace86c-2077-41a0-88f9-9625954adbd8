import 'package:acko_flutter/common/bloc/ApplicatioBloc.dart';
import 'package:acko_flutter/common/util/scroll_fold_mixin.dart';
import 'package:acko_flutter/feature/acko_services/ui/acko_service_loading_screen.dart';
import 'package:acko_flutter/feature/homeSDUI/bloc/home_bloc/home_cubit.dart';
import 'package:acko_flutter/feature/homeSDUI/bloc/home_bloc/home_states.dart';
import 'package:acko_flutter/main.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/analytics.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:design_module/uikit/widgets/scroll_to_top.dart';
import 'package:design_module/utilities/hide_scroll_glow_effect.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';
import 'package:session_manager_module/session_manager.dart';
import 'package:utilities/remote_config/remote_config.dart';
import 'package:utilities/state_provider/StateProvider.dart';

import '../../../common/util/strings.dart';
import '../../../common/view/ErrorWidget.dart';
import '../../../network/ApiResponse.dart';

class HomeScreenSDUI extends StatefulWidget {
  const HomeScreenSDUI({Key? key}) : super(key: key);

  @override
  State<HomeScreenSDUI> createState() => _HomeScreenSDUIState();
}

class _HomeScreenSDUIState extends State<HomeScreenSDUI>
    with AutomaticKeepAliveClientMixin, ScrollFoldMixin
    implements StateListener, ErrorViewRetryCallBack {
  late HomeCubit bloc;
  late CommonDataStoreBloc commonDataStoreBloc;
  StateProvider _stateProvider = StateProvider();

  @override
  void initState() {
    _stateProvider.subscribe(this);
    sendLoadedEvent();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      bloc = context.read<HomeCubit>();
      commonDataStoreBloc = context.read<CommonDataStoreBloc>();
      bloc.homeScrollController.addListener(bloc.addHomeScrollListner);
      addScrollFoldListener(bloc.homeScrollController, 'home');
    });
    super.initState();
  }

  @override
  dispose() {
    _stateProvider.dispose(this);
    super.dispose();
  }

  _checkForSavedDeeplink() {
    if (!mounted) return;

    if (bloc.savedHomeTabIndex != null) {
      _stateProvider.notify(ObserverState.CHANGE_SDUI_TAB,
          data: {"index": bloc.savedHomeTabIndex, "tab_id": "insurance_tabs"});

      /// Save tab change index from deeplink as a User preferred tab
      bloc.storeUserPreferredTab(bloc.savedHomeTabIndex!);
    }
  }

  sendLoadedEvent() async{
    final appVersion = (await RemoteConfigInstance.instance.getGbAsyncData(RemoteConfigKeysSet.APP_IA_VERSION)) ?? "app_v9";
    AnalyticsTrackerManager.instance.sendEvent(
        event: PageLoadedConstants.APP_HOME_FULLY_LOADED_SUCCESSFULLY,
        properties: {'platform': appVersion});
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return ScrollsToTop(
        child: Scaffold(
          backgroundColor: Colors.white,
          body: BlocListener<CommonDataStoreBloc, CommonDataStoreBlocStates>(
            listenWhen: (prev, cur) => cur is LobDataUpdated,
            listener: (context, state) {
              if (state is LobDataUpdated && state.allLobsApisCalled) {
                getUserPropertiesAndAttach();
              }
            },
            child: BlocConsumer<HomeCubit, HomeStates>(
              builder: (context, state) {
                if (state is LoadingState) {
                  return SizedBox(
                      width: MediaQuery.of(context).size.width,
                      height: MediaQuery.of(context).size.height,
                      child: Center(child: AckoServiceLoadingScreen()));
                } else if (state is LoadedState) {
                  return RefreshIndicator(
                    onRefresh: () => context.read<HomeCubit>().getHomeView(
                        showLoading: false, refreshDataStore: true),
                    child: Stack(
                      children: [
                        ScrollConfiguration(
                          behavior: HideScrollGlowEffect(),
                          child: ListView.builder(
                            padding: EdgeInsets.zero,
                            shrinkWrap: true,
                            controller:
                                context.read<HomeCubit>().homeScrollController,
                            itemCount: state.widgets.length,
                            itemBuilder: (buildContext, index) =>
                                state.widgets[index],
                          ),
                        ),
                        AnimatedPositioned(
                          bottom: state.showSnackBar ? 48 : 0,
                          duration: Duration(milliseconds: 300),
                          child: Transform.translate(
                            offset: Offset(0, 48),
                            child: Align(
                              alignment: Alignment.bottomCenter,
                              child: state.homeScrollableSnackBar,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                } else {
                  return ErrorPage(
                      errorHandler: ErrorHandler(something_went_wrong),
                      retryCallbackFunc: this);
                }
              },
              listener: (BuildContext context, HomeStates state) {
                if (state is onPageInitState) {
                  bloc.executeActions(context);
                } else if (state is LoadedState) {
                  if (!state.isSnacbkbarEvent) {
                    _checkForSavedDeeplink();
                  }
                }
              },
            ),
          ),
        ),
        onScrollsToTop: _onScrollsToTop);
  }

  Future<void> _onScrollsToTop(ScrollsToTopEvent event) async {
    final scrollController = context.read<HomeCubit>().homeScrollController;
    if (scrollController.hasClients) {
      scrollController.animateTo(
        0.0,
        duration: Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void getUserPropertiesAndAttach() {
    List<Map<String, dynamic>> activePolicies = [];
    for (String location in bloc.userPropertyLocation) {
      Map<String, dynamic>? data =
          commonDataStoreBloc.getPositionedDataFromMap(location);
      if (data != null && data.isNotEmpty) activePolicies.add(data);
    }
    bloc.attachUserProperty(activePolicies);
  }

  @override
  bool get wantKeepAlive => true;

  @override
  void onStateChanged(ObserverState state, {data}) async {
    ///Check user logged in
    final String? userEkey =
        await SessionManager.instance.storageManager.getUserEkey();
    if (!mounted || userEkey.isNullOrEmpty) {
      logException(Exception(
          'user logged out mounted:${mounted} ${state.name} but home page opened'));
      return;
    }

    if (state == ObserverState.PROFILE_REFRESH) {
      bloc.getHomeView(showLoading: false);
    } else if (state == ObserverState.HOME_TAB_SCROLL_TO_TOP &&
        data['index'] == 0) {
      bloc.homeScrollController.animateTo(0,
          duration: Duration(milliseconds: 300),
          curve: Curves.fastEaseInToSlowEaseOut);
    } else if (state == ObserverState.SDUI_TAB_CLICKED &&
        data['tab_id'] == 'insurance_tabs') {
      bloc.storeUserPreferredTab(data['index']);
    } else if (state == ObserverState.SDUI_TAB_CHANGED &&
        data['tab_id'] == 'insurance_tabs') {
      bloc.storeDefaultTabSelection(data['index']);
    } else if (state == ObserverState.SDUI_TAB_BAR_LOADED) {
      _checkForSavedDeeplink();
    } else if (state == ObserverState.CHANGE_SDUI_TAB &&
        data['tab_id'] == 'insurance_tabs' &&
        data['from'] == 'application_bloc') {
      bloc.savedHomeTabIndex = data['index'];
    }
  }

  @override
  onRetry() {
    HomeCubit bloc = BlocProvider.of<HomeCubit>(context);
    bloc.refresh();
  }
}
