import 'dart:async';
import 'dart:io';

import 'package:acko_flutter/common/model/UserPropertiesModel.dart';
import 'package:acko_flutter/common/util/PreferenceHelper.dart';
import 'package:acko_flutter/feature/homeSDUI/bloc/home_bloc/home_states.dart';
import 'package:acko_flutter/feature/homeSDUI/components/flexi_update_footer/flexi_update_footer.dart';
import 'package:acko_flutter/feature/homeSDUI/parser/home_parser.dart';
import 'package:acko_flutter/util/analytic_util.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_flutter/util/in_app_update_util.dart';
import 'package:flutter/material.dart';
import 'package:sdui/sdui.dart' as sdui;
import 'package:sdui/sdui.dart';
import 'package:session_manager_module/session_manager.dart';
import 'package:utilities/remote_config/remote_config.dart';
import 'package:utilities/utilities.dart';
import 'package:utilities/widgets/acko_safe_cubit.dart';

import '../../../../common/util/bottom_insets_observer.dart';
import '../../data/home_default_tab_index.dart';

class HomeCubit extends AckoSafeCubit<HomeStates> with LocationHeaderMixin {
  final String url;
  int? savedHomeTabIndex;

  HomeCubit(this.url) : super(LoadingState()) {
    _homeParser = HomeParser();
    getHomeView();
    homeScrollController = ScrollController();
    _insetObserver = BottomInsetObserver()
      ..addListener(_keyboardVisibiltyStateListener);
  }

  @override
  Future<void> close() {
    homeScrollController.removeListener(() {});
    homeScrollController.dispose();
    _insetObserver.dispose();
    _isKeyboardVisible.close();
    return super.close();
  }

  refresh() {
    emit(LoadingState());
    getHomeView(refreshDataStore: true);
  }

  final BaseRepository _baseRepository = BaseRepository();
  List<Widget> homeWidgets = [];
  StateProvider _stateProvider = StateProvider();
  Widget? appUpdateBanner;

  /// SnackBar Variables
  ///
  Widget? homeScrollableSnackBar;
  List<SDUIAction?> onPageInitActions = [];
  final _isKeyboardVisible = StreamController<bool>.broadcast();
  bool _showSnackBar = true;
  late ScrollController homeScrollController;
  late BottomInsetObserver _insetObserver;
  bool onPageInitExecuted = false;
  Map<String, dynamic> homePageAlertsData = {};
  late HomeParser _homeParser;

  /// Home [ScrollController] listner to listen to offset changes for Snackbar control
  ///
  void addHomeScrollListner() {
    if (homeScrollController.hasClients) {
      bool _canShowBottomStripTemp = !((homeScrollController.offset) > 100);
      if (_showSnackBar != _canShowBottomStripTemp) {
        _showSnackBar = _canShowBottomStripTemp;
        if (state is LoadedState) {
          final currentstate = state as LoadedState;
          emit(LoadedState(currentstate.widgets,
              homeScrollableSnackBar: currentstate.homeScrollableSnackBar,
              showSnackBar: _showSnackBar && homeScrollableSnackBar != null));
        }
      }
    }
  }

  /// Keyboard visibility Controller for Snackbar control
  ///
  void _keyboardVisibiltyStateListener(BottomInsetChanges change) {
    if ((change.currentInset > 0) != _isKeyboardVisible)
      _isKeyboardVisible.add(change.currentInset > 0);
    if (state is LoadedState) {
      final currentstate = state as LoadedState;
      emit(
        LoadedState(
          currentstate.widgets,
          homeScrollableSnackBar: currentstate.homeScrollableSnackBar,
          showSnackBar: _showSnackBar &&
              homeScrollableSnackBar != null &&
              // TODO: Verify this condition
              _isKeyboardVisible.sink == false,
          isSnacbkbarEvent: true,
        ),
      );
    }
  }

  bool checkIOSUpdates() {
    if (Platform.isIOS) {
      UpdateType? updateAvailableType = InAppUpdateUtility.availableUpdateType;
      return (updateAvailableType != null &&
          updateAvailableType == UpdateType.flexi);
    }
    return false;
  }

  Future<void> executeActions(BuildContext context) async {
    bool isShown = await getBoolPrefs(
            BoolDataSharedPreferenceKeys.ASK_NAME_BOTTOM_SHEET_SHOWN) ??
        false;
    if (!isShown) {
      onPageInitActions.forEach((element) {
        element?.executeAction(context, null);
      });
      await setBoolPrefs(
          BoolDataSharedPreferenceKeys.ASK_NAME_BOTTOM_SHEET_SHOWN, true);
    }
  }

  _getAlerts() async {
    final alertsResponse =
        await _baseRepository.getResponse('/pages/alerts/pages/home');
    if (alertsResponse.error == null) {
      homePageAlertsData.clear();
      homePageAlertsData.addAll(alertsResponse.data!);
    }
  }

  getHomeView({bool showLoading = true, bool refreshDataStore = false}) async {
    if (showLoading) emit(LoadingState());
    await _getAlerts();
    if (refreshDataStore) {
      _stateProvider.notify(ObserverState.MyAccount_Refresh);
    }

    final tabPrefQuery = await getDefaultTabQueryParamFromPref();
    await ensureLocationHeadersInitialised();
    ResponseWrapper response = await _baseRepository.getResponse(
      url,
      requestHeaders: locationDetails?.toJson(),
      queryParams: tabPrefQuery,
    );

    if (response.error == null) {
      if (response.data != null) {
        homeWidgets = _homeParser.fromJson(response.data!,
            homePageDataStore: homePageAlertsData);

        ///  Parser for the bottom SnackBar on Home
        if (response.data!['snackBar'] != null) {
          homeScrollableSnackBar = sdui.SDUIParser.getInstance()
              .fromJson(response.data!['snackBar']['snackBar']);
        }

        /// Parse onInit actins
        if (response.data!['onInitAction'] != null) {
          var children = response.data!['onInitAction'];
          if (children is List<dynamic>) {
            onPageInitActions
                .addAll(children.map((e) => SDUIAction().fromJson(e)));
            if (onPageInitActions.isNotEmpty && !onPageInitExecuted) {
              emit(onPageInitState());
              onPageInitExecuted = true;
            }
          }
        }

        if (checkIOSUpdates()) {
          appUpdateBanner = FlexiUpdateFooter(onTap: () {
            ///to load the home footer banner on close of update banner
            emit(LoadedState(homeWidgets,
                homeScrollableSnackBar: homeScrollableSnackBar,
                showSnackBar: _showSnackBar && homeScrollableSnackBar != null));
          });

          emit(LoadedState(homeWidgets,
              homeScrollableSnackBar: appUpdateBanner, showSnackBar: true));
          return;
        }

        emit(LoadedState(homeWidgets,
            homeScrollableSnackBar: homeScrollableSnackBar,
            showSnackBar: _showSnackBar && homeScrollableSnackBar != null));
      } else {
        emit(ErrorState("An error occurred in sdui repository"));
      }
    } else {
      emit(ErrorState("An error occurred in sdui repository"));
    }
  }

  void attachUserProperty(List<Map<String, dynamic>> activeProperties) {
    if (activeProperties.isNotEmpty)
      AnalyticsUtil.instance.setUserProperties(
          UserPropertiesModel(activePoliciesList: activeProperties));
  }

  Future<Map<String, dynamic>> getDefaultTabQueryParamFromPref() async {
    /// Here `HOME_USER_PREFERED_TAB` is either an intentiaonl click or deeplink
    /// selection made by the user on the home tabs. This is used to differentaite
    /// from the default index that the tabs are intialized with.
    final String? json = await getStringPrefs(
        StringDataSharedPreferenceKeys.HOME_USER_PREFERED_TAB);
    final userPreferredTab = (json != null && json.isNotEmpty)
        ? HomeDefaultTabIndex.fromJson(json)
        : null;

    /// defaultTabIndex is the default index the tab was intialized
    /// with by any logic on app or SDUI.
    final int? defaultTabIndex =
        (await getIntPrefs(IntDataSharedPreferenceKeys.HOME_DEFAULT_TAB));

    return {
      'defaultTabIndex': defaultTabIndex,
      'clickedTabIndex': userPreferredTab?.index,
      'clickedTabTimestamp': userPreferredTab?.timestamp,
    };
  }

  Future<void> storeUserPreferredTab(int index) async {
    final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final HomeDefaultTabIndex tabIndex = HomeDefaultTabIndex(
      index: index,
      timestamp: timestamp,
    );

    await setStringPrefs(
      StringDataSharedPreferenceKeys.HOME_USER_PREFERED_TAB,
      tabIndex.toJson(),
    );
  }

  Future<void> storeDefaultTabSelection(int index) async {
    await setIntPrefs(
      IntDataSharedPreferenceKeys.HOME_DEFAULT_TAB,
      index,
    );
  }

  List<String> userPropertyLocation = [
    'auto.policies.user_property.active_policy',
    'health.policies.user_property.active_policy',
    'travel.policies.user_property.active_policy',
    'life.policies.user_property.active_policy',
    'embedded.policies.user_property.active_policy'
  ];
}
