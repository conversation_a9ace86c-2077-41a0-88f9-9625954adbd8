import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';

class HomeStatusCardWrapper extends SDUIStatelessWidget {
  List<String>? modes;
  EdgeInsets? margin;
  Widget? shimmerWidget;

  @override
  fromJson(Map<String, dynamic>? json) {
    if (json != null) {
      margin = getEdgeInsets(json?["margin"]);
      modes = (json['modes'] != null
          ? (json['modes'] as List).map((e) => e.toString()).toList()
          : null);

      shimmerWidget = SDUIParser.getInstance().fromJson(json['shimmerView']);
    }
    return this;
  }

  @override
  Widget build(BuildContext context) {
    CommonDataStoreViewBloc commonDataStoreViewBloc = BlocProvider.of(context);
    CommonDataStoreBloc globalCommonDataStore = BlocProvider.of(context);
    return AnimatedSize(
      duration: Duration(milliseconds: 300),
      child: BlocConsumer<CommonDataStoreBloc, CommonDataStoreBlocStates>(
        listener: (prev, state) {
          Map<String, dynamic> uiJson =
              globalCommonDataStore.getAllCards(modes ?? []);
          if (uiJson.isNotEmpty) {
            commonDataStoreViewBloc.getAllCards(modes ?? [], uiJson);
          }
        },
        builder: (prev, state) {
          if (!globalCommonDataStore.allLobsApisCalled &&
              !globalCommonDataStore.isFirstLoadInistialized)
            return getShimmerView();

          return BlocBuilder<CommonDataStoreViewBloc, CommonDataStoreViewState>(
              builder: (prev, state) {
            if (state is CommonDataStoreViewEmptyState) {
              return SizedBox.shrink();
            } else if (state is CommonDataStoreViewLoadedState) {
              if (state.widgets.isEmpty) return const SizedBox.shrink();
              return SingleChildScrollView(
                padding: margin,
                scrollDirection: Axis.horizontal,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: state.widgets,
                ),
              );
            }
            return const SizedBox.shrink();
          });
        },
      ),
    );
  }

  getShimmerView() {
    return shimmerWidget ?? getShimmerLoader();
  }
}
