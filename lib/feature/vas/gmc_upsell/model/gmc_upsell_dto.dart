class GMCUpsellSheetDto {
  CardData? cardData;
  BottomSheetData? bottomSheetData;
  int? sumInsuredCap;
  LifeUpsellCardData? lifeUpsellCardData;
  LifeUpsellBottomSheetData? lifeUpsellBottomSheetData;
  List<String>? excludedCorporates;
  bool? disableBanner;

  GMCUpsellSheetDto({this.cardData, this.bottomSheetData, this.sumInsuredCap, this.excludedCorporates, this.disableBanner});

  GMCUpsellSheetDto.fromJson(Map<String, dynamic> json) {
    cardData = json['card_data'] != null
        ? new CardData.fromJson(json['card_data'])
        : null;
    bottomSheetData = json['bottom_sheet_data'] != null
        ? new BottomSheetData.fromJson(json['bottom_sheet_data'])
        : null;
    sumInsuredCap =
        int.tryParse(json['sum_insured_cap']?.toString() ?? '') ?? 0;
    lifeUpsellCardData = json['life_upsell_card_data'] != null
        ? LifeUpsellCardData.fromJson(json['life_upsell_card_data'])
        : null;
    lifeUpsellBottomSheetData = json['life_upsell_bottom_sheet_data'] != null
        ? LifeUpsellBottomSheetData.fromJson(
            json['life_upsell_bottom_sheet_data'])
        : null;
    excludedCorporates = json['excluded_corporates'] != null
        ? List<String>.from(json['excluded_corporates'])
        : null;
    disableBanner = json['disable_banner'] != null
        ? json['disable_banner']
        : false;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.cardData != null) {
      data['card_data'] = this.cardData!.toJson();
    }
    if (this.bottomSheetData != null) {
      data['bottom_sheet_data'] = this.bottomSheetData!.toJson();
    }
    if (this.sumInsuredCap != null) {
      data['sum_insured_cap'] = this.sumInsuredCap.toString();
    }
    if (this.lifeUpsellCardData != null) {
      data['life_upsell_card_data'] = this.lifeUpsellCardData!.toJson();
    }
    if (this.lifeUpsellBottomSheetData != null) {
      data['life_upsell_bottom_sheet_data'] =
          this.lifeUpsellBottomSheetData!.toJson();
    }
    if (this.excludedCorporates != null) {
      data['excluded_corporates'] = this.excludedCorporates;
    }
    if (this.disableBanner != null) {
      data['disable_banner'] = this.disableBanner;
    }
    return data;
  }
}

class CardData {
  String? title;
  String? subtitle;
  String? imageUrl;
  String? upsellCardType;
  String? action;
  String? redirectUrl;

  CardData({
    this.title,
    this.subtitle,
    this.imageUrl,
    this.upsellCardType,
    this.action,
    this.redirectUrl,
  });

  CardData.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    subtitle = json['subtitle'];
    imageUrl = json['imageUrl'];
    upsellCardType = json['upsell_card_type'];
    action = json['action'];
    redirectUrl = json['redirect_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['title'] = title;
    data['subtitle'] = subtitle;
    data['imageUrl'] = imageUrl;
    data['upsell_card_type'] = upsellCardType;
    data['action'] = action;
    data['redirect_url'] = redirectUrl;
    return data;
  }
}

class BottomSheetData {
  String? title;
  String? subtitle;
  String? imageUrl;
  String? description;
  Cta? cta;

  BottomSheetData(
      {this.title, this.subtitle, this.imageUrl, this.description, this.cta});

  BottomSheetData.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    subtitle = json['subtitle'];
    imageUrl = json['imageUrl'];
    description = json['description'];
    cta = json['cta'] != null ? new Cta.fromJson(json['cta']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['title'] = this.title;
    data['subtitle'] = this.subtitle;
    data['imageUrl'] = this.imageUrl;
    data['description'] = this.description;
    if (this.cta != null) {
      data['cta'] = this.cta!.toJson();
    }
    return data;
  }
}

class Cta {
  String? ctaText;
  String? ctaSubtext;
  String? redirectUrl;

  Cta({this.ctaText, this.ctaSubtext, this.redirectUrl});

  Cta.fromJson(Map<String, dynamic> json) {
    ctaText = json['cta_text'];
    ctaSubtext = json['cta_subtext'];
    redirectUrl = json['redirect_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['cta_text'] = this.ctaText;
    data['cta_subtext'] = this.ctaSubtext;
    data['redirect_url'] = this.redirectUrl;
    return data;
  }
}

class LifeUpsellCardData {
  String? title;
  String? subtitle;
  String? imageUrl;

  LifeUpsellCardData({this.title, this.subtitle, this.imageUrl});

  LifeUpsellCardData.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    subtitle = json['subtitle'];
    imageUrl = json['imageUrl'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['title'] = this.title;
    data['subtitle'] = this.subtitle;
    data['imageUrl'] = this.imageUrl;
    return data;
  }
}

class LifeUpsellBottomSheetData {
  String? title;
  String? subtitle;
  String? imageUrl;
  String? description;
  Cta? cta;

  LifeUpsellBottomSheetData({
    this.title,
    this.subtitle,
    this.imageUrl,
    this.description,
    this.cta,
  });

  LifeUpsellBottomSheetData.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    subtitle = json['subtitle'];
    imageUrl = json['imageUrl'];
    description = json['description'];
    cta = json['cta'] != null ? new Cta.fromJson(json['cta']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['title'] = this.title;
    data['subtitle'] = this.subtitle;
    data['imageUrl'] = this.imageUrl;
    data['description'] = this.description;
    if (this.cta != null) {
      data['cta'] = this.cta!.toJson();
    }
    return data;
  }
}

enum BuyType { life_cross_sell, gmc_up_sell }
