import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/util/screen_size_helper.dart';
import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/common/view/AckoText.dart';
import 'package:acko_flutter/common/view/app_bar.dart';
import 'package:acko_flutter/common/view/shimmer_mask.dart';
import 'package:acko_flutter/common/view/shimmer_wrapper.dart';
import 'package:acko_flutter/feature/content/models/contents_data.dart';
import 'package:acko_flutter/feature/content/video/bloc/video/video_cubit.dart';
import 'package:acko_flutter/feature/content/video/bloc/video_interactions/video_interaction_cubit.dart';
import 'package:acko_flutter/feature/content/video/bloc/video_with_list/video_list_page_cubit.dart';
import 'package:acko_flutter/feature/content/video/bloc/video_with_list/video_list_page_states.dart';
import 'package:acko_flutter/feature/content/video/utils/util_functions.dart';
import 'package:acko_flutter/feature/content/video/views/video_interaction_view.dart';
import 'package:acko_flutter/feature/content/video/widgets/video_view_widget.dart';
import 'package:acko_flutter/feature/home/<USER>/constants.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/screeen_utils/screen_utils.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/tap_events.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';

class VideoListPage extends StatefulWidget {
  const VideoListPage({Key? key}) : super(key: key);

  @override
  State<VideoListPage> createState() => _VideoListPageState();
}

class _VideoListPageState extends State<VideoListPage> {
  late VideoListPageCubit _cubit;

  @override
  void initState() {
    super.initState();
    _cubit = BlocProvider.of<VideoListPageCubit>(context);
  }

  @override
  void dispose() {
    ScreenUtils.changeStatusBarIconColor(false);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<VideoListPageCubit, VideoListPageStates>(
        bloc: _cubit,
        buildWhen: (prev, cur) => (cur is ErrorState ||
            cur is LoadingState ||
            cur is VideoDataLoadedState),
        builder: (context, state) {
          return Scaffold(
              appBar: getAppBar("", context,
                  iconColor: _cubit.isVideoLoading ? color000000 : colorFFFFFF,
                  bgColor: _cubit.isVideoLoading ? colorFFFFFF : color000000,
                  keepLightStatusBarIcons:
                      _cubit.isVideoLoading ? false : true),
              body: renderBody());
        });
  }

  Widget renderBody() {
    return ShimmerWrapper(
      gradient: shimmerGradient,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          VideoView(),
          Flexible(fit: FlexFit.loose, child: renderScrollablePage())
        ],
      ),
    );
  }

  Widget VideoView() {
    return BlocBuilder(
      bloc: _cubit,
      buildWhen: (prev, cur) => (cur is VideoSelectedState ||
          cur is LoadingState ||
          cur is VideoDataLoadedState),
      builder: (context, state) {
        Content? selectedContent = _cubit.selectedContent;
        return (_cubit.isVideoLoading)
            ? ShimmerMask(
                isLoading: _cubit.isVideoLoading,
                child: Container(
                  height: VideoUtilFunctions.getVideoPlayerHeight(context),
                  color: color000000,
                ))
            : BlocProvider<VideoCubit>(
                key: Key('${_cubit.selectedVideoId}'),
                create: (context) => VideoCubit(contentData: selectedContent),
                child: VideoViewWidget());
      },
    );
  }

  Widget renderScrollablePage() => SingleChildScrollView(
        physics: ClampingScrollPhysics(),
        child: BlocBuilder(
            bloc: _cubit,
            buildWhen: (prev, cur) => (cur is VideoSelectedState ||
                cur is LoadingState ||
                cur is RecommendedDataLoadedState ||
                cur is VideoDataLoadedState),
            builder: (context, state) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  renderInteractions(),
                  Flexible(fit: FlexFit.loose, child: VideoListView())
                ],
              );
            }),
      );

  Widget renderInteractions() => BlocProvider<VideoInteractionCubit>(
        key: Key('${_cubit.selectedVideoId} interactions'),
        create: (context) => VideoInteractionCubit(_cubit.selectedVideoId),
        child: VideoInteractionView(
          title: _cubit.selectedContent?.title,
          publishedOn: _cubit.selectedContent?.publishedOnFormatted,
          videoShareData: _cubit.selectedContent?.shareData,
        ),
      );

  Widget VideoListView() => Padding(
        padding: EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Divider(),
            Flexible(fit: FlexFit.loose, child: RecommendedSection()),
          ],
        ),
      );

  Widget RecommendedSection() => Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 16),
            child: ShimmerMask(
                isLoading: _cubit.isListLoading,
                child:
                    (!_cubit.isListLoading && _cubit.recommendationList == null)
                        ? SizedBox.shrink()
                        : Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              color: _cubit.isListLoading
                                  ? color000000
                                  : Colors.transparent,
                            ),
                            child: TextEuclidSemiBold16(recommended_videos))),
          ),
          Flexible(
            fit: FlexFit.loose,
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 16),
              child: ListView.builder(
                  physics: NeverScrollableScrollPhysics(),
                  padding: EdgeInsets.zero,
                  itemCount: _cubit.recommendationList?.length ??
                      (_cubit.isListLoading ? 3 : 0),
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    Content? data = _cubit.recommendationList?[index];
                    return InkWell(
                        onTap: () {
                          if (data != null) {
                            _cubit.videoSelected(data.id);
                            AnalyticsTrackerManager.instance.sendEvent(
                                event: TapConstants.TAP_BTN_CMS_VIDEO_THUMBNAIL,
                                properties: {
                                  'platform': Util.getPlatform(),
                                  'name': data.title,
                                  'unique_id': data.id
                                });
                          }
                        },
                        child: SecondaryVideoTile(data, index));
                  }),
            ),
          ),
        ],
      );

  Widget SecondaryVideoTile(Content? data, int index) => Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Container(
            padding: EdgeInsets.symmetric(vertical: 8),
            child: ShimmerMask(
              isLoading: _cubit.isListLoading,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  color:
                      _cubit.isListLoading ? color000000 : Colors.transparent,
                ),
                height: _cubit.isListLoading ? 70 : null,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Stack(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(12),
                            child: (data?.videoData.thumbnails.last.link ?? "")
                                    .isNotEmpty
                                ? CachedNetworkImage(
                                    imageUrl:
                                        data!.videoData.thumbnails.last.link,
                                    fit: BoxFit.fill,
                                  )
                                : Container(
                                    color: Colors.black,
                                  ),
                          ),
                          Positioned(
                            left: 6,
                            bottom: 6,
                            child: Container(
                                padding: EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: color000000.withOpacity(0.5),
                                  shape: BoxShape.circle,
                                ),
                                child: SvgPicture.asset(
                                  'assets/images/videos/video_play.svg',
                                  height: 8,
                                )),
                          )
                        ],
                      ),
                    ),
                    SizedBox(
                      width: 12,
                    ),
                    Expanded(
                      flex: 2,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Flexible(
                            child: TextEuclidMedium14(
                              data?.title ?? '',
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          const SizedBox(
                            height: 8,
                          ),
                          TextEuclidRegular(
                              '${data?.activityStats.view.compactTotal} views'),
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
          if ((index != (_cubit.recommendationList?.length ?? 0) - 1) &&
              !_cubit.isListLoading)
            SizedBox(width: getScreenWidth() / 2, child: Divider())
        ],
      );
}
