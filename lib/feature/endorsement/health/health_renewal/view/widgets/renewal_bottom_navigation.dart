import 'dart:convert';

import 'package:acko_flutter/common/util/AckoTextStyle.dart';
import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/view/AckoText.dart';
import 'package:acko_flutter/common/view/FullPageLoader.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_nodes.dart';
import 'package:acko_flutter/feature/endorsement/health/health_renewal/core/constants/image_urls.dart';
import 'package:acko_flutter/feature/endorsement/health/health_renewal/cubit/renewal-home-cubit/renewal_home_cubit.dart';
import 'package:acko_flutter/feature/endorsement/health/health_renewal/domain/enum/policy_type.dart';
import 'package:acko_flutter/feature/endorsement/health/health_renewal/domain/models/proposal_details_model.dart';
import 'package:acko_flutter/feature/endorsement/health/health_renewal/view/widgets/ModalWidget/premium_calculation.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/auto_pay_mandate_sheet_model.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/view/bottomsheets/auto_pay_mandate_sheet.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/events/health_life/tap_events/health_life_tap_events.dart';
import 'package:design_module/uikit/widgets/bottom_sheet/show_bottom_sheet.dart';
import 'package:design_module/utilities/hybrid_image.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/remote_config/remote_config.dart';

import 'ModalWidget/compliance_changes_info_modal.dart';

class RenewalBottomNavigation extends StatelessWidget {
  const RenewalBottomNavigation({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<RenewalHomeCubit, RenewalHomeState>(
      builder: (context, state) {
        final _analyticsManager =
            context.read<RenewalHomeCubit>().analyticsManager;
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (state.policyType != PolicyType.AROGYASANJIVINI)
              Container(
                padding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment(0.992, -0.125), // Angle of 276 degrees
                    end: Alignment(-0.992, 0.125),
                    colors: [
                      Color.fromRGBO(
                          254, 244, 221, 0.60), // rgba(254, 244, 221, 0.60)
                      Color(0xFFF8E4FF), // #F8E4FF
                    ],
                    stops: [0.0025, 0.9971], // Equivalent to 0.25% and 99.71%
                  ),
                  color: Colors.white, // Fallback color (#FFF)
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    HybridImage(
                      imageUrl: ImageUrls.PriceHike,
                    ),
                    SizedBox(width: 8),
                    Flexible(
                      child: Padding(
                        padding: EdgeInsets.only(right: 20),
                        child: RichText(
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: "Price update: ",
                                style: TextStyle(
                                  color: color000000,
                                  fontSize: 12,
                                  height: 1.5,
                                  fontWeight: FontWeight.w700,
                                  fontFamily: 'euclid_circularB_regular',
                                ),
                              ),
                              TextSpan(
                                text:
                                    "Following IRDAI’s new guidelines,\nwe’ve increased your premium. ",
                                style: TextStyle(
                                  color: color000000,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w400,
                                  fontFamily: 'euclid_circularB_regular',
                                ),
                              ),
                              TextSpan(
                                text: "Why the price change?",
                                style: TextStyleInterMediumL1(
                                  color: color1B73E8,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                  height: 1.5,
                                ),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    _analyticsManager.sendTapEvent(
                                      event: HLTrackEvents
                                          .TAP_RENEWAL_PRICE_CHANGE_BANNER,
                                    );
                                    context.showAckoModalBottomSheet(
                                        child: BlocProvider.value(
                                      value: context.read<RenewalHomeCubit>(),
                                      child: ComplianceChangesInfoModal(),
                                    ));
                                  },
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            Container(
              width: MediaQuery.sizeOf(context).width,
              padding: const EdgeInsets.only(
                  top: 12, left: 20, right: 20, bottom: 20),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Color(0x0F36354C),
                    blurRadius: 8,
                    offset: Offset(0, -4),
                    spreadRadius: -2,
                  )
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text:
                                  "₹${state.isMonthly ? state.monthlyDisplayAmount : state.yearlyDisplayAmount}",
                              style: TextStyleInterSemiBold(
                                color: color040222,
                                fontSize: 24,
                              ),
                            ),
                            TextSpan(
                              text: state.isMonthly ? "/mo" : "/yr",
                              style: TextStyleInterRegular(
                                color: color040222,
                                fontSize: 16,
                              ),
                            ),
                            TextSpan(
                              text: " (incl. GST)",
                              style: TextStyleInterRegular(
                                color: color040222,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          _analyticsManager.sendTapEvent(
                            event: HLTrackEvents.TAP_RENEWALS_PREMIUM_BREAKUP,
                          );
                          context.showAckoModalBottomSheet(
                              child: BlocProvider.value(
                            value: context.read<RenewalHomeCubit>(),
                            child: PremiumClaculationModal(),
                          ));
                        },
                        child: TextEuclidMedium14('See premium breakup',
                            textColor: color1B73E8),
                      )
                    ],
                  ),
                  GestureDetector(
                    onTap: () async {
                      _analyticsManager.sendTapEvent(
                        event: HLTrackEvents.TAP_RENEWALS_LANDING_PAGE_CONTINUE,
                      );
                      final response = await RemoteConfigInstance.instance
                          .getData(RemoteConfigKeysSet.PPE_MANDATE_BOTTOMSHEET);
                      final autoRenewalData = AutoRenewalConfig.fromJson(jsonDecode(response)).renewalData;
                      if(autoRenewalData?.newPremiumText != null) {
                        autoRenewalData?.newPremiumText?.title = autoRenewalData.newPremiumText?.title.replaceAll('%s', state.isMonthly ? 'Monthly' : 'Yearly') ?? '';
                        autoRenewalData?.newPremiumText?.value =  state.isMonthly ? '₹ ${state.monthlyDisplayAmount}' : '₹ ${state.yearlyDisplayAmount}';
                      }
                      if (autoRenewalData != null && state.proposalDetails!.renewal!.userPaymentJourneyFlow
                          .isNotNullAndEmpty &&
                          state.proposalDetails!.renewal!.userPaymentJourneyFlow
                              .equalsIgnoreCase("mandate_flow")) {
                        final _cubit = context.read<RenewalHomeCubit>();
                        if(_cubit.inputData != null && autoRenewalData.validity != null) {
                          autoRenewalData.validity?.value = '${_cubit.inputData?.mandateMaxYears} years';
                        }
                        context.showAckoModalBottomSheet(
                            child: BlocProvider.value(
                              value: _cubit,
                              child: AutoPayMandateSheet(autoRenewalData: autoRenewalData),
                            )).then((value){
                          handleHealthPolicyRenewal(
                              context: context,
                              isMandate: value,
                              eventName: value
                                  ? HLTrackEvents
                                      .TAP_RENEWALS_LONGTERM_MANDATE_CONFIRM
                                  : HLTrackEvents.TAP_RENEWALS_ONETIME_PAYMENT_CONFIRM);
                        });
                      } else {
                        final _cubit = context.read<RenewalHomeCubit>();
                        _analyticsManager.sendTapEvent(
                          event: HLTrackEvents.TAP_RENEWALS_PAY_NOW,
                        );
                        FullPageLoader.instance.showFullPageLoader(context);
                        final proposalDetails =
                            await _cubit.getPaymentEkey(false);
                        if (proposalDetails.error == null &&
                            proposalDetails
                                .renewal!.paymentLink.isNotNullOrEmpty) {
                          await Navigator.pushNamed(context, Routes.PAYMENT,
                              arguments: {
                                "ekey": proposalDetails
                                    .renewal!.paymentLink!['payment_order_id']
                              });
                        }
                        FullPageLoader.instance.dismissFullPageLoader(context);
                        if (state.policyNumber.isNullOrEmpty) {
                          Navigator.pop(context);
                          return;
                        }
                        await _cubit.getProposalDetails(
                          policyNumber: state.policyNumber!,
                          memberDetailResponse: state.memberDetails,
                          showModal: false,
                          requestBody: ProposalDetails(
                            currentNodeId: RenewalNodes.HYDRATE_OVERVIEW.value,
                          ).toJson(),
                        );
                      }
                    },
                    child: Container(
                      padding:
                          EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                      decoration: BoxDecoration(
                        color: color121212,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: TextEuclidMediumL16(
                        'Renew',
                        textColor: colorFFFFFF,
                      ),
                    ),
                  )
                ],
              ),
            )
          ],
        );
      },
    );
  }

  Future<void> handleHealthPolicyRenewal({
    required BuildContext context,
    required bool isMandate,
    required HLTrackEvents eventName,
  }) async {
    final _cubit = context.read<RenewalHomeCubit>();
    final _analyticsManager = _cubit.analyticsManager;
    final state = _cubit.state;

    _analyticsManager.sendTapEvent(event: eventName);
    FullPageLoader.instance.showFullPageLoader(context);

    final proposalDetails = await _cubit.getPaymentEkey(isMandate);

    if (proposalDetails.error == null &&
        proposalDetails.renewal?.paymentLink.isNotNullOrEmpty == true) {
      await Navigator.pushNamed(
        context,
        Routes.PAYMENT,
        arguments: {
          "ekey": proposalDetails.renewal!.paymentLink!['payment_order_id'],
        },
      );
    }
    FullPageLoader.instance.dismissFullPageLoader(context);

    if (state.policyNumber.isNullOrEmpty) {
      Navigator.pop(context);
      return;
    }

    await _cubit.getProposalDetails(
      policyNumber: state.policyNumber!,
      memberDetailResponse: state.memberDetails,
      showModal: false,
      requestBody: ProposalDetails(
        currentNodeId: RenewalNodes.HYDRATE_OVERVIEW.value,
      ).toJson(),
    );
  }
}


