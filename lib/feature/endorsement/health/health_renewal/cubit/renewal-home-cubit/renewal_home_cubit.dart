import 'dart:collection';
import 'dart:convert';

import 'package:acko_flutter/feature/endorsement/health/domain/models/health_jm_response.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_nodes.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_repo.dart';
import 'package:acko_flutter/feature/endorsement/health/health_renewal/domain/enum/policy_type.dart';
import 'package:acko_flutter/feature/endorsement/health/health_renewal/domain/models/member_details.dart';
import 'package:acko_flutter/feature/endorsement/health/health_renewal/domain/models/proposal_details_model.dart';
import 'package:acko_flutter/feature/endorsement/health/health_renewal/view/pages/renewal-home/health_renewal_home_page.dart';
import 'package:acko_flutter/framework/pdp/health/common/pdp_utils.dart';
import 'package:acko_flutter/network/ApiResponse.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_flutter/util/health/utils.dart';
import 'package:analytics/analytics.dart';
import 'package:equatable/equatable.dart';
import 'package:session_manager_module/session_manager.dart';
import 'package:utilities/widgets/acko_safe_cubit.dart';

import '../../domain/models/plan_parameters.dart';

part 'renewal_home_state.dart';

class RenewalHomeCubit extends AckoSafeCubit<RenewalHomeState> {
  RenewalHomeCubit(
    this._journeyManagerRepository,
    this.analyticsManager,
  ) : super(RenewalHomeState());

  final JourneyManagerRepository _journeyManagerRepository;
  final HlAnalyticsManager<HlCommonEventProperty> analyticsManager;
  Set<String>? notCoveredRelationShip;
  InputData? inputData;
  String? communicationEmail;

  Future<void> getProposalDetails(
      {required String? policyNumber,
      required Map<String, dynamic>? requestBody,
      HealthJourneyManagerResponse? memberDetailResponse,
      bool showModal = true}) async {
    emit(
      state.copyWith(
        status: RenewalHomeViewStatus.loading,
      ),
    );

    if (memberDetailResponse != null) {
      requestBody = memberDetailResponse
          .toRequestBody(RenewalNodes.HYDRATE_OVERVIEW.value);
      emit(
        state.copyWith(
          memberDetails: memberDetailResponse,
        ),
      );
    }

    if (policyNumber.isNullOrEmpty || requestBody.isNullOrEmpty) {
      return;
    }
    Map<String, dynamic> queryParameters = {
      'policy-number': policyNumber,
    };

    HealthJourneyManagerResponse healthJMResponse =
        await _journeyManagerRepository.manageJourney(
            JourneyType.RENEWAL, requestBody!,
            queryParameters: queryParameters);

    if (healthJMResponse.error != null) {
      emit(
        state.copyWith(
          status: RenewalHomeViewStatus.failure,
          error: healthJMResponse.error,
        ),
      );
      return;
    }

    emit(
      state.copyWith(
          proposalDetails: healthJMResponse, policyNumber: policyNumber),
    );

    if (state.proposalDetails != null &&
        state.proposalDetails!.renewal != null &&
        state.proposalDetails!.renewal!.cloneProposal != null) {
      getMultiYearMandateInputData();
      getBasePlan();
      setPolicyType();
      getPolicyEndDateAndStartDate();
      setInflationProtect();
      setCoveredMember();
      setUncoveredMember();
      setOldPremiumDetails();
      getYearlyAndMonthlyAmount();
      setDeductible();
      setNoClaimBonus();
      setEndorsements();
      setDiscount();
      getBannerType();
      setEventProperties();
    }
    emit(
      state.copyWith(
        showBonusModal: showModal,
        status: RenewalHomeViewStatus.success,
      ),
    );

    communicationEmail = await SessionManager.instance.storageManager
        .getStringDataInPreferences(StringDataSharedPreferenceKeys.EMAIL_ID);
  }

  Future<InputData?> callMultiYearNodeApi(String packageId, String paymentFrequency) async {
    Map<String, dynamic> requestBody = {
      "current_node_id": PrePolicyEditNodes.MULTI_YEAR_MANDATE.value,
      "input_data": {
        "journey": "renewal",
        "package_id": packageId,
        "payment_frequency": paymentFrequency
      }
    };
    HealthJourneyManagerResponse? ppeResponse =
    await _journeyManagerRepository.manageJourney(JourneyType.RENEWAL, requestBody);
    return ppeResponse.inputData;
  }

  getMultiYearMandateInputData() async {
    final parameters = state.proposalDetails!.renewal!.cloneProposal!.header?.parameters; //prePolicyEditingResponse?.edit?.proposal?['header']?['parameters'];
    final packageId = (parameters?['packages']?['value'] as List?)
        ?.firstWhere((e) => e['id'] == 'package', orElse: () => null)?['value']
        ?.firstWhere((e) => e['id'] == 'package_id', orElse: () => null)?['value'];
    inputData = await callMultiYearNodeApi(packageId, state.isMonthly ? 'monthly': 'yearly');
  }

  Future<HealthJourneyManagerResponse> getPaymentEkey(bool isMandate) async {
    Map<String, dynamic> queryParameters = {
      'policy-number': state.policyNumber!,
    };

    state.proposalDetails!.renewal!.quotes!.forEach((element) {
      if (state.isMonthly) {
        if ("monthly".equalsIgnoreCase(element.quoteParameter!['frequency'])) {
          state.proposalDetails!.renewal!.cloneProposal!.quotes![0] = state
              .proposalDetails!.renewal!.cloneProposal!.quotes![0]
              .copyWith(
            quoteData: state
                .proposalDetails!.renewal!.cloneProposal!.quotes![0].quoteData
                .copyWith(
              factor: element.factor,
              installmentFactor: element.installmentFactor,
              premiumDetail: null,
              quoteParameter: element.quoteParameter,
              metaData: element.metaData,
            ),
          );
        }
      } else {
        if ("yearly".equalsIgnoreCase(element.quoteParameter!['frequency'])) {
          state.proposalDetails!.renewal!.cloneProposal!.quotes![0] = state
              .proposalDetails!.renewal!.cloneProposal!.quotes![0]
              .copyWith(
            quoteData: state
                .proposalDetails!.renewal!.cloneProposal!.quotes![0].quoteData
                .copyWith(
              factor: element.factor,
              installmentFactor: element.installmentFactor,
              premiumDetail: null,
              quoteParameter: element.quoteParameter,
              metaData: element.metaData,
            ),
          );
          print(state.proposalDetails);
        }
      }
    });

    Map<String, dynamic> requestBody = isMandate
        ? state.proposalDetails!
            .copyWith(currentNodeId: RenewalNodes.MANDATE.value)
            .toJson()
        : state.proposalDetails!
            .copyWith(currentNodeId: RenewalNodes.OVERVIEW.value)
            .toJson();
    HealthJourneyManagerResponse healthJMResponse =
        await _journeyManagerRepository.manageJourney(
            JourneyType.RENEWAL, requestBody,
            queryParameters: queryParameters);
    emit(
      state.copyWith(
        proposalDetails: healthJMResponse,
      ),
    );
    return healthJMResponse;
  }

  void updatePaymentOption(bool isMonthly) {
    emit(
      state.copyWith(
        isMonthly: isMonthly,
        payableAmount:
            isMonthly ? state.monthlyDisplayAmount : state.yearlyDisplayAmount,
      ),
    );
  }

  void getBasePlan() {
    state.proposalDetails!.renewal!.cloneProposal!.plans?.forEach((element) {
      if ("base"
          .equalsIgnoreCase(element['parameters']['plan_type']['value'])) {
        int sumInsured = 0;
        int originalSumInsured = 0;
        if (element['parameters']['sum_insured'] != null) {
          sumInsured = element['parameters']['sum_insured']['value'];
        }
        if (element['parameters']['original_sum_insured'] != null) {
          originalSumInsured =
              element['parameters']['original_sum_insured']['value'];
        }
        emit(
          state.copyWith(
            planData: PlanParameters(
              sumInsuredNum: sumInsured,
              sumInsuredDisplayValue:
                  HealthPDPUtils.formatIndianCurrencyForRenewal(sumInsured),
              sumInsured: NumberUtils.commaSeparatedNumber(sumInsured),
              originalSumInsured:
                  NumberUtils.commaSeparatedNumber(originalSumInsured),
              originalSumInsuredNum: originalSumInsured,
            ),
          ),
        );
        return;
      }
    });
  }

  void setInflationProtect() {
    if (state.policyType == PolicyType.BASE) {
      var renewalDetails = state.proposalDetails!.renewal!.cloneProposal!
          .header!.proposalData!["renewal_details"];
      if (renewalDetails != null) {
        if (renewalDetails!["inflation_protect"] != null) {
          int currentSumInsured = 0;
          if (state.planData!.sumInsuredNum != null) {
            currentSumInsured = (state.planData!.sumInsuredNum! -
                renewalDetails["inflation_protect"]) as int;
          }
          emit(state.copyWith(
              planData: state.planData!.copyWith(
                  currentSumInsuredNum: currentSumInsured,
                  currentSumInsuredDisplayValue:
                      HealthPDPUtils.formatIndianCurrencyForRenewal(
                          currentSumInsured),
                  currentSumInsured:
                      NumberUtils.commaSeparatedNumber(currentSumInsured),
                  currentInflationProtect: getPremiumDiff(
                      renewalDetails["inflation_protect"], false,
                      showCalculation: true),
                  lastYearInflationProtect: NumberUtils.commaSeparatedNumber(
                      renewalDetails["inflation_protect_till_last_year"]),
                  currentInflationProtectDisplayValue:
                      HealthPDPUtils.formatIndianCurrencyForRenewal(
                          renewalDetails["inflation_protect"]),
                  lastYearInflationProtectDisplayValue:
                      HealthPDPUtils.formatIndianCurrencyForRenewal(
                          renewalDetails["inflation_protect_till_last_year"]),
                  lastYearInflationNum:
                      renewalDetails["inflation_protect_till_last_year"],
                  totalSumInsuredtillLastYear: NumberUtils.commaSeparatedNumber((renewalDetails["inflation_protect_till_last_year"] + currentSumInsured)))));
        }
      }

      state.proposalDetails!.renewal!.cloneProposal!.plans?.forEach((element) {
        if ("inflation_protect_sum_insured_plan_template"
            .equalsIgnoreCase(element['plan_id'])) {
          emit(state.copyWith(
              planData: state.planData!.copyWith(
            inflationProtectPercentageValue: element['parameters']
                ['inflation_protect_sum_insured']['value'],
          )));
          return;
        }
      });
    }
  }

  void setDeductible() {
    if (state.policyType == PolicyType.TOPUP) {
      if (state.proposalDetails!.renewal!.cloneProposal!.header != null &&
          state.proposalDetails!.renewal!.cloneProposal!.header!.parameters
              .isNotNullOrEmpty &&
          state.proposalDetails!.renewal!.cloneProposal!.header!
                  .parameters!['deductible'] !=
              null) {
        emit(state.copyWith(
          planData: state.planData!.copyWith(
            deductible: HealthPDPUtils.formatIndianCurrencyForRenewal(state
                .proposalDetails!
                .renewal!
                .cloneProposal!
                .header!
                .parameters!['deductible']['value']),
          ),
        ));
      }
    }
  }

  void setNoClaimBonus() {
    if (state.policyType == PolicyType.AROGYASANJIVINI) {
      var renewalDetails = state.proposalDetails!.renewal!.cloneProposal!
          .header!.proposalData!["renewal_details"];
      if (state.proposalDetails!.renewal!.cloneProposal!.header != null &&
          renewalDetails != null &&
          renewalDetails['no_claim_bonus'] != null) {
        int currentSumInsured = 0;
        if (state.planData!.sumInsuredNum != null) {
          currentSumInsured = (state.planData!.sumInsuredNum! -
              renewalDetails["no_claim_bonus"]) as int;
        }
        emit(state.copyWith(
            planData: state.planData!.copyWith(
                currentSumInsuredNum: currentSumInsured,
                currentSumInsuredDisplayValue:
                    HealthPDPUtils.formatIndianCurrencyForRenewal(
                        currentSumInsured),
                currentSumInsured:
                    NumberUtils.commaSeparatedNumber(currentSumInsured),
                noClaimBonus: renewalDetails['no_claim_bonus'],
                noClaimBonusCommaSeperated: getPremiumDiff(
                    renewalDetails!['no_claim_bonus'], false,
                    showCalculation: true),
                noClaimBonusDisplayValue: HealthPDPUtils.formatIndianCurrencyForRenewal(
                    renewalDetails['no_claim_bonus']),
                lastYearNoClaimBonus: NumberUtils.commaSeparatedNumber(
                    renewalDetails['no_claim_bonus_till_last_year']),
                lastYearBonus: renewalDetails['no_claim_bonus_till_last_year'],
                totalSumInsuredtillLastYear: NumberUtils.commaSeparatedNumber(
                    (renewalDetails["no_claim_bonus_till_last_year"] +
                        currentSumInsured)))));
      }

      state.proposalDetails!.renewal!.cloneProposal!.plans?.forEach((element) {
        if ("arogya_sanjeevani_policy_sp"
            .equalsIgnoreCase(element['sellable_plan_unit_id'])) {
          emit(state.copyWith(
              planData: state.planData!.copyWith(
                  ncbPercentageValue: element['parameters']!['no_claim_bonus']
                      ['value'],
                  isClaimed: renewalDetails!['is_claimed'],
                  partialDeduction: renewalDetails!['partial_deduction'])));
          return;
        }
      });
    }
  }

  void setPolicyType() {
    if (state.proposalDetails!.renewal!.cloneProposal!.header != null &&
        state.proposalDetails!.renewal!.cloneProposal!.header!.parameters
            .isNotNullOrEmpty &&
        state.proposalDetails!.renewal!.cloneProposal!.header!
                .parameters!['packages'] !=
            null) {
      state.proposalDetails!.renewal!.cloneProposal!.header!
          .parameters!['packages']['value']
          .forEach((element) {
        element['value'].forEach((element) {
          if ("package_id".equalsIgnoreCase(element['id'])) {
            if ("arogya_sanjeevani_health_package"
                .equalsIgnoreCase(element['value'])) {
              emit(state.copyWith(
                policyType: PolicyType.AROGYASANJIVINI,
              ));
            } else if ("ashp_retail_health_package"
                    .equalsIgnoreCase(element['value']) ||
                "aphp_retail_health_package"
                    .equalsIgnoreCase(element['value'])) {
              emit(state.copyWith(
                policyType: PolicyType.BASE,
              ));
            } else {
              emit(state.copyWith(
                policyType: PolicyType.TOPUP,
              ));
            }
            return;
          }
        });
      });
    }
  }

  void setCoveredMember() {
    CloneProposal cloneProposal =
        state.proposalDetails!.renewal!.cloneProposal!;
    Map<String, List<WaitingPeriod>> waitingPeriods = {};
    if (state.proposalDetails!.renewal!.declarations != null) {
      waitingPeriods =
          getWaitingPeriod(state.proposalDetails!.renewal!.declarations!);
    }
    if (cloneProposal.insured.isNotNullOrEmpty) {
      final List<MemberDetails> coveredMember = [];
      cloneProposal.insured!.forEach((element) {
        MemberDetails memberDetails = MemberDetails(
          id: element['parameters']['id']['value'],
          name: element['parameters']['name']['value']!,
          relationship: element['parameters']['relation']['value']!,
          age: element['parameters']['age']['value']!,
          waitingPeriods: waitingPeriods[element['parameters']['id']['value']!],
        );
        coveredMember.add(memberDetails);
      });
      if (coveredMember.isNotNullOrEmpty) {
        emit(state.copyWith(
          coveredMember: coveredMember,
        ));
      }
    }
  }

  void setUncoveredMember() {
    List<dynamic> removedMemberDetails = [];
    if (state.proposalDetails!.renewal!.cloneProposal!.header!.proposalData !=
            null &&
        state.proposalDetails!.renewal!.cloneProposal!.header!
                .proposalData!['removed_member_details'] !=
            null) {
      removedMemberDetails = state.proposalDetails!.renewal!.cloneProposal!
          .header!.proposalData!['removed_member_details'];
      final List<MemberDetails> unCoveredMember = [];
      removedMemberDetails.forEach((element) {
        if (notCoveredRelationShip == null) {
          notCoveredRelationShip ??= <String>{};
        }
        notCoveredRelationShip!
            .add(element['parameters']['relationship']['value']);
        unCoveredMember.add(MemberDetails(
            id: element!['parameters']!['id']['value'],
            name: element['parameters']['name']['value'],
            relationship: element['parameters']['relationship']['value'],
            age: element['parameters']['age']['value']));
      });
      if (unCoveredMember.isNotNullOrEmpty) {
        emit(state.copyWith(
          notCoveredMember: unCoveredMember,
        ));
      }
    }
  }

  void setOldPremiumDetails() {
    var oldProposalData = state.proposalDetails!.renewal;
    var renewalDetails = state.proposalDetails!.renewal!.cloneProposal!.header!
        .proposalData!["renewal_details"] as Map<String, dynamic>;
    if (oldProposalData!.proposal != null &&
        oldProposalData.proposal!['premium']['premium_details'] != null) {
      final premiumDetails =
          oldProposalData.proposal!['premium']['premium_details'];
      final oldPremium = renewalDetails.containsKey('customer_paid_premium')
          ? renewalDetails['customer_paid_premium'] as num
          : premiumDetails['gross_premium'] as num;
      emit(state.copyWith(
          oldPremium: oldPremium,
          oldDisplayPremium: NumberUtils.commaSeparatedNumber(
              oldPremium),
          lastYearFrequency: oldProposalData.paymentFrequency));
    }
  }

  String getEndorsementTitle(String type) {
    switch (type) {
      case "age_band_change":
        return "Age bracket change";
      case "child_removal":
        return "Member removed";
      case "name_change":
        return "Name change";
      case "member_addition":
        return "Member added this year";
      case "member_removal":
        return "Member removed";
      case "gender_change":
        return "Gender change";
      case "dob_change":
        return "Date of birth change";
      case "email_change":
        return "E-mail change";
      case "mid_year_endorsement":
        return "Member added last year";
      default:
        return "";
    }
  }

  String getPremiumDiff(num diff, bool showDecimal,
      {bool showCalculation = true}) {
    return showCalculation
        ? '${diff > 0 ? '+' : diff == 0 ? '' : '-'} ₹${NumberUtils.commaSeparatedNumber(diff.abs(), showDecimal: showDecimal)}'
        : '₹${NumberUtils.commaSeparatedNumber(diff.abs(), showDecimal: showDecimal)}';
  }

  String getMemberName(id) {
    return state.coveredMember
            ?.firstWhere((element) => element.id == id)
            .name ??
        "";
  }

  void setEndorsements() {
    List<Map<String, dynamic>> endorsementsData = [];
    var proposalData =
        state.proposalDetails?.renewal?.cloneProposal?.header?.proposalData;

    if (proposalData != null) {
      if (proposalData['renewal_delta']?.isNotEmpty == true) {
        final List<dynamic>? renewalDelta = proposalData['renewal_delta'];
        final List<dynamic>? midYearMembers = proposalData['mid_year_added_members'];
        renewalDelta?.forEach((endorsement) {
          if (endorsement is Map<String, dynamic>) {
            // Ensure endorsement is a Map
            String? id = endorsement["id"] as String?;
            Map<String, dynamic>? totalPremiumDiff =
                endorsement["total_premium_diff"] as Map<String, dynamic>?;

            if (totalPremiumDiff != null &&
                totalPremiumDiff["premium_diff"] != 0) {
              num premiumDiff = totalPremiumDiff["premium_diff"] ??
                  0.0; // Safeguard against null

              if (id == "age_band_change") {
                endorsementsData.add({
                  'type': getEndorsementTitle("age_band_change"),
                  'description':
                      'Premium changed as some members have moved to another age bracket',
                  "premiumChange": getPremiumDiff(premiumDiff, true),
                });
              } else if (id == "child_removal") {
                endorsementsData.add({
                  'type': getEndorsementTitle("child_removal"),
                  'description':
                      'Premium changed as ${state.notCoveredMember?.map((e) => e.name).join(' and ')} is above 25 years old and cannot be added as a child',
                  "premiumChange": getPremiumDiff(premiumDiff, true),
                });
              } else if (id == "compliance_change") {
                emit(state.copyWith(
                    complianceDiff:
                    NumberUtils.commaSeparatedNumber(premiumDiff),
                    complianceDiffNum: premiumDiff));
              }  else if (id == "mid_year_endorsement" && midYearMembers.isNotNullOrEmpty) {
                endorsementsData.add({
                  'type': getEndorsementTitle("mid_year_endorsement"),
                  'description': getMemberAddedLastYearDescription(midYearMembers!),
                  "premiumChange": getPremiumDiff(premiumDiff, true),
                });
              }
            }
          }
        });
      }
      if (proposalData['endorsements']?.isNotEmpty == true) {
        final List<dynamic>? endorsementsList = proposalData['endorsements'];
        endorsementsList?.forEach((endorsement) {
          if (endorsement is Map<dynamic, dynamic>) {
            // Ensure endorsement is a Map
            String? endorsementType =
                endorsement["endorsement_type"] as String?;
            String? memberName;
            if (endorsementType == "member_addition") {
              memberName = endorsement['endorsements'][0]['new_value'][0]
                  ['parameters']['name']['value'] as dynamic;
            } else if (endorsementType == "member_removal") {
              memberName = endorsement['endorsements'][0]['old_value'][0]
                  ['parameters']['name']['value'] as dynamic;
            } else {
              memberName = getMemberName(
                  endorsement['endorsements'][0]['parameters']['insured_id']);
            }
            Map<dynamic, dynamic>? totalPremiumDiff =
                endorsement["delta"] as Map<dynamic, dynamic>?;

            if (totalPremiumDiff != null) {
              num premiumDiff = totalPremiumDiff["gross_amount"] ??
                  0.0; // Safeguard against null

              if (endorsementType == "name_change") {
                endorsementsData.add({
                  'type': getEndorsementTitle("name_change"),
                  'description':
                      'No change in premium for name change of $memberName',
                  "premiumChange": getPremiumDiff(premiumDiff, true),
                });
              } else if (endorsementType == "member_addition") {
                endorsementsData.add({
                  'type': getEndorsementTitle("member_addition"),
                  'description': 'Premium increased for adding $memberName',
                  "premiumChange": getPremiumDiff(premiumDiff, true),
                });
              } else if (endorsementType == "member_removal") {
                endorsementsData.add({
                  'type': getEndorsementTitle("member_removal"),
                  'description': 'Premium reduced for removing $memberName',
                  "premiumChange": getPremiumDiff(premiumDiff, true),
                });
              } else if (endorsementType == "gender_change") {
                endorsementsData.add({
                  'type': getEndorsementTitle("gender_change"),
                  'description':
                      'Premium changed due to change in the gender of $memberName',
                  "premiumChange": getPremiumDiff(premiumDiff, true),
                });
              } else if (endorsementType == "dob_change") {
                endorsementsData.add({
                  'type': getEndorsementTitle("dob_change"),
                  'description':
                      'Premium changed due to change in the date of birth of $memberName',
                  "premiumChange": getPremiumDiff(premiumDiff, true),
                });
              } else if (endorsementType == "email_change") {
                endorsementsData.add({
                  'type': getEndorsementTitle("email_change"),
                  'description':
                      'No change in premium for email change of $memberName',
                  "premiumChange": getPremiumDiff(premiumDiff, true),
                });
              }
            }
          }
        });
      }
      emit(state.copyWith(endorsements: endorsementsData));
    } else {
      emit(state.copyWith(endorsements: []));
    }
  }

  String getMemberAddedLastYearDescription(List<dynamic> members) {
    if (members.isEmpty) return "";

    if (members.length == 1) {
      var member = members.first;
      return "We have only charged partial premium for ${member['name']}, who was added on ${member['added_on']}. We will charge the full premium starting this year.";
    } else {
      StringBuffer message = StringBuffer("We have only charged partial premium for the following members:\n");

      for (var member in members) {
        message.writeln("• ${member['name']} added on ${member['added_on']}");
      }

      message.writeln("We will charge the full premium starting this year.");
      return message.toString();
    }
  }

  void setDiscount() {
    var renewalDetails = state.proposalDetails!.renewal!.cloneProposal!.header!
        .proposalData!["renewal_details"];
    if (renewalDetails != null) {
      if (renewalDetails!['discount_percent'] != null) {
        emit(state.copyWith(
            lastYearDiscount: renewalDetails['discount_percent'] ?? 0 as num));
      }
      if (renewalDetails!['old_premium_without_discount'] != null) {
        emit(state.copyWith(oldPremiumWithoutDiscount: NumberUtils.commaSeparatedNumber(renewalDetails['old_premium_without_discount'])));
      } else if (renewalDetails!['premium_without_discount'] != null) {
        emit(state.copyWith(
            oldPremiumWithoutDiscount: NumberUtils.commaSeparatedNumber(
                state.complianceDiffNum != null
                    ? renewalDetails['premium_without_discount'] -
                    state.complianceDiffNum
                    : renewalDetails['premium_without_discount'])));
      }
    }
  }

  Map<String, List<WaitingPeriod>> getWaitingPeriod(
      Map<String, dynamic> declarations) {
    Map<String, List<WaitingPeriod>> waitingPeriods = new HashMap();
    if (declarations.isNotNullOrEmpty) {
      if (declarations['declaration_options'] != null) {
        declarations['declaration_options'][0]['declarations']
            .forEach((waitingPeriod) {
          if (waitingPeriods[waitingPeriod['member_meta_data']['entity_id']]
              .isNullOrEmpty) {
            waitingPeriods[waitingPeriod['member_meta_data']['entity_id']] = [];
          }
          if (waitingPeriod['clauses'] != null) {
            String waitingPeriodType = "";
            if (waitingPeriod['disease_meta_data'] != null) {
              if ("pre_existing_diseases".equalsIgnoreCase(
                  (waitingPeriod['disease_meta_data']['group_code']))) {
                waitingPeriodType = "pre existing disease";
              } else {
                waitingPeriodType = "some medical conditions";
              }
            }
            waitingPeriod['clauses'].forEach((clause) {
              if ("waiting_period".equalsIgnoreCase(clause['clause_type']) &&
                  "years".contains(clause['parameters']['unit'])) {
                if (clause['parameters']['previous_value'] == null ||
                    clause['parameters']['previous_value'] !=
                        clause['parameters']['value']) {
                  waitingPeriods[waitingPeriod['member_meta_data']
                          ['entity_id']]!
                      .add(WaitingPeriod(
                          waitingPeriod: clause['parameters']['value'],
                          waitingPeriodType: waitingPeriodType,
                          previousWaitingPeriod:
                              clause['parameters']['previous_value'] ?? null,
                          initialWaitingPeriod:
                              clause['parameters']['initial_value'] ?? null));
                }
              }
            });
          }
        });
      }
    }
    return waitingPeriods;
  }

  void shouldExpand(int index) {
    emit(
      state.copyWith(
        coveredMember: [
          ...state.coveredMember!.map((item) {
            if (item == state.coveredMember![index]) {
              return item.copyWith(
                shouldExpand: !item.shouldExpand,
              );
            }
            return item;
          }),
        ],
      ),
    );
  }

  void getYearlyAndMonthlyAmount() {
    if (state.proposalDetails!.renewal!.quotes.isNotNullOrEmpty) {
      List<Quotes> quotes = state.proposalDetails!.renewal!.quotes!;
      quotes.forEach((element) {
        if (element.quoteParameter != null) {
          if ("yearly".equalsIgnoreCase(element.quoteParameter?['frequency'])) {
            //element['premium_detail']['gross_premium']
            emit(
              state.copyWith(
                  yearlyAmount: element.premiumDetail?['gross_premium'],
                  yearlyDisplayAmount: NumberUtils.commaSeparatedNumber(
                      element.premiumDetail?['gross_premium']),
                  discountAmount: NumberUtils.commaSeparatedNumber(element
                          .premiumDetail?['premium_attributes']?['discount'] ??
                      0)),
            );
          } else if ("monthly"
              .equalsIgnoreCase(element.quoteParameter?['frequency'])) {
            emit(
              state.copyWith(
                  monthlyAmount: element.premiumDetail?['gross_premium'] / 12,
                  monthlyDisplayAmount: NumberUtils.commaSeparatedNumber(
                      element.premiumDetail?['gross_premium'] / 12),
                  monthlyTotalAmount: element.premiumDetail?['gross_premium'],
                  payableAmount: NumberUtils.commaSeparatedNumber(
                      element.premiumDetail?['gross_premium'] / 12),
                  monthlyDiscountAmount: NumberUtils.commaSeparatedNumber(
                      element.premiumDetail?['premium_attributes']
                              ?['discount'] ??
                          0)),
            );
          }
        }
      });
    }
    if (state.proposalDetails!.renewal!.allowedFrequencies.isNotNullOrEmpty &&
        state.proposalDetails!.renewal!.allowedFrequencies!.length == 1) {
      if ("monthly".equalsIgnoreCase(
          state.proposalDetails!.renewal!.allowedFrequencies!.first)) {
        emit(
          state.copyWith(
            isMonthly: true,
            payableAmount: state.monthlyDisplayAmount,
          ),
        );
      } else {
        emit(
          state.copyWith(
            isMonthly: false,
            payableAmount: state.yearlyDisplayAmount,
          ),
        );
      }
    }
  }

  void getBannerType() {
    if ((state.policyType == PolicyType.AROGYASANJIVINI &&
            state.planData!.noClaimBonus! > 0) ||
        state.policyType == PolicyType.BASE) {
      emit(
        state.copyWith(
          bannerType: BannerType.info,
        ),
      );
    } else if (state.policyType == PolicyType.AROGYASANJIVINI &&
        state.planData!.noClaimBonus! == 0) {
      emit(
        state.copyWith(
          bannerType: BannerType.neutral,
        ),
      );
    } else {
      emit(
        state.copyWith(
          bannerType: BannerType.alert,
        ),
      );
    }
  }

  void getPolicyEndDateAndStartDate() {
    if (state.proposalDetails!.renewal!.cloneProposal!.header != null &&
        state.proposalDetails!.renewal!.cloneProposal!.header!.parameters
            .isNotNullOrEmpty) {
      if (state.proposalDetails!.renewal!.cloneProposal!.header!
              .parameters!['previous_policy_end_date'] !=
          null) {
        emit(
          state.copyWith(
              policyEndDate: DateTime.parse(state
                  .proposalDetails!
                  .renewal!
                  .cloneProposal!
                  .header!
                  .parameters!['previous_policy_end_date']['value'])),
        );
      }
    }
    if (state.proposalDetails!.renewal!.policyStartDate.isNotNullOrEmpty) {
      emit(
        state.copyWith(
          policyStartDate:
              DateTime.parse(state.proposalDetails!.renewal!.policyStartDate!),
        ),
      );
    }
  }

  void setEventProperties() {
    analyticsManager.propertyStore.setBulkProperties = {
      HlCommonEventProperty.policy_number: state.policyNumber,
      HlCommonEventProperty.page: 'renewals_home_page',
      HlCommonEventProperty.proposal_id:
          state.proposalDetails!.renewal!.latestProposalId,
      HlCommonEventProperty.journey: 'renewals',
      HlCommonEventProperty.bonus_type:
          state.policyType == PolicyType.AROGYASANJIVINI
              ? 'No Claim Bonus'
              : 'Inflation Protect Bonus',
      HlCommonEventProperty.product_category:
          state.policyType == PolicyType.AROGYASANJIVINI ||
                  state.policyType == PolicyType.BASE
              ? 'health_retail_base_plan'
              : 'health_retail_top-up_plan',
      HlCommonEventProperty.payment_frequency:
          state.isMonthly ? 'monthly' : 'yearly',
      HlCommonEventProperty.product: 'health_retail',
      HlCommonEventProperty.origin: 'SureOS',
      HlCommonEventProperty.plan:
          state.proposalDetails!.renewal!.productPlanType,
      HlCommonEventProperty.communication_email: communicationEmail,
      HlCommonEventProperty.group_type: state.policyType.toString(),
    };
  }
}
