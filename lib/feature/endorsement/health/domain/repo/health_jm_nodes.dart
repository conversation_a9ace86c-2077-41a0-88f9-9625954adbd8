/// JM Nodes

enum PrePolicyEditNodes {
  OVERVIEW('overview:ppe'),
  PREMIUM('premium:ppe'),
  ADD_MEMBER('add_member:ppe'),
  EDIT('edit:ppe'),
  HYDRATE_DETAILS('hydrate_details:ppe'),
  REVIEW('review:ppe'),
  SUMMARY('summary:ppe'),
  TRACKING('track:ppe'),
  CHECKOUT('checkout:ppe'),
  CANCEL_DRAFT('cancel_draft:ppe'),
  MULTI_YEAR_MANDATE('multi_year_mandate'),
  SUCCESS('success:ppe');
  // EDIT_ACTION('edit_action:ppe'); // rap node

  final String value;
  const PrePolicyEditNodes(this.value);
}

enum RenewalNodes {
  HYDRATE_OVERVIEW('hydrate_overview:renewal'),
  OVERVIEW('overview:renewal'),
  HYDRATE_MEMBERS_DETAIL('hydrate_members_detail:renewal'),
  MEMBERS_DETAIL(
      'members_detail:renewal'), // to be called after continue on review screen which will land us to overview screen
  // HYDRATE_ADD_MEMBER('hydrate_add_member:renewal'),
  // ADD_MEMBER('add_member:renewal'),
  UPDATE_MEMBERS_DETAIL(
      'update_members_detail:renewal'), // to be called while adding and updating member
  HYDRATE_REVIEW("hydrate_review:renewal"),
  MANDATE(
      "overview_with_mandate:renewal"); //to be called after continue on endo screen

  final String value;
  const RenewalNodes(this.value);
}
