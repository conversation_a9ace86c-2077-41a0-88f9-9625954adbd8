import 'package:acko_flutter/common/util/AckoTextStyle.dart';
import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/view/AckoText.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/auto_pay_mandate_sheet_model.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:design_module/uikit/widgets/button/acko_button.dart';
import 'package:design_module/utilities/hybrid_image.dart';
import 'package:flutter/material.dart';
import 'package:sdui/sdui.dart';

class AutoPayMandateSheet extends StatelessWidget {
  final AutoRenewalData autoRenewalData;
  const AutoPayMandateSheet({super.key, required this.autoRenewalData});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 20.0,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(
            height: 36,
          ),
          if (autoRenewalData.iconUrl.isNotNullOrEmpty)
            HybridImage(
              imageUrl: autoRenewalData.iconUrl ?? '',
              height: 80,
              width: 80,
            ),
          const SizedBox(
            height: 16,
          ),
          if (autoRenewalData.title.isNotNullOrEmpty)
            SDUIText(
              value: autoRenewalData.title,
              textStyle: "hXSmall",
              textColor: color121212,
              maxLines: 5,
              alignment: TextAlign.left,
            ),
          const SizedBox(
            height: 16,
          ),
          if (autoRenewalData.subtitle.isNotNullOrEmpty)
            SDUIText(
              value: autoRenewalData.subtitle,
              textStyle: "pSmall",
              textColor: color121212,
              maxLines: 10,
              alignment: TextAlign.left,
            ),
          const SizedBox(
            height: 24,
          ),
          if(autoRenewalData.benefits.isNotNullOrEmpty) ...[
            ListView.separated(
              shrinkWrap: true,
              padding: EdgeInsets.zero,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: autoRenewalData.benefits?.length ?? 0,
              itemBuilder: (context, index) {
                final benefit = autoRenewalData.benefits![index];
                return Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (benefit?.icon.isNotNullOrEmpty ?? false)
                      HybridImage(
                        imageUrl: benefit.icon ?? '',
                        height: 32,
                        width: 32,
                      ),
                    const SizedBox(
                      width: 8,
                    ),
                    Flexible(
                      child: Text.rich(
                        TextSpan(
                          children: [
                            TextSpan(
                              text: benefit.title,
                              style:  TextStyleInterMediumL16(
                                fontSize: 14,
                                color: color121212,
                              ),
                            ),
                            TextSpan(
                              text: ' ',
                              style: TextStyleInterMediumL16(
                                fontSize: 14,
                                color: color121212,
                              ),
                            ),
                            TextSpan(
                              text: benefit.description,
                              style: TextStyleInterRegular(
                                fontSize: 14,
                                color: color121212,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                );
              },
              separatorBuilder: (context, index) => const SizedBox(
                height: 16,
              ),
            ),
            const SizedBox(
              height: 24,
            ),
          ],
          AutoRenewalInfoSection(
            data: autoRenewalData,
          ),
          const SizedBox(
            height: 16,
          ),
          if (autoRenewalData.primaryCta.isNotNullOrEmpty)
            AckoDarkButtonFullWidth(
              text: autoRenewalData.primaryCta!,
              onTap: () {
                Navigator.pop(context, true);
              },
            ),
          const SizedBox(
            height: 16,
          ),
          if (autoRenewalData.secondaryCta.isNotNullOrEmpty)
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: color040222),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(
                      12,
                    ),
                  ),
                  padding: EdgeInsets.symmetric(
                    vertical: 12,
                  ),
                ),
                onPressed: () {
                  Navigator.pop(context, false);
                },
                child: TextEuclidMediumL16(
                  autoRenewalData.secondaryCta!,
                  textColor: color040222,
                ),
              ),
            ),
          const SizedBox(
            height: 20,
          ),
        ],
      ),
    );;
  }
}

class AutoRenewalInfoSection extends StatelessWidget {
  final AutoRenewalData data;

  const AutoRenewalInfoSection({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    final items = [
      data.oldPremiumText,
      data.newPremiumText,
      data.remainingBalance,
      data.validity,
      data.nextPaymentDate,
    ].where((item) => item != null).toList();

    if (items.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: colorE0E0E8),
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: items.map((item) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: _InfoColumn(item: item!),
          );
        }).toList(),
      ),
    );
  }
}


class _InfoColumn extends StatelessWidget {
  final InfoItem item;

  const _InfoColumn({required this.item});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        SDUIText(
          value: item.title,
          textStyle: "pSmall",
          textColor: color4B4B4B,
          alignment: TextAlign.left,
        ),
        const SizedBox(height: 4),
        SDUIText(
          value: item.value,
          textStyle: "lMedium",
          textColor: color040222,
          alignment: TextAlign.left,
        ),
      ],
    );
  }
}

