import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/feature/acko_services/ui/acko_service_loading_screen.dart';
import 'package:acko_flutter/feature/acko_services/ui/acko_services_initial_screen.dart';
import 'package:acko_flutter/feature/endorsement/core/util/force_login_util.dart';
import 'package:acko_flutter/feature/endorsement/core/util/node_router.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/models/health_jm_response.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_nodes.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/bloc/tracking_bloc/ppe_tracking_bloc.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/bloc/tracking_bloc/ppe_tracking_states.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_edit_tracking_model.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/view/bottomsheets/auto_pay_mandate_sheet.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/view/widgets/edit_tracking_header_widget.dart';
import 'package:acko_flutter/feature/endorsement/shared/widgets/edits_tracking_list_widget.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_flutter/util/health/health_constants.dart';
import 'package:analytics/events/health_life/tap_events/health_life_tap_events.dart';
import 'package:design_module/uikit/widgets/bottom_sheet/show_bottom_sheet.dart';
import 'package:design_module/uikit/widgets/button/acko_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:utilities/utilities.dart';

class PolicyUpdateSubmittedScreen extends StatefulWidget {
  const PolicyUpdateSubmittedScreen({super.key});

  @override
  State<PolicyUpdateSubmittedScreen> createState() =>
      _PolicyUpdateSubmittedScreenState();
}

class _PolicyUpdateSubmittedScreenState
    extends State<PolicyUpdateSubmittedScreen> {
  PPETrackingCubit? _cubit;
  bool _hasShownBottomSheet = false;

  @override
  void initState() {
    super.initState();
    _cubit = BlocProvider.of<PPETrackingCubit>(context);
    // _cubit?.getSummaryData();
  }

  /// disable back nav
  Future<bool> _popPage() async {
    return Future.value(false);
  }

  @override
  Widget build(BuildContext context) {
    return AckoWillPopScope(
      onWillPop: _popPage,
      child: BlocConsumer<PPETrackingCubit, PPETrackingState>(
          listener: (context, state) {
            if (state is Loaded) {
              final modalShown = ForceLoginUtil.showForceLoginModal(
                context,
                state.jmResponse,
              );
              if (modalShown) return;
              if (_cubit?.autoRenewalData != null && !_hasShownBottomSheet) {
                _hasShownBottomSheet = true;
                _cubit?.triggerTapEvent(HLTrackEvents.TAP_PPE_LONGTERM_MANDATE_ACK);
                context.showAckoModalBottomSheet(
                  child: AutoPayMandateSheet(
                    autoRenewalData: _cubit!.autoRenewalData!,
                  ),
                );
              }
            }
          },
          buildWhen: (prev, curr) =>
              curr is Loading || curr is Loaded || curr is Error,
          builder: (context, state) {
            if (state is Loaded) {
              if (state.jmResponse?.showForceLogin == true) {
                return _getErrorView(
                  context,
                  title: HealthConstants.unauthorizedAccess,
                  subtitle: HealthConstants.unauthorizedAccessMessage,
                  showButton: false,
                  assetImage: 'acko_payments/ic_error_alert.svg',
                );
              }
              return _buildLoadedView(state.ppeEditingHistory);
            } else if (state is Loading || state is Initial)
              return _getLoadingView();
            else
              return _getErrorView(context);
          }),
    );
  }

  Widget _getLoadingView() {
    return Scaffold(body: Center(child: AckoServiceLoadingScreen()));
  }

  Widget _getErrorView(
    BuildContext context, {
    String? title,
    String? subtitle,
    String? btnTitle,
    bool showButton = true,
    String? assetImage,
  }) {
    return Scaffold(
      body: AckoServicesIntiailScreen(
        title: title ?? something_went_wrong,
        subTitle: subtitle ?? api_something_went_wrong_sory,
        btnTitle: btnTitle ?? go_back,
        isOutlinedButton: showButton,
        onTap: () => Navigator.pop(context),
        imgUrl:
            Util.getAssetImage(assetName: assetImage ?? 'ic_bucket_drop.svg'),
      ),
    );
  }

  Widget _buildLoadedView(
      List<PrePolicyEditsTrackingModel>? ppeEditingHistory) {
    return Scaffold(
      body: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          EditTrackingHeader(
            trackingState: _cubit?.ppeTrackingState,
          ),
          Expanded(
            child: ListView.separated(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                physics: AlwaysScrollableScrollPhysics(),
                itemBuilder: (context, index) => EditHistoryListWidget(
                      editGroup: ppeEditingHistory?[index],
                      ppeTrackingState: _cubit?.ppeTrackingState,
                    ),
                separatorBuilder: (context, index) => SizedBox(
                      height:
                          ppeEditingHistory?[index].editDetails.isNullOrEmpty ??
                                  true
                              ? 0
                              : 16,
                    ),
                itemCount: ppeEditingHistory?.length ?? 0),
          ),
        ],
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(16),
        child: AckoDarkButtonFullWidth(
          text: got_it,
          onTap: () {
            JourneyManagerRouter().routeNodeToNativeRoutes(
                PrePolicyEditNodes.SUMMARY,
                {
                  "proposal_id": _cubit?.proposalId,
                  // "data": _cubit?.prePolicyEditingResponse,
                  "refresh_home": true
                },
                context,
                replace: true);
          },
        ),
      ),
    );
  }
}
