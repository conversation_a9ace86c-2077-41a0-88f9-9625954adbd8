import 'dart:async';

import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_constants.dart';
import 'package:acko_flutter/feature/endorsement/core/util/node_router.dart';
import 'package:acko_flutter/feature/endorsement/domain/models/hl_endorsement_copies.dart'
    as hlEndorsementCopies;
import 'package:acko_flutter/feature/endorsement/domain/repository/hl_endorsement_repository.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/models/health_jm_response.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_nodes.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/utilities.dart';

class PPEChangesSubmittedScreen extends StatefulWidget {
  final String? proposalId;
  final HealthJourneyManagerResponse? prePolicyEditingResponse;
  final PrePolicyEditNodes? nextNode;
  final String? isBasba;
  final String? isMandateEnabled;

  PPEChangesSubmittedScreen(
      {super.key,
      this.proposalId,
      this.prePolicyEditingResponse,
      this.nextNode,
      this.isBasba,
      this.isMandateEnabled});

  @override
  State<PPEChangesSubmittedScreen> createState() =>
      _PPEChangesSubmittedScreenState();
}

class _PPEChangesSubmittedScreenState extends State<PPEChangesSubmittedScreen> {
  late Timer timer;
  late bool isBasbaPolicyWithPayment;

  hlEndorsementCopies.PpeRemoveMemberSheet? rcPpeChangesSubmittedScreen;

  @override
  void initState() {
    super.initState();
    isBasbaPolicyWithPayment = widget.isBasba.equalsIgnoreCase('true') && widget.prePolicyEditingResponse == null;
    rcPpeChangesSubmittedScreen = HlEndorsementRepository()
        .prePolicyEdits
        .view
        ?.screens
        ?.ppeChangesSubmittedScreen;
    timer = Timer(Duration(seconds: 5), () {
      JourneyManagerRouter().routeNodeToNativeRoutes(
          PrePolicyEditNodes.TRACKING,
          {
            "proposal_id": widget.proposalId,
            "next_node": PrePolicyEditNodes.TRACKING,
          },
          context,
          replace: true);
    });
  }

  @override
  void dispose() {
    timer.cancel();
    super.dispose();
  }

  /// disable back nav
  Future<bool> _popPage() async {
    return Future.value(false);
  }

  @override
  Widget build(BuildContext context) {
    return AckoWillPopScope(
      onWillPop: _popPage,
      child: Scaffold(
        body: Container(
          width: MediaQuery.of(context).size.width,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SDUIImage(
                imageUrl: HealthJourneyManagerAssets.successIcon,
                width: 154,
                height: 154,
              ),
              SDUIText(
                padding: EdgeInsets.all(16),
                value: isBasbaPolicyWithPayment ? rcPpeChangesSubmittedScreen?.basbaTitle ?? ppe_mandate_updated : rcPpeChangesSubmittedScreen?.title ?? ppe_changes_submitted,
                textStyle: "hMedium",
                textColor: color0B753E,
                maxLines: 10,
                alignment: TextAlign.center,
              ),
              if(widget.isMandateEnabled.equalsIgnoreCase('true') && widget.prePolicyEditingResponse == null)...[
                SDUIText(
                  padding: EdgeInsets.all(16),
                  value: mandateInfo,
                  textStyle: "pSmall",
                  textColor: color4B4B4B,
                  maxLines: 10,
                  alignment: TextAlign.center,
                ),
              ]
            ],
          ),
        ),
      ),
    );
  }
}
