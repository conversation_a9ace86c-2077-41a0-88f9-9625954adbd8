import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/common/view/FullPageLoader.dart';
import 'package:acko_flutter/common/view/dashed_line.dart';
import 'package:acko_flutter/feature/acko_services/ui/acko_service_loading_screen.dart';
import 'package:acko_flutter/feature/acko_services/ui/acko_services_initial_screen.dart';
import 'package:acko_flutter/feature/car_journey/view/icon_app_bar.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_utils.dart';
import 'package:acko_flutter/feature/endorsement/core/util/node_router.dart';
import 'package:acko_flutter/feature/endorsement/domain/models/hl_endorsement_copies.dart'
    as hlEndorsementCopies;
import 'package:acko_flutter/feature/endorsement/domain/repository/hl_endorsement_repository.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/models/health_jm_response.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_nodes.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/bloc/checkout_bloc/ppe_checkout_bloc.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/bloc/checkout_bloc/ppe_checkout_states.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_premium_changes_model.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/view/bottomsheets/ppe_basba_mandate_sheet.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/view/bottomsheets/ppe_review_changes_sheet.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/view/widgets/mandate_info_banner.dart';
import 'package:acko_flutter/feature/endorsement/shared/widgets/edits_refund_note_widget.dart';
import 'package:acko_flutter/feature/endorsement/shared/widgets/premium_comparision_card.dart';
import 'package:acko_flutter/feature/endorsement/shared/widgets/pricing_footer.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/events/health_life/tap_events/health_life_tap_events.dart';
import 'package:design_module/design_module.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/constants/constants.dart';

import '../bottomsheets/auto_pay_mandate_sheet.dart';

class PPECheckoutScreen extends StatefulWidget {
  const PPECheckoutScreen({super.key});

  @override
  State<PPECheckoutScreen> createState() => _PPECheckoutScreenState();
}

class _PPECheckoutScreenState extends State<PPECheckoutScreen> {
  PPECheckoutCubit? _cubit;
  final HealthJourneyManagerUtils _healthJMUtil = HealthJourneyManagerUtils();

  hlEndorsementCopies.PpeCheckoutScreen? rcPpeCheckoutScreen;

  @override
  void initState() {
    super.initState();
    rcPpeCheckoutScreen = HlEndorsementRepository()
        .prePolicyEdits
        .view
        ?.screens
        ?.ppeCheckoutScreen;
    _cubit = BlocProvider.of<PPECheckoutCubit>(context);
    _cubit?.getEditsData();
  }

  @override
  Widget build(BuildContext context) {
    if (rcPpeCheckoutScreen == null) {
      return SizedBox.shrink();
    }
    return BlocConsumer<PPECheckoutCubit, PPECheckoutState>(
        listenWhen: (prev, curr) =>
            curr is NavigateToNextNode ||
            curr is FullPageLoading ||
            curr is ErrorToast ||
            curr is ShowReviewSheet,
        listener: (context, state) {
          if (state is ErrorToast) {
            FullPageLoader.instance.dismissFullPageLoader(context);
            _healthJMUtil.showToastMessage(message: "Something went wrong");
          }
          if (state is FullPageLoading) {
            FullPageLoader.instance.showFullPageLoader(context);
          }
          if (state is NavigateToNextNode) {
            FullPageLoader.instance.dismissFullPageLoader(context);
            if (state.redirectionUrl.isNotNullOrEmpty) {
              String ekey = extractIdFromUrl(state.redirectionUrl!);
              if((_cubit?.prePolicyEditingResponse?.edit?.entityDetails?.basbaProposal ?? false) && (_cubit?.prePolicyEditingResponse?.edit?.entityDetails?.isActiveMandate ?? false)) {
                Navigator.pushNamed(context, Routes.PPE_MANDATE_CANCELLED_SCREEN,
                    arguments: {"order_id": ekey});
              } else {
                Navigator.pushNamed(context, Routes.PAYMENT,
                    arguments: {"ekey": ekey});
              }
            } else {
              JourneyManagerRouter().routeNodeToNativeRoutes(
                  PrePolicyEditNodes.SUCCESS,
                  {
                    "proposal_id": _cubit?.proposalId,
                    "data": _cubit?.prePolicyEditingResponse, // not req ig??
                    "next_node": _cubit?.nextNode
                  },
                  context,
                  replace: true,
                  popUntil: true,
                  previousRoute: Routes.PPE_EDIT_OVERVIEW_SCREEN);
            }
          }
          if (state is ShowReviewSheet) {
            context.showAckoModalBottomSheet(
                child: ReviewChangesSheet(
                    policyChangesData: state.policyChangesData,
                    totalEdits: state.totalEdits,
                    isBasbaProposal: _cubit?.prePolicyEditingResponse?.edit
                        ?.entityDetails?.basbaProposal));
          }
        },
        buildWhen: (prev, curr) =>
            curr is Loading || curr is Loaded || curr is Error,
        builder: (context, state) {
          if (state is Loaded)
            return _buildLoadedView(
                premiumDetails: state.premiumDetails,
                premiumChange: state.premiumChange,
                totalEdits: state.totalEdits,
                amount: state.amount, isBasbaProposal: state.isBasbaProposal, isActiveMandate: state.isActiveMandate, mandateDetails: state.mandateDetails);
          else if (state is Loading)
            return _getLoadingView();
          else
            return _getErrorView();
        });
  }

  Widget _getLoadingView() {
    return Scaffold(body: Center(child: AckoServiceLoadingScreen()));
  }

  Widget _getErrorView() {
    return Scaffold(
      body: AckoServicesIntiailScreen(
        title: something_went_wrong,
        subTitle: api_something_went_wrong_sory,
        btnTitle: go_back,
        isOutlinedButton: true,
        onTap: () => Navigator.pop(context),
        imgUrl: Util.getAssetImage(assetName: 'ic_bucket_drop.svg'),
      ),
    );
  }

  _buildLoadedView(
      {PremiumChange? premiumChange,
      PremiumDetails? premiumDetails,
      int totalEdits = 0,
      String amount = "",
      bool? isBasbaProposal,
      bool? isActiveMandate,
      MandateDetails? mandateDetails}) {
    return Scaffold(
      appBar: IconAppbar.newBackBtnAppBar(context, background: colorFFFFFF),
      bottomNavigationBar: PricingFooter(
        title: _cubit?.premiumDetails?.getFooterTitle(totalEdits, isBasbaProposal ?? false),
        price: amount,
        totalEdits: totalEdits,
        onContinue: () {
          if ((isBasbaProposal == true) &&
              (isActiveMandate == true) && (premiumDetails?.isPaymentRequired == true)) {
            context.showAckoModalBottomSheet(child: PpeBasbaMandateSheet(
              oldMandateAmount: mandateDetails?.formattedOldMandate,
              newMandateAmount: mandateDetails?.formattedNewMandate,
            )).then((value) {
              if(value == true) {
                _cubit?.callCheckoutApi();
              }
            });
          } else if (_cubit?.autoRenewalData != null) {
            context.showAckoModalBottomSheet(child: AutoPayMandateSheet(autoRenewalData: _cubit!.autoRenewalData!)).then((value) {
              if(value) {
                _cubit?.triggerTapEvent(HLTrackEvents.TAP_PPE_LONGTERM_MANDATE_CONTINUE);
                _cubit?.callCheckoutApi(inputData: _cubit?.inputData);
              }
            });
          } else {
            _cubit?.callCheckoutApi(inputData: _cubit?.inputData);
          }
        },
        onTotalEditsTap: () {
          _cubit?.onTotalEditsTap();
        },
        enableCta: true,
        ctaText: premiumDetails?.getCheckoutCtaText(isBasbaProposal ?? false, isActiveMandate ?? true) ?? submit,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SDUIText(
              value: rcPpeCheckoutScreen!.title,
              textStyle: "hSmall",
              textColor: color040222,
              maxLines: 2,
            ),
            const SizedBox(
              height: 32,
            ),
            PremiumComparisionCard(
              premiumChange: premiumChange,
              isDark: false,
            ),
            const SizedBox(
              height: 24,
            ),
            if (!(isBasbaProposal ?? false)) ...[
              _getPremiumDetailsWidget(premiumDetails),
              const SizedBox(
                height: 24,
              ),
            ],
            if ((isBasbaProposal ?? false) &&
                (mandateDetails?.checkoutNote.isNotNullOrEmpty ?? false))
              MandateInfoBanner(infoText: mandateDetails?.checkoutNote),
            if (!(isBasbaProposal ?? false) &&
                (premiumDetails?.isRefundRequired ?? false))
              EditsRefundNote(),
            const SizedBox(
              height: 32,
            )
          ],
        ),
      ),
    );
  }

  String extractIdFromUrl(String url) {
    Uri uri = Uri.parse(url);
    return uri.queryParameters['id'] ?? '';
  }

  _getPremiumDetailsWidget(PremiumDetails? premiumDetails) {
    return Container(
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorE8E8E8, width: 1),
        color: Colors.white,
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SDUIText(
                  value: rcPpeCheckoutScreen!.premiumDetails?.premiumDue,
                  textStyle: "lMedium",
                  textColor: color121212,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  child: DottedLine(
                    dashColor: colorE7E7F0,
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SDUIText(
                      value:
                          rcPpeCheckoutScreen!.premiumDetails?.totalPremiumDue,
                      textStyle: "pSmall",
                      textColor: color4B4B4B,
                    ),
                    SDUIText(
                      value: premiumDetails?.totalPremiumDueFormatted,
                      textStyle: "lSmall",
                      textColor: color121212,
                    ),
                  ],
                ),
                const SizedBox(
                  height: 12,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SDUIText(
                      value: rcPpeCheckoutScreen!.premiumDetails?.alreadyPaid,
                      textStyle: "pSmall",
                      textColor: color4B4B4B,
                    ),
                    SDUIText(
                      value: premiumDetails?.premiumAlreadyPaidFormatted,
                      textStyle: "lSmall",
                      textColor: color121212,
                    ),
                  ],
                ),
              ],
            ),
          ),
          ClipRRect(
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(12),
              bottomRight: Radius.circular(12),
            ),
            child: Container(
                width: MediaQuery.of(context).size.width,
                padding: EdgeInsets.fromLTRB(16, 16, 16, 12),
                color: colorF9F6FF,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SDUIText(
                      value: (premiumDetails?.isRefundRequired ?? false)
                          ? rcPpeCheckoutScreen!.premiumDetails?.ackoWillRefund
                          : rcPpeCheckoutScreen!.premiumDetails!.youNeedToPay,
                      textStyle: "lSmall",
                      textColor: color121212,
                    ),
                    SDUIText(
                      value: premiumDetails?.toBePaidFormatted,
                      textStyle: "hXXSmall",
                      textColor: color121212,
                    ),
                  ],
                )),
          ),
        ],
      ),
    );
  }
}
