import 'dart:async';
import 'dart:convert';

import 'package:acko_flutter/common/util/PreferenceHelper.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_instrumentation_mixin.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/models/health_jm_response.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_nodes.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_repo.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/bloc/tracking_bloc/ppe_tracking_states.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/auto_pay_mandate_sheet_model.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_edit_tracking_model.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_premium_changes_model.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/events/health_life/page_events/health_life_page_events.dart';
import 'package:analytics/events/health_life/tap_events/health_life_tap_events.dart';
import 'package:session_manager_module/StorageManager/shared_preferences_storage.dart';
import 'package:utilities/remote_config/remote_config.dart';
import 'package:utilities/widgets/acko_safe_cubit.dart';

enum PPETrackingStates { CHANGES_SUBMITTED, CHANGES_TRACKING }

class PPETrackingCubit extends AckoSafeCubit<PPETrackingState>
    with HealthJourneyManagerInstrumentationMixin {
  final _repo = JourneyManagerRepository();
  final PPETrackingStates ppeTrackingState;
  bool? refreshHome;

  String? proposalId;
  HealthJourneyManagerResponse? prePolicyEditingResponse;

  String? phoneNumber;
  int _currentApiCallIdentifier = 0;
  int maxDurationTime = 10;
  Timer? timerInstance;
  PremiumDetails? premiumDetails;
  PremiumChange? premiumChange;
  AutoRenewalData? autoRenewalData;

  PPETrackingCubit({
    required this.ppeTrackingState,
    this.proposalId,
    this.prePolicyEditingResponse,
    this.refreshHome = false,
  }) : super(Initial()) {
    if (ppeTrackingState == PPETrackingStates.CHANGES_SUBMITTED) {
      getTrackingData(_currentApiCallIdentifier, force: true);
    } else {
      getSummaryData();
    }
  }

  _stopTimerOnMaxDurationExceed() async {
    await Future.delayed(Duration(seconds: maxDurationTime));
    stopTimer();
    List<PrePolicyEditsTrackingModel> ppeEditingHistory =
        prePolicyEditingResponse?.edit?.generatePrePolicyEditsTrackingModels(
                proposalId, ppeTrackingState) ??
            [];
    emit(Loaded(
      ppeEditingHistory: ppeEditingHistory,
      jmResponse: prePolicyEditingResponse,
    ));
    // if (_shouldEmitLoadedState()) {}
  }

  bool _shouldEmitLoadedState() {
    // Checks if the response is not null and the status is not "draft"
    return prePolicyEditingResponse != null &&
        (prePolicyEditingResponse?.edit?.editGroups?.firstOrNull?.status
                .notEqualsIgnoreCase("draft") ??
            false);
  }

  stopTimer() {
    timerInstance?.cancel();
  }

  _startStatusCheckTimer() {
    timerInstance = Timer.periodic(const Duration(seconds: 2), (timer) {
      _currentApiCallIdentifier = DateTime.now().millisecondsSinceEpoch;
      getTrackingData(_currentApiCallIdentifier, force: true);
    });
  }

  Future<void> getTrackingData(int currentTime, {bool force = false}) async {
    int _functionTimeIdentifier = currentTime;
    try {
      /// Emit Loading state only if this is the first call or when forced
      emit(Loading());

      phoneNumber =
          await getStringPrefs(StringDataSharedPreferenceKeys.MOBILE_NUMBER);

      /// Check if a network call is needed
      if (force ||
          prePolicyEditingResponse == null ||
          _shouldCheckForUpdates()) {
        Map<String, dynamic> requestBody = {
          "current_node_id":
              ppeTrackingState == PPETrackingStates.CHANGES_SUBMITTED
                  ? PrePolicyEditNodes.TRACKING.value
                  : PrePolicyEditNodes.SUMMARY.value,
          "edit": {
            "entity_id": proposalId,
            "journey": "health_pre_policy_edit",
            "entity_type": "proposal",
          },
          "input_data_id": null,
        };

        /// Cancel the previous API call if it's still ongoing
        _currentApiCallIdentifier = _functionTimeIdentifier;
        prePolicyEditingResponse =
            await _repo.manageJourney(JourneyType.PRE_POLICY_EDIT, requestBody);
      }

      premiumDetails =
          mapToPremiumDetails(prePolicyEditingResponse?.edit?.deltaPremium);

      premiumChange =
          mapToPremiumChange(prePolicyEditingResponse?.edit?.deltaPremium);

      final parameters = prePolicyEditingResponse?.edit?.proposal?['header']?['parameters'];
      final isMandateEnabled = parameters?['is_mandate_enabled']?['value'] as bool?;
      if(isMandateEnabled ?? false) {
        final response = await RemoteConfigInstance.instance
            .getData(RemoteConfigKeysSet.PPE_MANDATE_BOTTOMSHEET);
        final autoRenewalConfigData = AutoRenewalConfig.fromJson(jsonDecode(response));
        autoRenewalData = (premiumDetails?.isRefundRequired ?? false) ? autoRenewalConfigData.ppeDecrease : null;
        var newPremiumText = autoRenewalData?.newPremiumText;
        var oldPremiumText = autoRenewalData?.newPremiumText;
        if(newPremiumText != null) {
          newPremiumText.title = newPremiumText.title.replaceAll('%s', prePolicyEditingResponse?.edit?.deltaPremium?.paymentFrequency.equalsIgnoreCase('monthly') == true ? 'monthly' : 'yearly') ?? '';
          newPremiumText.value = premiumDetails?.totalPremiumDueFormatted ?? '';
        }
        if(oldPremiumText != null) {
          oldPremiumText.title = oldPremiumText.title.replaceAll('%s', prePolicyEditingResponse?.edit?.deltaPremium?.paymentFrequency.equalsIgnoreCase('monthly') == true ? 'monthly' : 'yearly') ?? '';
          oldPremiumText.value = premiumChange?.oldPremiumFormattedWithoutFrequency ?? '';
        }
        List<PrePolicyEditsTrackingModel> ppeEditingHistory =
            prePolicyEditingResponse?.edit
                ?.generatePrePolicyEditsTrackingModels(
                proposalId, ppeTrackingState) ??
                [];
        emit(Loaded(
          ppeEditingHistory: ppeEditingHistory,
          jmResponse: prePolicyEditingResponse,
        ));
      }

      /// Emit Loaded state only when the required condition is met
      if (_shouldEmitLoadedState()) {
        List<PrePolicyEditsTrackingModel> ppeEditingHistory =
            prePolicyEditingResponse?.edit
                    ?.generatePrePolicyEditsTrackingModels(
                        proposalId, ppeTrackingState) ??
                [];
        emit(Loaded(
          ppeEditingHistory: ppeEditingHistory,
          jmResponse: prePolicyEditingResponse,
        ));
      }
    } catch (e) {
      emit(Error());
    }
  }

  Future<void> getSummaryData({bool force = false}) async {
    try {
      emit(Loading());
      phoneNumber =
          await getStringPrefs(StringDataSharedPreferenceKeys.MOBILE_NUMBER);
      if (force || prePolicyEditingResponse == null) {
        Map<String, dynamic> requestBody = {
          "current_node_id":
              ppeTrackingState == PPETrackingStates.CHANGES_SUBMITTED
                  ? PrePolicyEditNodes.TRACKING.value
                  : PrePolicyEditNodes.SUMMARY.value,
          "edit": {
            "entity_id": proposalId,
            "journey": "health_pre_policy_edit",
            "entity_type": "proposal"
          },
          "input_data_id": null
        };
        prePolicyEditingResponse =
            await _repo.manageJourney(JourneyType.PRE_POLICY_EDIT, requestBody);
      }
      List<PrePolicyEditsTrackingModel> ppeEditingHistory =
          prePolicyEditingResponse?.edit?.generatePrePolicyEditsTrackingModels(
                  proposalId, ppeTrackingState) ??
              [];
      emit(Loaded(
        ppeEditingHistory: ppeEditingHistory,
        jmResponse: prePolicyEditingResponse,
      ));
    } catch (e) {
      emit(Error());
    }
  }

  PremiumDetails mapToPremiumDetails(DeltaPremium? deltaPremium) {
    return PremiumDetails(
      totalPremiumDue: deltaPremium?.newInstallment,
      premiumAlreadyPaid: deltaPremium?.paidAmount,
      toBePaid: deltaPremium?.adhocPayment,
      dueTime: deltaPremium?.premiumPendingCount,
    );
  }

  PremiumChange mapToPremiumChange(DeltaPremium? deltaPremium) {
    return PremiumChange(
      oldPremium: deltaPremium?.previousInstallment,
      newPremium: deltaPremium?.newInstallment,
      paymentFrequency: deltaPremium?.paymentFrequency,
    );
  }

  triggerPageEvent(HLPageEvents event) {
    String? groupType =
        prePolicyEditingResponse?.edit?.newValue?.currentPlan?.packageType;
    String? communicationEmail = prePolicyEditingResponse
        ?.edit
        ?.newValue
        ?.usersContainer
        ?.usersMap
        .values
        .firstOrNull
        ?.parameters
        .parameterMap['email']
        ?.value;
    String? insuredId = prePolicyEditingResponse
        ?.edit?.newValue?.usersContainer?.usersMap.values.firstOrNull?.userId;

    triggerPageAnalyticalEventsForPPE(
      pageEvent: event,
      proposalId: proposalId,
      platform: Util.getPlatform(),
      product: "health_retail",
      page: "overview:ppe",
      groupType: groupType,
      communicationEmail: communicationEmail,
      communicationPhone: phoneNumber,
      journey: "pre-policy-edit",
      uniqueId: insuredId,
      origin: "SUREOS",
    );
  }

  triggerTapEvent(HLTrackEvents event) {
    String? groupType =
        prePolicyEditingResponse?.edit?.newValue?.currentPlan?.packageType;
    String? paymentFrequency =
        prePolicyEditingResponse?.edit?.deltaPremium?.paymentFrequency;
    String? communicationEmail = prePolicyEditingResponse
        ?.edit
        ?.newValue
        ?.usersContainer
        ?.usersMap
        .values
        .firstOrNull
        ?.parameters
        .parameterMap['email']
        ?.value;
    String? insuredId = prePolicyEditingResponse
        ?.edit?.newValue?.usersContainer?.usersMap.values.firstOrNull?.userId;

    triggerTapAnalyticalEventsForPPE(
      trackEvent: event,
      proposalId: proposalId,
      platform: Util.getPlatform(),
      product: "health_retail",
      page: "overview:ppe",
      groupType: groupType,
      communicationEmail: communicationEmail,
      communicationPhone: phoneNumber,
      uniqueId: insuredId,
      journey: "pre-policy-edit",
      origin: "SUREOS",
      paymentFrequency: paymentFrequency,
    );
  }

  bool _shouldCheckForUpdates() {
    return prePolicyEditingResponse?.edit?.editGroups?.firstOrNull?.status
            .equalsIgnoreCase("draft") ??
        false;
  }
}
