import 'dart:convert';

import 'package:acko_flutter/common/util/PreferenceHelper.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_constants.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_instrumentation_mixin.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_utils.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/models/health_jm_response.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_nodes.dart';
import 'package:acko_flutter/feature/endorsement/health/domain/repo/health_jm_repo.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/bloc/checkout_bloc/ppe_checkout_states.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/bloc/tracking_bloc/ppe_tracking_bloc.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/auto_pay_mandate_sheet_model.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_form_editing_models.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_policy_changes_model.dart';
import 'package:acko_flutter/feature/endorsement/health/pre_policy_edits/models/ui_models/ppe_premium_changes_model.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/events/health_life/tap_events/health_life_tap_events.dart';
import 'package:session_manager_module/StorageManager/shared_preferences_storage.dart';
import 'package:utilities/remote_config/remote_config.dart';
import 'package:utilities/widgets/acko_safe_cubit.dart';

class PPECheckoutCubit extends AckoSafeCubit<PPECheckoutState> with HealthJourneyManagerInstrumentationMixin {
  final _repo = JourneyManagerRepository();
  final EditType editType;
  PremiumChange? premiumChange;
  PremiumDetails? premiumDetails;
  final String proposalId;
  int totalEdits = 0;
  PrePolicyEditNodes? nextNode;
  FormValues formValues = FormValues();
  AutoRenewalData? autoRenewalData;
  InputData? inputData;
  String? phoneNumber;

  HealthJourneyManagerResponse? prePolicyEditingResponse;
  PPECheckoutCubit(
      {required this.proposalId,
      this.editType = EditType.NONE,
      this.prePolicyEditingResponse})
      : super(Initial());

  HealthJourneyManagerUtils _ppeUtils = HealthJourneyManagerUtils();

  Future<void> getEditsData({bool force = false}) async {
    emit(Loading());

    phoneNumber =
    await getStringPrefs(StringDataSharedPreferenceKeys.MOBILE_NUMBER);

    if (force || prePolicyEditingResponse == null) {
      Map<String, dynamic> requestBody = {
        "current_node_id": PrePolicyEditNodes.HYDRATE_DETAILS.value,
        "edit": {
          "entity_id": proposalId,
          "journey": "health_pre_policy_edit",
          "entity_type": "proposal"
        },
        "input_data_id": null
      };

      prePolicyEditingResponse =
          await _repo.manageJourney(JourneyType.PRE_POLICY_EDIT, requestBody);
    }

    if (prePolicyEditingResponse == null ||
        prePolicyEditingResponse!.error != null) {
      emit(Error());
      return;
    }

    nextNode = prePolicyEditingResponse?.getHealthJourneyManagerNode(
        trackingState: PPETrackingStates.CHANGES_SUBMITTED);

    premiumChange =
        mapToPremiumChange(prePolicyEditingResponse?.edit?.deltaPremium);
    premiumDetails =
        mapToPremiumDetails(prePolicyEditingResponse?.edit?.deltaPremium);

    totalEdits = prePolicyEditingResponse?.edit?.getTotalPendingEdits ?? 0;

    final parameters = prePolicyEditingResponse?.edit?.proposal?['header']?['parameters'];
    final isMandateEnabled = parameters?['is_mandate_enabled']?['value'] as bool?;
    final packageId = (parameters['packages']?['value'] as List?)
        ?.firstWhere((e) => e['id'] == 'package', orElse: () => null)?['value']
        ?.firstWhere((e) => e['id'] == 'package_id', orElse: () => null)?['value'];
    if(isMandateEnabled ?? false) {
      inputData = await callMultiYearNodeApi(packageId, prePolicyEditingResponse?.edit?.deltaPremium?.paymentFrequency ?? '');
      final response = RemoteConfigInstance.instance
          .getData(RemoteConfigKeysSet.PPE_MANDATE_BOTTOMSHEET);
      final autoRenewalConfigData = AutoRenewalConfig.fromJson(jsonDecode(response));
      autoRenewalData = premiumDetails?.isPaymentRequired == true ? autoRenewalConfigData.ppeIncrease : null;
      if(autoRenewalData?.newPremiumText != null) {
        autoRenewalData?.newPremiumText?.title = autoRenewalData?.newPremiumText?.title.replaceAll('%s', prePolicyEditingResponse?.edit?.deltaPremium?.paymentFrequency.equalsIgnoreCase('monthly') == true ? 'monthly' : 'yearly') ?? '';
        autoRenewalData?.newPremiumText?.value = premiumDetails?.totalPremiumDueFormatted ?? '';
      }
      if(autoRenewalData?.remainingBalance != null) {
        autoRenewalData?.remainingBalance?.value = premiumDetails?.toBePaidFormatted ?? '';
      }
      if(autoRenewalData?.validity != null) {
        autoRenewalData?.validity?.value = '${inputData?.mandateMaxYears} years';
      }
    }

    emit(Loaded(
        premiumChange: premiumChange,
        premiumDetails: premiumDetails,
        totalEdits: totalEdits,
        amount: premiumDetails?.footerAmountToBePaid ?? '', isBasbaProposal: prePolicyEditingResponse?.edit?.entityDetails?.basbaProposal, isActiveMandate: prePolicyEditingResponse?.edit?.entityDetails?.isActiveMandate, mandateDetails: prePolicyEditingResponse?.edit?.entityDetails?.mandateDetails));
  }

  callCheckoutApi({InputData? inputData = null}) async {
    emit(FullPageLoading());

    Map<String, dynamic>? requestBody = prePolicyEditingResponse
        ?.toRequestBody(PrePolicyEditNodes.CHECKOUT.value, checkout: true, inputData: inputData);

    HealthJourneyManagerResponse? ppeResponse =
        await _repo.manageJourney(JourneyType.PRE_POLICY_EDIT, requestBody!);
    if (ppeResponse.error == null) {
      prePolicyEditingResponse = ppeResponse;
      nextNode = prePolicyEditingResponse?.getHealthJourneyManagerNode(
          trackingState: PPETrackingStates.CHANGES_SUBMITTED);
      emit(FullPageLoaded());
      await Future.delayed(Duration(milliseconds: 100));
      String? redirectionUrl = prePolicyEditingResponse?.edit?.redirectUrl;
      emit(NavigateToNextNode(redirectionUrl: redirectionUrl));
    } else {
      emit(ErrorToast());
    }
  }

 Future<InputData?> callMultiYearNodeApi(String packageId, String paymentFrequency) async {
    Map<String, dynamic> requestBody = {
      "current_node_id": PrePolicyEditNodes.MULTI_YEAR_MANDATE.value,
      "input_data": {
        "journey": "ppe",
        "package_id": packageId,
        "payment_frequency": paymentFrequency
      }
    };
    Map<String, dynamic> queryParameters = {
      "proposal_id": proposalId
    };
    HealthJourneyManagerResponse? ppeResponse =
    await _repo.manageJourney(JourneyType.PRE_POLICY_EDIT, requestBody, queryParameters: queryParameters);
    return ppeResponse.inputData;
  }

  onTotalEditsTap() {
    PolicyChangesData policyChangesData = PolicyChangesData();

    Map<String, String?> insuredNameMap = HealthJourneyManagerUtils()
        .getInsuredIdNameMap(prePolicyEditingResponse);
    final deltaGrossPremium = prePolicyEditingResponse?.edit?.deltaPremium?.deltaGrossPremium;

    policyChangesData.addChangesFromEditRequest(
        prePolicyEditingResponse?.edit?.editRequest,
        prePolicyEditingResponse?.edit?.documents,
        insuredNameMap,
        prePolicyEditingResponse?.edit?.entityDetails);
    policyChangesData.amountToBePaid(prePolicyEditingResponse?.edit?.deltaPremium?.adhocPayment);

    emit(ShowReviewSheet(
        policyChangesData: policyChangesData, totalEdits: totalEdits));
  }

  PremiumChange mapToPremiumChange(DeltaPremium? deltaPremium) {
    return PremiumChange(
      oldPremium: deltaPremium?.previousInstallment,
      newPremium: deltaPremium?.newInstallment,
      paymentFrequency: deltaPremium?.paymentFrequency,
    );
  }

  PremiumDetails mapToPremiumDetails(DeltaPremium? deltaPremium) {
    return PremiumDetails(
      totalPremiumDue: deltaPremium?.newInstallment,
      premiumAlreadyPaid: deltaPremium?.paidAmount,
      toBePaid: deltaPremium?.adhocPayment,
      dueTime: deltaPremium?.premiumPendingCount,
    );
  }

  triggerTapEvent(HLTrackEvents event) {
    String? groupType =
        prePolicyEditingResponse?.edit?.newValue?.currentPlan?.packageType;
    String? paymentFrequency =
        prePolicyEditingResponse?.edit?.deltaPremium?.paymentFrequency;
    String? communicationEmail = prePolicyEditingResponse
        ?.edit
        ?.newValue
        ?.usersContainer
        ?.usersMap
        .values
        .firstOrNull
        ?.parameters
        .parameterMap['email']
        ?.value;
    String? insuredId = prePolicyEditingResponse
        ?.edit?.newValue?.usersContainer?.usersMap.values.firstOrNull?.userId;

    triggerTapAnalyticalEventsForPPE(
      trackEvent: event,
      proposalId: proposalId,
      platform: Util.getPlatform(),
      product: "health_retail",
      page: "overview:ppe",
      groupType: groupType,
      communicationEmail: communicationEmail,
      communicationPhone: phoneNumber,
      uniqueId: insuredId,
      journey: "pre-policy-edit",
      origin: "SUREOS",
      paymentFrequency: paymentFrequency,
    );
  }
}
