class AutoRenewalConfig {
  final AutoRenewalData? ppeIncrease;
  final AutoRenewalData? ppeDecrease;
  final AutoRenewalData? renewalData;

  AutoRenewalConfig({
    this.ppeIncrease,
    this.ppeDecrease,
    this.renewalData,
  });

  factory AutoRenewalConfig.fromJson(Map<String, dynamic> json) {
    return AutoRenewalConfig(
      ppeIncrease: json['ppe_increase'] != null
          ? AutoRenewalData.fromJson(json['ppe_increase'])
          : null,
      ppeDecrease: json['ppe_decrease'] != null
          ? AutoRenewalData.fromJson(json['ppe_decrease'])
          : null,
      renewalData: json['health_renewals'] != null
          ? AutoRenewalData.fromJson(json['health_renewals'])
          : null,
    );
  }
}

class AutoRenewalData {
  final String? iconUrl;
  final String? title;
  final String? subtitle;
  final InfoItem? oldPremiumText;
  final InfoItem? newPremiumText;
  final InfoItem? remainingBalance;
  final InfoItem? validity;
  final InfoItem? nextPaymentDate;
  final List<BenefitItem>? benefits;
  final String? primaryCta;
  final String? secondaryCta;

  AutoRenewalData({
    this.iconUrl,
    this.title,
    this.subtitle,
    this.oldPremiumText,
    this.newPremiumText,
    this.remainingBalance,
    this.validity,
    this.nextPaymentDate,
    this.benefits,
    this.primaryCta,
    this.secondaryCta
  });

  factory AutoRenewalData.fromJson(Map<String, dynamic> json) {
    return AutoRenewalData(
      iconUrl: json['iconUrl'] ?? '',
      title: json['title'] ?? '',
      subtitle: json['subtitle'] ?? '',
      oldPremiumText: json['old_premium_text'] != null
          ? InfoItem.fromJson(json['old_premium_text'])
          : null,
      newPremiumText: json['new_premium_text'] != null
          ? InfoItem.fromJson(json['new_premium_text'])
          : null,
      remainingBalance: json['remaining_balance'] != null
          ? InfoItem.fromJson(json['remaining_balance'])
          : null,
      validity: json['validity'] != null
          ? InfoItem.fromJson(json['validity'])
          : null,
      nextPaymentDate: json['next_payment_date'] != null
          ? InfoItem.fromJson(json['next_payment_date'])
          : null,
      benefits: (json['benefits'] as List<dynamic>? ?? [])
          .map((e) => BenefitItem.fromJson(e))
          .toList(),
      primaryCta: json['primaryCta'] ?? '',
      secondaryCta: json['secondaryCta'] ?? '',
    );
  }
}

class InfoItem {
  String title;
  String value;

  InfoItem({
    required this.title,
    required this.value,
  });

  factory InfoItem.fromJson(Map<String, dynamic> json) {
    return InfoItem(
      title: json['title'] ?? '',
      value: json['value'] ?? '',
    );
  }
}

class BenefitItem {
  final String icon;
  final String title;
  final String description;

  BenefitItem({
    required this.icon,
    required this.title,
    required this.description,
  });

  factory BenefitItem.fromJson(Map<String, dynamic> json) {
    return BenefitItem(
      icon: json['icon'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
    );
  }
}
