import 'package:acko_flutter/util/health/utils.dart';
import 'package:utilities/core/string_extensions.dart';

class PremiumChange {
  final double? oldPremium;
  final double? newPremium;
  final String? paymentFrequency;

  const PremiumChange({
    this.oldPremium,
    this.newPremium,
    this.paymentFrequency,
  });

  String get oldPremiumFormatted => _formatOldPremium(withFrequency: true);

  String get oldPremiumFormattedWithoutFrequency => _formatOldPremium(withFrequency: false);

  String _formatOldPremium({required bool withFrequency}) {
    if (oldPremium == null) return '';
    final formattedAmount = '₹${NumberUtils.commaSeparatedNumber(oldPremium!.toInt())}';
    if (!withFrequency) return formattedAmount;
    final frequency = paymentFrequency.equalsIgnoreCase('Yearly') ? 'yr' : 'mo';
    return '$formattedAmount/$frequency';
  }

  String get newPremiumFormatted {
    if (newPremium == null) return '';
    String frequency =
        paymentFrequency.equalsIgnoreCase('Yearly') ? 'yr' : 'mo';
    return '₹${NumberUtils.commaSeparatedNumber(newPremium!.toInt())}/$frequency';
  }
}

class PremiumDetails {
  final double? totalPremiumDue;
  final double? premiumAlreadyPaid;
  final double? toBePaid;
  final int? dueTime;

  const PremiumDetails({
    this.totalPremiumDue,
    this.premiumAlreadyPaid,
    this.toBePaid,
    this.dueTime,
  });

  String get totalPremiumDueFormatted {
    return totalPremiumDue != null
        ? '₹${NumberUtils.commaSeparatedNumber(totalPremiumDue!.toInt())}'
        : '';
  }

  String get premiumAlreadyPaidFormatted {
    return premiumAlreadyPaid != null
        ? '₹${NumberUtils.commaSeparatedNumber(premiumAlreadyPaid!.toInt())}'
        : '';
  }

  String get toBePaidFormatted {
    return toBePaid != null
        ? '₹${NumberUtils.commaSeparatedNumber(toBePaid!.toInt().abs())}'
        : '';
  }

  String get footerAmountToBePaid {
    if (toBePaid == null) {
      return '';
    }

    return '₹${NumberUtils.commaSeparatedNumber(toBePaid!.toInt().abs())}';
  }

  String getCheckoutCtaText(bool isBasbaProposal, bool isActiveMandate) {
    if (isRefundRequired) {
      if (isBasbaProposal) {
        return !isActiveMandate ? "Set up mandate" : "Submit";
      } else {
        return "Submit";
      }
    } else if (isNonFinancial) {
      return "Submit";
    } else {
      if (isBasbaProposal) {
        return !isActiveMandate ? "Set up mandate" : "Continue";
      } else {
        return "Proceed to pay";
      }
    }

    /// normal logic
    // ((premiumDetails?.isNonFinancial ?? true) ||
    //       (premiumDetails?.isRefundRequired ?? false))
    //   ? "Submit"
    //   : "Proceed to pay",

  }

  String getFooterTitle(int totalEdits, bool isBasbaProposal) {
    if (toBePaid == null || toBePaid?.toInt() == 0) {
      return '';
    }
    if ((toBePaid?.toInt() ?? 0) < 0) {
      return isBasbaProposal ? 'Release amount' : 'Refund amount';
    }
    final premiumIncreaseText = 'Additional premium for ' + (totalEdits > 1 ? 'edits' : 'edit');
    return premiumIncreaseText;
  }

  bool get isPaymentRequired {
    return toBePaid != null && toBePaid! > 0;
  }

  bool get isRefundRequired {
    return toBePaid != null && toBePaid! < 0;
  }

  bool get isNonFinancial {
    return toBePaid != null && toBePaid! == 0;
  }
}
