import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/health_life/page_events/health_life_page_events.dart';
import 'package:analytics/events/health_life/tap_events/health_life_tap_events.dart';

/// proposal_id - ✅ proposal_id
/// platform - ✅ android / iOS
/// product - ✅ health_retail
/// page - ✅ current node id
/// group_type - in entity_details
/// communication_email - ✅ in entity_details
/// communication_phone - ✅ yes
/// unique_id - ✅ proposal_id
/// phone - ❌ ignore
/// origin - ✅ sureOs
/// journey - 🚩pre-policy-edits (new)

mixin HealthJourneyManagerInstrumentationMixin {
  /// pre policy edits tap event handler
  void triggerTapAnalyticalEventsForPPE({
    required HLTrackEvents trackEvent,
    required String? proposalId,
    required String? platform,
    required String? product,
    required String? page,
    required String? groupType,
    required String? communicationEmail,
    required String? communicationPhone,
    required String? uniqueId,
    required String? journey,
    // required String? phone,
    required String? origin,
    String? paymentFrequency,
    Map<String, dynamic>? additionalProperties,
  }) {
    final properties = {
      "proposal_id": proposalId,
      "platform": platform,
      "product": product,
      "page": page,
      "group_type": groupType,
      "communication_email": communicationEmail,
      "communication_phone": communicationPhone,
      "unique_id": uniqueId,
      "journey": journey,
      "origin": origin,
      "payment_frequency": paymentFrequency,
      if (additionalProperties != null) ...additionalProperties,
    };

    AnalyticsTrackerManager.instance
        .sendEvent(event: trackEvent, properties: properties);
  }

  /// pre policy edits page event handler
  void triggerPageAnalyticalEventsForPPE({
    required HLPageEvents pageEvent,
    required String? proposalId,
    required String? platform,
    required String? product,
    required String? page,
    required String? groupType,
    required String? communicationEmail,
    required String? communicationPhone,
    required String? uniqueId,
    required String? journey,
    // required String? phone,
    required String? origin,
    String? paymentFrequency,
    Map<String, dynamic>? additionalProperties,
  }) {
    final properties = {
      "proposal_id": proposalId,
      "platform": platform,
      "product": product,
      "page": page,
      "group_type": groupType,
      "communication_email": communicationEmail,
      "communication_phone": communicationPhone,
      "unique_id": uniqueId,
      "journey": journey,
      // "phone": phone,
      "origin": origin,
      "payment_frequency": paymentFrequency,
      if (additionalProperties != null) ...additionalProperties,
    };

    AnalyticsTrackerManager.instance
        .sendEvent(event: pageEvent, properties: properties);
  }

  /// you can write event handlers for other journeys as well.
}
