import 'package:acko_flutter/common/view/ErrorWidget.dart';
import 'package:acko_flutter/feature/vas-sdui/bloc/vas_cubit.dart';
import 'package:acko_flutter/feature/challan_sdui/widgets/challan_content_section.dart';
import 'package:acko_flutter/feature/vas-sdui/util/util.dart';
import 'package:acko_flutter/network/ApiResponse.dart';
import 'package:analytics/analytics.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/utilities.dart';

import 'challan_custom_loader_view.dart';
import 'cubit/challan_cubit.dart';
import '../vas-sdui/gradient_header_scaffold.dart';
import 'model/challan_layout_data.dart';
import 'challan_multi_asset_view.dart';
import 'challan_single_asset_view.dart';
import '../vas-sdui/view/widgets/vas_loading_view.dart';

class ChallanJourney extends StatefulWidget {
  final String? flowType;
  const ChallanJourney({super.key, this.flowType});

  @override
  State<ChallanJourney> createState() => _ChallanJourneyState();
}

class _ChallanJourneyState extends State<ChallanJourney>
    implements StateListener, ErrorViewRetryCallBack {
  late ChallanCubit cubit;
  Map<int, String> indexToTabMap = {};
  StateProvider _stateProvider = StateProvider();

  @override
  void initState() {
    cubit = context.read<SduiParentCubit>() as ChallanCubit;
    _stateProvider.subscribe(this);
    if (widget.flowType == 'monthly_report') {
      AnalyticsTrackerManager.instance.sendEvent(
        event: PageLoadedConstants.APP_VIEW_PAGE,
        properties: {'page_name': "monthly report unlocked"},
      );
    }
    super.initState();
  }

  @override
  dispose() {
    super.dispose();
    _stateProvider.dispose(this);
  }

  @override
  Widget build(BuildContext context) {
    return GradientHeaderScaffold(
      onLeadingIconTapped: () {
        Navigator.pop(context);
      },
      headerExtent: 150,
      body: BlocConsumer<ChallanCubit, SduiParentState>(
        bloc: cubit,
        listener: (prev, state) {
          if (state is ChallanRunInitActions) {
            if (!mounted) return;
            state.actions.forEach(
              (action) => action.executeAction(context, null),
            );
          } else if (state is VasJourneyLoaded) {
            if (cubit.registrationNumber != null) {
              /// Refresh asset so that new vehicles added load.
              _stateProvider.notify(ObserverState.REFRESH_ASSETS);
              _stateProvider.notify(
                ObserverState.UPDATE_FROM_PARENT_JSON,
                data: {'usecase': 'CHALLAN_ADD_ASSET'},
              );
            }

            cubit.parseJson(showLoader: true);
          } else if (state is UpdateFromParentJson) {
            _stateProvider.notify(
              ObserverState.UPDATE_FROM_PARENT_JSON,
              data: {'usecase': 'CHALLAN_REFRESH', 'sduiJson': cubit.sduiJson},
            );

            cubit.parseJson();
          }
        },
        buildWhen: (previous, current) => current is! UpdateFromParentState,
        builder: (context, state) {
          if (state is ChallanLoading) {
            final text = getChallanLoaderText(
              widget.flowType,
            );
            return VasLoadingScreen(
              text: text,
              pageName: 'challan details loader',
            );
          } else if (state is ChallanLoader) {
            return ChallanCustomLoaderView(loaderLayout: state.data);
          } else if (state is ChallanDataLoaded) {
            if (state.data.type == ChallanAssetViewType.multiAssetView) {
              return ChallanMultiAssetView(
                onTabSwtich: (index) {
                  String? assetNumber = indexToTabMap[index];
                  if (assetNumber != null) {
                    cubit.updateVehicleChallanState(assetNumber);
                  }
                  cubit.changeDefaultTabIndex(index);
                },
                tabChildren: getTabContent(state.data),
                tabs: getTabbarHeaders(state.data),
                title: state.data.headerConfig!.title!,
                defaultIndex: cubit.getDefaultTabIndex() ??
                    state.data.headerConfig!.defaultIndex!,
              );
            } else if (state.data.type ==
                ChallanAssetViewType.singleAssetView) {
              return ChallanSingleAssetView(
                content: getTabContent(state.data).first,
                header: getTabbarHeaders(state.data).first,
                footerConfig: state.data.footerConfig!,
                stateKey: state.data.body!.entries.first.key,
              );
            } else {
              return SizedBox.shrink();
            }
          } else if (state is ChallanError) {
            return ErrorPage(
              retryCallbackFunc: this,
              errorHandler: ErrorHandler(state.error),
            );
          } else {
            return SizedBox.shrink();
          }
        },
      ),
    );
  }

  @override
  onRetry() {
    cubit.fetchJourneyData();
  }

  List<Widget> getTabbarHeaders(ChallanLayoutData data) {
    if (data.body == null) return [];

    List<Widget> headers = [];
    indexToTabMap.clear();
    int index = 0;
    data.body?.forEach((key, value) {
      if (value.header != null) {
        headers.add(value.header!);
        indexToTabMap[index] = key;
        index++;
      }
    });
    return headers;
  }

  List<Widget> getTabContent(ChallanLayoutData data) {
    if (data.body == null) return [];

    List<Widget> sections = [];
    data.body?.forEach((assetNumber, value) {
      if (value.content != null) {
        sections.add(
          ChallanContentSection(
            stateKey: assetNumber,
            child: value.content!,
            footerBanner: value.footerBanner,
            footerConfig: data.footerConfig!,
          ),
        );
      }
    });
    return sections;
  }

  @override
  void onStateChanged(ObserverState state, {data}) {
    if (state == ObserverState.CHALLAN_RELOAD) {
      if (data != null)
        cubit.updateVehicleChallanState(data, forceRefresh: true);
    }
  }
}
