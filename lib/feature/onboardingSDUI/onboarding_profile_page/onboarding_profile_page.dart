import 'package:acko_flutter/common/util/AckoTextStyle.dart';
import 'package:acko_flutter/feature/onboardingSDUI/onboarding_mixins/section_type_mixin.dart';
import 'package:acko_flutter/feature/onboardingSDUI/onboarding_model.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:analytics/events/tap_events.dart';
import 'package:design_module/design_module.dart';
import 'package:design_module/utilities/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:session_manager_module/StorageManager/shared_preferences_storage.dart';
import 'package:utilities/core/base_model.dart';

import '../../../common/util/PreferenceHelper.dart';
import '../../../common/util/strings.dart' as strings;
import '../../home_view_util.dart';
import '../initialPage/onboarding_inital_page_cubit.dart';
import 'onboarding_profile_cubit.dart';
import 'onboarding_profile_state.dart';

class OnboardingProfilePage extends StatefulWidget {
  final Pages page;
  final String? firstName;
  final String? lastName;

  const OnboardingProfilePage({
    Key? key,
    required this.page,
    this.firstName,
    this.lastName,
  }) : super(key: key);

  @override
  State<OnboardingProfilePage> createState() => _OnboardingProfilePageState();
}

class _OnboardingProfilePageState extends State<OnboardingProfilePage>
    with SectionWidgetType {
  List<TextEditingController> _textControllers = [];
  List<FocusNode> _focusNodes = [];

  final RegExp nameRegExp = RegExp(r"^[a-zA-Z\s]+$");
  List<String> previousValues = ["", ""];
  bool showLoader = false;
  bool pageLoaded = false;
  bool canSubmit = false;
  String? phoneNumber;

  @override
  void initState() {
    super.initState();
    fetchPhoneNumber();
    _initializeControllers();

    AnalyticsTrackerManager.instance.sendEvent(
        event: PageLoadedConstants.APP_VIEW_PAGE,
        properties: {
          'platform': Util.getPlatform(),
          'page_name': 'app_onboarding_screen_name'
        });
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      setState(() {
        pageLoaded = true;
      });
    });
  }

  void _initializeControllers() {
    _textControllers.clear();
    _focusNodes.clear();
    for (var section in widget.page.sections!) {
      for (var component in section.components!) {
        String? initialValue = '';
        if (component.type == 'input_text_field') {
          if (component.id == 'fname') {
            initialValue = widget.firstName;
            previousValues[0] = widget.firstName ?? "";
          } else if (component.id == 'lname') {
            initialValue = widget.lastName;
            previousValues[1] = widget.lastName ?? "";
          }

          _textControllers.add(TextEditingController(text: initialValue));
          FocusNode focusNode = FocusNode();
          _focusNodes.add(focusNode);
        }
      }
    }

    _validateCaptureValues();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedOpacity(
      duration: Duration(milliseconds: 700),
      opacity: pageLoaded ? 1.0 : 0,
      child: Stack(
        children: [
          Container(
            height: getScreenAwareHeight(300),
            decoration: BoxDecoration(
                image: DecorationImage(
                    image: AssetImage(
                        '${assetImage}ic_onboarding_gradient_bg_testpng.png'),
                    fit: BoxFit.fill)),
          ),
          Scaffold(
            backgroundColor: Colors.transparent,
            appBar: AppBar(
              backgroundColor: Colors.transparent,
              leading: IconButton(
                icon: Icon(
                  Icons.arrow_back_ios,
                  color: color121212,
                  size: 20,
                ),
                onPressed: () {
                  Util.exitApp();
                },
              ),
            ),
            bottomNavigationBar: BottomAppBar(
              color: Colors.white,
              surfaceTintColor: colorFFFFFF,
              elevation: 12,
              shadowColor: Colors.black,
              child: BlocConsumer<OnboardingProfilePageCubit,
                  OnBoardingProfilePageState>(
                listener: (BuildContext context, state) {},
                buildWhen: (_, state) =>
                    (state is OnBoardingInputValidationFailed ||
                        state is OnBoardingInputValidationSucceeded),
                builder: (context, state) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 4.0),
                    child: GestureDetector(
                      onTap: () {
                        if (showLoader || !canSubmit) {
                          if (!canSubmit) {
                            _showNameError();
                          }
                          return;
                        }
                        _submitValue();
                      },
                      child: AnimatedContainer(
                          duration: Duration(milliseconds: 100),
                          width: MediaQuery.of(context).size.width,
                          decoration: BoxDecoration(
                              color:
                                  (state is OnBoardingInputValidationSucceeded)
                                      ? color121212
                                      : colorE0E0E8,
                              borderRadius: BorderRadius.circular(12)),
                          child: Center(
                            child: showLoader
                                ? SizedBox(
                                    height: 24,
                                    width: 24,
                                    child: CircularProgressIndicator(
                                      color: Colors.white,
                                    ),
                                  )
                                : Text(
                                    'Continue',
                                    style: TextStyle(
                                        fontWeight: FontWeight.w600,
                                        fontSize: 16.0,
                                        color: Colors.white,
                                        fontFamily: 'euclid_circularB_medium'),
                                  ),
                          )),
                    ),
                  );
                },
              ),
            ),
            body: BlocConsumer<OnboardingProfilePageCubit,
                    OnBoardingProfilePageState>(
                buildWhen: (_, state) =>
                    !(state is OnBoardingInputValidationFailed ||
                        state is OnBoardingInputValidationSucceeded),
                listener: (_, state) {
                  if (state is OnBoardingInputSubmitFailed ||
                      state is OnBoardingInputSubmittedSuccessfully) {
                    setState(() {
                      showLoader = false;
                    });
                  } else if (state is OnboardingInputSubmitting) {
                    setState(() {
                      showLoader = true;
                    });
                  }
                },
                builder: (context, state) {
                  return SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(top: 16),
                            child:
                                hMediumText.text(getTitle(widget.page.title)),
                          ),
                          (widget.page.description.isNotNullAndEmpty)
                              ? Padding(
                                  padding: const EdgeInsets.only(
                                      bottom: 32.0, top: 8),
                                  child: pSmallText.text(
                                      getTitle(widget.page.description),
                                      textColor: color4B4B4B),
                                )
                              : SizedBox(
                                  height: 32,
                                ),
                          ...widget.page.sections!.map((section) {
                            List<Widget> children = [];

                            if (section.title != null)
                              children.add(Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 15.0),
                                child:
                                    hXSmallText.text(getTitle(section.title!)),
                              ));

                            if (section.type == 'input')
                              children.addAll(
                                section.components!.asMap().keys.toList().map(
                                  (index) {
                                    if (section.components![index].type ==
                                        'input_text_field') {
                                      return getInputTextfield(
                                        index,
                                        section.components![index],
                                      );
                                    } else if (section.components![index] ==
                                        'Checkbox') {
                                      return getCheckBoxField(section);
                                    } else if (section.components![index] ==
                                        'selector') {
                                      return getSelectorField(section);
                                    } else {
                                      return SizedBox.shrink();
                                    }
                                  },
                                ),
                              );

                            return Column(
                              children: children,
                            );
                          }),
                        ],
                      ),
                    ),
                  );
                }),
          ),
        ],
      ),
    );
  }

  String getTitle(String? title) {
    if (title.isNullOrEmpty) {
      return '';
    }
    return title!;
  }

  Widget getInputTextfield(int textFieldIndex, Components component) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20.0),
      child: TextFormField(
        controller: _textControllers[textFieldIndex],
        cursorColor: color4B4B4B,
        focusNode: _focusNodes[textFieldIndex],
        textInputAction: TextInputAction.done,
        textAlignVertical: TextAlignVertical.center,
        maxLines: 1,
        keyboardType: TextInputType.text,
        enableSuggestions: false,
        autocorrect: false,
        style: TextStyleInterRegular(color: color121212, fontSize: 16.0),
        decoration: InputDecoration(
          floatingLabelBehavior: FloatingLabelBehavior.never,
          fillColor: Colors.white,
          filled: true,
          hintText: component.placeholderText,
          hintStyle: TextStyle(
            fontSize: 16,
            color: color4B4B4B.withOpacity(0.7),
            fontFamily: 'euclid_circularB_regular',
          ),
          contentPadding: EdgeInsets.only(left: 16.0, top: 12, bottom: 12),
          errorStyle: TextStyleInterMediumL1(color: colorD83D37),
          border: AckoInputBorderStyle.border(),
          errorBorder: AckoInputBorderStyle.errorBorder(),
          enabledBorder: AckoInputBorderStyle.enabledBorder(),
          focusedBorder: AckoInputBorderStyle.focusedBorder(),
          counter: Offstage(),
        ),
        onChanged: (String val) {
          if (!nameRegExp.hasMatch(val) && val.isNotEmpty) {
            _textControllers[textFieldIndex].value = TextEditingValue(
              text: previousValues[textFieldIndex],
              selection: TextSelection.collapsed(
                  offset: previousValues[textFieldIndex].length),
            );
            return;
          }
          previousValues[textFieldIndex] = val;

          _validateCaptureValues();
        },
      ),
    );
  }

  fetchPhoneNumber() async{
    phoneNumber = await getStringPrefs(StringDataSharedPreferenceKeys.MOBILE_NUMBER);
  }

  Future<void> _submitValue() async {
    AnalyticsTrackerManager.instance
        .sendEvent(event: TapConstants.TAP_BTN_INTERACTION, properties: {
      "from_page": "onboarding_name",
      "type": "button",
      "cta_text": "continue",
      "phone": phoneNumber,
      "product": "onboarding",
      "journey": "onboarding_name",
      'platform': Util.getPlatform(),
    });

    final values = _validateCaptureValues();

    if (values.isNotEmpty) {
      await context.read<OnboardingProfilePageCubit>().submitOnboardingInput(
            widget.page.ctaEndpoint!,
            values,
          );
      context
          .read<OnboardingInitialPageCubit>()
          .handleOnboardingNavigation(args: {
        'firstName': context.read<OnboardingProfilePageCubit>().firstName ?? ''
      });
    } else {
      _showNameError();
    }
  }

  void _showNameError() {
    if (FocusScope.of(context).hasFocus) {
      FocusScope.of(context).unfocus();
    }
    HomeViewUtil.sharedInstance.showSnackBar(
        strings.name_can_only_have_characters, context,
        secondDuration: 2, image: 'assets/images/ic_red_cross.svg');
  }

  String _validateCaptureValues() {
    String capturedValues = '';
    var page = widget.page;
    var enteredAllInput = false;

    for (var section in page.sections!) {
      if (section.type == "Checkbox" || section.type == "selector") {
        if (section.capturedValues.isEmpty) {
          capturedValues = '';
          enteredAllInput = false;
          break;
        }
        enteredAllInput = true;
        for (var id in section.capturedValues) {
          capturedValues += '&${id}=true';
        }
      } else {
        for (int componentIndex = 0;
            componentIndex < section.components!.length;
            componentIndex++) {
          if (section.components![componentIndex].type == 'input_text_field') {
            String componentId = section.components![componentIndex].id ?? '';

            if (_textControllers[componentIndex].text.isEmpty ||
                !nameRegExp.hasMatch(_textControllers[componentIndex].text)) {
              capturedValues = '';
              enteredAllInput = false;
              break;
            }
            capturedValues +=
                '&${componentId}=${_textControllers[componentIndex].text.trim()}';
          }

          enteredAllInput = true;
        }
      }
    }

    if (canSubmit != enteredAllInput) {
      setState(() {
        canSubmit = enteredAllInput;
      });
    }

    if (enteredAllInput) {
      context.read<OnboardingProfilePageCubit>().validationSucceeded();
    } else {
      context.read<OnboardingProfilePageCubit>().validationFailed();
    }

    return capturedValues;
  }
}
