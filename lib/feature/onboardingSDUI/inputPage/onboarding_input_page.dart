import 'package:acko_flutter/common/util/PreferenceHelper.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/card_loaded_events.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:analytics/events/tap_events.dart';
import 'package:design_module/design_module.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:session_manager_module/StorageManager/shared_preferences_storage.dart';
import 'package:utilities/constants/constants.dart';

import '../../../common/util/AckoTextStyle.dart';
import '../../../common/util/color_constants.dart';
import '../../../common/util/strings.dart';
import '../../../common/view/AckoText.dart';
import '../../../util/Utility.dart';
import '../../home_view_util.dart';
import '../checkbox/checkbox.dart';
import '../inputPage/progress_indicator.dart';
import '../onboarding_model.dart';
import 'onboarding_input_page_cubit.dart';
import 'onboarding_input_page_state.dart';

class OnBoardingInputPage extends StatefulWidget {
  final OnboardingContentModel onboardingContent;

  OnBoardingInputPage({required this.onboardingContent});

  @override
  _OnBoardingInputPageState createState() => _OnBoardingInputPageState();
}

class _OnBoardingInputPageState extends State<OnBoardingInputPage> {
  int _currentPage = 0;
  bool showLoader = false;
  int _progressBarIndex = 0;
  List<List<TextEditingController>> _textControllers = [];
  List<List<FocusNode>> _focusNodes = [];
  OnboardingInputPageCubit? onboardingInputPageCubit;
  final PageController _pageController = PageController(initialPage: 0);
  bool _inputValidation = false;
  String? phoneNumber;

  @override
  void initState() {
    super.initState();
    onboardingInputPageCubit =
        BlocProvider.of<OnboardingInputPageCubit>(context);
    _initializeControllers();
    fetchPhoneNumber();
  }

  fetchPhoneNumber() async{
    phoneNumber = await getStringPrefs(StringDataSharedPreferenceKeys.MOBILE_NUMBER);
  }

  void _initializeControllers() {
    _textControllers.clear();
    _focusNodes.clear();

    for (var pageIndex = 0;
        pageIndex < widget.onboardingContent.pages!.length;
        pageIndex++) {
      var page = widget.onboardingContent.pages![pageIndex];
      List<TextEditingController> controllers = [];
      List<FocusNode> focusNodes = [];
      for (var section in page.sections!) {
        for (var component in section.components!) {
          if (component.type == 'input_text_field') {
            controllers.add(TextEditingController());
            FocusNode focusNode = FocusNode();
            focusNodes.add(focusNode);
          }
        }
      }
      _textControllers.add(controllers);
      _focusNodes.add(focusNodes);
    }
  }

  void captureCheckboxEventsBasedOnPage(
      String pageCTAEndpoint, List<String> capturedValues) {
    if (pageCTAEndpoint == Urls.pagesOnboardingMaritalStatus) {
      AnalyticsTrackerManager.instance
          .sendEvent(event: TapConstants.TAP_BTN_INTERACTION, properties: {
        "from_page": "onboarding_marital_status",
        "type": "button",
        "cta_text": "continue",
        "product": "onboarding",
        "phone": phoneNumber,
        "journey": "onboarding_marital_status",
        "rating_answer": capturedValues,
        'platform': Util.getPlatform(),
      });
    } else if (pageCTAEndpoint == Urls.pagesOnboardingInsuranceCoverage) {
      AnalyticsTrackerManager.instance
          .sendEvent(event: TapConstants.TAP_BTN_INTERACTION, properties: {
        "from_page": "onboarding_coverages",
        "type": "button",
        "cta_text": "continue",
        "product": "onboarding",
        "journey": "onboarding_coverages",
        "phone": phoneNumber,
        "rating_answer": capturedValues,
        'platform': Util.getPlatform(),
      });
    } else if (pageCTAEndpoint == Urls.pagesOnboardingFlights) {
      AnalyticsTrackerManager.instance
          .sendEvent(event: TapConstants.TAP_BTN_INTERACTION, properties: {
        "from_page": "onboarding_flights",
        "type": "button",
        "cta_text": "continue",
        "product": "onboarding",
        "journey": "onboarding_flights",
        "phone": phoneNumber,
        "rating_answer": capturedValues,
        'platform': Util.getPlatform(),
      });
    }
  }

  Future<void> _captureValues() async {
    String capturedValues = '';
    var page = widget.onboardingContent.pages![_currentPage];
    int textFieldIndex = 0;
    var enteredAllInput = false;
    List<String> allCheckBoxCapturedValues = [];
    for (var section in page.sections!) {
      if (section.type == "Checkbox" || section.type == "selector") {
        if (section.type == "Checkbox" &&
            section.components != null &&
            section.components!
                    .where((element) => (element.ackoCovered ?? false))
                    .length >
                0) {
          enteredAllInput = true;
          section.components!
              .where((element) => (element.ackoCovered ?? false))
              .toList()
              .forEach((element) {
            capturedValues += '&${element.id}=true';
          });
        }

        if (capturedValues.isEmpty && section.capturedValues.isEmpty) {
          enteredAllInput = false;
          break;
        }
        enteredAllInput = true;
        allCheckBoxCapturedValues.addAll(section.capturedValues);

        for (var id in section.capturedValues) {
          capturedValues += '&${id}=true';
        }
      } else {
        // TODO:  This needs tp be a generic event,  we can't assume a textfield indicates only name

        AnalyticsTrackerManager.instance
            .sendEvent(event: TapConstants.TAP_BTN_INTERACTION, properties: {
          "from_page": "onboarding_name",
          "type": "button",
          "cta_text": "continue",
          "phone": phoneNumber,
          "product": "onboarding",
          "journey": "onboarding_name",
          'platform': Util.getPlatform(),
        });

        for (var component in section.components!) {
          if (component.type == 'input_text_field') {
            String componentId = component.id ?? '';
            if (textFieldIndex < _textControllers[_currentPage].length) {
              String textFieldValue =
                  _textControllers[_currentPage][textFieldIndex].text;
              if (textFieldValue.isEmpty) {
                enteredAllInput = false;
                break;
              }

              capturedValues += '&${componentId}=${textFieldValue}';
              textFieldIndex++;
              enteredAllInput = true;
            }
          }
        }
      }
    }

    if (allCheckBoxCapturedValues.isNotEmpty) {
      captureCheckboxEventsBasedOnPage(
          page.ctaEndpoint ?? '', allCheckBoxCapturedValues);
    }

    await submitOnboardingInput(
        page.ctaEndpoint,
        capturedValues,
        (_currentPage == ((widget.onboardingContent.pages?.length ?? 0) - 1)),
        enteredAllInput);
  }

  Future<void> submitOnboardingInput(String? ctaEndpoint, String inputURL,
      bool isFinalPage, bool enteredAllInput) async {
    if (ctaEndpoint.isNullOrEmpty) {
      return;
    }
    await onboardingInputPageCubit?.submitOnboardingInput(
        ctaEndpoint!, inputURL, isFinalPage, enteredAllInput);
  }

  Future<void> _nextPage() async {
    resetValidation();
    await _captureValues();
  }

  resetValidation() {
    setState(() {
      _inputValidation = false;
    });
  }

  void captureScreenLoadEvents(String ctaEndpoint) {
    if (ctaEndpoint == Urls.pagesOnboardingMaritalStatus) {
      AnalyticsTrackerManager.instance
          .sendEvent(event: PageLoadedConstants.APP_VIEW_PAGE, properties: {
        'platform': Util.getPlatform(),
        'page_name': 'app_onboarding_screen_marital_status'
      });
    } else if (ctaEndpoint == Urls.pagesOnboardingInsuranceCoverage) {
      AnalyticsTrackerManager.instance.sendEvent(
          event: PageLoadedConstants.APP_VIEW_PAGE,
          properties: {
            'platform': Util.getPlatform(),
            'page_name': 'app_onboarding_screen_coverages'
          });
    } else if (ctaEndpoint == Urls.pagesOnboardingFlights) {
      AnalyticsTrackerManager.instance.sendEvent(
          event: PageLoadedConstants.APP_VIEW_PAGE,
          properties: {
            'platform': Util.getPlatform(),
            'page_name': 'app_onboarding_screen_flights'
          });
    }
  }

  Widget buildPageContent(int pageIndex) {
    int textFieldIndex = 0;
    var page = widget.onboardingContent.pages![pageIndex];
    captureScreenLoadEvents(page.ctaEndpoint ?? '');
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 32.0),
            child: hMediumText.text(getTitle(page.title)),
          ),
          ...page.sections!.expand((section) {
            return [
              if (section.title != null)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 15.0),
                  child: hXSmallText.text(getTitle(section.title!)),
                ),
              if (section.type == 'input')
                ...section.components!.map((component) {
                  if (component.type == 'input_text_field') {
                    return getInputTextfield(textFieldIndex++, component);
                  } else {
                    return Container();
                  }
                }),
              if (section.type == 'Checkbox') getCheckBoxField(section),
              if (section.type == 'selector') getSelectorField(section)
            ];
          }),
        ],
      ),
    );
  }

  String getTitle(String? title) {
    if (title.isNullOrEmpty) {
      return '';
    }
    return title!.contains('{firstName}')
        ? title.replaceAll(
            '{firstName}', onboardingInputPageCubit!.firstName ?? '')
        : title;
  }

  bool _handleBack() {
    if (_currentPage > 0) {
      setState(() {
        _currentPage--;
        _progressBarIndex--;
        _inputValidation = true;
        _pageController.previousPage(
          duration: Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      });
    } else {
      Navigator.pop(context);
      return true;
    }

    return false;
  }

  Widget _getSkipButton(BuildContext context) {
    return Material(
        color: Colors.transparent,
        child: InkWell(
            highlightColor: colorD760BAE0,
            onTap: () {
              onboardingInputPageCubit?.skipButtonClicked();
            },
            child: TextEuclidMedium14('Skip', textColor: color1C73E8)));
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          height: getScreenAwareHeight(300),
          decoration: BoxDecoration(
              image: DecorationImage(
                  image: AssetImage(
                      '${assetImage}ic_onboarding_gradient_bg_testpng.png'),
                  fit: BoxFit.fill)),
        ),
        WillPopScope(
          onWillPop: () async {
            return Future.value(_handleBack());
          },
          child: Scaffold(
            backgroundColor: Colors.transparent,
            appBar: PreferredSize(
              preferredSize: Size.fromHeight(kToolbarHeight + 16),
              child: AppBar(
                backgroundColor: Colors.transparent,
                leading: IconButton(
                  icon: Icon(
                    Icons.arrow_back_ios,
                    color: color121212,
                    size: 20,
                  ),
                  onPressed: () {
                    _handleBack();
                  },
                ),
                titleSpacing: 0,
                centerTitle: true,
                title: Center(
                  child: Transform.translate(
                    offset: Offset(-24, 0),
                    child: Container(
                      child: ProgressIndicatorWithLabel(
                        currentPage: _progressBarIndex,
                        progressBarWidth:
                            MediaQuery.of(context).size.width * 0.4,
                        totalPages: widget.onboardingContent.pages!.length,
                      ),
                    ),
                  ),
                ),
                automaticallyImplyLeading: false,
                actions: [
                  if (widget.onboardingContent.showSkipCTA ?? false)
                    Padding(
                      padding: const EdgeInsets.only(right: 15.0),
                      child: Center(
                        child: GestureDetector(
                            onTap: () {
                              onboardingInputPageCubit?.skipButtonClicked();
                            },
                            child: TextEuclidSemiBold16(skip)),
                      ),
                    ),
                ],
              ),
            ),
            body: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              child: BlocConsumer<OnboardingInputPageCubit,
                  OnBoardingInputPageState>(
                listener: (context, state) async {
                  if (state is OnBoardingInputSubmittedSuccessfully) {
                    updateLoader(false);
                    setState(() {
                      if (_currentPage <
                          widget.onboardingContent.pages!.length - 1) {
                        _progressBarIndex++;
                        _pageController.nextPage(
                          duration: Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                        Future.delayed(Duration(milliseconds: 300), () {
                          checkInputValidation();
                        });
                      }
                    });
                  } else if (state is OnBoardingJourneyCompleted) {
                    updateLoader(false);
                    Navigator.pushNamed(
                      context,
                      Routes.ONBOARDING_PERSONALISING_PAGE,
                      arguments: {
                        "onBoardingUserInfo":
                            onboardingInputPageCubit?.onBoardingUserInfo
                      },
                    );
                  } else if (state is OnBoardingInputSubmitFailed) {
                    updateLoader(false);
                    // FullPageLoader.instance.dismissFullPageLoader(context);
                    HomeViewUtil.sharedInstance.showSnackBar(
                        somethingWentWrong, context,
                        image: 'assets/images/ic_red_cross.svg');
                  } else if (state is OnBoardingInputNotEntered) {
                    updateLoader(false);
                    // FullPageLoader.instance.dismissFullPageLoader(context);
                    HomeViewUtil.sharedInstance.showSnackBar(
                        please_answer_all_questions_to_proceed, context,
                        secondDuration: 2,
                        image: 'assets/images/ic_red_cross.svg');
                  } else if (state is OnboardingInputSubmitting) {
                    updateLoader(true);
                  } else if (state is OnboardingInputSkipClicked) {
                    onboardingInputPageCubit
                        ?.navigateUserAfterOnboardingFlow(context);
                  }
                },
                builder: (context, state) {
                  return PageView.builder(
                    physics: NeverScrollableScrollPhysics(),
                    controller: _pageController,
                    onPageChanged: (index) {
                      _currentPage = index;
                    },
                    itemCount: widget.onboardingContent.pages!.length,
                    itemBuilder: (context, index) {
                      return AnimatedBuilder(
                        animation: _pageController,
                        builder: (context, child) {
                          double value = 1.0;
                          if (_pageController.position.haveDimensions) {
                            value = _pageController.page! - index;
                            value = (1 - (value.abs() * 0.3)).clamp(0.0, 1.0);
                          }
                          return AnimatedOpacity(
                            opacity: value < 0.9 ? 0 : 1,
                            duration: Duration(milliseconds: 300),
                            child: child,
                          );
                        },
                        child: buildPageContent(index),
                      );
                    },
                  );
                },
              ),
            ),
            bottomNavigationBar: BottomAppBar(
              color: Colors.white,
              surfaceTintColor: Colors.white,
              shadowColor: Colors.black,
              elevation: 12,
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 4.0),
                child: GestureDetector(
                  onTap: () {
                    if (showLoader) return;
                    _nextPage();
                  },
                  child: AnimatedContainer(
                      duration: Duration(milliseconds: 200),
                      width: MediaQuery.of(context).size.width,
                      decoration: BoxDecoration(
                          color: (_inputValidation) ? color121212 : colorE0E0E8,
                          borderRadius: BorderRadius.circular(12)),
                      child: Center(
                        child: showLoader
                            ? SizedBox(
                                height: 24,
                                width: 24,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                ),
                              )
                            : Text(
                                'Continue',
                                style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    fontFamily: 'euclid_circularB_medium',
                                    fontSize: 16.0,
                                    color: Colors.white),
                              ),
                      )),
                ),
              ),
            ),
          ),
        )
      ],
    );
  }

  updateLoader(bool value) {
    setState(() {
      showLoader = value;
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Widget getInputTextfield(int textFieldIndex, Components component) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 15.0),
      child: TextFormField(
        controller: _textControllers[_currentPage][textFieldIndex],
        cursorColor: color4B4B4B,
        focusNode: _focusNodes[_currentPage][textFieldIndex],
        textInputAction: TextInputAction.done,
        textAlignVertical: TextAlignVertical.center,
        maxLines: 1,
        keyboardType: TextInputType.text,
        enableSuggestions: false,
        autocorrect: false,
        style: TextStyleInterRegular(color: color434F5A, fontSize: 16.0),
        decoration: InputDecoration(
          floatingLabelBehavior: FloatingLabelBehavior.never,
          fillColor: Colors.white,
          filled: true,
          contentPadding: EdgeInsets.only(left: 20.0, top: 10, bottom: 10),
          errorStyle: TextStyleInterMediumL1(color: colorD83D37),
          labelText: component.placeholderText,
          labelStyle: TextStyle(
            fontSize: 16,
            color: color4B4B4B,
            fontFamily: 'euclid_circularB_regular',
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(10.0)),
            borderSide: BorderSide(
              color: colorE8E8E8,
              width: 1.0,
            ),
          ),
          counter: Offstage(),
        ),
        onChanged: (String val) {},
      ),
    );
  }

  Widget getCheckBoxField(Sections section) {
    List<CheckboxDisplayItem> checkBoxDisplayItems = [];

    for (var element in section.components!) {
      var checkboxDisplayItem = CheckboxDisplayItem(
        displayCheckBox: true,
        displayText: element.placeholderText,
        id: element.id,
        autoSelected: false,
        coveredByAcko: element.ackoCovered,
      );
      checkBoxDisplayItems.add(checkboxDisplayItem);
    }

    return CheckBox(
      options: checkBoxDisplayItems,
      allowMultiSelection: section.allowMultiSelect ?? false,
      addCheckMark: true,
      onSelectionChanged: (selectedOptions) {
        if (selectedOptions.isNotNullOrEmpty &&
            selectedOptions.first == 'none_of_above') {
          section.capturedValues = selectedOptions.toSet();
          checkInputValidation();
          return;
        } else if (section.capturedValues.contains('none_of_above')) {
          section.capturedValues.remove('none_of_above');
        }

        if (section.allowMultiSelect ?? false) {
          section.capturedValues.clear();
          section.capturedValues.addAll(selectedOptions);
        } else {
          section.capturedValues = selectedOptions.toSet();
        }
        checkInputValidation();
      },
    );
  }

  checkInputValidation() {
    String capturedValues = '';
    var page = widget.onboardingContent.pages![_currentPage];
    int textFieldIndex = 0;
    var enteredAllInput = false;
    for (var section in page.sections!) {
      if (section.type == "Checkbox" || section.type == "selector") {
        if (section.type == "Checkbox" &&
            section.components != null &&
            section.components!
                    .where((element) => (element.ackoCovered ?? false))
                    .length >
                0) {
          enteredAllInput = true;
          break;
        }
        if (section.capturedValues.isEmpty) {
          enteredAllInput = false;
          break;
        }
        enteredAllInput = true;
        for (var id in section.capturedValues) {
          capturedValues += '&${id}=true';
        }
      } else {
        for (var component in section.components!) {
          if (component.type == 'input_text_field') {
            String componentId = component.id ?? '';
            if (textFieldIndex < _textControllers[_currentPage].length) {
              String textFieldValue =
                  _textControllers[_currentPage][textFieldIndex].text;
              if (textFieldValue.isEmpty) {
                enteredAllInput = false;
                break;
              }

              capturedValues += '&${componentId}=${textFieldValue}';
              textFieldIndex++;
              enteredAllInput = true;
            }
          }
        }
      }
    }

    setState(() {
      _inputValidation = enteredAllInput;
    });
  }

  Widget getSelectorField(Sections section) {
    List<CheckboxDisplayItem> checkBoxDisplayItems = [];

    for (var element in section.components!) {
      var checkboxDisplayItem = CheckboxDisplayItem(
        displayCheckBox: false,
        displayText: element.placeholderText,
        id: element.id,
        autoSelected: false,
        coveredByAcko: element.ackoCovered,
      );
      checkBoxDisplayItems.add(checkboxDisplayItem);
    }

    return CheckBox(
      options: checkBoxDisplayItems,
      allowMultiSelection: section.allowMultiSelect ?? false,
      addCheckMark: false,
      onSelectionChanged: (selectedOptions) {
        if (section.allowMultiSelect ?? false) {
          section.capturedValues.addAll(selectedOptions);
        } else {
          section.capturedValues = selectedOptions.toSet();
          _nextPage();
        }
        checkInputValidation();
      },
    );
  }
}
