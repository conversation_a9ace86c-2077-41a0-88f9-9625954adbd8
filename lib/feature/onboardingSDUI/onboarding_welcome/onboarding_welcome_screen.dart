import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:analytics/events/tap_events.dart';
import 'package:design_module/typography/typography.dart';
import 'package:design_module/uikit/widgets/button/acko_button.dart';
import 'package:design_module/utilities/color_constants.dart';
import 'package:design_module/utilities/hybrid_image.dart';
import 'package:design_module/utilities/screen_size_helper.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:session_manager_module/StorageManager/shared_preferences_storage.dart';
import 'package:utilities/core/base_model.dart';

import '../../../common/util/PreferenceHelper.dart';
import '../../../util/Utility.dart';
import '../initialPage/onboarding_inital_page_cubit.dart';
import '../onboarding_model.dart';

class OnboardingWelcomeScreen extends StatefulWidget {
  final Pages page;
  final OnboardingInitialPageCubit cubit;
  final String? firstName;
  final String? lastName;
  const OnboardingWelcomeScreen(
      {Key? key,
      required this.page,
      required this.cubit,
      this.firstName,
      this.lastName})
      : super(key: key);

  @override
  State<OnboardingWelcomeScreen> createState() =>
      _OnboardingWelcomeScreenState();
}

class _OnboardingWelcomeScreenState extends State<OnboardingWelcomeScreen> {
  bool pageLoaded = false;
  String? phoneNumber;

  @override
  void initState() {
    super.initState();
    fetchPhoneNumber();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      setState(() {
        pageLoaded = true;
      });
    });
  }

  fetchPhoneNumber() async{
    phoneNumber = await getStringPrefs(StringDataSharedPreferenceKeys.MOBILE_NUMBER);
  }

  @override
  Widget build(BuildContext context) {
    AnalyticsTrackerManager.instance.sendEvent(
        event: PageLoadedConstants.APP_VIEW_PAGE,
        properties: {
          'platform': Util.getPlatform(),
          'page_name': 'app_onboarding_starter_screen'
        });

    return AnimatedOpacity(
      duration: Duration(milliseconds: 700),
      opacity: pageLoaded ? 1.0 : 0,
      child: PopScope(
        onPopInvoked: (val) {
          widget.cubit.onProfilePageEnter(args: {
            'firstName': widget.firstName,
            'lastName': widget.lastName,
          });
        },
        child: Stack(
          children: [
            Container(
              height: getScreenAwareHeight(300),
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage(
                      '${assetImage}ic_onboarding_gradient_bg_testpng.png'),
                  fit: BoxFit.fill,
                ),
              ),
            ),
            if (widget.page.uiConfig?.bgImageUrl != null)
              Align(
                alignment: Alignment.bottomCenter,
                child: HybridImage(
                  imageUrl: widget.page.uiConfig!.bgImageUrl!,
                ),
              ),
            Scaffold(
              appBar: AppBar(
                backgroundColor: Colors.transparent,
                leading: IconButton(
                  icon: Icon(
                    Icons.arrow_back_ios,
                    color: color121212,
                    size: 20,
                  ),
                  onPressed: () {
                    Navigator.pop(context);
                  },
                ),
              ),
              body: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        height: 200,
                        width: 200,
                        child: Lottie.asset(
                            'assets/anim/onboarding_get_started.json'),
                      ),
                      SizedBox(height: 24),
                      if (widget.page.title != null)
                        hMediumText.text(
                          getTitle(widget.page.title),
                          textAlign: TextAlign.center,
                        ),
                      SizedBox(height: 8),
                      if (widget.page.uiConfig?.caption != null)
                        pMediumText.text(widget.page.uiConfig!.caption!,
                            textColor: color4B4B4B),
                    ],
                  ),
                ],
              ),
              backgroundColor: Colors.transparent,
              bottomNavigationBar: BottomAppBar(
                color: Colors.white,
                surfaceTintColor: colorFFFFFF,
                shadowColor: Colors.black,
                elevation: 12,
                child: AckoDarkButtonFullWidth(
                  text: widget.page.ctaTitle!,
                  onTap: () {
                    AnalyticsTrackerManager.instance.sendEvent(
                        event: TapConstants.TAP_BTN_INTERACTION,
                        properties: {
                          "from_page": "onboarding_name",
                          "type": "button",
                          "cta_text": "personalize_app",
                          "phone": phoneNumber,
                          "product": "onboarding",
                          "journey": "app_onboarding_starter_screen",
                          'platform': Util.getPlatform(),
                        });

                    widget.cubit.handleOnboardingNavigation(args: {
                      'firstName': widget.firstName,
                      'lastName': widget.lastName,
                    });
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String getTitle(String? title) {
    if (title.isNullOrEmpty) {
      return '';
    }

    return title!.contains('{firstName}')
        ? title.replaceAll('{firstName}', widget.firstName ?? '')
        : title;
  }
}
