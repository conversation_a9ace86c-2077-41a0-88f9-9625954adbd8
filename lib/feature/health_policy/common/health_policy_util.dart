import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/health_life/page_events/health_life_page_events.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:session_manager_module/session_manager.dart';
import 'package:utilities/constants/constants.dart';

import '../../../common/util/color_constants.dart';
import '../../../common/view/AckoText.dart';
import '../../../util/Utility.dart';
import '../model/health_policy_details.dart';

class HealthPolicyUtils {
  static showAddOnDetailsBottomSheet(List<HealthPolicyCover>? addOnsList,
      BuildContext buildContext, Map<String, dynamic> map) {
    if (addOnsList?.isNotNullOrEmpty ?? false) {
      showModalBottomSheet(
          context: buildContext,
          isScrollControlled: true,
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(top: Radius.circular(24.0))),
          builder: (context) {
            AnalyticsTrackerManager.instance.sendEvent(
                event: HLPageEvents.VIEW_ADDON_DETAIL, properties: map);
            return Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: 20.0, vertical: 30.0),
                child: ListView.separated(
                    shrinkWrap: true,
                    separatorBuilder: (context, index) => Padding(
                          padding:
                              const EdgeInsets.only(top: 20.0, bottom: 20.0),
                          child: Divider(
                              height: 1.0, color: colorE7E7F0, thickness: 1.0),
                        ),
                    itemCount: addOnsList?.length ?? 0,
                    itemBuilder: (context, index) =>
                        _showAddOnsList(index, addOnsList?[index], context)));
          });
    }
  }

  static _showAddOnsList(
      int index, HealthPolicyCover? addon, BuildContext buildContext) {
    return Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                    child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                      getImage(addon?.iconUrl ?? ''),
                      const SizedBox(width: 12),
                      Expanded(
                          child: TextEuclidSemiBold(addon?.title ?? '',
                              textColor: color040222, textSize: 18.0))
                    ])),
                (index == 0)
                    ? InkWell(
                        onTap: () {
                          Navigator.pop(buildContext);
                        },
                        child: IgnorePointer(
                          child:
                              Icon(Icons.close, color: color36354C, size: 24.0),
                        ))
                    : const SizedBox.shrink()
              ]),
          getDescription(addon?.description)
        ]);
  }

  static Widget getDescription(String? description) {
    return description?.isNotEmpty ?? false
        ? Padding(
            padding: EdgeInsets.only(top: 20.0),
            child: TextEuclidRegular14(description ?? ''))
        : const SizedBox.shrink();
  }

  static Widget getImage(String? url) {
    return url != null
        ? Image.network(url, height: 32.0, width: 32.0,
            errorBuilder: (context, exp, _) {
            return SvgPicture.asset(
                Util.getAssetImage(assetName: 'ic_no_policies.svg'));
          })
        : const SizedBox.shrink();
  }

  static bool isHealthVasService(Uri? url) {
    if (url == null) return false;

    if (url.toString().containsIgnoreCase('1mg.com') ||
        url.toString().containsIgnoreCase('mfine.co')) {
      return true;
    }
    return false;
  }

  static getGmcLinkUrl() async {
    var userEkey = await SessionManager.instance.storageManager.getUserEkey();

    final gmcLinkRedirectUrl = '${Constants.BASE_URL}/gmc/users/$userEkey/connect-users';
    final gmcLinkUrl = '${Constants.BASE_URL}/discover/email-verify/?redirect=$gmcLinkRedirectUrl';

    return gmcLinkUrl;
  }
}
