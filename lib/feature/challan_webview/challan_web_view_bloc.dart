import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:acko_flutter/feature/challan/challan_enums.dart';
import 'package:acko_flutter/feature/challan_webview/enums/challan_webview_enums.dart';

import 'package:acko_flutter/feature/challan_webview/models/challan_keyword_model.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_logger/acko_logger.dart';
import 'package:acko_logger/events/info/app_debug_info.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sdui/sdui.dart';
import 'package:session_manager_module/StorageManager/shared_preferences_storage.dart';
import 'package:utilities/remote_config/remote_config.dart';

import '../../common/util/PreferenceHelper.dart';
import '../acko_services/model/acko_data_response.dart';
import '../central_webview/model/webview_challan_response.dart';
import '../challan/challan_utility.dart';
import 'Utility/challan_webview_utility.dart';
import 'challan_automation_repository.dart';
import 'models/challan_automation_misc_models.dart';
import 'models/ka_challan_history_model.dart';
import 'models/ka_payment_url_model.dart';
import 'models/payment_status_request.dart';

class ChallanWebViewBloc extends Cubit<ChallanWebViewStates> {
  final String phoneNumber;
  final String searchSource;
  final List<String> violationNumbers;
  final String registrationNumber;
  ChallanWebViewBloc(
      {required String this.phoneNumber,
      required String this.searchSource,
      required List<String> this.violationNumbers,
      required String this.registrationNumber})
      : super(ChallanWebViewLoading());

  WebviewChallanResponse? _challanAutomationResponse;
  ChallanAutomation? stateDetailsToProcess;
  Completer<String>? _inputCompleter;
  final UserFlowData userFlowData = UserFlowData();
  Timer? _pollTimer;

  final _automationRepo = ChallanAutomationRepository();
  final BaseRepository _baseRepository = BaseRepository();

  fetchAutomationResponse(Uri? currentUrl) {
    if (stateDetailsToProcess != null) return;
    final response = RemoteConfigInstance.instance
        .getData(RemoteConfigKeysSet.CHALLAN_AUTOMATION_V3);
    _challanAutomationResponse =
        WebviewChallanResponse.fromJson(jsonDecode(response));
    for (var entry in _challanAutomationResponse!.challanAutomation) {
      if (entry.state!.toLowerCase() == searchSource.toLowerCase()) {
        stateDetailsToProcess = entry;
      }
    }
  }


  void _cancelPolling() {
    _pollTimer?.cancel();
    _pollTimer = null;
  }

  getAutomationScript(String url) async {
    List<String> decodedAutomationScripts = _fetchDecodedScriptString(url);
    for (var decodedScript in decodedAutomationScripts) {
      decodedScript =
          await updateChallanMetaDataInAutomationScript(decodedScript);
      emit(ExecuteScript(scripts: [decodedScript]));
    }
  }

  Future<String> updateChallanMetaDataInAutomationScript(
      String decodedScript) async {
    final replacementConstants =
        challanAutomationConstants.replacementText;
    return decodedScript
        .replaceAll(
            replacementConstants.paramRegistrationNumber, registrationNumber)
        .replaceAll(replacementConstants.paramViolationNumbers, jsonEncode(violationNumbers))
        .replaceAll(replacementConstants.paramPhoneNumber, phoneNumber);
  }

  askUserInput(ChallanKeywordModel keywordModel) async {

    keywordModel = await interceptModelProperties(keywordModel);

    AckoLoggerManager.instance.logInfo(
        event: AppDebugInfoEvent(
            page: 'challan_automation',
            infoMessage: 'Question asked to user',
            journey: 'challan_automation',
            data: {
          "journey": "Challan Automation",
          "source": searchSource,
          "responseType": keywordModel.responseType.toString(),
          "modelProperties": keywordModel.modelProperties.toString(),
        }));

    if (keywordModel.responseType == KeywordResponseType.solve_captcha)
      return solveCaptchaTask(keywordModel);

    if (isBackgroundTask(keywordModel.responseType)) {
      var isTaskCompleted = await runBackgroundTask(keywordModel);
      return isTaskCompleted;
    }

    String? userInput = getUserData(keywordModel);
    if (userInput != null) {
      return userInput;
    }

    emit(GetUserInput(keywordResponseModel: keywordModel));

    _inputCompleter = Completer<String>();

    userInput = await _inputCompleter?.future;

    _inputCompleter = null;
    setUserData(keywordModel, userInput);

    AckoLoggerManager.instance.logInfo(
        event: AppDebugInfoEvent(
            page: 'challan_automation',
            infoMessage: 'User reply to Question',
            journey: 'challan_automation',
            data: {
          "journey": "Challan Automation",
          "source": searchSource,
          "responseType": keywordModel.responseType.toString(),
          "modelProperties": keywordModel.modelProperties.toString(),
          "userInput": userInput,
        }));
    sendChallanAutomationAnalyticEvent(
        model: keywordModel, userInput: userInput, source: searchSource);

    if (!keywordModel.stayOnPage) emit(ChallanWebViewLoading());

    return userInput;
  }

  String? getUserData(ChallanKeywordModel keywordModel) {
    if (keywordModel.error == null && keywordModel.responseType == KeywordResponseType.pay_through_upi ) {
      return userFlowData.userUpiId;
    }
    return null;
  }

  setUserData(ChallanKeywordModel keywordModel, String? userInput) {
    if (userInput != null) {
      if (keywordModel.responseType == KeywordResponseType.pay_through_upi) {
        userFlowData.userUpiId = userInput;
      }
    }
  }

  Future<ChallanKeywordModel> interceptModelProperties(
      ChallanKeywordModel keywordModel) async {
    switch (keywordModel.responseType) {
      case KeywordResponseType.upi_in_progress:
        final upiInProgressProps =
            keywordModel.modelProperties as UpiInProgressProperties;
        if (upiInProgressProps.overrideUrl != null) {
          userFlowData.nextUrlOverride = upiInProgressProps.overrideUrl;
        }
        break;
      case KeywordResponseType.script_error:
        final scriptErrorProps =
            keywordModel.modelProperties as ScriptErrorProperties;
        AnalyticsTrackerManager.instance.sendEvent(
          event: PageLoadedConstants.NETWORK_API_ERROR,
          properties: {
            "type": 'Challan Automation',
            "error_message": scriptErrorProps.errorMessage,
          },
        );
        AckoLoggerManager.instance.logInfo(
            event: AppDebugInfoEvent(
                page: 'challan_automation',
                infoMessage: 'Page Error',
                journey: 'challan_automation',
                data: {
                  "journey": "Challan Automation",
                  "source": searchSource,
                  "responseType": keywordModel.responseType.toString(),
                  "userInput": scriptErrorProps.errorMessage,
                  "identifer": scriptErrorProps.identifier,
                })
        );
        break;
      case KeywordResponseType.pay_through_upi:
        final props = keywordModel.modelProperties as PayThroughUpiProperties;
        props.upiIds = await checkAndSetUsersUpiIds();
        await sendPaymentStatusForIngestion(
            PaymentStatusRequest.ingestion(paymentStatus: ChallanPaymentStatus.PAYMENT_INITIATED, source: searchSource)
        );
        break;
      case KeywordResponseType.otp_field:
        final props = keywordModel.modelProperties as OtpFieldProperties;
        if (props.isTransactionDetailsFlow) userFlowData.shouldContinueDetailsFlow = true;
        break;
      default:
        break;
    }
    return keywordModel;
  }

  Future<List<String>?> checkAndSetUsersUpiIds() async {
    final upiDetails = await _automationRepo
        .getUsersUpiIds(mapStringToSearchSource(searchSource));
    List<String> referenceIds = [];
    if (upiDetails?.bankDetails != null &&
        (upiDetails!.bankDetails?.isNotEmpty ?? false)) {
      referenceIds = upiDetails.bankDetails!
          .where((detail) => detail.referenceId != null)
          .map((detail) => detail.referenceId!)
          .toList();
    }
    if (referenceIds.isNotEmpty) {
      return referenceIds.take(4).toList();
    }
    return null;
  }

  sendPaymentStatusForIngestion(PaymentStatusRequest request) async {
    await _automationRepo.sendPaymentStatus(
      phoneNumber: phoneNumber,
      noticeNumbers: violationNumbers,
      referenceNumber: userFlowData.paymentReferenceNumber!,
      registrationNumber: registrationNumber,
      source: request.source,
      receiptUrl: request.receiptUrl,
      challanAmount: request.challanAmount,
      ackoPaymentStatus: request.ackoPaymentStatus,
      paymentStatus: request.ackoPaymentStatus.name,
      shouldSendHistroyProps: request.shouldSendHistoryProps ?? false,
    );
  }

  runBackgroundTask(ChallanKeywordModel keywordModel) async {
    if (keywordModel.responseType == KeywordResponseType.skip_page) {
      final skipPageProps = keywordModel.modelProperties as SkipPageProperties;

      switch (skipPageProps.skipType) {
        case SkipType.skip_to_ka_payment:
          _automationRepo.setUserIdValue(
              userFlowData.userWebviewCookie, mapStringToSearchSource(searchSource));
          if (skipPageProps.email.isNotNullOrEmpty) {
            KaPaymentUrlModel urlToRedirect =
                await _automationRepo.getKaPaymentUrl(
                    skipPageProps.email!,
                    violationNumbers,
                    userFlowData.userWebviewCookie,
                    mapStringToSearchSource(searchSource));
            if (urlToRedirect.error == null &&
                urlToRedirect.url.isNotNullOrEmpty) {
              emit(WebviewLoadUrl(url: urlToRedirect.url!));
              return true;
            }
            return false;
          }
        case SkipType.skip_to_transaction_details_page:
          emit(LoadTransactionDetailsPage());
          return true;

        case SkipType.start_cron_job:
          if (userFlowData.paymentReferenceNumber != null && userFlowData.userWebviewCookie != null)
            _automationRepo.startCronJob(userFlowData.paymentReferenceNumber!,
                mapStringToSearchSource(searchSource), userFlowData.userWebviewCookie!);
          return true;
        case SkipType.save_upi_id:
          saveUsersUpiId();
          return true;
        case SkipType.save_reference_number:
          if (skipPageProps.referenceNumber.isNullOrEmpty) {
            emit(ChallanError());
            return false;
          }
          userFlowData.paymentReferenceNumber = skipPageProps.referenceNumber;
          return true;
        case SkipType.update_payment_details:
          await updatePaymentDetails(mapStringToSearchSource(searchSource), skipPageProps);
          return true;
        default:
          return false;
      }
    } else if (keywordModel.responseType == KeywordResponseType.open_bottomsheet) {
      final bottomsheetProps =
      keywordModel.modelProperties as BottomsheetProperties;
      emit(OpenWebviewBottomsheet(title: bottomsheetProps.title, subtitle: bottomsheetProps.subtitle, buttonCta: bottomsheetProps.buttonCta, imageUrl: bottomsheetProps.imageUrl));
      return true;
    } else if (keywordModel.responseType == KeywordResponseType.save_amount_breakup) {
      final amountBreakUpProps = keywordModel.modelProperties as AmountBreakupProperties;
      userFlowData.amountBreakup = amountBreakUpProps;
      return true;
    } else {
      final loggingProps =
          keywordModel.modelProperties as AutomationLoggingProperties;
      logChallanAutomationEvents(loggingProps);
      return true;
    }
  }

  List<String> _fetchDecodedScriptString(String url) {
    final List<String>? encodedScripts = _fetchEncodedScriptsForChallanUrl(url);

    if (encodedScripts.isNullOrEmpty) {
      return [];
    }

    return decodeBase64Scripts(encodedScripts!);
  }

  List<String>? _fetchEncodedScriptsForChallanUrl(String inputUrl) {
    if (stateDetailsToProcess?.automationFlowUrls != null) {
      for (var flowUrl in stateDetailsToProcess!.automationFlowUrls!) {
        if (flowUrl.url != null && inputUrl.contains(flowUrl.url!)) {
          return flowUrl.scripts;
        }
      }
    }
    return null;
  }

  void onUserInputSubmitted(String input) {
    if (_inputCompleter?.isCompleted == false) {
      _inputCompleter!.complete(input);
    }
  }

  executeHelperScript(HelperScript scriptKeyword) {
    final scripts = stateDetailsToProcess?.helperScripts?[scriptKeyword.name];
    final decodedScripts = decodeBase64Scripts(scripts ?? []);
    if (decodedScripts.isNotEmpty) {
      emit(ExecuteScript(scripts: decodedScripts));
    }
  }

  checkUrlValidity(Uri? url) {
    if (url == null) return false;
    return true;
  }

  onScriptRetryButtonTapped(ScriptErrorType errorType, [String? url]) {
    if (errorType == ScriptErrorType.navigate_to_home && url != null) {
      emit(WebviewLoadUrl(url: url));
      return;
    }
    executeHelperScript(HelperScript.reload_page);
  }

  Future<void> pollForKaResponse(ChallanSearchSource source) async {
    if (userFlowData.paymentReferenceNumber.isNullOrEmpty) {
      emit(ChallanError());
      return;
    }
    emit(ChallanResultsLoading());

    await Future.delayed(Duration(seconds: 3));

    final KaChallanHistoryModel result =
        await _automationRepo.checkStatusOfChallan(
            referenceNumber: userFlowData.paymentReferenceNumber!,
            userIdValue: userFlowData.userWebviewCookie,
            source: source);

    if (result.error == null) {
      final transactionConstants =
          getTransactionStatusConstants(result.ackoPaymentStatus, source);

      await sendPaymentStatusForIngestion(PaymentStatusRequest.ingestion(
        paymentStatus: result.ackoPaymentStatus,
        challanAmount: result.challanAmount.toString(),
        receiptUrl: result.ackoPaymentStatus == ChallanPaymentStatus.PAYMENT_SUCCESS ? challanAutomationConstants.getChallanReceiptUrl(result.noticeNumber!, userFlowData.paymentReferenceNumber!) : null,
        source: searchSource,
      ));

      emit(ChallanResultsLoaded(
          transactionModel: transactionConstants,
          paymentStatus: result.ackoPaymentStatus));

    } else {
      emit(ChallanResultsError());
      return;
    }
  }

  updatePaymentDetails(ChallanSearchSource source, SkipPageProperties skipPageProps) async {
    if (source == ChallanSearchSource.TS && skipPageProps.referenceNumber.isNotNullOrEmpty && skipPageProps.paymentStatus != null) {
      String? documentId;
      if (skipPageProps.paymentImage != null) {
        documentId = await uploadReceiptToDocumentService(skipPageProps.paymentImage!);
      }
      userFlowData.paymentReferenceNumber = skipPageProps.referenceNumber;
      await sendPaymentStatusForIngestion(PaymentStatusRequest.ingestion(
        paymentStatus: skipPageProps.paymentStatus!,
        receiptUrl: documentId,
        source: searchSource,
        challanAmount: skipPageProps.challanAmount
      ));
    }
    emit(LoadTransactionDetailsPage());
  }

  @override
  Future<void> close() {
    _cancelPolling();
    return super.close();
  }

  Future<String?> solveCaptchaTask(ChallanKeywordModel keywordModel) async {
    final captchaProps = keywordModel.modelProperties as SolveCaptchaProperties;


    //Step 1: Create captcha task
    final createTaskSduiResponse = await _baseRepository.postResponse(challanAutomationConstants.getCreateCaptchaUrl(searchSource), requestBody: {'base64Img' : Uri.encodeComponent(captchaProps.base64Img)});
    if (createTaskSduiResponse.error != null) return null;


    final AckoDataResponse<CaptchaCreateSduiResponse> createTaskResult = AckoDataResponse<CaptchaCreateSduiResponse>.fromJson(
        createTaskSduiResponse.data, (json) => CaptchaCreateSduiResponse.fromJson(json));


    final taskId = createTaskResult.data?.taskId;
    if (taskId == null) return null;


    final completer = Completer<String?>();
    _pollTimer?.cancel();

    _pollTimer = Timer.periodic(const Duration(seconds: 3), (timer) async {
      final response = await _baseRepository.getResponse(challanAutomationConstants.getCaptchaResultUrl(taskId));
      if (response.error != null) {
        _cancelPolling();
        completer.complete(null);
        return;
      }
      final resultData = AckoDataResponse<CaptchaResultSduiResponse>.fromJson(
        response.data!,
            (j) => CaptchaResultSduiResponse.fromJson(j),
      );
      final status   = resultData.data?.captchaStatus;
      final solution = resultData.data?.captchaSolution;

      if (status == CaptchaProgressType.ready && solution.isNotNullOrEmpty) {
        _cancelPolling();
        completer.complete(solution);
      } else if (status == CaptchaProgressType.unknown) {
        _cancelPolling();
        completer.complete(null);
      }
    });

    return completer.future;
  }

  checkChallanPaymentStatus(FinalScreenProperties? finalScreenProps) async {
    emit (ChallanResultsLoading());
    if (finalScreenProps?.paymentStatus != null && finalScreenProps!.searchSource == ChallanSearchSource.TS) {
      String? documentId;
      if (finalScreenProps.paymentImage != null) {
        documentId = await uploadReceiptToDocumentService(finalScreenProps.paymentImage!);
      }

      await sendPaymentStatusForIngestion(PaymentStatusRequest.ingestion(
        receiptUrl: documentId, source: searchSource, paymentStatus: finalScreenProps.paymentStatus!, challanAmount: userFlowData.amountBreakup?.totalPayable
      ));

      emit(ChallanResultsLoaded(
          transactionModel: getTransactionStatusConstants(finalScreenProps.paymentStatus!, mapStringToSearchSource(searchSource)),
          paymentStatus: finalScreenProps.paymentStatus!)
      );
    } else {
      emit(ChallanResultsError());
    }
  }
  saveUsersUpiId() async {
    if (userFlowData.saveUserUpiID == null || !userFlowData.saveUserUpiID! || userFlowData.userUpiId == null) return;
    String? userName = await getStringPrefs(StringDataSharedPreferenceKeys.USER_PROFILE_NAME);
    if (userName == null || userName.isEmpty) {
      userName = extractNameFromUpiID(userFlowData.userUpiId!);
    }
    _baseRepository.getResponse(challanAutomationConstants.saveUpiId(userFlowData.userUpiId!, userName));
  }

  uploadReceiptToDocumentService(String base64Img) async {
    final decodedBase64 = Uri.decodeComponent(base64Img);
    final cleanBase64 = decodedBase64.split(',').last;
    Uint8List bytes = base64Decode(cleanBase64);
    String fileExtension = getReceiptFileExtension(searchSource);

    final directory = await getApplicationDocumentsDirectory();
    final filePath = '${directory.path}/challan_receipt_${DateTime.now().millisecondsSinceEpoch}${fileExtension}';
    final file = File(filePath);
    await file.writeAsBytes(bytes);

    DocumentServiceResponse uploadDocumentResult = await _automationRepo.uploadChallanReceipt("challan_receipt", filePath, searchSource);

    if (uploadDocumentResult.error == null) return uploadDocumentResult.id;
    return null;
  }
}

abstract class ChallanWebViewStates {}

class ChallanWebViewLoading extends ChallanWebViewStates {}

class ChallanWebViewLoaded extends ChallanWebViewStates {}

class GetUserInput extends ChallanWebViewStates {
  final ChallanKeywordModel keywordResponseModel;

  GetUserInput({required this.keywordResponseModel});
}

class ChallanError extends ChallanWebViewStates {}

class ExecuteScript extends ChallanWebViewStates {
  final List<String> scripts;
  ExecuteScript({required this.scripts});
}

class ChallanAutomationComplete extends ChallanWebViewStates {}

class WebviewLoadUrl extends ChallanWebViewStates {
  final String url;

  WebviewLoadUrl({required this.url});
}

class OpenWebviewBottomsheet extends ChallanWebViewStates {
  final String title;
  final String subtitle;
  final String buttonCta;
  final String? imageUrl;

  OpenWebviewBottomsheet({required this.title, required this.subtitle, required this.buttonCta, this.imageUrl});
}

class LoadTransactionDetailsPage extends ChallanWebViewStates {
  final bool? shouldRunPaymentUpdateFlow;

  LoadTransactionDetailsPage({this.shouldRunPaymentUpdateFlow});
}

class ChallanResultsLoading extends ChallanWebViewStates {}

class ChallanResultsError extends ChallanWebViewStates {}

class ChallanResultsLoaded extends ChallanWebViewStates {
  final TransactionStatusModel transactionModel;
  final ChallanPaymentStatus paymentStatus;

  ChallanResultsLoaded(
      {required this.transactionModel,
      required this.paymentStatus});
}
