import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/feature/challan/challan_enums.dart';
import 'package:acko_flutter/feature/challan_sdui/model/challan_transaction_model.dart';
import 'package:acko_logger/acko_logger.dart';
import 'package:acko_logger/events/info/app_debug_info.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/card_loaded_events.dart';
import 'package:utilities/constants/constants.dart';

import '../../../util/extensions.dart';
import '../enums/challan_webview_enums.dart';
import '../models/challan_automation_misc_models.dart';
import '../models/challan_keyword_model.dart';

class ChallanAutomationConstants {
  final String addUpiId;
  final String payNow;
  final String enterUpiId;
  final String saveMyUpiId;
  final String yourPaymentHasInitiated;
  final String payment_initiated;
  final String payment_success;
  final String challanPaymentSuccessMessage;
  final String payment_failed;
  final String paymentPendingAutomation;
  final String paymentSuccessfulCheckDetails;
  final String paymentFailedCheckDetails;
  final String rupeeSymbol;
  final String getOtp;
  final String redirectedToGovernmentWebsite;
  final String directlyPayingTheGovernment;
  final String paymentCompleted;
  final String otpEntered;
  final String upiRequestSent;
  final String finalScreen;
  final String upiIdEntered;
  final String somethingWentWrong;
  final String tryAgain;
  final String unableToCompleteRequest;
  final String statusCouldNotBeRefereshed;
  final String takesTwentyFourHoursForRtoPayment;
  final String processingYourPayment;
  final String pleaseDoNotGoBackOrCloseApp;
  final String cancelPayment;
  final String minsRemaining;
  final String payUsingUpiID;
  final String paymentDetails;
  final String fetchingChallanDetails;
  final String resendOtp;
  final String submit;
  final String no;
  final String okay;
  final String tryAutomationRequestLater;
  final String yesCancel;
  final String continueWord;
  final String areYouSureYouWantToCancel;
  final String pendingChallanConsequences;
  final String thisWasUnexpected;
  final String karnatakaRto;
  final String telanganaRto;
  final String didYouCompletePayment;
  final String weNeedToUpdateStatus;
  final String imageTrackingScreenEvaluation;
  final String imageSomethingWentWrong;
  final String imageUnexpectedError;
  final String imageDocumentMissing;
  final String rtoFlowPaymentProcessingMessage;

  final FileExtensions fileExtensions;
  final ReplacementText replacementText;

  const ChallanAutomationConstants({
    this.addUpiId = 'Add new UPI ID',
    this.payNow = 'Pay now',
    this.enterUpiId = 'Enter your UPI ID',
    this.saveMyUpiId = 'Save my UPI ID for future payments',
    this.yourPaymentHasInitiated = 'Track your payment status ➜',
    this.rupeeSymbol = '\u{20B9}',
    this.payment_initiated = 'Initiated',
    this.payment_failed = 'Failed',
    this.payment_success = 'Successful',
    this.paymentPendingAutomation = 'Your payment is pending',
    this.challanPaymentSuccessMessage = 'It usually takes up to 24 hours for the RTO to process payments',
    this.paymentSuccessfulCheckDetails = 'View your payment details ➜',
    this.paymentFailedCheckDetails = 'Check your failed payment details ➜ ',
    this.getOtp = 'Get OTP',
    this.redirectedToGovernmentWebsite = "You will be redirected to the government's website",
    this.directlyPayingTheGovernment = 'You will be paying directly to the government',
    this.paymentCompleted = 'Payment completed',
    this.otpEntered = 'OTP entered',
    this.upiRequestSent = 'UPI request sent',
    this.finalScreen = 'Final Screen',
    this.upiIdEntered = 'UPI Id entered',
    this.somethingWentWrong = 'Something went wrong',
    this.tryAgain = 'Try Again',
    this.unableToCompleteRequest = 'We’re unable to complete your request. Please try again after some time.',
    this.statusCouldNotBeRefereshed = 'Status could not be refreshed',
    this.takesTwentyFourHoursForRtoPayment = 'It usually takes up to 24 hours for the RTO to process payments.',
    this.processingYourPayment = 'Processing your payment',
    this.pleaseDoNotGoBackOrCloseApp = 'Please do not go back or close the app',
    this.cancelPayment = 'Cancel Payment',
    this.minsRemaining = 'mins remaining',
    this.payUsingUpiID = 'Pay using UPI',
    this.paymentDetails = 'Payment Details',
    this.fetchingChallanDetails = 'Fetching challan details...',
    this.resendOtp = 'Resend OTP',
    this.submit = 'Submit',
    this.no = 'No',
    this.okay = 'Okay',
    this.tryAutomationRequestLater = 'We’re unable to complete your request. Please try again.',
    this.yesCancel = 'Yes, cancel',
    this.rtoFlowPaymentProcessingMessage = 'Your payment will be processed by the RTO within 48 hours',
    this.continueWord = 'Continue',
    this.areYouSureYouWantToCancel = 'Are you sure you want to cancel?',
    this.pendingChallanConsequences = 'Having pending challans could result in unnecessary legal troubles',
    this.thisWasUnexpected = 'Well, this is unexpected.',
    this.karnatakaRto = 'Karnataka',
    this.telanganaRto = 'Telangana',
    this.didYouCompletePayment = 'Were you able to complete your challan payment?',
    this.imageTrackingScreenEvaluation = 'https://auto.ackoassets.com/central-app/features/asset_v9/Processing_opt.json',
    this.imageSomethingWentWrong = 'https://auto.ackoassets.com/central-app/features/asset_v9/Error_3.svg',
    this.imageUnexpectedError = 'https://auto.ackoassets.com/central-app/features/asset_v9/Error_2.svg',
    this.imageDocumentMissing = 'https://auto.ackoassets.com/central-app/features/app_v9/common/ic_doc_miss.svg',
    this.weNeedToUpdateStatus = "We need this to update your challan’s status",
    this.fileExtensions = const FileExtensions(),
    this.replacementText = const ReplacementText(),
  });

  String payingRTO(String rtoName) => 'You will be directly paying the ${rtoName} RTO';

  String buildUpiPaymentMessage(String upiProvider) =>
      'Open ${upiProvider} and complete the payment request by the RTO';

  String generateOtpInstruction(String phoneNumber) =>
      'The RTO will send you an OTP on ${phoneNumber}';

  String numberedChallanMessage(int numOfChallans) =>
      '$numOfChallans challan${numOfChallans == 1 ? '' : 's'} • ';

  String getCreateCaptchaUrl(String source) =>
      '/auto-asset/create-captcha-task?searchSource=${source}';

  String getCaptchaResultUrl(int taskId) =>
      '/auto-asset/get-captcha-result?taskId=${taskId}';

  String getChallanPaymentFailureMessage(String searchSource) =>
        'This usually does not happen. If any amount is debited, please contact the ${searchSource} RTO.';

  String getKaPaymentUrl(String searchSource, String userIdValue, List<String> noticeNumbers) {
    final csv = noticeNumbers.join(',');
    return '/vas/api/v1/challans/payment-link'
        '?source=${searchSource}'
        '&session-id=${userIdValue}'
        '&notice-number=${csv}';
  }

  String getChallanReceiptUrl(String noticeNumber, String referenceNumber) {
    return "https://kspapp.ksp.gov.in/ksp/api/traffic-challan/download-receipt-web?noticeNumber=${noticeNumber}&deptRefNum=${referenceNumber}";
  }

  String getDefaultChallanAutomationStatus() {
    return
        """
        {
          "ka": {
            "redirection_url": "https://kspapp.ksp.gov.in/ksp/api/traffic-challan",
            "is_automation_active": "false",
            "ios_min_build_number": "999",
            "android_min_build_number": "999"
          },
          "parivahan": {
            "redirection_url": "https://echallan.parivahan.gov.in/index/accused-challan",
            "is_automation_active": "false",
            "ios_min_build_number": "999",
            "android_min_build_number": "999"
          },
          "mh": {
            "redirection_url": "https://mahatrafficechallan.gov.in/payechallan/PaymentService.htm",
            "is_automation_active": "false",
            "ios_min_build_number": "999",
            "android_min_build_number": "999"
          },
          "dl": {
            "redirection_url": "https://traffic.delhipolice.gov.in/notice/pay-notice",
            "is_automation_active": "false",
            "ios_min_build_number": "999",
            "android_min_build_number": "999"
          },
          "delhi traffic police": {
            "redirection_url": "https://traffic.delhipolice.gov.in/notice/pay-notice",
            "is_automation_active": "false",
            "ios_min_build_number": "999",
            "android_min_build_number": "999"
          },
          "ts": {
            "redirection_url": "https://echallan.tspolice.gov.in/publicview/",
            "is_automation_active": "false",
            "ios_min_build_number": "999",
            "android_min_build_number": "999"
          }
        }
        """;
  }

  String saveUpiId(String userUpiId, String accountHolder) =>
      '/auto-asset/save-upi-id?user_upi_id=${userUpiId}&account_holder=${accountHolder}';
}

class FileExtensions {
  final String jpegExtension;
  final String pdfExtension;
  const FileExtensions({
    this.jpegExtension = '.jpeg',
    this.pdfExtension = '.pdf',
  });
}

class ReplacementText {
  final String paramRegistrationNumber;
  final String paramViolationNumbers;
  final String paramPhoneNumber;
  const ReplacementText({
    this.paramRegistrationNumber = 'parameter_registrationNumber',
    this.paramViolationNumbers = 'parameter_violationNumbers',
    this.paramPhoneNumber = 'parameter_phoneNumber',
  });
}

const challanAutomationConstants = ChallanAutomationConstants();

TransactionStatusModel getTransactionStatusConstants(
    ChallanPaymentStatus statusText, ChallanSearchSource searchSource) {
  if (statusText == ChallanPaymentStatus.PAYMENT_FAILED) {
    return TransactionStatusModel(
        title: payment_failed, subtitle: challanAutomationConstants.getChallanPaymentFailureMessage(mapSearchSourceToString(searchSource)), transactionImage: AssetsConstants.failed_anim);
  }
  return TransactionStatusModel(
      title: challanAutomationConstants.paymentCompleted,
      subtitle: challanAutomationConstants.challanPaymentSuccessMessage,
      transactionImage: AssetsConstants.success_anim);
}

String mapPaymentStatusToString(ChallanPaymentStatus statusText) {
  if (statusText == ChallanPaymentStatus.PAYMENT_INITIATED) {
    return challanAutomationConstants.payment_initiated;
  } else if (statusText == ChallanPaymentStatus.PAYMENT_FAILED) {
    return challanAutomationConstants.payment_failed;
  } else if (statusText == ChallanPaymentStatus.PAYMENT_SUCCESS) {
    return challanAutomationConstants.payment_success;
  }
  return "";
}

String mapSearchSourceToString(ChallanSearchSource searchSource) {
  if (searchSource == ChallanSearchSource.TS) {
    return challanAutomationConstants.telanganaRto;
  }
  return challanAutomationConstants.karnatakaRto;
}

String extractNameFromUpiID(String upiId) {
  return upiId.contains('@') ? upiId.split('@')[0] : " ";
}

String appendFragmentToUrl(String url, String key, String value) {
  final newUri = Uri.parse(url);
  final frag = newUri.fragment;
  final pairs = <String>[];
  if (frag.isNotEmpty) pairs.addAll(frag.split('&'));
  pairs.add('$key=$value');
  return newUri.replace(fragment: pairs.join('&')).toString();
}

ChallanPaymentStatus mapStringToPaymentStatus(String statusText) {
  if (statusText.equalsIgnoreCase(ChallanPaymentStatus.PAYMENT_FAILED.name)) {
    return ChallanPaymentStatus.PAYMENT_FAILED;
  } else if (statusText
      .equalsIgnoreCase(ChallanPaymentStatus.PAYMENT_SUCCESS.name)) {
    return ChallanPaymentStatus.PAYMENT_SUCCESS;
  } else {
    return ChallanPaymentStatus.PAYMENT_INITIATED;
  }
}

ChallanSearchSource mapStringToSearchSource(String searchSource) {
  if (searchSource == ChallanSearchSource.KA.name) {
    return ChallanSearchSource.KA;
  } else if (searchSource == ChallanSearchSource.TS.name) {
    return ChallanSearchSource.TS;
  }
  return ChallanSearchSource.UNKNOWN;
}

String getReceiptFileExtension(String? searchSource) {
  ChallanSearchSource source = mapStringToSearchSource(searchSource!);
  if (source == ChallanSearchSource.TS) {
    return challanAutomationConstants.fileExtensions.jpegExtension;
  }
  return challanAutomationConstants.fileExtensions.pdfExtension;
}

bool isBackgroundTask(KeywordResponseType responseType) {
  if (responseType == KeywordResponseType.skip_page ||
      responseType == KeywordResponseType.open_bottomsheet ||
      responseType == KeywordResponseType.automation_logging ||
      responseType == KeywordResponseType.save_amount_breakup)
    return true;
  return false;
}

ChallanTransactionDetailRow getViolationDetails(List<ChallanNumberData> challanNumbers) {
  final List<Map<String,String>> violationList = [];
  for (final cn in challanNumbers) {
    for (final v in cn.violation) {
      violationList.add({ v.offence: challanAutomationConstants.rupeeSymbol+ int.parse(v.amount).formatPriceWithCommas()});
    }
  }

  final isSingleViolation = violationList.length == 1;

  return ChallanTransactionDetailRow(
      label: 'Challan details',
      value: isSingleViolation ? "${violationList.first.keys.first}" : "${violationList.length} challans",
      openBottomsheet: !isSingleViolation,
      detailItems: isSingleViolation ? null : violationList,
      isBold: false,
      valueColor: color121212
  );
}


ErrorScreenConstants getErrorScreenConstants(bool shouldTryAgain) {
  if (shouldTryAgain) {
    return ErrorScreenConstants(
        mainImage: challanAutomationConstants.imageUnexpectedError,
        title: challanAutomationConstants.thisWasUnexpected,
        subtitle: challanAutomationConstants.tryAutomationRequestLater,
        buttonText: challanAutomationConstants.tryAgain);
  } else {
    return ErrorScreenConstants(
        mainImage: challanAutomationConstants.imageSomethingWentWrong,
        title: challanAutomationConstants.somethingWentWrong,
        subtitle: challanAutomationConstants.unableToCompleteRequest,
        buttonText: challanAutomationConstants.okay);
  }
}

sendChallanAutomationAnalyticEvent(
    {required ChallanKeywordModel model,
    String source = "KA",
    String? userInput}) {
  String? name;

  if (model.responseType == KeywordResponseType.otp_field) {
    name = challanAutomationConstants.otpEntered;
  } else if (model.responseType == KeywordResponseType.upi_in_progress) {
    name = challanAutomationConstants.upiRequestSent;
  } else if (model.responseType == KeywordResponseType.final_screen) {
    name = challanAutomationConstants.finalScreen;
  } else if (model.responseType == KeywordResponseType.pay_through_upi) {
    name = challanAutomationConstants.upiIdEntered;
  }

  AnalyticsTrackerManager.instance
      .sendEvent(event: CardLoadedConstants.TRACK_EVENT_COMPLETE, properties: {
    'journey': "Challan Automation",
    'name': name,
    'product': userInput,
    'source': source
  });
}


logChallanAutomationEvents(AutomationLoggingProperties loggingProps) {
  AckoLoggerManager.instance.logInfo(
      event: AppDebugInfoEvent(
          page: 'challan_automation',
          infoMessage: loggingProps.message,
          journey: 'challan_automation',
          data: loggingProps.metadata
          ));
}


class kaFinalScreeConstants {
  final String title;
  final String image;

  kaFinalScreeConstants({
    required this.title,
    required this.image,
  });
}

class TransactionStatusModel {
  final String title;
  final String subtitle;
  final String transactionImage;

  TransactionStatusModel({
    required this.title,
    required this.subtitle,
    required this.transactionImage,
  });
}
