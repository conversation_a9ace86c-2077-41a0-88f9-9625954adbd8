import 'dart:async';
import 'package:acko_web_view_module/bloc/states.dart';
import 'package:acko_web_view_module/common/custom_dartz.dart';
import 'package:acko_web_view_module/lob_contract_classes/view_contract.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/src/in_app_webview/in_app_webview_controller.dart';
import 'package:flutter_inappwebview_platform_interface/src/types/console_message.dart';
import 'package:flutter_inappwebview_platform_interface/src/types/create_window_action.dart';
import 'package:flutter_inappwebview_platform_interface/src/types/download_start_request.dart';
import 'package:flutter_inappwebview_platform_interface/src/types/navigation_action.dart';
import 'package:flutter_inappwebview_platform_interface/src/types/navigation_action_policy.dart';

class ChallanWebViewLogic extends WebViewUIBase {
  final Function(Uri?) onLoadStopCallback;
  final Function(Uri?) onLoadStartCallback;
  final Function(InAppWebViewController) onWebViewCreatedCallback;
  final Function(InAppWebViewController) onPageCompleteLoadedCallback;
  final Function(NavigationAction) shouldOverrideUrlLoadingCallback;
  final Function(InAppWebViewController, CreateWindowAction)
      onCreateWindowCallback;
  final Function(InAppWebViewController, Uri?, int, String) onLoadErrorCallback;
  Timer? _loadStopTimer;

  ChallanWebViewLogic(
      this.onLoadStopCallback,
      this.onLoadStartCallback,
      this.onWebViewCreatedCallback,
      this.onPageCompleteLoadedCallback,
      this.shouldOverrideUrlLoadingCallback,
      this.onCreateWindowCallback,
      this.onLoadErrorCallback);

  @override
  void addJsHandler(InAppWebViewController controller) {}

  @override
  void blocStateListener(WebViewState state) {}

  @override
  Future<Either<NotHandlingMethod, bool>> canGoBack(
      InAppWebViewController controller) {
    Navigator.of(pageContext!).pop(true);
    return Future.value(Right(true));
  }

  @override
  void dispose() {}

  @override
  Future<Either<NotHandlingMethod, dynamic>> handleWebViewActions(
      args, InAppWebViewController controller) {
    return Future.value(Left(NotHandlingMethod()));
  }

  @override
  Either<NotHandlingMethod, HandlingVoidMethod> onAppBarActionPressed(
      String actionValue) {
    return Left(NotHandlingMethod());
  }

  @override
  void onCloseWindow(InAppWebViewController controller) {}

  @override
  void onConsoleMessage(
      InAppWebViewController controller, ConsoleMessage message) {}

  @override
  Either<NotHandlingMethod, HandlingVoidMethod> onCreateWindow(
      InAppWebViewController controller, CreateWindowAction request) {
    return onCreateWindowCallback(controller, request);
  }

  @override
  Either<NotHandlingMethod, HandlingVoidMethod> onDownloadStart(
      InAppWebViewController controller,
      DownloadStartRequest downloadStartRequest) {
    return Left(NotHandlingMethod());
  }

  @override
  void onLoadError(
      InAppWebViewController controller, Uri? url, int code, String message) {
    onLoadErrorCallback(controller, url, code, message);
  }

  @override
  void onLoadStart(InAppWebViewController controller, Uri? url) {
    onLoadStartCallback(url);
  }

  @override
  void onLoadStop(InAppWebViewController controller, Uri? url) {
    onLoadStopCallback(url);
  }

  @override
  void onPageCommitVisible(InAppWebViewController controller, Uri? url) {}

  @override
  void onWebViewCreated(InAppWebViewController controller) {
    onWebViewCreatedCallback(controller);
  }

  @override
  Future<Either<NotHandlingMethod, NavigationActionPolicy>>
      shouldOverrideUrlLoading(
          InAppWebViewController controller, NavigationAction action) {
    return shouldOverrideUrlLoadingCallback(action);
  }

  @override
  Future<bool> shouldSubtractKeypadSpacing(String url) {
    return Future.value(false);
  }

  @override
  void onPageCompleteLoaded(InAppWebViewController controller) {
    _loadStopTimer?.cancel();

    _loadStopTimer = Timer(Duration(seconds: 1), () {
      onPageCompleteLoadedCallback(controller);
    });
  }
}
