import 'package:acko_flutter/feature/challan_sdui/model/challan_transaction_model.dart';
import 'package:acko_flutter/feature/challan_webview/Utility/challan_webview_utility.dart';
import 'package:acko_flutter/feature/challan_webview/models/challan_keyword_model.dart';
import 'package:flutter/material.dart';

import '../../../util/Utility.dart';
import '../enums/challan_webview_enums.dart';

enum ChallanPaymentStatus {
  PAYMENT_INITIATED,
  PAYMENT_SUCCESS,
  PAYMENT_FAILED,
  PAYMENT_PENDING,
  PAYMENT_LOCKED,
  unknown
}

class ChallanAutomationCronJobModel {
  String? success;
  String? error;

  ChallanAutomationCronJobModel({this.success, this.error});

  ChallanAutomationCronJobModel.error(this.error);

  ChallanAutomationCronJobModel.success(this.success);
}

class ChallanAutomationVerifyUserUpiIds {
  String? success;
  String? error;

  ChallanAutomationVerifyUserUpiIds({this.success, this.error});

  ChallanAutomationVerifyUserUpiIds.error(this.error);

  ChallanAutomationVerifyUserUpiIds.success(this.success);
}


class ChallanAutomationDetailArgs {
  final List<String>? noticeNumbers;
  final String? registrationNumber;
  final String? violationLocation;
  final String? phoneNumber;
  final bool? shouldRunPaymentUpdateFlow;

  ChallanAutomationDetailArgs(
      {this.noticeNumbers,
      this.registrationNumber,
      this.violationLocation,
      this.phoneNumber,
      this.shouldRunPaymentUpdateFlow});


  factory ChallanAutomationDetailArgs.fromJson(Map<String, dynamic> json) {
    return ChallanAutomationDetailArgs(
      noticeNumbers: json['violation_number'] as List<String>?,
      registrationNumber: json['registration_number'] as String?,
      violationLocation: json['violation_location'] as String?,
      phoneNumber: json['phone_number'] as String?,
    );
  }
}


class ChallanHistoryArgs {
  final List<String>? noticeNumbers;
  final String? challanAmount;
  final List<ChallanNumberData>? challanNumbers;
  final String? registrationNumber;
  final String? challanSearchSource;
  final String? referenceNumber;
  ChallanPaymentStatus? paymentStatus;
  final bool? shouldRunPaymentUpdateFlow;

  ChallanHistoryArgs(
      {this.noticeNumbers,
        this.challanAmount,
        this.registrationNumber,
        this.challanNumbers,
        this.challanSearchSource,
        this.referenceNumber,
        this.paymentStatus,
        this.shouldRunPaymentUpdateFlow});

  factory ChallanHistoryArgs.fromJson(Map<String, dynamic> map) {
    return ChallanHistoryArgs(
        challanSearchSource:  map['rto_source'] as String?,
        challanAmount: map['challan_amount'] as String?,
        noticeNumbers: map['notice_numbers'] as List<String>?,
        registrationNumber: map['registration_number'] as String?,
        challanNumbers: map['challan_numbers'] as List<ChallanNumberData>?,
        referenceNumber: map['ref_number'] as String?,
        paymentStatus: (map['payment_status'] != null) ? mapStringToPaymentStatus(map['payment_status']) : null,
        shouldRunPaymentUpdateFlow: map['should_run_payment_update_flow'] as bool?
    );
  }
}

class PaymentConfirmationContent {
  final String title;
  final String subtitle;
  final String primaryButtonText;
  final String? secondaryButtonText;

  const PaymentConfirmationContent({
    required this.title,
    required this.subtitle,
    required this.primaryButtonText,
    this.secondaryButtonText,
  });
}



  CaptchaProgressType captchaProgressTypeFromString(String? value) {
    switch (value) {
      case 'ready':
        return CaptchaProgressType.ready;
      case 'processing':
        return CaptchaProgressType.processing;
      default:
        return CaptchaProgressType.unknown;
    }
  }



class UserFlowData {
  String? nextUrlOverride;
  String? userWebviewCookie;
  bool? shouldContinueDetailsFlow;
  String? paymentReferenceNumber;
  String? userUpiId;
  bool? saveUserUpiID;
  AmountBreakupProperties? amountBreakup;

  UserFlowData({
        this.nextUrlOverride,
        this.userWebviewCookie,
        this.shouldContinueDetailsFlow,
        this.paymentReferenceNumber,
        this.userUpiId,
        this.saveUserUpiID,
        this.amountBreakup,
      });
}

class DocumentServiceResponse {
  List<DocumentCategory>? categories;
  String? id;
  String? error;

  DocumentServiceResponse({this.id, this.categories});

  DocumentServiceResponse.error(this.error);


  factory DocumentServiceResponse.fromJson(Map<String, dynamic>? json) {
    final data = json?['data'];


    final categoriesJson = data?['document_category'] as List<dynamic>?;
    final id = data?['id'] as String?;

    final categories = categoriesJson
        ?.map((item) => DocumentCategory.fromJson(item))
        .toList();

    return DocumentServiceResponse(categories: categories, id: id);
  }
}

class DocumentCategory {
  final String? documentType;
  final List<DocumentItem>? documents;

  DocumentCategory({required this.documentType, required this.documents});

  factory DocumentCategory.fromJson(Map<String, dynamic> json) {
    final docsJson = json['documents'] as List<dynamic>? ?? [];

    return DocumentCategory(
      documentType: json['document_type'] ?? '',
      documents: docsJson.map((doc) => DocumentItem.fromJson(doc)).toList(),
    );
  }
}

class DocumentItem {
  final String? id;
  final String? documentUrl;
  final bool? deleted;

  DocumentItem({
    required this.id,
    required this.documentUrl,
    required this.deleted,
  });

  factory DocumentItem.fromJson(Map<String, dynamic> json) {
    return DocumentItem(
      id: json['id'] ?? '',
      documentUrl: json['document_url'] ?? '',
      deleted: json['deleted'] ?? false,
    );
  }
}

class CaptchaCreateSduiResponse extends AckoDataDefaultResponse {
  int? taskId;
  CaptchaCreateSduiResponse({this.taskId});

  CaptchaCreateSduiResponse.fromJson(Map<String, dynamic> json) {
    taskId = json['taskId'] != null
        ? json['taskId']
        : null;
  }
}

class CaptchaResultSduiResponse extends AckoDataDefaultResponse {
  CaptchaProgressType? captchaStatus;
  String? captchaSolution;
  CaptchaResultSduiResponse({this.captchaStatus, this.captchaSolution});

  CaptchaResultSduiResponse.fromJson(Map<String, dynamic> json) {
    captchaStatus = captchaProgressTypeFromString(json['captchaStatus']);
    captchaSolution = (json['captchaSolution'] != null) ?  json['captchaSolution'] : null;
  }
}

class ErrorScreenConstants {
  String mainImage;
  String title;
  String subtitle;
  String buttonText;
  ErrorScreenConstants(
      {
        required this.mainImage,
        required this.title,
        required this.subtitle,
        required this.buttonText,
      });

}

class ChallanTransactionDetailRow {
  final String label;
  final String value;
  final Color? valueColor;
  final bool isBold;
  final bool openBottomsheet;
  final List<Map<String, String>>? detailItems;

  const ChallanTransactionDetailRow({
    required this.label,
    required this.value,
    this.valueColor,
    this.isBold = false,
    this.openBottomsheet = false,
    this.detailItems,
  });
}