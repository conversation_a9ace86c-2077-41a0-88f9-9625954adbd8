import 'package:acko_flutter/feature/challan_webview/enums/challan_webview_enums.dart';

import '../../challan/challan_enums.dart';
import '../Utility/challan_webview_utility.dart';
import 'challan_automation_misc_models.dart';

class ChallanKeywordModel {
  final KeywordResponseType responseType;
  final ErrorProps? error;
  final bool stayOnPage;
  final ModelProperties? modelProperties;

  ChallanKeywordModel({
    required this.responseType,
    this.error,
    required this.stayOnPage,
    this.modelProperties,
  });

  factory ChallanKeywordModel.fromJson(Map<String, dynamic> json) {
    final responseType = keywordResponseTypefromString(json['responseType']);
    return ChallanKeywordModel(
      responseType: responseType,
      error: json['error'] != null
          ? ErrorProps.fromJson(json['error'] as Map<String, dynamic>)
          : null,
      stayOnPage: (json['stayOnPage'] != null) ? json['stayOnPage'] : false,
      modelProperties: responseType.parseModelProperties(
          json['modelProperties'] as Map<String, dynamic>?),
    );
  }
}

class ErrorProps {
  final String errorMessage;
  final String? upiID;
  ErrorProps({
    required this.errorMessage,
    this.upiID,
  });

  factory ErrorProps.fromJson(Map<String, dynamic> json) {
    return ErrorProps(
      errorMessage: json['errorMessage'],
      upiID: (json['upiID'] != null) ? json['upiID'] : null,
    );
  }
}

abstract class ModelProperties {
  const ModelProperties();
}

class OtpFieldProperties extends ModelProperties {
  final int timer;
  final int textfieldLength;
  final String title;
  final String subtitle;
  final bool isTransactionDetailsFlow;

  OtpFieldProperties(
      {required this.timer,
      required this.textfieldLength,
      required this.title,
      required this.subtitle,
      required this.isTransactionDetailsFlow});

  factory OtpFieldProperties.fromJson(Map<String, dynamic> json) {
    return OtpFieldProperties(
      timer: json['timer'] as int,
      title: json['title'],
      subtitle: json['subtitle'],
      textfieldLength:
          (json["textfieldLength"] != null) ? json['textfieldLength'] : 5,
      isTransactionDetailsFlow: (json['isTransactionDetailsFlow'] != null)
          ? json['isTransactionDetailsFlow']
          : false,
    );
  }
}

class TextfieldProperties extends ModelProperties {
  final String title;
  final String subtitle;

  TextfieldProperties({
    required this.title,
    required this.subtitle,
  });

  factory TextfieldProperties.fromJson(Map<String, dynamic> json) {
    return TextfieldProperties(
      title: json['title'],
      subtitle: json['subtitle'],
    );
  }
}

class UpiInProgressProperties extends ModelProperties {
  final String title;
  final String? overrideUrl;
  final int timerInSeconds;

  UpiInProgressProperties({
    required this.title,
    this.overrideUrl,
    required this.timerInSeconds,
  });

  factory UpiInProgressProperties.fromJson(Map<String, dynamic> json) {
    return UpiInProgressProperties(
      title: json['title'],
      overrideUrl: (json['overrideUrl'] != null) ? json['overrideUrl'] : null,
      timerInSeconds: (json['timerInSeconds'] != null) ? json['timerInSeconds'] : 300,
    );
  }
}

class UpiCompletedProperties extends ModelProperties {
  final String title;
  final String paymentReferenceId;
  UpiCompletedProperties({
    required this.title,
    required this.paymentReferenceId,
  });

  factory UpiCompletedProperties.fromJson(Map<String, dynamic> json) {
    return UpiCompletedProperties(
      title: json['title'],
      paymentReferenceId: json['paymentReferenceId'],
    );
  }
}

class PayThroughUpiProperties extends ModelProperties {
  final String title;
  final String subtitle;
  final String? amount;
  List<String>? upiIds;

  PayThroughUpiProperties({
    required this.title,
    required this.subtitle,
    this.amount,
    this.upiIds,
  });

  factory PayThroughUpiProperties.fromJson(Map<String, dynamic> json) {
    return PayThroughUpiProperties(
      title: json['title'],
      subtitle: json['subtitle'],
      amount: (json['amount'] != null) ? json['amount'] : null,
      upiIds: json['upiIds'] != null ? List<String>.from(json['upiIds']) : null,
    );
  }
}

class SkipPageProperties extends ModelProperties {
  final SkipType skipType;
  final String? state;
  final String? email;
  final String? referenceNumber;
  final ChallanPaymentStatus? paymentStatus;
  final String? paymentImage;
  final String? challanAmount;

  SkipPageProperties({
    required this.skipType,
    this.state,
    this.email,
    this.referenceNumber,
    this.paymentStatus,
    this.paymentImage,
    this.challanAmount,
  });

  factory SkipPageProperties.fromJson(Map<String, dynamic> json) {
    final parsedSkipType = skipTypeFromString(json['skipType']);
    return SkipPageProperties(
      skipType: parsedSkipType,
      state: (json['state'] != null) ? json['state'] : null,
      email: (json['email'] != null) ? json['email'] : null,
      referenceNumber: (json['referenceNumber'] != null) ? json['referenceNumber'] : null,
      paymentStatus: (json['paymentStatus'] != null) ? mapStringToPaymentStatus(json['paymentStatus']) : null,
      paymentImage: (json['paymentImage'] != null) ? json['paymentImage'] : null,
      challanAmount: (json['challanAmount'] != null) ? json['challanAmount'] : null,
    );
  }
}

class CustomLoadingScreenProperties extends ModelProperties {
  final String title;

  CustomLoadingScreenProperties({
    required this.title,
  });

  factory CustomLoadingScreenProperties.fromJson(Map<String, dynamic> json) {
    return CustomLoadingScreenProperties(
      title: json['title'],
    );
  }
}

class TrackingLoadingScreenProperties extends ModelProperties {
  final String title;
  final List<LoadingSubtitleProperties> subtitles;

  TrackingLoadingScreenProperties(
      {required this.title, required this.subtitles});

  factory TrackingLoadingScreenProperties.fromJson(Map<String, dynamic> json) {
    return TrackingLoadingScreenProperties(
      title: json['title'],
      subtitles: (json['subtitles'] as List)
          .map((e) => LoadingSubtitleProperties.fromJson(e))
          .toList(),
    );
  }
}

class LoadingSubtitleProperties {
  final String text;
  final TrackingScreenStateType loadingState;

  LoadingSubtitleProperties({
    required this.text,
    required this.loadingState,
  });

  factory LoadingSubtitleProperties.fromJson(Map<String, dynamic> json) {
    final loadingState =
    trackingScreenStateTypeFromString(json['loadingState']);

    return LoadingSubtitleProperties(
      text: json['text'],
      loadingState: loadingState,
    );
  }
}

class AutomationLoggingProperties extends ModelProperties {
  final String message;
  final Map<String, dynamic> metadata;

  AutomationLoggingProperties({
    required this.message,
    required this.metadata,
  });

  factory AutomationLoggingProperties.fromJson(Map<String, dynamic> json) {
    return AutomationLoggingProperties(
      message: json['message'],
      metadata: json['metadata'],
    );
  }
}

class BottomsheetProperties extends ModelProperties {
  final String title;
  final String subtitle;
  final String buttonCta;
  final String? imageUrl;

  BottomsheetProperties({
    required this.title,
    required this.subtitle,
    required this.buttonCta,
    this.imageUrl,
  });

  factory BottomsheetProperties.fromJson(Map<String, dynamic> json) {
    return BottomsheetProperties(
      title: json['title'],
      subtitle: json['subtitle'],
      buttonCta: json['buttonCta'],
      imageUrl: (json['imageUrl'] != null) ? json['imageUrl'] : null,
    );
  }
}

class AmountBreakupProperties extends ModelProperties {
  final String? totalPayable;
  final Map<String, dynamic>? breakup;

  AmountBreakupProperties({
    this.totalPayable,
    this.breakup,
  });

  factory AmountBreakupProperties.fromJson(Map<String, dynamic> json) {
    final tp =  json['totalPayable']?.toString() ?? null;
    final rawBreakup = (json['breakup'] as Map<String, dynamic>?) ?? {};

    return AmountBreakupProperties(
      totalPayable: tp,
      breakup: rawBreakup,
    );
  }
}

class SolveCaptchaProperties extends ModelProperties {
  final String base64Img;
  final bool isNumeric;

  SolveCaptchaProperties({
    required this.base64Img,
    required this.isNumeric,
  });

  factory SolveCaptchaProperties.fromJson(Map<String, dynamic> json) {
    return SolveCaptchaProperties(
      base64Img: json['base64Img'],
      isNumeric: (json['numeric'] != null) ? json['numeric'] : false,
    );
  }
}

class TimerLoadingScreenProperties extends ModelProperties {
  final List<TimerLoadingMessage> messages;

  TimerLoadingScreenProperties({
    required this.messages,
  });

  factory TimerLoadingScreenProperties.fromJson(Map<String, dynamic> json) {
    var messagesJson = json['messages'] as List<dynamic>;
    List<TimerLoadingMessage> messagesList = messagesJson
        .map((message) => TimerLoadingMessage.fromJson(message))
        .toList();

    return TimerLoadingScreenProperties(messages: messagesList);
  }
}

class TimerLoadingMessage {
  final String title;
  final String subtitle;
  final int? timer;

  TimerLoadingMessage({
    required this.title,
    required this.subtitle,
    required this.timer,
  });

  factory TimerLoadingMessage.fromJson(Map<String, dynamic> json) {
    return TimerLoadingMessage(
      title: json['title'] as String,
      subtitle: json['subtitle'] as String,
      timer:  (json['timer'] != null) ? json['timer'] as int : 0,
    );
  }
}

class ScriptErrorProperties extends ModelProperties {
  final ScriptErrorType errorType;
  final String errorMessage;
  final String identifier;
  final String? url;
  ScriptErrorProperties({
    required this.errorType,
    required this.errorMessage,
    required this.identifier,
    this.url,
  });

  factory ScriptErrorProperties.fromJson(Map<String, dynamic> json) {
    final errorResponseType = scriptErrorTypeFromString(json['errorType']);
    return ScriptErrorProperties(
      errorType: errorResponseType,
      errorMessage: json['errorMessage'],
      identifier: json['identifier'],
      url: (json['url'] != null) ? json['url'] : null,
    );
  }
}

class FinalScreenProperties extends ModelProperties {
  final List<String>? noticeNumbers;
  final String? vehicleNumber;
  final ChallanPaymentStatus? paymentStatus;
  final ChallanSearchSource searchSource;
  final String? bankReferenceNumber;
  final String? paymentImage;

  FinalScreenProperties({
    this.noticeNumbers,
    this.vehicleNumber,
    this.paymentStatus,
    required this.searchSource,
    this.bankReferenceNumber,
    this.paymentImage,
  });

  factory FinalScreenProperties.fromJson(Map<String, dynamic> json) {
    final parsedSearchSource = challanSearchSourceFromString(json['searchSource']);
    final paymentStatus = mapStringToPaymentStatus(json['paymentStatus'] ?? '');
    return FinalScreenProperties(
      searchSource: parsedSearchSource,
      paymentStatus: (json['paymentStatus'] != null) ? mapStringToPaymentStatus(json['paymentStatus']) : null,
      noticeNumbers:
          (json['noticeNumbers'] != null) ? (json['noticeNumbers'] as List<dynamic>?)
              ?.cast<String>() : null,
      vehicleNumber:
          (json['vehicleNumber'] != null) ? json['vehicleNumber'] : null,
      bankReferenceNumber: (json['bankReferenceNumber'] != null) ? json['bankReferenceNumber'] : null,
      paymentImage: (json['paymentImage'] != null) ? json['paymentImage'] : null,
    );
  }
}

extension ResponseTypeToModelProperties on KeywordResponseType {
  ModelProperties? parseModelProperties(Map<String, dynamic>? json) {
    if (json == null) return null;
    switch (this) {
      case KeywordResponseType.otp_field:
        return OtpFieldProperties.fromJson(json);
      case KeywordResponseType.textfield:
        return TextfieldProperties.fromJson(json);
      case KeywordResponseType.upi_in_progress:
        return UpiInProgressProperties.fromJson(json);
      case KeywordResponseType.upi_completed:
        return UpiCompletedProperties.fromJson(json);
      case KeywordResponseType.final_screen:
        return FinalScreenProperties.fromJson(json);
      case KeywordResponseType.pay_through_upi:
        return PayThroughUpiProperties.fromJson(json);
      case KeywordResponseType.skip_page:
        return SkipPageProperties.fromJson(json);
      case KeywordResponseType.custom_loading_screen:
        return CustomLoadingScreenProperties.fromJson(json);
      case KeywordResponseType.tracking_loading_screen:
        return TrackingLoadingScreenProperties.fromJson(json);
      case KeywordResponseType.automation_logging:
        return AutomationLoggingProperties.fromJson(json);
      case KeywordResponseType.solve_captcha:
        return SolveCaptchaProperties.fromJson(json);
      case KeywordResponseType.script_error:
        return ScriptErrorProperties.fromJson(json);
      case KeywordResponseType.timer_loading_screen:
        return TimerLoadingScreenProperties.fromJson(json);
      case KeywordResponseType.open_bottomsheet:
        return BottomsheetProperties.fromJson(json);
      case KeywordResponseType.save_amount_breakup:
        return AmountBreakupProperties.fromJson(json);
      default:
        return null;
    }
  }
}

KeywordResponseType keywordResponseTypefromString(String value) {
    switch (value) {
      case 'textfield':
        return KeywordResponseType.textfield;
      case 'otp_field':
        return KeywordResponseType.otp_field;
      case 'upi_in_progress':
        return KeywordResponseType.upi_in_progress;
      case 'upi_completed':
        return KeywordResponseType.upi_completed;
      case 'final_screen':
        return KeywordResponseType.final_screen;
      case 'error':
        return KeywordResponseType.error;
      case 'pay_through_upi':
        return KeywordResponseType.pay_through_upi;
      case 'skip_page':
        return KeywordResponseType.skip_page;
      case 'custom_loading_screen':
        return KeywordResponseType.custom_loading_screen;
      case 'tracking_loading_screen':
        return KeywordResponseType.tracking_loading_screen;
      case 'automation_logging':
        return KeywordResponseType.automation_logging;
      case 'solve_captcha':
        return KeywordResponseType.solve_captcha;
      case 'script_error':
        return KeywordResponseType.script_error;
      case 'timer_loading_screen':
        return KeywordResponseType.timer_loading_screen;
      case 'open_bottomsheet':
        return KeywordResponseType.open_bottomsheet;
      case 'save_amount_breakup':
        return KeywordResponseType.save_amount_breakup;
      default:
        return KeywordResponseType.textfield;
    }
  }



  SkipType skipTypeFromString(String value) {
    switch (value) {
      case 'skip_to_ka_payment':
        return SkipType.skip_to_ka_payment;
      case 'skip_to_transaction_details_page':
        return SkipType.skip_to_transaction_details_page;
      case 'start_cron_job':
        return SkipType.start_cron_job;
      case 'save_upi_id':
        return SkipType.save_upi_id;
      case 'save_reference_number':
        return SkipType.save_reference_number;
      case 'update_payment_details':
        return SkipType.update_payment_details;
      default:
        return SkipType.no_skip;
    }
  }


  ChallanSearchSource challanSearchSourceFromString(String value) {
    switch (value) {
      case 'KA':
        return ChallanSearchSource.KA;
      default:
        return ChallanSearchSource.TS;
    }
  }



ScriptErrorType scriptErrorTypeFromString(String value) {
  switch (value) {
    case 'reload_page':
      return ScriptErrorType.reload_page;
    case 'navigate_to_home':
      return ScriptErrorType.navigate_to_home;
    default:
      return ScriptErrorType.navigate_to_home;
  }
}

  TrackingScreenStateType trackingScreenStateTypeFromString(String value) {
    switch (value) {
      case 'loaded':
        return TrackingScreenStateType.loaded;
      case 'loading':
        return TrackingScreenStateType.loading;
      default:
        return TrackingScreenStateType.not_started;
    }
  }


