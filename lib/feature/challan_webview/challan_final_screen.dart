import 'package:acko_flutter/common/util/strings.dart';
import 'package:design_module/typography/typography.dart';
import 'package:design_module/uikit/widgets/button/acko_button.dart';
import 'package:design_module/uikit/widgets/button/uikit_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:lottie/lottie.dart';
import 'package:utilities/constants/constants.dart';

import '../challan/challan_enums.dart';
import 'Utility/challan_webview_utility.dart';
import 'challan_web_view_bloc.dart';
import 'enums/challan_webview_enums.dart';
import 'models/challan_automation_misc_models.dart';
import 'models/challan_keyword_model.dart';

class ChallanFinalScreen extends StatefulWidget {
  final Function(HelperScript) helperScript;
  final ChallanKeywordModel challanKeywordModel;
  final FinalScreenProperties finalScreenProps;

  const ChallanFinalScreen({
    Key? key,
    required this.helperScript,
    required this.challanKeywordModel,
    required this.finalScreenProps,
  }) : super(key: key);

  @override
  _ChallanFinalScreenState createState() => _ChallanFinalScreenState();
}

class _ChallanFinalScreenState extends State<ChallanFinalScreen> {
  ChallanWebViewBloc? _bloc;
  bool isButtonActive = false;

  TransactionStatusModel? displayData;

  @override
  void initState() {
    super.initState();
    _bloc = BlocProvider.of(context);
    if (widget.finalScreenProps.searchSource == ChallanSearchSource.KA) {
      _bloc?.pollForKaResponse(widget.finalScreenProps.searchSource);
    } else {
      _bloc?.checkChallanPaymentStatus(widget.finalScreenProps);
    }
  }

  void _updateButtonAction(ChallanPaymentStatus status) {
    bool shouldActivate = (status == ChallanPaymentStatus.PAYMENT_SUCCESS ||
        status == ChallanPaymentStatus.PAYMENT_FAILED);
    setState(() {
      isButtonActive = shouldActivate;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: BlocConsumer<ChallanWebViewBloc, ChallanWebViewStates>(
        listenWhen: (prev, current) =>
        current is ChallanResultsError || current is ChallanResultsLoaded || current is ChallanResultsLoading,
        listener: (context, state) {
          if (state is ChallanResultsError) {
            Fluttertoast.showToast(
                msg: challanAutomationConstants.statusCouldNotBeRefereshed);
          } else if (state is ChallanResultsLoaded) {
            _updateButtonAction(state.paymentStatus);
            sendChallanAutomationAnalyticEvent(
                model: widget.challanKeywordModel,
                userInput: state.paymentStatus.name,
                source: widget.finalScreenProps.searchSource.name);
          }
        },
        builder: (context, state) {
          if (state is ChallanResultsLoaded) {
            displayData = state.transactionModel;
            return Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Flexible(
                  flex: 1,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      displayData!.transactionImage.endsWith('.json')
                          ? Lottie.network(
                              displayData!.transactionImage,
                              height: 120,
                              width: 120,
                            )
                          : Image.network(
                              displayData!.transactionImage,
                              height: 120,
                              width: 120,
                            ),
                      SizedBox(height: 16),
                      hMediumText.text(displayData!.title),
                      SizedBox(height: 16),
                       pSmallText.text(
                          displayData!.subtitle,
                              textAlign: TextAlign.center)

                    ],
                  ),
                ),
                AckoDarkButtonFullWidth(
                    buttonState: isButtonActive
                        ? UIKitButtonState.active
                        : UIKitButtonState.disabled,
                    text: viewDetails,
                    onTap: () {
                      Navigator.pushReplacementNamed(
                        context,
                        Routes.CHALLAN_TRANSACTION_PAYMENT_DETAILS,
                        arguments: {
                          'challanHistoryArgs': ChallanHistoryArgs(
                            noticeNumbers: widget.finalScreenProps.noticeNumbers,
                            registrationNumber: widget.finalScreenProps.vehicleNumber,
                            challanSearchSource: _bloc?.searchSource,
                          )
                        },
                      );
                    })
              ],
            );
          } else {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Lottie.asset('assets/anim/updated_loading_anim.json',
                      height: 120, width: 120),
                  SizedBox(
                    height: 12,
                  ),
                  hSmallText.text(challanAutomationConstants.processingYourPayment),
                  SizedBox(
                    height: 4,
                  ),
                  pSmallText.text(challanAutomationConstants.pleaseDoNotGoBackOrCloseApp),
                ],
              ),
            );
          }
        },
      ),
    );
  }
}
