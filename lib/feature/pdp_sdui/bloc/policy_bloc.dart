import 'package:flutter/material.dart';
import 'package:networking_module/util/etag.dart';
import 'package:sdui/sdui.dart';
import 'package:sdui/src/core/extensions/string_extensions.dart';
import 'package:sdui/src/core/parsers/pdp_parser/pdp_parser.dart';
import 'package:utilities/connection_status/connection_status.dart';
import 'package:utilities/core/map_extensions.dart';
import 'package:utilities/widgets/acko_safe_cubit.dart';

part 'policy_state.dart';

class PolicyBloc extends AckoSafeCubit<PolicyState> {
  PdpParser pdpParser = PdpParser();
  final BaseRepository _baseRepository = BaseRepository();
  Map queryParams;
  List<Widget> pdpWidgets = [];

  PolicyBloc(this.queryParams) : super(PolicyInitial()) {
    _getPolicyView();
  }

  refresh() {
    emit(PolicyInitial());
    _getPolicyView();
  }

  _getPolicyView({String? etag, bool refreshCache = false}) async {
    if ((queryParams['policyId']??"").toString().isNullOrEmpty) {
      emit(PolicyError("policyId not provided"));
      return;
    }
    final currentState = state;
    if (currentState is PolicyInitial) emit(PolicyLoading());
    /// call repository and get json
    ResponseWrapper response = await _baseRepository.getResponse(
        "/policy/detailPage?${queryParams.mapToQueryString()}",
        etag: etag,
        refreshCache: refreshCache);

    if (response.error == null) {
      /// create widget here in the bloc itself and pass it to UI for rendering.
      if (response.data != null && response.data!['widgets'] != null) {
        pdpWidgets = pdpParser.fromJson(response.data!);
        List<SDUIAction> pdpActions = pdpParser.parseActionsFromJson(response.data!);

        emit(PolicyLoaded(pdpWidgets));
        if(pdpActions.isNotEmpty){
          emit(PolicyActionsLoaded(pdpActions));
        }
      } else {
        emit(PolicyError("An error occurred in sdui repository"));
      }
    } else {
      emit(PolicyError("An error occurred in sdui repository"));
    }
    fetchCacheRefresh(response);
  }

  Future<void> fetchCacheRefresh(ResponseWrapper response) async {
    if (await ConnectionStatus.instance.isNetworkAvaialable() &&
        response.statusCode == 304 &&
        response.fromCache!) {
      final etag = createEtag(response.data);
      _getPolicyView(refreshCache: true, etag: etag);
    } else {
      return;
    }
  }
}
