import 'package:acko_core_utilities/acko_core_utilities.dart';
import 'package:acko_flutter/common/model/drawer_item.dart';
import 'package:acko_flutter/common/util/AckoTextStyle.dart';
import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/view/AckoText.dart';
import 'package:acko_flutter/common/view/acko_text_config.dart';
import 'package:acko_flutter/feature/car_journey/view/icon_app_bar.dart';
import 'package:acko_flutter/feature/home_view_util.dart';
import 'package:acko_flutter/feature/profile/bloc/UserProfileBloc.dart';
import 'package:acko_flutter/feature/profile/model/edit_detail_callback.dart';
import 'package:acko_flutter/feature/profile_completion/util/profile_completion_util.dart';
import 'package:acko_flutter/r2d2/events.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_flutter/util/health/health_constants.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/card_loaded_events.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:analytics/events/tap_events.dart';
import 'package:design_module/design_module.dart';
import 'package:design_module/utilities/hybrid_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/remote_config/remote_config.dart';
import 'package:utilities/state_provider/StateProvider.dart';

import '../../../common/util/strings.dart';
import '../../../common/widgets/acko_circular_progress_indicator.dart';
import '../../../util/deeplink/next_url_handling_mixin.dart';
import '../../profile_completion/bloc/bloc_singleton_instance.dart';
import '../../profile_completion/bloc/profile_completion_bloc.dart';
import '../../tab_view_sdui/constants/home_tab_constants.dart';
import '../bloc/ProfileUtil.dart';

class ProfilePage extends StatefulWidget {
  final Function(EditDetails? details)? updateProfileDataCallback;
  final Map<dynamic, dynamic>? params;

  const ProfilePage({Key? key, this.updateProfileDataCallback, this.params})
      : super(key: key);

  @override
  _ProfilePageState createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> with NextUrlHandlingMixin {
  late UserProfileBloc userProfileBlock;
  StateProvider _stateProvider = StateProvider();
  ProfileCompletionBloc? profileCompletionBloc;

  @override
  void initState() {
    userProfileBlock = BlocProvider.of(context);
    profileCompletionBloc =
        ProfileCompeltionBlocSingletonInstance.instance.blocInstance;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      handleNextUrlNavigation(widget.params);
    });
    super.initState();
    AnalyticsTrackerManager.instance.sendEvent(
        event: CardLoadedConstants.USER_PROFILE_COMPLETION_LEVEL_PERCENTAGE,
        properties: {
          'platform': Util.getPlatform(),
          'count': profileCompletionBloc
              ?.profileCompletionModel?.completionPercentage,
          'from_page': 'main_profile',
          'addons': ProfileCompletionUtil()
              .getAddOns(profileCompletionBloc?.profileCompletionModel)
        });
    AnalyticsTrackerManager.instance.sendEvent(
        event: PageLoadedConstants.USER_PROFILE_MAIN_SCREEN,
        properties: {'platform': Util.getPlatform()});
  }

  @override
  void dispose() {
    if (widget.updateProfileDataCallback != null) {
      widget.updateProfileDataCallback!(userProfileBlock.userDetails);
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    debugPrint("profile page");

    return BlocConsumer<UserProfileBloc, UserProfileState>(
      listenWhen: (_, state) {
        return state is UserLogoutSate || state is ErrorState;
      },
      listener: (context, state) {
        if (state is ErrorState) {
          showSnackBar(state.errorResponse);
        }
        if (state is UserLogoutSate)
          Navigator.pushNamedAndRemoveUntil(
              context, Routes.LOGIN_LANDING_PAGE, (route) => false,
              arguments: {"is_user_logged_out": true});
      },
      builder: (context, state) {
        return _getProfileWidget(state);
      },
    );
  }

//TODO add logout button
  Widget _getProfileWidget(UserProfileState state) {
    List<ProfileData> items = ProfileUtil()
        .getProfileItemData(isGMCLinkBannerEnabled: userProfileBlock.isAppV10, gmcLinkUrl: userProfileBlock.gmcLinkUrl);
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(leading: IconButton(onPressed: _onBackPressed, icon: Icon(Icons.arrow_back_ios_new_rounded, size: 18,)),backgroundColor: colorFFFFFF,),
        body:  Padding(
          padding: const EdgeInsets.only(
            top: 20.0,
          ),
          child: ListView.builder(
              shrinkWrap: true,
              itemBuilder: (context, position) {
                return _getProfileItems(items[position], state);
              },
              itemCount: items.length),
        ),
      ),
    );
  }

  Future<void> _onBackPressed() async {
     var isPop = await Navigator.maybePop(context);
    if (!isPop) {
      Navigator.of(context, rootNavigator: true).pop();
    }
  }

  Padding _getSeparator() {
    return Padding(
        padding: EdgeInsets.symmetric(vertical: 16.0, horizontal: 20.0),
        child: SizedBox.shrink());
  }

  Widget _getUserDetails(EditDetails? userDetails) {
    if (userDetails != null) {
      if (userDetails.name == null || userDetails.name == "") {
        return TextEuclidSemiBold(
          userDetails.phoneNumber!,
          textColor: color040222,
          textSize: 22.0,
        );
      } else {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Padding(
              padding: EdgeInsets.only(bottom: 5.0),
              child: AckoTextConfig.i.headingSmall.text(
                userDetails.name!.toCapitalCase(),
                textColor: color040222,
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if(userDetails.phoneNumber.isNotNullOrEmpty)
            AckoTextConfig.i.paragraphMedium.text(
              userDetails.phoneNumber!,
              textColor: color5B5675,
            ),
            const SizedBox(height: 8,),
            GestureDetector(
              onTap: () {
                Navigator.pushNamed(
                  context,
                  Routes.PROFILE_COMPLETION,
                );
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  AckoTextConfig.i.labelSmall.text(
                    view_your_profile,
                    textColor: color1B73E8,
                  ),
                  Icon(Icons.navigate_next_rounded,
                      color: color1B73E8, size: 24),
                ],
              ),
            )
          ],
        );
      }
    } else {
      return Container();
    }
  }

  Widget _getNewHeader() {
    return BlocBuilder<ProfileCompletionBloc, ProfileCompletionState>(
      bloc: profileCompletionBloc,
      builder: (_, state) => Container(
        margin: const EdgeInsets.fromLTRB(20, 0, 20, 40),
        padding: const EdgeInsets.symmetric(vertical: 24),
        decoration: BoxDecoration(
          border: Border.all(color: colorE4EAF1, width: 1.0),
          color: colorFFFFFF,
          boxShadow: [
            BoxShadow(
              color: color706F87.withOpacity(0.2),
              offset: Offset(0, 0),
              blurRadius: 8,
              spreadRadius: -2,
            ),
          ],
          borderRadius: BorderRadius.circular(16.0),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Stack(alignment: AlignmentDirectional.bottomCenter, children: [
              InkWell(
                  onTap: () {
                    Navigator.pushNamed(
                      context,
                      Routes.PROFILE_COMPLETION,
                    );
                  },
                  child: Padding(
                    padding: EdgeInsets.only(top: 4.0),
                    child: SizedBox(
                      height: getScreenAwareHeight(75),
                      child: Stack(
                        alignment: AlignmentDirectional.center,
                        children: [
                          Hero(
                            tag: 'profileImage',
                            child: SizedBox(
                              height: getScreenAwareHeight(70),
                              width: getScreenAwareWidth(70),
                              child: AckoCircularProgressIndicator(
                                strokeWidth: 8,
                                strokeCap: StrokeCap.round,
                                backgroundColor: colorA9DDFE,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.black),
                                value: (profileCompletionBloc
                                            ?.profileCompletionModel
                                            ?.completionPercentage ??
                                        0) /
                                    100,
                              ),
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.all(getScreenAwareHeight(10)),
                            child: Container(
                              alignment: Alignment.center,
                              child: ((profileCompletionBloc
                                          ?.userName.isNullOrEmpty ==
                                      true))
                                  ? SvgPicture.asset(Util.getAssetImage(
                                      assetName: 'ic_gender_generic.svg'))
                                  : TextEuclidBoldL18(
                                      _getUserNameInitials(
                                          '${profileCompletionBloc?.profileCompletionModel?.firstName} ${profileCompletionBloc?.profileCompletionModel?.lastName}'),
                                    ),
                              decoration: BoxDecoration(
                                  shape: BoxShape.circle, color: colorE2F5FF),
                            ),
                          )
                        ],
                      ),
                    ),
                  )),
              InkWell(
                onTap: () {
                  Navigator.pushNamed(
                    context,
                    Routes.PROFILE_COMPLETION,
                  );
                },
                child: Align(
                  heightFactor: 0.75,
                  child: Container(
                    padding: EdgeInsets.symmetric(
                        vertical: getScreenAwareHeight(4),
                        horizontal: getScreenAwareHeight(8)),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(62),
                        color: color040222),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        TextEuclidBoldL18(
                          '${profileCompletionBloc?.profileCompletionModel?.completionPercentage ?? 0}%',
                          textColor: colorFFFFFF,
                          textSize: 12.0,
                        ),
                        Padding(
                          padding:
                              EdgeInsets.only(left: getScreenAwareWidth(3)),
                          child: Icon(
                            Icons.arrow_forward_ios,
                            color: colorFFFFFF,
                            size: 12,
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              )
            ]),
            Padding(
              padding: EdgeInsets.only(top: getScreenAwareHeight(12)),
              child:
                  _getUserDetails(profileCompletionBloc?.getUserProfileData()),
            ),
          ],
        ),
      ),
    );
  }

  void openPersonalDetails() async {
    EditDetails? profileData = profileCompletionBloc?.getUserProfileData();
    if (profileData != null) {
      R2D2Events.instance.trackPersonalDetailClicked(profileData);
      var result = await Navigator.pushNamed(context, Routes.PERSONAL_DETAILS,
          arguments: {
            'user_details': profileData,
          });
      if (result is EditDetailsCallBack) {
        setState(() {
          userProfileBlock.userDetails = profileData;
        });
        Future.delayed(Duration(milliseconds: 30), () {
          showSnackBar(result.message!);
          _stateProvider.notify(ObserverState.NAME_UPDATE, data: profileData);
        });
      }
    } else {
      showSnackBar(something_went_wrong);
    }
  }

  void _openWhatsAppPreference() async {
    if (userProfileBlock.userDetails != null) {
      var result = await Navigator.pushNamed(context, Routes.WHATSAPP_OPT_IN,
          arguments: {
            'phone': userProfileBlock.userDetails!.phoneNumber,
            'is_whatsapp_true': userProfileBlock.userDetails!.whatsappOptIn,
          });
      if (result is EditDetailsCallBack) {
        showSnackBar(result.message!);
        setState(() {
          userProfileBlock.userDetails = result.userDetails;
        });
      }
    }
  }

  Widget _getProfileItems(ProfileData profileData, UserProfileState state) {
    switch (profileData.viewType) {
      case ViewType.HEADER:
        return _getNewHeader();
      case ViewType.ITEM:
        return _getItem(profileData);
      case ViewType.LOGOUT:
        return getVersion(state);
      case ViewType.DIVIDER:
        return _getSeparator();
    }
  }

  Widget _getItem(ProfileData profileData) {
    return InkWell(
      onTap: () async {
        AnalyticsTrackerManager.instance.sendEvent(
            event: ProfileUtil.getSegmentEvent(profileData.title),
            properties: {'type': profileData.title});
        profileData.analytics?.call(); /// Custom analytics
        if (profileData.deeplink.isNotNullOrEmpty) {
          var map = profileData.deeplink!.getRoute();
          if (map != null) {
            Navigator.pushNamed(
              context,
              map["route"],
              arguments: map,
            );
          }
        } else {
          switch (profileData.route) {
            case Routes.PERSONAL_DETAILS:
              {
                openPersonalDetails();
                break;
              }
            case Routes.WHATSAPP_OPT_IN:
              {
                if (userProfileBlock.userDetails != null) {
                  R2D2Events.instance.trackWhatsappClicked(
                      userProfileBlock.userDetails!.whatsappOptIn);
                }
                _openWhatsAppPreference();
                break;
              }
            case Routes.SUPPORT:
              Navigator.of(context)
                  .popUntil(ModalRoute.withName(Routes.APP_HOME));
              _stateProvider
                  .notify(ObserverState.CHANGE_TAB, data: {"index": 3, "tab": HomeTabs.SUPPORT});
              break;
            case Routes.PAYOUT_PREFERENCE:
              {
                R2D2Events.instance.trackPayoutPreferenceEvents(
                    event: 'payout_preference_clicked');
                _openPayoutPreference();
                break;
              }
            case Routes.KYC_INFORMATION:
              Navigator.pushNamed(context, Routes.KYC_INFORMATION);
              break;
            case Routes.WEB_PAGE:
              {
                if(profileData.url.isNullOrEmpty) {
                  showSnackBar(something_went_wrong);
                } else {
                  Navigator.pushNamed(context, Routes.WEB_PAGE,
                      arguments: {"url": profileData.url});
                }
                break;
              }
            case Routes.BOTTOM_SHEET:
              {
                _handleBottomSheet();
                break;
              }
            case Routes.MANAGE_ACCOUNT_BOTTOMSHEET:
              {
                _handleManageAccountBottomSheet();
                break;
              }
            case Routes.YOU_AND_YOUR_FAMILY:
              if (mounted) {
                bool useV10Design = (await RemoteConfigInstance.instance.getGbAsyncData(RemoteConfigKeysSet.APP_IA_VERSION)).toString().equalsIgnoreCase("app_v10");
                if(useV10Design){
                  Navigator.pushNamed(context, Routes.ASSET_TAB_VIEW, arguments: {"showBackButton": true, "defaultTab": 1});
                }else{
                  Navigator.pushNamed(context, Routes.ASSET_POLICY_HEALTH);
                }
              }
              break;
            case Routes.MANAGE_ASSETS:
              if (mounted) {
                  Navigator.pushNamed(context, Routes.SDUI_ASSET_DETAIL_PAGE);
              }
              break;
            default:
              {
                handleDefaultRouting(profileData);
              }
          }
        }
      },
      child: Padding(
        padding: const EdgeInsets.only(
          left: 20.0,
          right: 20.0,
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
                margin: EdgeInsets.only(
                  right: 16.0,
                ),
                child: SvgPicture.asset(
                  Util.getAssetImage(assetName: profileData.image),
                )),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AckoTextConfig.i.paragraphMedium
                      .text(profileData.title!, textColor: color040222)
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(right: 6.0, left: 10.0),
              child: SvgPicture.asset('assets/images/ic_right_arrow.svg'),
            ),
          ],
        ),
      ),
    );
  }

  void handleDefaultRouting(ProfileData profileData) {
    if (profileData.route != null) {
      if (profileData.route == Routes.PROFILE_ADDRESS ||
          profileData.route == Routes.MANAGE_ASSETS ||
          profileData.route == Routes.MANAGE_PROFILE_MEMBERS)
        AnalyticsTrackerManager.instance.sendEvent(
            event: TapConstants.TAP_BTN_COMPLETE_PROFILE_ADD_DATA_INITIATE,
            properties: {
              'platform': Util.getPlatform(),
              'type': profileData.title == 'Addresses'
                  ? 'address'
                  : profileData.title?.toLowerCase(),
              'from_page': 'main_profile',
            });
      if (context.mounted) {
        Navigator.pushNamed(context, profileData.route!,
            arguments: (profileData.route == Routes.MANAGE_ASSETS)
                ? {'source': 'profile_completion'}
                : (profileData.route == Routes.ABHA_LANDING_PAGE)
                    ? {'from_page': 'profile'}
                    : null);
      }
    }
  }

  _handleManageAccountBottomSheet() {
    showModalBottomSheet(
        barrierColor: color040222.withOpacity(0.7),
        isScrollControlled: true,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(24.0))),
        context: context,
        builder: (BuildContext bc) {
          return SingleChildScrollView(
            child: Padding(
              padding:
                  const EdgeInsets.symmetric(vertical: 30.0, horizontal: 24.0),
              child: Column(
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      TextEuclidBoldL24(manage_account),
                      Spacer(),
                    ],
                  ),
                  SizedBox(
                    height: 20,
                  ),
                  InkWell(
                    onTap: () {
                      Navigator.pushNamed(context, Routes.DEACTIVATE_ACCOUNT);
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        SvgPicture.asset(
                          Util.getAssetImage(
                              assetName: 'ic_deactivate_account_icon.svg'),
                        ),
                        TextEuclidRegular(deactivate_account,
                            textSize: 16.0, textColor: color040222),
                        Spacer(),
                        Icon(
                          Icons.navigate_next,
                          color: color5B5675,
                        )
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 18),
                    child: Divider(
                        thickness: 1.0, height: 1.0, color: colorE4EAF1),
                  ),
                  InkWell(
                    onTap: () {
                      AnalyticsTrackerManager.instance.sendEvent(
                          event: TapConstants.TAP_BTN_LOGOUT,
                          properties: {'platform': Util.getPlatform()});
                      userProfileBlock.getLogout();
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        SvgPicture.asset(
                          Util.getAssetImage(
                              assetName: 'ic_logout_account_icon.svg'),
                        ),
                        TextEuclidRegular(log_out,
                            textSize: 16.0, textColor: color040222),
                        Spacer(),
                        Icon(
                          Icons.navigate_next,
                          color: color5B5675,
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        });
  }

  _handleBottomSheet() {
    List<Map<String, String>> ackoStoryBottomSheetData = [
      {
        'title': acko_general_insurance,
        'uri': Constants.BASE_URL + ApiPath.ABOUT_US
      },
      {
        'title': acko_life_insurance,
        'uri': Constants.BASE_URL + ApiPath.ABOUT_US_LIFE
      }
    ];

    showModalBottomSheet(
        barrierColor: color040222.withOpacity(0.7),
        isScrollControlled: true,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(24.0))),
        context: context,
        builder: (BuildContext bc) {
          return SingleChildScrollView(
            child: Padding(
              padding:
                  const EdgeInsets.symmetric(vertical: 30.0, horizontal: 24.0),
              child: Column(
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      TextEuclidBoldL18(the_acko_story),
                      Spacer(),
                      InkWell(
                        key: Key('bottomSheet_close'),
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: Padding(
                          padding: const EdgeInsets.all(10.0),
                          child: SvgPicture.asset(Util.getAssetImage(
                              assetName: 'ic_black_close.svg')),
                        ),
                      ),
                    ],
                  ),
                  ListView.separated(
                    padding: const EdgeInsets.only(top: 24),
                    shrinkWrap: true,
                    itemCount: ackoStoryBottomSheetData.length,
                    itemBuilder: (context, index) {
                      return InkWell(
                        onTap: () {
                          Navigator.pushNamed(context, Routes.WEB_PAGE,
                              arguments: {
                                "url": ackoStoryBottomSheetData[index]['uri']
                              });
                        },
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            TextEuclidRegular(
                                ackoStoryBottomSheetData[index]['title']!,
                                textSize: 16.0,
                                textColor: color040222),
                            Icon(
                              Icons.navigate_next,
                              color: color5B5675,
                            )
                          ],
                        ),
                      );
                    },
                    separatorBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 18),
                        child: Divider(
                            thickness: 1.0, height: 1.0, color: colorE4EAF1),
                      );
                    },
                  )
                ],
              ),
            ),
          );
        });
  }

  List<Widget> _getConfirmationView() {
    List<Widget> widgets = [
      SvgPicture.asset(Util.getAssetImage(assetName: 'ic_attention.svg')),
      SizedBox(
        height: 20.0,
      ),
      TextEuclidSemiBold16(
        want_to_delete_account,
        textColor: color040222,
        textAlign: TextAlign.center,
      ),
      SizedBox(
        height: 12.0,
      ),
      TextEuclidMedium14(
        delete_account_message,
        textAlign: TextAlign.center,
        textColor: color040222,
      )
    ];

    return widgets;
  }

  _openPayoutPreference() async {
    var result = await Navigator.pushNamed(
      context,
      Routes.PAYOUT_PREFERENCE,
    );
    if (result is EditDetailsCallBack) {
      showSnackBar(result.message!);
    }
  }

  Widget getVersion(UserProfileState state) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(20.0, 86.0, 20.0, 20.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 16),
            child: HomeViewUtil.sharedInstance.showServiceTerm(context),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                TextEuclidRegular(
                  "Version ${Constants.APP_VERSION}",
                  textAlign: TextAlign.center,
                  textColor: color5B5675,
                ),
                TextEuclidRegular(
                  userProfileBlock.lastVisitTime != null
                      ? "  |  " + userProfileBlock.lastVisitTime!
                      : "",
                  textAlign: TextAlign.center,
                  textColor: color5B5675,
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  void showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
        backgroundColor: Colors.black,
        behavior: SnackBarBehavior.floating,
        padding: EdgeInsets.all(12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0)),
        content: Text(
          message,
          style: TextStyleInterRegular(
              color: colorFFFFFF, fontWeight: FontWeight.w400, fontSize: 14),
        )));
  }

  getImage(double height, String image, Color bgColor, double imageHeight) {
    return Container(
      height: height,
      width: height,
      decoration: BoxDecoration(shape: BoxShape.circle, color: bgColor),
      child: Padding(
        padding: EdgeInsets.all(6.0),
        child: SvgPicture.asset(
          image,
          height: imageHeight,
          width: imageHeight,
        ),
      ),
    );
  }

  String _getUserNameInitials(String? userName) {
    String _name = userName ?? "";
    if (_name.isEmpty) return 'M';
    String _initials = '';
    List<String> words = [];
    _name.split(' ').forEach((element) {
      if (element.trim().isNotEmpty) words.add(element[0].toUpperCase());
    });
    if (words.length > 1) {
      _initials +=
          _name[0].toUpperCase() + words[words.length - 1][0].toUpperCase();
    } else {
      _initials += _name[0].toUpperCase();
    }
    return _initials;
  }
}
