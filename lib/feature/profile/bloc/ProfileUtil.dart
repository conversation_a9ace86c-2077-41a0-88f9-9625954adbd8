import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/util/health/health_constants.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/tap_events.dart';
import 'package:utilities/constants/constants.dart';

class ProfileUtil {
  List<ProfileData> getProfileItemData({bool isGMCLinkBannerEnabled = false, String? gmcLinkUrl}) {
    List<ProfileData> items = [];
    items.add(ProfileData(
      viewType: ViewType.HEADER,
    ));
    if (isGMCLinkBannerEnabled) {
      items.add(ProfileData(
          viewType: ViewType.ITEM,
          image: 'ic_link_gmc.svg',
          title: acko_corporate_policy,
          route: Routes.WEB_PAGE,
          url: gmcLinkUrl,
          analytics: () {
            AnalyticsTrackerManager.instance.sendEvent(
              event: TapConstants.TAP_BTN_HEALTH_GMC_LINK_CARD,
              properties: {
                'journey': 'health_gmc',
                'product': 'health',
              },
            );
            AnalyticsTrackerManager.instance.sendEvent(
              event: TapConstants.GMC_LINK_CARD_CLICKED,
              properties: {
                'user_flow': "main_profile",
                'tab': "main_profile",
                'product': 'health',
              },
            );
          }
          ));
      items.add(ProfileData(
        viewType: ViewType.DIVIDER,
      ));
    }
    items.add(ProfileData(
        viewType: ViewType.ITEM,
        image: 'ic_personal_details.svg',
        title: you_and_your_family,
        route: Routes.YOU_AND_YOUR_FAMILY));
    items.add(ProfileData(
      viewType: ViewType.DIVIDER,
    ));
    items.add(ProfileData(
        viewType: ViewType.ITEM,
        image: 'ic_profile_assets_route.svg',
        title: assets,
        route: Routes.MANAGE_ASSETS));
    items.add(ProfileData(
      viewType: ViewType.DIVIDER,
    ));
    items.add(ProfileData(
        viewType: ViewType.ITEM,
        image: 'ic_profile_address.svg',
        title: add_your_address,
        route: Routes.PROFILE_ADDRESS));
    items.add(ProfileData(
      viewType: ViewType.DIVIDER,
    ));
    items.add(ProfileData(
        viewType: ViewType.ITEM,
        url: Constants.BASE_URL + ApiPath.TERMS_OF_USE,
        image: 'ic_debit_card.svg',
        title: 'Your ABHA cards',
        route: Routes.ABHA_LANDING_PAGE));
    items.add(ProfileData(
      viewType: ViewType.DIVIDER,
    ));
    items.add(ProfileData(
        viewType: ViewType.ITEM,
        image: 'ic_payout_preference.svg',
        title: payout_methods,
        route: Routes.PAYOUT_PREFERENCE));
    items.add(ProfileData(
      viewType: ViewType.DIVIDER,
    ));
    items.add(ProfileData(
        viewType: ViewType.ITEM,
        image: 'ic_kyc_information.svg',
        title: kyc_information,
        route: Routes.KYC_INFORMATION));
    items.add(ProfileData(
      viewType: ViewType.DIVIDER,
    ));
    items.add(ProfileData(
        viewType: ViewType.ITEM,
        image: 'ic_debit_card.svg',
        title: saved_cards,
        route: Routes.SAVED_CARD));
    items.add(ProfileData(
      viewType: ViewType.DIVIDER,
    ));
    items.add(ProfileData(
        viewType: ViewType.ITEM,
        image: 'ic_notification.svg',
        title: notifications,
        route: Routes.WHATSAPP_OPT_IN));
    items.add(ProfileData(
      viewType: ViewType.DIVIDER,
    ));
    // items.add(ProfileData(
    //     viewType: ViewType.ITEM,
    //     image: 'policy_transfer.svg',
    //     title: profile_ownership_transfer,
    //     route: Routes.TRANSFER_OWNERSHIP));
    // items.add(ProfileData(
    //   viewType: ViewType.DIVIDER,
    // ));
    items.add(ProfileData(
        viewType: ViewType.ITEM,
        image: 'ic_profile_support.svg',
        title: support,
        route: Routes.SUPPORT));
    items.add(ProfileData(
      viewType: ViewType.DIVIDER,
    ));
    items.add(ProfileData(
        viewType: ViewType.ITEM,
        url: Constants.BASE_URL + ApiPath.ABOUT_US,
        image: 'ic_about_us.svg',
        title: about_us,
        route: Routes.BOTTOM_SHEET));
    items.add(ProfileData(
      viewType: ViewType.DIVIDER,
    ));
    items.add(ProfileData(
        viewType: ViewType.ITEM,
        url: Constants.BASE_URL + ApiPath.PRIVACY_POLICY,
        image: 'ic_privacy_policy.svg',
        title: privacy_policy,
        route: Routes.WEB_PAGE));
    items.add(ProfileData(
      viewType: ViewType.DIVIDER,
    ));
    items.add(ProfileData(
        viewType: ViewType.ITEM,
        url: Constants.BASE_URL + ApiPath.TERMS_OF_USE,
        image: 'ic_t_n_c.svg',
        title: terms_n_conditions,
        route: Routes.WEB_PAGE));

    /// Commented temporarily. Will use the same in next to next release
    // items.add(ProfileData(
    //       viewType: ViewType.DIVIDER,
    //     ));
    // items.add(ProfileData(
    //     viewType: ViewType.ITEM,
    //     image: 'policy_transfer.svg',
    //     title: transfer_ownership,
    //     route: Routes.TRANSFER_OWNERSHIP));
    // items.add(ProfileData(viewType: ViewType.DIVIDER,));
    items.add(ProfileData(
      viewType: ViewType.DIVIDER,
    ));
    items.add(ProfileData(
        viewType: ViewType.ITEM,
        image: 'ic_bin.svg',
        title: manage_account,
        route: Routes.MANAGE_ACCOUNT_BOTTOMSHEET));

    items.add(ProfileData(
      viewType: ViewType.LOGOUT,
    ));
    /*items.add(ProfileData(
        image: 'ic_help_center.svg',
        title: help,
        desc: help_info,
        url: Constants.BASE_URL + ApiPath.SUPPORT));*/
    return items;
  }

  static TapConstants getSegmentEvent(String? title) {
    switch (title) {
      case personal_info:
        return TapConstants.TAP_PROFILE_PERSONAL_DETAILS;
      case payout_methods:
        return TapConstants.TAP_PROFILE_PAYOUT_PREFERENCE;
      case saved_cards:
        return TapConstants.TAP_PROFILE_SAVED_CARDS;
      case notifications:
        return TapConstants.TAP_BTN_PROFILE_NOTIFICATIONS_SECTION;
      case about_us:
        return TapConstants.TAP_BTN_PROFILE_ABOUT_US_SECTION;
      case privacy_policy:
        return TapConstants.TAP_BTN_PROFILE_PRIVACY_POLICY_SECTION;
      case you_and_your_family:
      case support:
        return TapConstants.TAP_BTN_INTERACTION;
      default:
        return TapConstants.TAP_BTN_LOGOUT;
    }
  }
}

enum ViewType { HEADER, ITEM, LOGOUT, DIVIDER }

class ProfileData {
  String? image;
  String? title;
  String? desc;
  String? url;
  String? route;
  ViewType viewType;
  String? deeplink;
  Function? analytics;

  ProfileData(
      {required this.viewType,
      this.image,
      this.title,
      this.desc,
      this.url,
      this.route,
      this.deeplink,
      this.analytics});
}
