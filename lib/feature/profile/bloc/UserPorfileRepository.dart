import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:acko_flutter/util/extensions.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:acko_flutter/common/model/BaseModel.dart';
import 'package:acko_flutter/common/model/user_detail_response.dart';
import 'package:acko_flutter/feature/login/model/VerifyOtpRequest.dart';
import 'package:acko_flutter/network/ApiResponse.dart';
import 'package:acko_flutter/network/ApiService.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/remote_config/remote_config.dart';

class UserProfileRepository {
  Future<LastVisitResponse> getLastVisitDetails() async {
    ApiResponse response = await ApiService.apiServiceInstance
        .getApiRequest(ApiPath.LAST_VISIT_PATH);
    LastVisitResponse lastVisitResponse;
    if (response.error == null) {
      try {
        lastVisitResponse = LastVisitResponse.fromJson(response.data);
      } catch (error, stacktrace) {
        lastVisitResponse = LastVisitResponse.error(ErrorHandler(error));
        debugPrint("EXCEPTION : ${response.error.toString()}");
        await FirebaseCrashlytics.instance.recordError(
            error, stacktrace); // Firebase crashlytics recording an error.
        Zone.current.handleUncaughtError(error, stacktrace);
      }
    } else {
      debugPrint("EXCEPTION : ${response.error.toString()}");
      lastVisitResponse = LastVisitResponse.error(response.error);
    }
    return Future.value(lastVisitResponse);
  }

  logOutFCM(String phone) async {
    DeviceToken request = DeviceToken(
        null, Platform.isAndroid ? "android" : "ios", phone, "logout");
    await ApiService.apiServiceInstance.postApiRequest(
        ApiPath.SEND_DEVICE_TOKEN,
        request: jsonEncode(request));
  }

  Future<BaseModel> logout() async {
    ApiResponse response =
        await ApiService.apiServiceInstance.getApiRequest(ApiPath.LOGOUT);
    BaseModel responseModel = BaseModel();
    if (response.error != null) {
      try {
        responseModel.error = response.error;
      } catch (error, stacktrace) {
        responseModel.error = ErrorHandler(error);
        await FirebaseCrashlytics.instance.recordError(
            error, stacktrace); // Firebase crashlytics recording an error.
        Zone.current.handleUncaughtError(error, stacktrace);
      }
    }
    return Future.value(responseModel);
  }

  Future<bool> isAppV10() async => (await RemoteConfigInstance.instance.getGbAsyncData(RemoteConfigKeysSet.APP_IA_VERSION)).toString().equalsIgnoreCase("app_v10");

}
