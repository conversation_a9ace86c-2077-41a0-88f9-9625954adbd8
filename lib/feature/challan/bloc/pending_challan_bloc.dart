import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:acko_flutter/feature/challan/bloc/PendingChallanEvents.dart';
import 'package:acko_flutter/feature/challan/model/challan_winback_model.dart';
import 'package:sdui/sdui.dart';
import 'package:session_manager_module/StorageManager/shared_preferences_storage.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/core/string_extensions.dart';
import 'package:utilities/remote_config/remote_config.dart';
import 'package:utilities/widgets/acko_safe_cubit.dart';

import '../../../common/util/PreferenceHelper.dart';
import '../../../r2d2/events.dart';
import '../../acko_services/model/acko_data_response.dart';
import '../../acko_services/model/rto_model.dart';
import '../../acko_services/model/ui_wrapper.dart';
import '../../challan_webview/models/automation_availability_status_model.dart';
import '../model/challan_screen_model.dart';
import '../repo/challan_repositiory.dart';

class PendingChallanBloc extends AckoSafeCubit<PendingChallanState> {
  ChallanRepository _repository = ChallanRepository();
  final BaseRepository _baseRepo = BaseRepository();
  final String _registrationNumber;
  PendingChallanBloc(this._registrationNumber) : super(InitialState()) {}
  String? userPhoneNumber;
  String get registrationNumber => _registrationNumber;
  List<AckoServiceWrapper> unPaidChallans = [];
  bool get hasNoUnpaidChallans => unPaidChallans.isEmpty;


  Future<List<ChallanScreenModel>> fetchChallans({bool isForce = false}) async {
    RtoResponse vehicleResponse;
    unPaidChallans = [];
    var response =
        await _repository.getChallans(registrationNumber, isForce: isForce);

    if (response.error == null) {
      List<ChallanScreenModel> wrappers = [];
      List<AckoServiceWrapper>? paidChallans = [];
      List<TabbedView> tabbedViews = [];
      int numberOfChallans = 0;
      if (response.challanData != null && response.challanData!.isNotEmpty) {
        response.challanData?.forEach((element) {
          if (element.challanStatus == Constants.PAID) {
            paidChallans.add(AckoServiceWrapper(AckoServiceView.ITEMS,
                challanList: element, isPaid: true));
          } else {
            unPaidChallans.add(AckoServiceWrapper(AckoServiceView.ITEMS,
                challanList: element, isPaid: false));
          }
          numberOfChallans++;
        });
      }
      if (unPaidChallans.isNotEmpty) {
        tabbedViews.add(TabbedView(
          TabListView.LIST,
          challans: unPaidChallans,
        ));
      } else {
        tabbedViews.add(TabbedView(
          TabListView.UNPAID_CHALLAN_ERROR,
        ));
      }
      if (paidChallans.isNotEmpty) {
        tabbedViews.add(TabbedView(
          TabListView.LIST,
          challans: paidChallans,
        ));
      } else {
        tabbedViews.add(TabbedView(
          TabListView.PAID_CHALLAN_ERROR,
        ));
      }
      wrappers.add(
          ChallanScreenModel(AckoServiceView.ITEMS, tabbedView: tabbedViews));
      R2D2Events.instance.trackChallanSuccessPageLoad(true,
          regNum: registrationNumber,
          numberOfChallans: '$numberOfChallans',
          challanDetails: response.challanData?.toString());
      return wrappers;
    } else {
      return Future.value([]);
    }
  }

  ChallanConfig? getChallanAutomationAvailability(String challanSearchSource) {
    if (challanSearchSource.isNullOrEmpty) {
      return null;
    }

    final String? automationStatus = RemoteConfigInstance.instance.getData(RemoteConfigKeysSet.challanAutomationStatusV2);
    if (automationStatus.isNullOrEmpty) {
      return null;
    }

    final Map<String, dynamic> configMap = jsonDecode(automationStatus!);
    final data = configMap[challanSearchSource.trim().toLowerCase()];
    final config = AutomationAvailabilityStatusModel.fromJson(data);

    final int minVersion = Platform.isAndroid ? int.parse(config.minAndroidVersion) : int.parse(config.minIosVersion);

    bool isAutomationAvailable =  Constants.BUILD_NUMBER! >= minVersion && config.isAutomationActive;

    return ChallanConfig(isAutomationAvailable: isAutomationAvailable, redirectionUrl: config.redirectionUrl);
  }

  Future<ChallanWinbackModel?> _getChallanWinbackResponse() async {
    final sduiResponse = await _baseRepo.getResponse(
      Urls.challanWinackApiPath(_registrationNumber),
    );

    if (sduiResponse.error != null) {
      return null;
    }

    final response = AckoDataResponse<ChallanWinbackModel>.fromJson(
        sduiResponse.data, (json) => ChallanWinbackModel.fromJson(json));

    if (response.data != null) {
      return response.data!;
    }
    return null;
  }

  initChallanContent({bool isForce = false}) async {
    emit(AssetDataLoading());

    userPhoneNumber = await getStringPrefs(StringDataSharedPreferenceKeys.MOBILE_NUMBER);

    if (userPhoneNumber == null) {
      emit(AssetDataError());
      return;
    }

    final result = await Future.wait([
      _getChallanWinbackResponse(),
      fetchChallans(isForce: isForce),
    ]);

    ChallanWinbackModel? winbackData = result[0] as ChallanWinbackModel?;
    List<ChallanScreenModel>? challanResults =
        result[1] as List<ChallanScreenModel>?;

    if (challanResults != null && challanResults.isNotEmpty) {
      emit(AssetDataLoaded(challanResults, winbackData));
    } else {
      emit(AssetDataError());
    }
  }
}
