enum ChallanSearchSource {
  KA,
  TS,
  MH,
  DL,
  DELHI_TRAFFIC_POLICE,
  PARIVAHAN,
  UNKNOWN;

  static ChallanSearchSource fromString(String? value) {
    if (value == null) return ChallanSearchSource.UNKNOWN;

    switch (value.toUpperCase()) {
      case 'KA':
        return ChallanSearchSource.KA;
      case 'MH':
        return ChallanSearchSource.MH;
      case 'DL':
        return ChallanSearchSource.DL;
      case 'DELHI TRAFFIC POLICE':
        return ChallanSearchSource.DELHI_TRAFFIC_POLICE;
      case 'TS':
        return ChallanSearchSource.TS;
      case 'PARIVAHAN':
        return ChallanSearchSource.PARIVAHAN;
      default:
        return ChallanSearchSource.UNKNOWN;
    }
  }
}

enum Challan<PERSON>ider {
  paytm,
  google_pay,
  phone_pe,
  amazon_pay,
  cred,
  unknown
}
