import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/feature/tab_view_sdui/view/widget/bottom_navigation.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/card_loaded_events.dart';
import 'package:design_module/design_module.dart';
import 'package:design_module/utilities/hybrid_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/remote_config/remote_config.dart';
import 'package:utilities/state_provider/StateProvider.dart';
import 'package:utilities/utilities.dart';
import 'package:video_player/video_player.dart';

import '../../../common/util/route_observer.dart';
import '../../../common/view/ErrorWidget.dart';
import '../../../util/Utility.dart';
import '../../acko_services/ui/acko_service_loading_screen.dart';
import '../bloc/bloc.dart';
import '../constants/home_tab_constants.dart';

class HomeTabBarView extends StatefulWidget {
  @override
  State<HomeTabBarView> createState() => _HomeTabBarViewState();
}

class _HomeTabBarViewState extends State<HomeTabBarView>
    with
        TickerProviderStateMixin,
        WidgetsBindingObserver,
        RouteAware,
        AutomaticKeepAliveClientMixin
    implements StateListener, ErrorViewRetryCallBack {
  late HomeTabBarViewBloc _bloc;
  TabController? _tabController;
  StateProvider _stateProvider = StateProvider();
  final PageController _pageController = PageController();
  DateTime? currentBackPressTime;
  CommonDataStoreBloc? _commonDataStoreBloc;
  VideoPlayerController? _controller;
  bool isPlaying = false;
  bool dismissVideoPlayer = false;
  bool showFullScreen = false;
  final GlobalKey _videoKey = GlobalKey();
  bool isLoaded = false;
  bool _isRunningRefresh = false;
  bool? useV10Design;

  @override
  void initState() {
    _stateProvider.subscribe(this);
    AckoRouteObserver.instance.subscribe(this);
    WidgetsBinding.instance.addObserver(this);
    setV10DesignFlag().then((value) {
      ;
      _commonDataStoreBloc
          ?.initBaseConfig(queryParams: getV10QueryParam())
          .then((value) => _bloc.fetchNewData(value));
    });
    _commonDataStoreBloc = BlocProvider.of(context);
    _bloc = BlocProvider.of(context);
    _bloc.loadCachedData();

    _showNavTab();

    super.initState();
  }

  setV10DesignFlag() async {
    useV10Design = (await RemoteConfigInstance.instance.getGbAsyncData(RemoteConfigKeysSet.APP_IA_VERSION)).toString().equalsIgnoreCase("app_v10");
    setState(() {});
  }

  getV10QueryParam() {
    return {
      "useV10Design": useV10Design ?? false,
    };
  }

  @override
  void dispose() {
    _tabController?.dispose();
    _stateProvider.dispose(this);
    AckoRouteObserver.instance.unsubscribe(this);
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  Future<void> _showNavTab() async {
    Future.delayed(
        Duration(
          seconds: 1,
        ), () {
      if (mounted) {
        setState(() {
          isLoaded = true;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    if (useV10Design == null) return SizedBox.shrink();
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool isPopped, dynamic result) async {
        if (isPopped) return;
        bool shouldExit = _onBackPressed();
        if (shouldExit && mounted) {
          Util.exitApp();
        }
      },
      child: Scaffold(
        backgroundColor: colorFFFFFF,
        body: _getContent(),
        extendBody: useV10Design! ? true : false,
        bottomNavigationBar: BlocBuilder<HomeTabBarViewBloc, HomeTabBlocStates>(
          builder: (context, state) {
            if (state is LoadingState || _bloc.isInLoadingState)
              return SizedBox.shrink();
            if (showFullScreen &&
                (_bloc.videoConfig?.landscapeFullscreen ?? true))
              return SizedBox.shrink();
            _isRunningRefresh = false;
            return Offstage(
              offstage: !isLoaded,
              child: Theme(
                data: ThemeData(
                  navigationBarTheme: NavigationBarThemeData(
                    labelTextStyle: MaterialStateProperty.resolveWith((state) {
                      if (state.contains(MaterialState.selected)) {
                        return const TextStyle(
                            color: Colors.white,
                            fontFamily: 'euclid_circularB_medium');
                      }
                      return const TextStyle(
                          color: Colors.grey,
                          fontFamily: 'euclid_circularB_medium');
                    }),
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (useV10Design!)
                      BottomNavigation(items: _bloc.tabBarItemsList)
                    else ...[
                      if (_bloc.showOfflineBanner) OfflineFooterBanner(),
                      NavigationBar(
                          onDestinationSelected: (index) {
                            _bloc.changeTab(index);
                          },
                          selectedIndex: _bloc.selectedTabIndex,
                          indicatorColor: const Color.fromARGB(0, 26, 16, 16),
                          backgroundColor: Colors.black,
                          destinations: _bloc.tabBarItemsList)
                    ],
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _getContent() {
    return BlocConsumer<HomeTabBarViewBloc, HomeTabBlocStates>(
      listener: (ctx, state) {
        if (state is UpdateTabIndexState) {
          _changePage(state.index);
        } else if (state is ScrollToTopState) {
          _stateProvider.notify(ObserverState.HOME_TAB_SCROLL_TO_TOP,
              data: {"index": state.index});
        } else if (state is UserLogoutState) {
          Navigator.pushNamedAndRemoveUntil(
              context, Routes.LOGIN_LANDING_PAGE, (route) => false,
              arguments: {"is_user_logged_out": true});
        }
      },
      builder: (ctx, state) {
        if (state is LoadingState || _bloc.isInLoadingState)
          return _loadingView();
        _initTabBarController();
        return Stack(
          children: [
            IndexedStack(
              index: _tabController!.index,
              children: _bloc.tabBarItemsList.map((e) => e.page).toList(),
            ),
            if (_bloc.videoConfig != null)
              AnimatedPositioned(
                duration: Duration(milliseconds: 100),
                bottom: (_bloc.videoConfig?.position?.bottom == null)
                    ? null
                    : (showFullScreen
                        ? 0
                        : (_bloc.videoConfig!.position!.bottom! + 0.0)),
                right: (_bloc.videoConfig?.position?.right == null)
                    ? null
                    : (showFullScreen
                        ? 0
                        : (_bloc.videoConfig!.position!.right! + 0.0)),
                top: (_bloc.videoConfig?.position?.top == null)
                    ? null
                    : (showFullScreen
                        ? 0
                        : (_bloc.videoConfig!.position!.top! + 0.0)),
                left: (_bloc.videoConfig?.position?.left == null)
                    ? null
                    : (showFullScreen
                        ? 0
                        : (_bloc.videoConfig!.position!.left! + 0.0)),
                child: _getVideoView(),
              ),
          ],
        );
      },
    );
  }

  _getVideoView() {
    if (dismissVideoPlayer) return SizedBox.shrink();

    return BlocBuilder<HomeTabBarViewBloc, HomeTabBlocStates>(
        buildWhen: (prev, state) =>
            state is VideoUrlLoadedState || state is VideoViewUpdatedState,
        builder: (ctx, state) {
          if (state is VideoUrlLoadedState) {
            _initController(state.model.url!);
          }

          if (_bloc.videoConfig == null ||
              _controller == null ||
              !_controller!.value.isInitialized) {
            return SizedBox.shrink();
          }

          return ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Stack(
              children: [
                Container(
                  width: showFullScreen
                      ? MediaQuery.of(context).size.width
                      : (_bloc.videoConfig?.width != null
                          ? checkDouble(_bloc.videoConfig?.width,
                              context: context)
                          : null),
                  height: showFullScreen
                      ? MediaQuery.of(context).size.height
                      : (_bloc.videoConfig?.height != null
                          ? checkDouble(_bloc.videoConfig?.height,
                              context: context, isHeight: true)
                          : null),
                  color: Colors.black,
                  child: Center(
                    child: GestureDetector(
                      onTap: _toggleVideoPlayingStatus,
                      child: AspectRatio(
                        key: _videoKey,
                        aspectRatio: _controller!.value.aspectRatio,
                        child: VideoPlayer(_controller!),
                      ),
                    ),
                  ),
                ),
                if (!_controller!.value.isPlaying)
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: GestureDetector(
                        onTap: _toggleVideoPlayingStatus,
                        child: Icon(
                          Icons.play_circle,
                          color: Colors.white,
                          size: (showFullScreen ? 72 : 24),
                        )),
                  ),
                Positioned(
                  top: showFullScreen
                      ? ((_bloc.videoConfig?.landscapeFullscreen ?? true)
                          ? 48
                          : 120)
                      : 4,
                  left: 4,
                  child: SizedBox(
                    width: showFullScreen
                        ? MediaQuery.of(context).size.width
                        : (_bloc.videoConfig?.width != null
                            ? checkDouble(_bloc.videoConfig?.width,
                                context: context)
                            : null),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        GestureDetector(
                            onTap: _dismissVideoPlayer,
                            child: Icon(
                              Icons.cancel,
                              color: Colors.white,
                              size: (showFullScreen ? 36 : 24),
                            )),
                        Padding(
                          padding: const EdgeInsets.only(right: 4.0),
                          child: GestureDetector(
                              onTap: _toggleFullScreenView,
                              child: Icon(
                                showFullScreen
                                    ? Icons.close_fullscreen
                                    : Icons.open_in_full_rounded,
                                color: Colors.white,
                                size: (showFullScreen ? 36 : 24),
                              )),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          );
        });
  }

  _toggleFullScreenView() {
    setState(() {
      showFullScreen = !showFullScreen;
      updateScreenOrientation();
    });
  }

  updateScreenOrientation() {
    if (!(_bloc.videoConfig?.landscapeFullscreen ?? true)) return;

    if (showFullScreen) {
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
      ]);
    } else {
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
      ]);
    }
  }

  _dismissVideoPlayer() {
    showFullScreen = false;
    setState(() {
      dismissVideoPlayer = true;
      updateScreenOrientation();
    });
    _controller?.pause();
    _controller?.dispose();
  }

  _toggleVideoPlayingStatus() {
    setState(() {
      _controller!.value.isPlaying ? _controller!.pause() : _controller!.play();
    });
  }

  _initController(String url) {
    if (_controller != null) return;

    _controller = VideoPlayerController.networkUrl(Uri.parse(url))
      ..initialize().then((_) {
        _bloc.setVideoControllerInitialized();
        _toggleVideoPlayingStatus();
        _controller?.setLooping(true);
      });
  }

  _initTabBarController() {
    if (_tabController != null) {
      if (_tabController!.length == _bloc.tabBarItemsList.length) {
        return;
      }
    }

    _tabController =
        TabController(vsync: this, length: _bloc.tabBarItemsList.length);
  }

  Widget _getIcon(String path) {
    return HybridImage(
      imageUrl: path,
      height: 24,
      width: 24,
    );
  }

  _loadingView() => SizedBox(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height,
      child: Center(child: AckoServiceLoadingScreen()));

  ///Methods
  bool _onBackPressed() {
    if (_bloc.selectedTabIndex != 0) {
      _bloc.changeTab(0);
      return false;
    }

    DateTime date = new DateTime.now();
    if (currentBackPressTime == null ||
        (date.difference(currentBackPressTime!) > Duration(seconds: 2))) {
      currentBackPressTime = date;
      Fluttertoast.showToast(
          msg: "Press back again to close the app",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: colorF6F7FB,
          textColor: color737F8B,
          fontSize: 13.0);
      return false;
    }
    return true;
  }

  _changePage(int index) {
    _tabController?.animateTo(index);
  }

  @override
  void onStateChanged(ObserverState state, {data}) {
    if (state == ObserverState.MyAccount_Refresh) {
      _commonDataStoreBloc
          ?.initBaseConfig(queryParams: getV10QueryParam())
          .then((value) => _bloc.fetchNewData(value));
    } else if (state == ObserverState.REFRESH_ASSETS) {
      _commonDataStoreBloc?.refreshLobData('auto', refreshCache: true);
    } else if (state == ObserverState.PROFILE_REFRESH) {
      _commonDataStoreBloc?.refreshLobData('health', refreshCache: true);
    } else if (state == ObserverState.CHANGE_TAB) {
      if(data["tab"] != null && data["tab"] is HomeTabs){
        int index = (data["tab"] as HomeTabs).getIndex(v10Design: useV10Design ?? false);
        _bloc.changeTab(index);
      } else if (data["index"] != null) {
        int index =  (data["index"] < _bloc.tabBarItemsList.length) ? data["index"] : (_bloc.tabBarItemsList.length - 1);
        _bloc.changeTab(data["index"]);
      }
    } else if (state == ObserverState.REFRESH_SDUI_TAB) {
      if (_isRunningRefresh) return;
      _isRunningRefresh = true;
      _refreshTabView();
    } else if (state == ObserverState.HEALTH_STATUS_CARDS_REFRESH) {
      _commonDataStoreBloc?.refreshLobData('health', refreshCache: true);
    } else if (state == ObserverState.LOGOUT_USER) {
      AnalyticsTrackerManager.instance.sendEvent(
        event: CardLoadedConstants.TRACK_EVENT_COMPLETE,
        properties: {
          'product': 'app_debug',
          "platform": Util.getPlatform(),
          'journey': 'user_logged_out'
        },
      );
      _bloc.logoutUser();
    } else if (state == ObserverState.SUPPORT_ALERTS_UPDATES) {
      _bloc.updateTabBadgeComponent(data['badgeComponent'],
          tabBarComponent: data['tabBarComponent']);
    }
  }

  _refreshTabView() {
    _bloc.changeStateToLoading();
    _commonDataStoreBloc?.clearDataStore();
    _commonDataStoreBloc
        ?.initBaseConfig(queryParams: getV10QueryParam())
        .then((value) => _bloc.fetchNewData(value));
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed && mounted) {
      setState(() {});
    } else {
      if (_controller?.value.isPlaying ?? false) {
        _controller?.pause();
      }
    }
  }

  @override
  bool get wantKeepAlive => true;

  @override
  onRetry() {
    _bloc.changeStateToLoading();
    _commonDataStoreBloc
        ?.initBaseConfig(queryParams: getV10QueryParam())
        .then((value) => _bloc.fetchNewData(value));
  }
}
