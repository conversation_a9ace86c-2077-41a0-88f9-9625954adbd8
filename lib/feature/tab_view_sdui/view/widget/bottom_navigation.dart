import 'dart:io';
import 'dart:ui' as ui show Gradient;

import 'package:acko_flutter/common/view/acko_text_config.dart';
import 'package:design_module/design_module.dart';
import 'package:design_module/utilities/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';

import '../../bloc/bloc.dart';

const _kDefaultPrimaryColor = Color(0xff631FE6);
const _kIconSize = 24.0;
const _kLabelFontSize = 12.0;
const _kCenterItemScale = 1.4;
const _kDefaultItemScale = 1.0;

/// **For V10 Design Language**: Home Bottom Navigation
class BottomNavigation extends StatelessWidget {
  final List<NavigationDestinationComponent> items;

  const BottomNavigation({
    super.key,
    required this.items,
  }) : assert(
          items.length >= 3,
          'At least 3 items are required for a center item',
        );

  @override
  Widget build(BuildContext context) {
    final displayItems = items.take(3).toList();
    final centerIndex = (displayItems.length / 2).floor();
    final bloc = context.read<HomeTabBarViewBloc>();

    return Column(
      children: [
        CustomPaint(
          painter: BottomNavigationPainter(),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: List.generate(
              displayItems.length,
              (index) => NavigationBarItem(
                item: displayItems[index],
                isSelected: index == bloc.selectedTabIndex,
                isCenter: index == centerIndex,
                onTap: () => bloc.changeTab(index),
              ),
            ),
          ),
        ),
        if (Platform.isIOS)
          SizedBox(
              height: 16,
              width: MediaQuery.sizeOf(context).width,
              child: ColoredBox(color: color000000))
      ],
    );
  }
}

class NavigationBarItem extends StatelessWidget {
  final NavigationDestinationComponent item;
  final bool isSelected;
  final bool isCenter;
  final VoidCallback onTap;

  const NavigationBarItem({
    Key? key,
    required this.item,
    required this.isSelected,
    required this.isCenter,
    required this.onTap,
  }) : super(key: key);

  double get _scale => isCenter ? _kCenterItemScale : _kDefaultItemScale;
  double get _topPadding => isCenter ? 8.0 : 24.0;
  double get _betweenPadding => isCenter ? 12.0 : 4.0;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: isCenter ? 4 : 10,
      child: Transform.translate(
        offset: isCenter ? const Offset(0, -8) : Offset.zero,
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: onTap,
          child: Padding(
            padding: EdgeInsets.only(top: _topPadding),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildIconWithBadge(),
                SizedBox(height: _betweenPadding),
                _buildLabel(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getBadgeText() {
    if (item.badgeComponent != null || item.badgeComponent is SDUIText) {
      return ((item.badgeComponent as SDUIText?)?.value ?? '');
    }
    return '';
  }

  Widget _buildIconWithBadge() {
    return SizedBox(
      height: _kIconSize,
      width: _kIconSize,
      child: Transform.scale(
        scale: _scale,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            if (isSelected) _buildSelectedBackground(),
            Transform.scale(
              scale: _scale,
              child: isSelected ? item.selectedIcon! : item.unSelectedIcon!,
            ),
            BadgeComponentWidget(
              badgeComponent: AckoTextConfig.instance.labelXXSmall.text(
                _getBadgeText(),
                textColor: colorFFFFFF,
              ),
              badgeColor: item.badgeComponent != null
                  ? colorD83D37
                  : Colors.transparent,
              size: 14.0,
              top: -4.0,
              right: -4.0,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedBackground() {
    return SizedBox(
      height: _kIconSize + 16,
      width: _kIconSize + 16,
      child: DecoratedBox(
        decoration: BoxDecoration(
          color: item.backgroundColor ??
              _kDefaultPrimaryColor.withValues(alpha: 0.1),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: item.shadowColor ??
                  _kDefaultPrimaryColor.withValues(alpha: .3),
              spreadRadius: item.spreadRadius ?? 10,
              blurRadius: item.blurRadius ?? 20.0,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLabel() {
    return AckoTextConfig.instance.labelXSmall.text(
      item.label ?? '',
      textColor: isSelected ? colorFFFFFF : color757575,
    );
  }
}

class BottomNavigationPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    const heightFraction = 1 / 6;
    const centerX = 0.5;
    const third = 1 / 3;
    const twoThirds = 2 / 3;

    final path = Path()
      ..moveTo(size.width * centerX, 0)
      ..cubicTo(
        size.width * 0.57216,
        0,
        size.width * 0.6429173,
        size.height * heightFraction,
        size.width * 0.7150773,
        size.height * heightFraction,
      )
      ..lineTo(size.width, size.height * heightFraction)
      ..lineTo(size.width, size.height)
      ..cubicTo(
        size.width * twoThirds,
        size.height,
        size.width * third,
        size.height,
        0,
        size.height,
      )
      ..lineTo(0, size.height * heightFraction)
      ..cubicTo(
        size.width * 0.0763712,
        size.height * heightFraction,
        size.width * 0.1968952,
        size.height * heightFraction,
        size.width * 0.284992,
        size.height * heightFraction,
      )
      ..cubicTo(
        size.width * 0.357152,
        size.height * heightFraction,
        size.width * 0.42784,
        0,
        size.width * centerX,
        0,
      )
      ..close();

    final paint = Paint()
      ..style = PaintingStyle.fill
      ..shader = ui.Gradient.linear(
        Offset(size.width * centerX, 0),
        Offset(size.width * centerX, size.height),
        [Colors.black.withValues(alpha: 0.8), Colors.black],
        [0, 1],
      );

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
