// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:acko_flutter/common/util/PreferenceHelper.dart';
import 'package:acko_flutter/common/util/validator.dart';
import 'package:acko_flutter/feature/app_policy_page/utils/CommonDataStore.dart';
import 'package:acko_flutter/feature/coverages_sdui/bloc/coverages_sdui_bloc.dart';
import 'package:acko_flutter/feature/coverages_sdui/view/coverages_sdui.dart';
import 'package:acko_flutter/feature/discover_sdui/bloc/discover_sdui_bloc.dart';
import 'package:acko_flutter/feature/discover_sdui/view/discover_sdui.dart';
import 'package:acko_flutter/feature/homeSDUI/bloc/home_bloc/home_cubit.dart';
import 'package:acko_flutter/feature/homeSDUI/views/home_screen_sdui.dart';
import 'package:acko_flutter/feature/profile_completion/bloc/bloc_singleton_instance.dart';
import 'package:acko_flutter/feature/protect_sdui/bloc/protect_sdui_bloc.dart';
import 'package:acko_flutter/feature/protect_sdui/views/protect_screen_sdui.dart';
import 'package:acko_flutter/feature/support_sdui/bloc/support_sdui_bloc.dart';
import 'package:acko_flutter/feature/support_sdui/view/support_sdui.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/tap_events.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';
import 'package:session_manager_module/session_manager.dart';
import 'package:utilities/remote_config/remote_config.dart';
import 'package:utilities/utilities.dart';
import 'package:utilities/widgets/acko_safe_cubit.dart';

import '../../../util/Utility.dart';
import '../../assets_tab_view/ui/assets_tab_view.dart';
import '../model/home_pip_config_model.dart';

///TODO Sharan : Rename this to TabBarBloc
class HomeTabBarViewBloc extends AckoSafeCubit<HomeTabBlocStates> {
  bool isInLoadingState = true;
  bool logoutCalled = false;
  BaseRepository _baseRepo = BaseRepository();
  bool useV10Design = false;

  HomeTabBarViewBloc() : super(LoadingState()) {
    /// Check on first load
    initNetworkCheck();
    fetchLegacyAPIcalls();
    fetchVehicleRegex();
    v10Check();

    /// Listen to network changes during session
    ConnectionStatus.instance.onConnectionChange.listen((isOnline) {
      showOfflineBanner = !isOnline;
      emit(ConnectionStatusUpdatedState());
    });
  }

  initNetworkCheck() async {
    final isOnline = await ConnectionStatus.instance.isNetworkAvaialable();
    showOfflineBanner = !isOnline;
    emit(ConnectionStatusUpdatedState());
  }

  void v10Check() async {
    useV10Design = (await RemoteConfigInstance.instance.getGbAsyncData(RemoteConfigKeysSet.APP_IA_VERSION)).toString().equalsIgnoreCase("app_v10");
  }

  fetchVehicleRegex() async {
    final ResponseWrapper response =
        await _baseRepo.getResponse('/app-config/vehicle-regex');
    if (response.error == null && response.data?['data'] != null) {
      Validator.vehicleRegex =
          (response.data!['data'] as List).map((e) => e.toString()).toList();
    }
  }

  List<NavigationDestinationComponent> tabBarItemsList =
      List.empty(growable: true);
  int selectedTabIndex = 0;
  bool currentTabBarVisibilityStatus = true;
  bool showOfflineBanner = false;
  HomeVideoConfigModel? videoConfig;

  changeStateToLoading() {
    isInLoadingState = true;
    emit(LoadingState());
  }

  /// [Added on November 8 2024 : PR #1656]
  /// These API calls are required to ensure the working of some old features in the app.
  /// `getElectronicPolicyList` and `getInternetPolicyList` are called in multiple places
  /// which are not derived from SDUI data, hence needs to be intialised this way.
  /// So does `getProfileCompletionData` which shows the name and other informaiton on the profile screen.
  /// These can be removed once there is no longer a dependency on this data.
  void fetchLegacyAPIcalls() {
    ProfileCompeltionBlocSingletonInstance.instance.blocInstance
        .getProfileCompletionData();
    CommonDataStore.sharedInstance.getElectronicPolicyList();
    CommonDataStore.sharedInstance.getInternetPolicyList();
  }

  fetchNewData(ResponseWrapper response) async {
    if (response.error != null) {
      if (!(await isDataCached())) {
        isInLoadingState = false;
        emit(ErrorState());
      }

      ///IN case data is cached show older data
      return;
    }

    /// Cahcing of app-config API is handled by cache module.
    /// Cache is bust from remote config versioning.
    updateTabs(response.data!["bottomTabs"]);
    updateVideoViewUrl(response.data!["video"]);
  }

  updateVideoViewUrl(Map<String, dynamic>? config) {
    ///TODO sharan : check if we can add PIP video to home view only
    if (config != null) {
      videoConfig = HomeVideoConfigModel.fromJson(config);
      emit(VideoUrlLoadedState(videoConfig!));
    }
  }

  setVideoControllerInitialized() {
    emit(VideoViewUpdatedState());
  }

  Future<bool> isDataCached() async {
    String? cachedData =
        await getStringPrefs(StringDataSharedPreferenceKeys.BOTTOM_TABS_SDUI);
    return cachedData.isNotNullAndEmpty;
  }

  loadCachedData() async {
    String? cachedData =
        await getStringPrefs(StringDataSharedPreferenceKeys.BOTTOM_TABS_SDUI);
    if (cachedData.isNotNullOrEmpty) {
      updateTabs(jsonDecode(cachedData!));
    }
  }

  updateTabs(Map<String, dynamic> bottomTabs) {
    ///TODO : Add this UI related things to the view file, just pass the response to view file
    tabBarItemsList.clear();
    int index = 0;
    for (var element in (bottomTabs['children'] as List)) {
      NavigationDestinationComponent destination =
          NavigationDestinationComponent().fromJson(element);
      destination.page =
          _getPageFromId(element["id"].toString(), element["url"].toString());
      destination.index = index;
      tabBarItemsList.add(destination);
      index++;
    }
    isInLoadingState = false;
    emit(LoadedState());
  }

  _getPageFromId(String pageId, String url) {
    Widget page = Container();
    switch (pageId) {
      case "home":
        page = BlocProvider<HomeCubit>(
          create: (context) => HomeCubit(url),
          child: HomeScreenSDUI(),
        );
        break;
      case "coverages":
        page = BlocProvider<CoveragesSDUIBloc>(
          create: (context) => CoveragesSDUIBloc(url, false),
          child: CoverageSduiScreen(),
        );
        break;
      case "protect":
        page = BlocProvider<ProtectSDUIBloc>(
          create: (context) => ProtectSDUIBloc(url),
          child: ProtectScreenSDUI(),
        );
        break;
      case "discover":
        page = useV10Design
            ? AssetsTabView()
            : BlocProvider<DiscoverSDUIBloc>(
                create: (context) => DiscoverSDUIBloc(url),
                child: DiscoverSduiScreen(),
              );
        // page = MultiBlocProvider(providers: [
        //   BlocProvider<AssetSDUIBloc>(
        //     create: (context) =>
        //         AssetSDUIBloc(null, null),
        //   ),
        //   BlocProvider<CommonDataStoreBloc>.value(
        //     value: globalCommonDataStoreBloc!,
        //   )
        // ], child: AssetSduiView());
        break;
      case "support":
        page = BlocProvider<SupportSDUIBloc>(
          create: (context) => SupportSDUIBloc(url),
          child: SupportSduiScreen(),
        );
        break;
    }

    return page;
  }

  pauseVideo() {
    emit(PauseVideoState());
  }

  cacheData(Map data) {
    setStringPrefs(
        StringDataSharedPreferenceKeys.BOTTOM_TABS_SDUI, jsonEncode(data));
  }

  changeTab(int index) {
    if (selectedTabIndex == index) {
      emit(ScrollToTopState(index));
      return;
    }
    sendTabClickAnalytics(index, selectedTabIndex);
    selectedTabIndex = index;
    emit(UpdateTabIndexState(index));
  }

  void updateTabBadgeComponent(Widget? widget, {Widget? tabBarComponent = null }) {
    final index = tabBarItemsList.indexWhere((item) => item.id == "support");
    if (index != -1) {
      tabBarItemsList[index] = NavigationDestinationComponent()
        ..backgroundColor = tabBarItemsList[index].backgroundColor
        ..shadowColor = tabBarItemsList[index].shadowColor
        ..selectedIcon = tabBarComponent ?? tabBarItemsList[index].selectedIcon
        ..unSelectedIcon = tabBarComponent ?? tabBarItemsList[index].unSelectedIcon
        ..spreadRadius = tabBarItemsList[index].spreadRadius
        ..blurRadius = tabBarItemsList[index].blurRadius
        ..label = tabBarItemsList[index].label
        ..id = tabBarItemsList[index].id
        ..page = tabBarItemsList[index].page
        ..badgeComponent = widget
        ..index = tabBarItemsList[index].index;

      emit(LoadedState());
    }
  }

  logoutUser() async {
    if (!logoutCalled) {
      logoutCalled = true;
      await logoutHandling();
      emit(UserLogoutState());
    }
  }

  sendTabClickAnalytics(int index, prevIndex) async {
    final appVersion = (await RemoteConfigInstance.instance.getGbAsyncData(RemoteConfigKeysSet.APP_IA_VERSION)) ?? "app_v9";
    AnalyticsTrackerManager.instance.sendEvent(
        event: TapConstants.TAP_BTN_BOTTOM_NAVIGATION_TAB,
        properties: {
          'category': Util.getHomeTabString(index),
          'platform': Util.getPlatform(),
          'from_page': Util.getHomeTabString(prevIndex),
          'version': appVersion,
        });

    AnalyticsTrackerManager.instance
        .sendEvent(event: TapConstants.TAB_CLICKED, properties: {
      'user_flow': 'navbar',
      'screen': 'app_nav_bar',
      'product_state': 'App Navigation Bar',
      'tab': Util.getHomeTabString(index),
      'version': appVersion,
    });
  }

  changeTabVisibility(bool visibility) {
    if (visibility == currentTabBarVisibilityStatus) return;

    currentTabBarVisibilityStatus = visibility;
    emit(UpdateTabVisibilityState(visibility));
  }
}

abstract class HomeTabBlocStates {}

class LoadingState extends HomeTabBlocStates {}

class LoadedState extends HomeTabBlocStates {}

class VideoViewUpdatedState extends HomeTabBlocStates {}

class PauseVideoState extends HomeTabBlocStates {}

class ConnectionStatusUpdatedState extends HomeTabBlocStates {}

class VideoUrlLoadedState extends HomeTabBlocStates {
  final HomeVideoConfigModel model;

  VideoUrlLoadedState(this.model);
}

class ErrorState extends HomeTabBlocStates {}

class UpdateTabIndexState extends HomeTabBlocStates {
  final int index;

  UpdateTabIndexState(this.index);
}

class ScrollToTopState extends HomeTabBlocStates {
  final int index;

  ScrollToTopState(this.index);
}

class UpdateTabVisibilityState extends HomeTabBlocStates {
  final bool visibilityStatus;

  UpdateTabVisibilityState(this.visibilityStatus);
}

class UserLogoutState extends HomeTabBlocStates {}
