import 'dart:core';

import 'package:acko_flutter/feature/claim/my_claims_home/model/my_claims_models.dart';
import 'package:acko_web_view_module/common/appbar_action.dart';

class WebViewModel {
  String? action;
  ActionValue? actionValue;

  WebViewModel({this.action, this.actionValue});

  WebViewModel.fromJson(Map<String, dynamic> parsedJson) {
    action = parsedJson["action"];
    actionValue = parsedJson["action_value"] != null
        ? ActionValue.fromJson(parsedJson["action_value"])
        : null;
  }
}

class ActionValue {
  bool? isPhoneNumberEditable;
  bool? isPaymentSuccess;
  String? orderId;
  bool? refreshHome;
  String? url;
  String? destination;
  String? eventName;
  String? phoneNumber;
  String? product;
  String? journey;
  List<dynamic>? eventParams;
  String? redirectUrl;
  String? paymentUrl;
  String? policyNumber;
  String? policyId;
  String? next;
  String? text;
  List<AppBarAction>? appBarActions;
  String? orientation;
  bool? resize;
  String? deeplink;
  String? permissionRequest;
  ClaimInfoSummary? claimsData;

  ActionValue.fromJson(Map<String, dynamic> parsedJson) {
    isPaymentSuccess = parsedJson["is_payment_success"];
    orderId = parsedJson["order_id"];
    refreshHome = parsedJson["refresh_home"];
    url = parsedJson["url"];
    text = parsedJson["text"];
    product = parsedJson["product"];
    journey = parsedJson["journey"];
    orientation = parsedJson["orientation"];
    deeplink = parsedJson["deeplink"];
    destination = parsedJson["destination"];
    eventName = parsedJson["event_name"];
    eventParams = parsedJson["event_params"];
    phoneNumber = parsedJson["phone_number"];
    redirectUrl = parsedJson["redirect_url"];
    paymentUrl = parsedJson["redirection_url"];
    policyNumber = parsedJson['policy_number'];
    policyId = parsedJson["policy_id"];
    permissionRequest = parsedJson['permission_request'];
    next = parsedJson["next"];
    isPhoneNumberEditable = parsedJson['is_phone_number_editable'];
    if (parsedJson['appbar_actions'] != null) {
      appBarActions = List.empty(growable: true);
      ((parsedJson['appbar_actions'] as List).forEach((element) {
        appBarActions!.add(AppBarAction.fromJson(element));
      }));
    }
    resize = parsedJson['resize'];
    if(parsedJson['claims_data'] != null) {
      claimsData = ClaimInfoSummary.fromJson(parsedJson['claims_data']);
    }
  }
}