import 'dart:async';

import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/feature/acko_services/ui/acko_service_loading_screen.dart';
import 'package:acko_flutter/feature/car_journey/view/icon_app_bar.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/view/widgets/booking_success_animation.dart';
import 'package:acko_flutter/feature/medical_test_booking/schedule_booking/core/screens/booking_success_screen.dart';
import 'package:acko_flutter/feature/medical_test_booking/schedule_booking/core/widgets/booking_loading_widget.dart';
import 'package:acko_flutter/feature/medical_test_booking/schedule_booking/core/widgets/form_ui.dart';
import 'package:acko_flutter/feature/medical_test_booking/schedule_booking/cubit/medical_tests_booking/medical_test_booking_cubit.dart';
import 'package:acko_flutter/feature/medical_test_booking/schedule_booking/cubit/medical_tests_booking/medical_test_booking_states.dart';
import 'package:acko_flutter/feature/medical_test_booking/common/utils/booking_constants.dart';
import 'package:acko_flutter/feature/medical_test_booking/schedule_booking/domain/model/assessment_schedule_response.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_flutter/util/health/utils.dart';
import 'package:analytics/events/health_life/page_events/health_life_page_events.dart';
import 'package:analytics/events/health_life/tap_events/health_life_tap_events.dart';
import 'package:design_module/uikit/widgets/button/acko_button.dart';
import 'package:design_module/uikit/widgets/button/uikit_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/state_provider/StateProvider.dart';

class PpmcBookingScreen extends StatefulWidget {
  PpmcBookingScreen();

  @override
  State<PpmcBookingScreen> createState() => _PpmcBookingScreenState();
}

class _PpmcBookingScreenState extends State<PpmcBookingScreen>
    with SingleTickerProviderStateMixin {

  late MedicalTestBookingBloc _bloc;
  final StateProvider _stateProvider = StateProvider();
  Timer? timer;
  double _animContH = 200;

  @override
  void initState() {
    super.initState();
    _bloc = BlocProvider.of<MedicalTestBookingBloc>(context);
    _bloc.getAssessmentsList();
  }

  @override
  void dispose() {
    timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: BlocConsumer<MedicalTestBookingBloc, MedicalTestBookingStates>(
            listener: (context, state) {
              if (state is ScheduleSuccess) {
                _stateProvider.notify(ObserverState.REFRESH_MEDICAL_TEST_DETAILS);
                _bloc.getAssessmentsList(
                    showAnimation: true,
                    ppmcScheduleResponse: state.ppmcScheduleResponse);
              } else if (state is ScheduleError) {
                _bloc.getAssessmentsList();
                UiUtils.getInstance.showToast(something_went_wrong);
              } else if (state is MedicalTestBookingLoaded) {
                if (_bloc.testCategory.containsIgnoreCase("lab")) {
                  _bloc.triggerPageAnalyticalEvents(
                      HLPageEvents.VIEW_BOOK_LAB_VISIT_PAGE);
                } else if (_bloc.testCategory.containsIgnoreCase("home") ??
                    false) {
                  _bloc.triggerPageAnalyticalEvents(
                      HLPageEvents.VIEW_BOOK_HOME_TEST_PAGE);
                }
                if (state.showAnimation != null &&
                    state.showAnimation == true) {
                  if (context.mounted) {
                    timer = Timer(Duration(seconds: 3), () {
                      setState(() {
                        _animContH = 0;
                      });
                    });
                  }
                }
                if (_bloc.assessmetsResponse?.assessments?.isNullOrEmpty ?? false)
                  Navigator.of(context).push(MaterialPageRoute(
                      builder: (context) => MedicalTestBookingSuccessScreen(
                          testCategory: _bloc.testCategory,
                          proposalId: _bloc.referenceId,
                          product: (_bloc.policyType == AssessmentPolicyType.health)
                              ? 'health_retail'
                              : 'life',
                          assessmentId: _bloc.assessmentId,
                          ppmcScheduleResponse: state.ppmcScheduleResponse,
                          communicationEmail: _bloc.communicationEmail,
                          isReschedule: _bloc.isRescheduleFlow)));
              } else if (state is AllAssessmentsFinished) {
                Navigator.of(context).push(MaterialPageRoute(
                    builder: (context) => MedicalTestBookingSuccessScreen(
                        testCategory: _bloc.testCategory,
                        proposalId: _bloc.referenceId,
                        product: (_bloc.policyType == AssessmentPolicyType.health)
                            ? 'health_retail'
                            : 'life',
                        assessmentId: _bloc.assessmentId,
                        communicationEmail: _bloc.communicationEmail,
                        ppmcScheduleResponse: state.ppmcScheduleResponse,
                        isReschedule: _bloc.isRescheduleFlow)));
              }
            },
            listenWhen: (prev, curr) =>
                curr is ScheduleSuccess ||
                curr is AllAssessmentsFinished ||
                curr is MedicalTestBookingLoaded ||
                curr is ScheduleError,
            buildWhen: (prev, curr) =>
                curr is MedicalTestBookingLoaded || curr is BookingLoading,
            builder: (context, state) {
              if (state is MedicalTestBookingLoaded) {
                return _loadedState(state);
              } else if (state is MedicalTestBookingLoading) {
                return _loadingState();
              } else if (state is BookingLoading) {
                return _bookingLoadingWidget();
              } else
                return SizedBox.shrink();
            }));
  }

  Widget _loadingState() => Center(child: AckoServiceLoadingScreen());

  Widget _bookingLoadingWidget() => BookingLoadingWidget();

  Widget _loadedState(state) {
    final bool isLabTest = _bloc.testCategory.containsIgnoreCase("lab");
    final bool showAnimation = state.showAnimation == true;
    final bool hasSingleAssessment =
        (_bloc.assessmetsResponse?.assessments?.length ?? 0) == 1;
    final bookingText = _getBookingText(isLabTest);
    final MedicalAssessmentScheduleResponse? response =
        state.ppmcScheduleResponse;
    final confirmedMembers = response?.getAllSelectedMembers ?? [];
    final memberName = _bloc.assessmetsResponse?.assessments?.firstOrNull
            ?.entityDetails?.memberName ??
        '';

    return Column(
      children: [
        Stack(
          children: [
            if (showAnimation)
              SuccessAnimationWidget(
                confirmedMembers: confirmedMembers,
                animContH: _animContH,
              ),
            IconAppbar.newBackBtnAppBar(
              context,
              background: Colors.transparent,
            ),
          ],
        ),
        Expanded(
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SDUIText(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  value: bookingText,
                  textStyle: 'hLarge',
                  maxLines: 2,
                ),
                if (hasSingleAssessment)
                  Padding(
                    padding: const EdgeInsets.fromLTRB(20, 2, 20, 0),
                    child: Row(
                      children: [
                        SDUIText(
                          value: "for",
                          textStyle: 'lLarge',
                          textColor: color4B4B4B,
                        ),
                        SDUIText(
                          value: " $memberName",
                          textStyle: 'lLarge',
                          textColor: color5920C5,
                        ),
                      ],
                    ),
                  ),
                const SizedBox(height: 20),
                MedicalTestFormUI(),
              ],
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(20),
          child: AckoDarkButtonFullWidth(
            text: "Confirm booking",
            onTap: () {
              _bloc.triggerTapAnalyticalEvents(HLTrackEvents.TAP_CONFIRM_BOOKING);
              _bloc.confirmBooking();
            },
            buttonState: _bloc.hasAllRequiredData()
                ? UIKitButtonState.active
                : UIKitButtonState.disabled,
          ),
        ),
      ],
    );
  }

  String _getBookingText(bool isLabTest) {
    if (_bloc.ahc && _bloc.rescheduleOnly == true) {
      return kRescheduleHealthCheckup;
    }

    if (_bloc.rescheduleOnly == true) {
      return isLabTest ? kRescheduleLabVisit : kRescheduleHomeTests;
    }

    return isLabTest ? kBookLabVisit : kBookHomeTests;
  }
}
