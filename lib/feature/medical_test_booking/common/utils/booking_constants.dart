/// Booking Strings

enum AssessmentPolicyType {
  life,
  health,
}

enum TestInfoTextType { header, preparation }

const String kRescheduleHealthCheckup = "Reschedule health check-up";
const String kRescheduleLabVisit = "Reschedule lab visit";
const String kRescheduleHomeTests = "Reschedule home tests";
const String kBookLabVisit = "Book lab visit";
const String kBookHomeTests = "Book home tests";

// Booking Acknowledgement Messages
const String kBookingDetailsWhatsApp =
    "We will send all the details on WhatsApp";
const String kNewBookingHealthCheckup =
    "Make a new booking to move ahead with your health check-up";
const String kNewBookingPolicyApplication =
    "Make a new booking to move ahead with your policy application";
