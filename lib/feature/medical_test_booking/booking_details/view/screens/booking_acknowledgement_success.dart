import 'dart:async';

import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/feature/car_journey/view/icon_app_bar.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/view/widgets/booking_success_animation.dart';
import 'package:acko_flutter/feature/medical_test_booking/common/utils/booking_constants.dart';
import 'package:acko_flutter/feature/medical_test_booking/schedule_booking/domain/model/assessments_response.dart';
import 'package:acko_flutter/util/health/health_constants.dart';
import 'package:acko_flutter/util/health/utils.dart';
import 'package:design_module/uikit/widgets/button/acko_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/core/string_extensions.dart';
import 'package:utilities/utilities.dart';

class AvailibilityConfirmScreen extends StatefulWidget {
  final List<PpmcAssessments>? membersList;
  final bool bookedAll;
  /// Indicates whether the health check-up flow is active.
  /// Defaults to `false`.
  final bool ahc;
  const AvailibilityConfirmScreen({
    super.key,
    this.membersList,
    this.bookedAll = false,
    this.ahc = false,
  });

  @override
  State<AvailibilityConfirmScreen> createState() =>
      _AvailibilityConfirmScreenState();
}

class _AvailibilityConfirmScreenState extends State<AvailibilityConfirmScreen> {
  StateProvider _stateProvider = StateProvider();
  double _animContH = 200;
  late Timer timer;

  String? testCategory;
  final List<String?> cancelledMembers = [];
  final List<String?> confirmedMembers = [];

  @override
  void initState() {
    super.initState();
    testCategory = widget.membersList?.firstOrNull?.testCategory;
    widget.membersList?.forEach((element) {
      final memberName = element.entityDetails?.memberName ?? '';
      if (element.isSelected) {
        cancelledMembers.add(memberName);
      } else {
        confirmedMembers.add(memberName);
      }
    });

    timer = Timer(Duration(seconds: 3), () {
      setState(() {
        _animContH = 0;
      });
    });
  }

  @override
  dispose() {
    timer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AckoWillPopScope(
      onWillPop: _popPage,
      child: Scaffold(
        body: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            /// HEADER
            Stack(
              children: [
                if (!widget.bookedAll)
                  SuccessAnimationWidget(
                    confirmedMembers: confirmedMembers,
                    animContH: _animContH,
                  ),
                IconAppbar.newBackBtnAppBar(context,
                    background: Colors.transparent),
              ],
            ),

            /// BODY
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: SvgPicture.asset(
                      'assets/images/ic_done_thumbsup.svg',
                      width: 80,
                      height: 80,
                    ),
                  ),
                  const SizedBox(
                    height: 12,
                  ),
                  SDUIText(
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      value: _getAckSuccessString(),
                      textStyle: 'hMedium',
                      textColor: color040222,
                      maxLines: 3),
                  const SizedBox(
                    height: 2,
                  ),
                  if (widget.bookedAll)
                    SDUIText(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        value: HealthDateUtils.getInstance
                            .getCommaSeparatedString(confirmedMembers),
                        textStyle: 'lXLarge',
                        textColor: color5920C5,
                        maxLines: 3)
                  else
                    SDUIText(
                        padding: const EdgeInsets.symmetric(horizontal: 20),
                        value: HealthDateUtils.getInstance
                            .getCommaSeparatedString(cancelledMembers),
                        textStyle: 'lXLarge',
                        textColor: color5920C5,
                        maxLines: 3),
                  const SizedBox(
                    height: 4,
                  ),
                  SDUIText(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    value: widget.bookedAll
                        ? kBookingDetailsWhatsApp
                        : widget.ahc
                            ? kNewBookingHealthCheckup
                            : kNewBookingPolicyApplication,
                    textStyle: 'pSmall',
                    textColor: color757575,
                    maxLines: 3,
                  ),
                  const SizedBox(
                    height: 24,
                  ),
                ],
              ),
            ),

            /// FOOTER CTA
            Padding(
                padding: const EdgeInsets.all(20),
                child: AckoDarkButtonFullWidth(
                  text: widget.bookedAll
                      ? "Okay"
                      : testCategory.containsIgnoreCase("lab")
                          ? "Book lab test"
                          : "Book home test",
                  onTap: () {
                    _stateProvider
                        .notify(ObserverState.REFRESH_MEDICAL_TEST_DETAILS);
                    _stateProvider
                        .notify(ObserverState.REFRESH_LIFE_MEDICAL_EVALUATION);
                    Navigator.of(context)
                        .popUntil(HealthConstants.ppmcRoutePredicate);
                  },
                ))
          ],
        ),
      ),
    );
  }

  Future<bool> _popPage() async {
    _stateProvider.notify(ObserverState.REFRESH_MEDICAL_TEST_DETAILS);
    _stateProvider.notify(ObserverState.REFRESH_LIFE_MEDICAL_EVALUATION);
    Navigator.of(context).popUntil(HealthConstants.ppmcRoutePredicate);
    return Future.value(false);
  }

  _getAckSuccessString() => widget.bookedAll
      ? 'Booking confirmed for'
      : testCategory.containsIgnoreCase("lab")
          ? "Lab visit cancelled for"
          : "Home visit cancelled for";
}
