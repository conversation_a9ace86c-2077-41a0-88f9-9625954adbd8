import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/cubit/booking_acknowledgement/booking_acknowledgement_cubit.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/cubit/booking_details/booking_details_cubit.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/domain/model/booking_details_response.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/domain/repo/booking_details_repository.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/view/bottomsheets/booking_acknowledgement/acknowledge_booking_sheet.dart';
import 'package:acko_flutter/feature/medical_test_booking/common/utils/booking_constants.dart';
import 'package:acko_flutter/feature/medical_test_booking/schedule_booking/domain/repo/booking_schedule_repo.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/events/health_life/tap_events/health_life_tap_events.dart';
import 'package:design_module/design_module.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';

class InfoBannerWidget extends StatelessWidget {
  final ActionInfoBanner? infoBanner;
  final AssessmentPolicyType? policyType;
  final BookingDetailsCubit? cubit;
  const InfoBannerWidget({
    Key? key,
    this.infoBanner,
    this.policyType,
    this.cubit
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (infoBanner == null) {
      return const SizedBox.shrink();
    }

    final banner = infoBanner?.banner;
    final actionData = infoBanner?.actionData;
    final bgColor = banner?.style?.getBgColor ?? colorFFFFFF;
    final textColor = banner?.style?.getTextColor;
    final iconUrl = banner?.iconUrl;
    final text = banner?.text;

    return GestureDetector(
      onTap: () {
        if (actionData != null) {
          cubit?.triggerTapAnalyticalEvents(
              HLTrackEvents.TAP_CONFIRM_BOOKING_ACKNOWLEDGE_BUTTON);
          Navigator.pop(context);
          context.showAckoModalBottomSheet(
            child: BlocProvider<AcknowledgeBookingCubit>(
              create: (_) => AcknowledgeBookingCubit(
                referenceId: cubit?.referenceId,
                policyType: policyType,
                scheduleBookingRepo: MedicalTestBookingRepo(
                    ahc: cubit?.repo.ahc ?? false),
                bookingDetailsRepo: BookingDetailsRepository(
                    ahc: cubit?.repo.ahc ?? false),
              ),
              child: ConfirmAvailabilitySheet(
                notificationActionData: actionData,
                policyType: policyType,
              ),
            ),
          );
        }
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(
            color: bgColor,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(16),
          color: bgColor.withOpacity(0.5),
        ),
        child: Row(
          children: [
            if (iconUrl.isNotNullOrEmpty)
              SDUIImage(
                imageUrl: iconUrl,
                height: 16,
                width: 16,
              ),
            const SizedBox(width: 10),
            if (text.isNotNullOrEmpty)
              Expanded(
                child: SDUIText(
                  value: text,
                  maxLines: 3,
                  textStyle: "pXSmall",
                  textColor: textColor,
                ),
              ),
            if (actionData != null)
              Icon(
                Icons.navigate_next_rounded,
                color: color36354C,
              ),
          ],
        ),
      ),
    );
  }
}
