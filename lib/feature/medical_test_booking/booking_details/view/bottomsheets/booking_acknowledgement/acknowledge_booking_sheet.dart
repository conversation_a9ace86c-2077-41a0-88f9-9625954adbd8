import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/common/view/shimmer_view.dart';
import 'package:acko_flutter/common/view/toast.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/cubit/booking_acknowledgement/booking_acknowledgement_cubit.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/cubit/booking_acknowledgement/booking_acknowledgement_states.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/domain/model/booking_details_response.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/view/screens/booking_acknowledgement_success.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/view/widgets/booking_acknowledgement_widget.dart';
import 'package:acko_flutter/feature/medical_test_booking/common/utils/booking_constants.dart';
import 'package:acko_flutter/feature/medical_test_booking/schedule_booking/domain/model/assessments_response.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/events/health_life/page_events/health_life_page_events.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:utilities/utilities.dart';

class ConfirmAvailabilitySheet extends StatefulWidget {
  final NotificationActionData? notificationActionData;
  final AssessmentPolicyType? policyType;
  final Function(List<PpmcAssessments>? membersList)? successAnimation;
  const ConfirmAvailabilitySheet(
      {super.key,
      this.notificationActionData,
      this.policyType,
      this.successAnimation});

  @override
  State<ConfirmAvailabilitySheet> createState() =>
      _ConfirmAvailabilitySheetState();
}

class _ConfirmAvailabilitySheetState extends State<ConfirmAvailabilitySheet> {
  late AcknowledgeBookingCubit _cubit;
  StateProvider _stateProvider = StateProvider();
  bool ctaLoading = false;
  bool firstCtaDisabled = false;
  bool secondCtaDisabled = false;
  bool ctaDisabled = false;

  @override
  void initState() {
    super.initState();
    _cubit = context.read<AcknowledgeBookingCubit>();
    String? descStr = widget.notificationActionData?.description;
    String? testCategory = "";
    if (descStr != null) {
      if (descStr.containsIgnoreCase("home")) {
        testCategory = "HOME_TEST";
      } else if (descStr.containsIgnoreCase("lab")) {
        testCategory = "LAB_TEST";
      } else {
        testCategory = null;
      }
    }
    _cubit.getAssessmentsList(
        testCategory,
        widget.policyType == AssessmentPolicyType.life ? "LIFE" : "HEALTH_RETAIL",
        widget.notificationActionData?.assessmentGroupId,
        widget.notificationActionData?.status);
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AcknowledgeBookingCubit, AcknowledgeBookingState>(
      listenWhen: (prev, curr) => ((curr is AcknowledgeLoading) ||
          (curr is AcknowledgePositiveLoaded) ||
          (curr is AcknowledgeNegativeLoaded) ||
          (curr is AssessmentDataLoaded) || (curr is AssessmentDataError)
      ),
      listener: (BuildContext context, AcknowledgeBookingState state) {
        if (state is AssessmentDataLoaded) {
          _cubit.triggerPageAnalyticalEvents(
              HLPageEvents.VIEW_CONFIRM_BOOKING_MODAL);
        }
        if(state is AssessmentDataError) {
          Navigator.of(context).pop();
          AckoToast.show(something_went_wrong);
        }
        if (state is AcknowledgePositiveLoaded) {
          Navigator.of(context).pop();

          if (widget.policyType == AssessmentPolicyType.life) {
            /// canceled for none so just popping the sheet
            Navigator.of(context)
                .push(MaterialPageRoute(
                  builder: (context) => AvailibilityConfirmScreen(
                    membersList: state.assessments,
                    bookedAll: true,
                    ahc: _cubit.scheduleBookingRepo.ahc,
                  ),
                ))
                .then((value) => _stateProvider
                    .notify(ObserverState.REFRESH_MEDICAL_TEST_DETAILS));
          } else {
            if (widget.successAnimation != null)
              widget.successAnimation?.call(state.assessments);
          }
        }
        if (state is AcknowledgeNegativeLoaded) {
          Navigator.of(context)
              .push(MaterialPageRoute(
                builder: (context) => AvailibilityConfirmScreen(
                  membersList: state.assessments,
                  ahc: _cubit.scheduleBookingRepo.ahc,
                ),
              ))
              .then((value) => _stateProvider
                  .notify(ObserverState.REFRESH_MEDICAL_TEST_DETAILS));
        }
      },
      buildWhen: (prev, curr) => curr is AssessmentDataLoaded,
      builder: (context, state) {
        if (state is AssessmentDataLoaded)
          return BookingAcknowledgementWidget(
            ppmcAssessmentsResponse: state.ppmcAssessmentsResponse,
            notificationActionData: widget.notificationActionData,
            policyType: widget.policyType,
            ctaLoading: ctaLoading,
            firstCtaDisabled: firstCtaDisabled,
            secondCtaDisabled: secondCtaDisabled,
            ctaDisabled: ctaDisabled,
            updateState: (bool newCtaLoading, bool newFirstCtaDisabled, bool newSecondCtaDisabled, bool newCtaDisabled) {
              setState(() {
                ctaLoading = newCtaLoading;
                firstCtaDisabled = newFirstCtaDisabled;
                secondCtaDisabled = newSecondCtaDisabled;
                ctaDisabled = newCtaDisabled;
              });
            },
          );
        else
          return ShimmerView();
      },
    );
  }
}
