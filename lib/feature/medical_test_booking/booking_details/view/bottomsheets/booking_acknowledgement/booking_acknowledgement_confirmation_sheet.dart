import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/cubit/booking_acknowledgement/booking_acknowledgement_cubit.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/cubit/booking_acknowledgement/booking_acknowledgement_states.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/view/screens/booking_acknowledgement_success.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/view/widgets/member_selection_widget.dart';
import 'package:acko_flutter/feature/medical_test_booking/schedule_booking/domain/model/assessments_response.dart';
import 'package:design_module/uikit/widgets/button/acko_button.dart';
import 'package:design_module/uikit/widgets/button/uikit_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';

class DeniedMemberSelectionSheet extends StatefulWidget {
  final String? assessmentGroupId;
  final List<PpmcAssessments>? assessments;
  final String? acknowledgeUrl;

  const DeniedMemberSelectionSheet({
    super.key,
    this.assessmentGroupId,
    this.assessments,
    this.acknowledgeUrl,
  });

  @override
  State<DeniedMemberSelectionSheet> createState() =>
      _DeniedMemberSelectionSheetState();
}

class _DeniedMemberSelectionSheetState
    extends State<DeniedMemberSelectionSheet> {
  List<PpmcAssessments>? membersList;
  late AcknowledgeBookingCubit _cubit;

  @override
  void initState() {
    super.initState();
    _cubit = BlocProvider.of<AcknowledgeBookingCubit>(context);
    membersList = widget.assessments;
  }

  @override
  Widget build(BuildContext context) {
    final anyMemberSelected =
        membersList?.any((member) => member.isSelected) ?? false;

    return BlocListener<AcknowledgeBookingCubit, AcknowledgeBookingState>(
      listenWhen: (prev, current) => current is AcknowledgeNegativeLoaded,
      listener: (context, state) {
        if (state is AcknowledgeNegativeLoaded) {
          Navigator.of(context).push(MaterialPageRoute(
            builder: (context) => AvailibilityConfirmScreen(
              membersList: state.assessments,
              ahc: _cubit.scheduleBookingRepo.ahc,
            ),
          ));
        }
      },
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: SDUIText(
                    value: lab_ack_select_member,
                    textStyle: "hXSmall",
                    textColor: color040222,
                    maxLines: 5,
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: const Icon(Icons.close),
                ),
              ],
            ),
            ListView.separated(
              padding: const EdgeInsets.only(top: 30, bottom: 24),
              shrinkWrap: true,
              physics: const ClampingScrollPhysics(),
              itemBuilder: (context, index) {
                final member = membersList?[index];
                final entityDetails = member?.entityDetails;
                final isSelected = member?.isSelected ?? false;
                return member != null
                    ? MemberSelectionWidget(
                  member: entityDetails,
                  isSelected: isSelected,
                  onTap: () {
                    setState(() {
                      membersList?[index].isSelected = !isSelected;
                    });
                  },
                )
                    : const SizedBox.shrink();
              },
              separatorBuilder: (context, index) => const Padding(
                padding: EdgeInsets.symmetric(vertical: 16),
                child: Divider(
                  color: colorE7E7F0,
                  thickness: 1.0,
                ),
              ),
              itemCount: membersList?.length ?? 0,
            ),
            AckoDarkButtonFullWidth(
              text: confirm_cta_txt,
              onTap: () {
                Navigator.pop(context, true);
                _cubit.acknowledgeBooking(
                  widget.acknowledgeUrl,
                  widget.assessments,
                  widget.assessmentGroupId,
                );
              },
              buttonState: anyMemberSelected
                  ? UIKitButtonState.active
                  : UIKitButtonState.disabled,
            ),
          ],
        ),
      ),
    );
  }
}