import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/cubit/booking_details/booking_details_cubit.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/domain/model/booking_details_response.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/view/widgets/booking_details_card.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/view/widgets/completed_tests.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/view/widgets/pending_tests.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/view/widgets/precautions_widget.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/view/widgets/required_tests.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/view/widgets/test_details_info_banner.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/view/widgets/test_details_note.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/view/widgets/test_details_overview_section.dart';
import 'package:acko_flutter/feature/medical_test_booking/booking_details/view/widgets/test_details_phlebo_card.dart';
import 'package:acko_flutter/feature/medical_test_booking/common/utils/booking_constants.dart';
import 'package:flutter/material.dart';
import 'package:sdui/sdui.dart';

class TestDetailsBottomsheet extends StatelessWidget {
  final ActionData? memberActionData;
  final BookingDetailsCubit? cubit;
  final AssessmentPolicyType? policyType;
  const TestDetailsBottomsheet(
      {super.key,
      this.memberActionData,
      this.cubit,
      this.policyType = AssessmentPolicyType.health});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 24, 16, 32),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          /// header
          ..._buildSheetHeader(context),
          _buildSheetBody()
        ],
      ),
    );
  }

  List<Widget> _buildSheetHeader(context) => [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SDUIText(
              value: memberActionData?.title,
              textStyle: "hMedium",
              textColor: color121212,
            ),
            Spacer(),
            GestureDetector(
              child: Icon(Icons.close),
              onTap: () {
                Navigator.of(context).pop();
              },
            )
          ],
        ),
        SDUIText(
          value: memberActionData?.subTitle,
          textStyle: "hSmall",
          textColor: color5920C5,
        ),
        const SizedBox(
          height: 12,
        )
      ];

  Widget _buildSheetBody() {
    final phleboDetails = memberActionData?.phleboDetails;
    final infoBanner = memberActionData?.infoBanner;
    final testRequired = memberActionData?.testRequired;
    final completedTests = memberActionData?.completedTests;
    final pendingTests = memberActionData?.pendingTests;
    final note = memberActionData?.note;
    final memberActiontitle = memberActionData?.title;
    final precautions = memberActionData?.precautions;
    final testOverviewDetailsSectionData =
        memberActionData?.testOverviewDetailsSection;

    return Flexible(
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            BookingDetailsWidget(
              memberActionData: memberActionData,
              policyType: policyType,
              cubit: cubit,
            ),
            PhleboDetailsWidget(phleboDetails: phleboDetails),
            InfoBannerWidget(infoBanner: infoBanner, policyType: policyType, cubit: cubit,),
            TestsRequiredWidget(testRequired: testRequired),
            CompletedTestsWidget(completedTests: completedTests),
            PendingTestsWidget(pendingTests: pendingTests),
            NoteWidget(note: note, title: memberActiontitle),
            PrecautionsWidget(precautions: precautions),
            TestOverviewDetailsSectionWidget(
                testOverviewDetailsSection: testOverviewDetailsSectionData)
          ],
        ),
      ),
    );
  }
}
