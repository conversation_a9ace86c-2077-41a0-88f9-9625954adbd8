import 'package:acko_flutter/feature/support_sdui/models/support_alerts_meta_data.dart';
import 'package:acko_flutter/feature/support_sdui/parser/support_parser.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:flutter/material.dart';
import 'package:sdui/sdui.dart';
import 'package:session_manager_module/StorageManager/shared_preferences_storage.dart';
import 'package:utilities/remote_config/remote_config.dart';
import 'package:utilities/state_provider/StateProvider.dart';
import 'package:utilities/widgets/acko_safe_cubit.dart';

import '../../../common/util/PreferenceHelper.dart';
import '../../tab_view_sdui/constants/home_tab_constants.dart';

class SupportSDUIBloc extends AckoSafeCubit<SupportSDUIBlocState> {
  final BaseRepository _baseRepository = BaseRepository();
  Map<SupportSectionType, Widget> supportWidgets = {};
  final SupportParser _supportParser = SupportParser();
  final String url;

  SupportSDUIBloc(this.url) : super(SupportLoadingState()) {
    _getSupportPageView();
  }

  refresh() {
    emit(SupportLoadingState());
    _getSupportPageView();
  }

  refreshIntro() async {
    final currentState = state;

    if (currentState is SupportLoadedState ||
        currentState is SupportIntroLoadedState) {
      final header = (currentState as dynamic).header;
      final body = (currentState as dynamic).body;

      emit(SupportIntroLoadingState(header: header, body: body));

      final response = await _baseRepository.getResponse(url);

      if (response.error == null &&
          response.data != null &&
          response.data!['widgets'] != null) {
        final updatedWidgets = _supportParser.fromJson(response.data!);
        final introWidget = updatedWidgets[SupportSectionType.intro];

        if (introWidget != null) {
          emit(SupportIntroLoadedState(
            header: header,
            intro: introWidget,
            body: body,
          ));
        }
      }
    }
  }

  _getSupportPageView() async {
    emit(SupportLoadingState());

    /// call repository and get json
    ResponseWrapper response = await _baseRepository.getResponse(url);

    if (response.error == null) {
      /// create widget here in the bloc itself and pass it to UI for rendering.
      if (response.data != null && response.data!['widgets'] != null) {
        supportWidgets = _supportParser.fromJson(response.data!);
        if (supportWidgets.containsKey(SupportSectionType.header) &&
            supportWidgets.containsKey(SupportSectionType.body)) {
          emit(SupportLoadedState(
            header: supportWidgets[SupportSectionType.header]!,
            intro: supportWidgets[SupportSectionType.intro],
            body: supportWidgets[SupportSectionType.body]!,
          ));
        } else {
          emit(SupportErrorState("Required sections missing in SDUI response"));
        }
      } else {
        emit(SupportErrorState("An error occurred in SDUI repository"));
      }
      if (response.data != null && response.data!['metaData'] != null) {
        SupportAlertsMetaData supportAlertsData =
            _supportParser.parseSupportAlertsMetaData(response.data!);
        StateProvider().notify(ObserverState.SUPPORT_ALERTS_UPDATES, data: {
          "badgeComponent": supportAlertsData.supportAlertsCountBadge,
          "tabBarComponent": supportAlertsData.tabBarComponent
        });
        handleNewSupportAlerts(supportAlertsData.alertsIds);
      }
    } else {
      emit(SupportErrorState("An error occurred in SDUI repository"));
    }
  }

  Future<void> handleNewSupportAlerts(List<String> newAlertIds) async {
    final v10 = (await RemoteConfigInstance.instance.getGbAsyncData(RemoteConfigKeysSet.APP_IA_VERSION)).toString().equalsIgnoreCase("app_v10");
    List<String>? storedAlertIds = await getListPrefs(
            ListStringDataSharedPreferenceKeys.SUPPORT_ALERT_SERVICE_ID) ??
        [];

    List<String> newIds =
        newAlertIds.where((id) => !storedAlertIds.contains(id)).toList();

    if (newIds.isNotEmpty) {
      StateProvider()
          .notify(ObserverState.CHANGE_TAB, data: {"index": 3, "tab": HomeTabs.SUPPORT});
      setListPrefs(ListStringDataSharedPreferenceKeys.SUPPORT_ALERT_SERVICE_ID,
          newAlertIds);
    }
  }
}

@immutable
abstract class SupportSDUIBlocState {}

class SupportLoadingState extends SupportSDUIBlocState {}

class SupportLoadedState extends SupportSDUIBlocState {
  final Widget header;
  final Widget? intro;
  final Widget body;

  SupportLoadedState({required this.header,
    this.intro,
    required this.body});
}

class SupportIntroLoadingState extends SupportSDUIBlocState {
  final Widget header;
  final Widget body;
  SupportIntroLoadingState({
    required this.header,
    required this.body,
  });
}

class SupportIntroLoadedState extends SupportSDUIBlocState {
  final Widget header;
  final Widget? intro;
  final Widget body;

  SupportIntroLoadedState({
    required this.header,
    this.intro,
    required this.body,
  });
}

class SupportErrorState extends SupportSDUIBlocState {
  final String errorMessage;

  SupportErrorState(this.errorMessage);
}
