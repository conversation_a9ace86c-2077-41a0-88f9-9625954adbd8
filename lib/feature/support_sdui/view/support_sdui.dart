import 'package:acko_flutter/feature/support_sdui/bloc/support_sdui_bloc.dart';
import 'package:acko_flutter/network/ApiResponse.dart';
import 'package:design_module/utilities/hide_scroll_glow_effect.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:utilities/state_provider/StateProvider.dart';
import 'package:utilities/visibility_detector/src/visibility_detector.dart';
import '../../../common/util/strings.dart';
import '../../../common/view/ErrorWidget.dart';
import '../../../common/view/shimmer_view.dart';
import '../../acko_services/ui/acko_service_loading_screen.dart';


class SupportSduiScreen extends StatefulWidget {
  @override
  State<SupportSduiScreen> createState() => _SupportSduiScreenState();
}

class _SupportSduiScreenState extends State<SupportSduiScreen>
    with AutomaticKeepAliveClientMixin, ErrorViewRetryCallBack, TickerProviderStateMixin implements StateListener {

  bool _isIntroVisible = false;
  StateProvider _stateProvider = StateProvider();

  @override
  void initState() {
    super.initState();
    _stateProvider.subscribe(this);
  }

  @override
  void dispose() {
    super.dispose();
    _stateProvider.dispose(this);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      body: BlocBuilder<SupportSDUIBloc, SupportSDUIBlocState>(
        builder: (context, state) {
          if (state is SupportLoadingState) {
            return SizedBox(
                width: MediaQuery.of(context).size.width,
                height: MediaQuery.of(context).size.height,
                child: Center(child: AckoServiceLoadingScreen()));
          } else if (state is SupportErrorState) {
            return ErrorPage(
                errorHandler: ErrorHandler(something_went_wrong),
                retryCallbackFunc: this);
          } else if (state is SupportLoadedState ||
              state is SupportIntroLoadingState ||
              state is SupportIntroLoadedState) {

            final header = (state as dynamic).header;
            final body = (state as dynamic).body;
            final Widget intro = getIntroWidgetFromState(state);

            return ScrollConfiguration(
              behavior: HideScrollGlowEffect(),
              child: RefreshIndicator(
                onRefresh: () async {
                  if (mounted) {
                    context.read<SupportSDUIBloc>().refresh();
                  }
                },
                child: ListView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: EdgeInsets.only(bottom: 64),
                  children: [
                    header,
                    VisibilityDetector(
                      key: const Key('chatbot-detector'),
                      onVisibilityChanged: (info) {
                        if (info.visibleFraction > 0 && !_isIntroVisible) {
                          setState(() => _isIntroVisible = true);
                        }
                      },
                      child: _isIntroVisible
                          ? intro
                          : Container(
                                height: 0.1,
                                color: Colors.transparent,
                              )),
                    body,
                  ],
                ),
              ),
            );
          } else {
            return const Text("Failed to parse widget");
          }
        },
      ),
    );
  }

  Widget getIntroWidgetFromState(dynamic state) {
    return switch (state) {
      SupportLoadedState s => s.intro ?? const SizedBox.shrink(),
      SupportIntroLoadedState s => s.intro ?? const SizedBox.shrink(),
      SupportIntroLoadingState _ => getSimpleShimmerView(
        double.infinity,
        304,
        padding: const EdgeInsets.only(top: 24, left: 16.0, right: 16.0),
        borderRadius: 20.0,
      ),
      _ => const SizedBox.shrink(),
    };
  }

  @override
  bool get wantKeepAlive => true;

  @override
  onRetry() {
    SupportSDUIBloc bloc = BlocProvider.of<SupportSDUIBloc>(context);
    bloc.refresh();
  }

  @override
  void onStateChanged(ObserverState state, {data}) {
    if (state == ObserverState.REFRESH_SUPPORT_TAB && mounted) {
      SupportSDUIBloc bloc = BlocProvider.of<SupportSDUIBloc>(context);
      bloc.refreshIntro();
    }
  }
}
