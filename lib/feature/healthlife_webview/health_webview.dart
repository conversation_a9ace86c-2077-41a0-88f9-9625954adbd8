import 'dart:convert';

import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/common/view/AckoText.dart';
import 'package:acko_flutter/common/view/Application.dart';
import 'package:acko_flutter/feature/claim/advance_cash/common/advance_cash_repository.dart';
import 'package:acko_flutter/feature/claim/advance_cash/common/view/advance_cash_ready_reject_page.dart';
import 'package:acko_flutter/feature/claim/advance_cash/onboarding/view/cash_approved_page.dart';
import 'package:acko_flutter/feature/claim/claim_intro/customer_education/bloc/doc_config_bloc.dart';
import 'package:acko_flutter/feature/claim/claim_intro/customer_education/ce_fnol_doc.dart';
import 'package:acko_flutter/feature/claim/my_claims_home/model/my_claims_models.dart';
import 'package:acko_flutter/feature/endorsement/core/util/health_jm_constants.dart';
import 'package:acko_flutter/feature/health_home/models/health_header_data_model.dart';
import 'package:acko_flutter/feature/health_home/views/one_mg_tnc.dart';
import 'package:acko_flutter/feature/health_policy/common/health_policy_util.dart';
import 'package:acko_flutter/feature/health_policy/repository/health_policy_repository.dart';
import 'package:acko_flutter/feature/healthlife_webview/health_life_web_view_actions.dart';
import 'package:acko_flutter/feature/vas/health_vas_services_standalone.dart';
import 'package:acko_flutter/feature/vas/vas_models.dart';
import 'package:acko_flutter/feature/webview/web_view_actions.dart';
import 'package:acko_flutter/feature/webview/web_view_model.dart';
import 'package:acko_flutter/r2d2/model/data.dart';
import 'package:acko_flutter/util/TrackerManager.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/download_util.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_flutter/util/health/health_constants.dart';
import 'package:acko_web_view_module/bloc/states.dart';
import 'package:acko_web_view_module/common/appbar_action.dart';
import 'package:acko_web_view_module/common/custom_dartz.dart';
import 'package:acko_web_view_module/lob_contract_classes/view_contract.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:session_manager_module/SessionManager/session_manager_module.dart';
import 'package:session_manager_module/StorageManager/shared_preferences_storage.dart';
import 'package:utilities/constants/constants.dart';
import 'package:acko_core_utilities/deeplink_handler/DeeplinkHandler.dart';
import 'package:utilities/remote_config/remote_config.dart';
import 'package:utilities/state_provider/StateProvider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class HealthWebViewLogic extends WebViewUIBase {
  late InAppWebViewController healthWebViewController;
  StateProvider _stateProvider = StateProvider();
  bool _isGMCPolicyLinked = false;
  List<String>? hideHeaderActionUrls;

  @override
  void addJsHandler(InAppWebViewController controller) {}

  @override
  Future<Either<NotHandlingMethod, dynamic>> handleWebViewActions(
      args, InAppWebViewController controller) async {
    checkAndSetIfPageContextIsUnmounted();
    var result;
    Map<String, dynamic> jsonData = jsonDecode(args.trim());
    WebViewModel model = WebViewModel.fromJson(jsonData);
    switch (model.action) {
      case HealthLifeWebViewActions.OPEN_WEB_VIEW:
        var url = model.actionValue!.url!;
        webviewState?.handlePageLoaderVisibility(true);
        final result = await _healthVasServicesHandling(
            url: url, partner: model.actionValue?.text);
        webviewState?.handlePageLoaderVisibility(false);
        if (result) return Left(NotHandlingMethod());

        url = url.startsWith("https")
            ? url
            : url.startsWith("/")
                ? Constants.BASE_URL + url.replaceFirst("/", "")
                : Constants.BASE_URL + url;
        var map = url.getRoute();
        if (map != null) {
          if (map["route"] == Routes.WEB_PAGE_V2) {
            bool loadingGMC = url.contains(Constants.EMAIL_VERIFICATION_URL);
            bool isLoadingHealth = url.contains('health');
            final headerText =
                'is_gmc_url_loaded=$loadingGMC&source=aarogya_sanjeevani_page';
            url += url.contains('?') ? '&$headerText' : '?$headerText';
            if (loadingGMC) {
              url += '&hide_header=true&hide_back_arrow=false';
            } else if (isLoadingHealth) {
              url += '&hide_header=true';
            }
            final webviewArguments = {
              "url": url,
            };
            var result = await Navigator.pushNamed(
                pageContext!, Routes.WEB_PAGE_V2,
                arguments: webviewArguments);
            WebViewResult? webViewResult = result as WebViewResult?;
            if (webViewResult != null &&
                webViewResult.result == Result.RESULT_OK &&
                webViewResult.data != null) {
              if (webViewResult.data!.containsKey("is_from_gmc_page") &&
                  webViewResult.data!["is_from_gmc_page"]) {
                final uri = WebUri(url);
                final source = uri.queryParameters['source'] ?? '';
                final response = await checkGmcPolicyStatus(source);
                if (response?.hasAnyHealthPolicy == true) {
                  Navigator.of(pageContext!).pop(true);
                }
              } else if (webViewResult.data!
                      .containsKey("is_from_policy_success") &&
                  webViewResult.data!["is_from_policy_success"]) {
                Navigator.of(pageContext!).pop(true);
              }
            }
          } else {
            if (map["route"] == "health_card") {
              _stateProvider.notify(ObserverState.MyAccount_Refresh);
              Navigator.pushNamed(pageContext!, Routes.HEALTH_POLICY_CARDS,
                  arguments: {'isFromCoveragesPage': true});
            } else {
              Navigator.pushNamed(pageContext!, map["route"], arguments: map);
            }
          }
        }
        break;
      case HealthLifeWebViewActions.OPEN_HEALTH_RENEWAL:
        Uri uri = Uri.parse(model.actionValue?.url ?? '');
        final param = uri.queryParameters;
        Navigator.pushNamedAndRemoveUntil(
          pageContext!,
          Routes.HEALTH_RENEWAL_HOME_PAGE,
          ModalRoute.withName(
            Routes.APP_HOME,
          ),
          arguments: {
            'policy_number': param['policy_number'],
            'show_modal': false,
          },
        );
        break;
      case HealthLifeWebViewActions.REFRESH_MY_ACCOUNT:
        _stateProvider.notify(ObserverState.MyAccount_Refresh);
        Navigator.pop(pageContext!);
        break;
      case HealthLifeWebViewActions.CLAIMS_EDUCATION_COMPLETED:
        SessionManager.instance.storageManager.setBoolDataInPreferences(BoolDataSharedPreferenceKeys.CLAIMS_EDUCATION_COMPLETED, true);
        Navigator.pop(pageContext!);
        break;
      case HealthLifeWebViewActions.TRACK_CLAIM:
        ClaimInfoSummary? claimInfo = model.actionValue?.claimsData;

        bool isFirstTimeAdvanceCash = false;
        var subClaim = (claimInfo?.subClaims?.isNotNullOrEmpty ?? false)
            ? claimInfo!.subClaims!.first
            : null;
        var acrId = subClaim?.advanceCashInfo?.acrId;
        var status = subClaim?.advanceCashInfo?.status ?? '';
        var claimType = claimInfo?.claimType;
        if (claimType.isNotNullAndEmpty &&
            claimType.equalsIgnoreCase(advance_cash_label)) ;
        isFirstTimeAdvanceCash = await _checkIfAdvanceCashOpenedFirstTime(
            acrId, status, pageContext!, claimInfo);
        if (!isFirstTimeAdvanceCash) {
          TrackerManager.instance.instrumentUserAction(
              AdvanceCashEvents.TAP_BTN_ADVCASH_DETAILS,
              {"page": "Claims list", "status": "$status"});
          Navigator.pushNamed(pageContext!, Routes.IPD_TRACK_STATUS_PAGE, arguments: {
            'claimId': claimInfo?.ahecClaimId ?? null,
            'isIpd': true,
            'allSubClaimInfo': claimInfo?.subClaims,
            'claimsUrl': claimInfo?.claimsUrl
          });
        }
        break;
    }
    return Right(NotHandlingMethod());
  }

  @override
  void blocStateListener(WebViewState state) {}

  @override
  Future<Either<NotHandlingMethod, bool>> canGoBack(
      InAppWebViewController controller) async {
    final currentURL = await controller.getUrl();
    if (HealthPolicyUtils.isHealthVasService(currentURL)) {
      if (pageContext != null && pageContext!.mounted) {
        Navigator.pop(pageContext!);
      }
      return Right(true);
    }
    return Left(NotHandlingMethod());
  }

  checkAndSetIfPageContextIsUnmounted() {
    if ((pageContext?.mounted ?? false) == false) {
      pageContext = navigatorKey.currentContext!;
    }
  }

  Future<bool> _checkIfAdvanceCashOpenedFirstTime(int? acrId, String status,
      BuildContext context, ClaimInfoSummary? claimInfo) async {
    final key = acrId.toString() + ":" + status;
    final acrFlagValue = await SessionManager.instance.storageManager
        .getStringDataInPreferences(StringDataSharedPreferenceKeys.ACR_FLAG);
    String statuses = (acrFlagValue.isNotEmpty ? acrFlagValue : "[]");
    List<dynamic> keys = jsonDecode(statuses);
    if (!keys.contains(key)) {
      if (status.equalsIgnoreCase('request-approved')) {
        claimInfo = await AdvanceCashRepository().getAdvanceCashDetails(acrId);
        Navigator.of(context).push(
            MaterialPageRoute(builder: (_) => CashApprovedPage(claimInfo)));
      } else if (status.equalsIgnoreCase('request-rejected')) {
        Navigator.of(context).push(MaterialPageRoute(
            builder: (_) =>
                AdvanceCashReadyRejectPage('ac_reject', "Claims list page")));
      }
      keys.add(key);
      SessionManager.instance.storageManager.setStringDataInPreferences(
          StringDataSharedPreferenceKeys.ACR_FLAG, jsonEncode(keys));

      return Future.value(true);
    }
    return Future.value(false);
  }

  @override
  void dispose() {}

  @override
  Either<NotHandlingMethod, HandlingVoidMethod> onAppBarActionPressed(
      String actionValue) {
    if (loadingUrl == null) {
      return Left(NotHandlingMethod());
    }
    checkAndSetIfPageContextIsUnmounted();
    final uri = Uri.parse(loadingUrl!);
    String productType = uri.queryParameters['product_type'] ?? '';
    final helpUrl = productType.equalsIgnoreCase('retail')
        ? Constants.RETAIL_HEALTH_HELP_PAGE
        : productType.equalsIgnoreCase(Constants.TERM_LIFE_POLICY)
            ? Constants.TERM_LIFE_HELP_HOME
            : Constants.ENTERPRISE_HELP_PAGE;
    if (actionValue == 'HEALTH_HELP_CLICKED') {
      final redirectionUrl =
          Util.getFormattedRedirectionUrl(helpUrl, false, true);
      Navigator.pushNamed(pageContext!, Routes.WEB_PAGE_V2, arguments: {
        'url': redirectionUrl,
      });
    } else if (actionValue == 'HEALTH_FAQ_CLICKED') {
      final redirectionUrl =
          Util.getFormattedRedirectionUrl(Constants.RETAIL_FAQ, false, true);
      Navigator.pushNamed(pageContext!, Routes.WEB_PAGE_V2, arguments: {
        'url': redirectionUrl,
      });
    }
    return Left(NotHandlingMethod());
  }

  @override
  void onCloseWindow(InAppWebViewController controller) {}

  @override
  void onConsoleMessage(
      InAppWebViewController controller, ConsoleMessage message) {
    debugPrint("=======> console message ${message.message}");
  }

  @override
  Either<NotHandlingMethod, HandlingVoidMethod> onDownloadStart(
      InAppWebViewController controller,
      DownloadStartRequest downloadStartRequest) {
    return Left(NotHandlingMethod());
  }

  @override
  void onLoadError(
      InAppWebViewController controller, Uri? url, int code, String message) {
    debugPrint("=======> _onLoadError $url error $message code $code");
    webviewState?.handlePageLoaderVisibility(false);
  }

  @override
  void onLoadStart(InAppWebViewController controller, Uri? url) {
    debugPrint("=======> _onLoadStart " + url.toString());
    webviewState?.handlePageLoaderVisibility(true);
    FirebaseCrashlytics.instance.log("webview open Url " + url.toString());
  }

  @override
  void onLoadStop(InAppWebViewController controller, Uri? url) {
    debugPrint("=======> _onLoadStop" + url.toString());
    webviewState?.handlePageLoaderVisibility(false);
  }

  @override
  void onPageCommitVisible(InAppWebViewController controller, Uri? url) {}

  @override
  void onWebViewCreated(InAppWebViewController controller) async {
    var data = await RemoteConfigInstance.instance.getData(
      RemoteConfigKeysSet.HL_P2I_URLS,
    );

    if (data == null || data.isEmpty) {
      await FirebaseRemoteConfig.instance.fetchAndActivate();
      data = await RemoteConfigInstance.instance.getData(
        RemoteConfigKeysSet.HL_P2I_URLS,
      );
    }

    if (data != null && data.isNotEmpty) {
      try {
        Map<String, dynamic>? jsonData = jsonDecode(data);
        List<String>? healthUrls = jsonData?['health'] != null
            ? List<String>.from(jsonData?['health'])
            : [];
        hideHeaderActionUrls = healthUrls;
      } catch (e) {
        debugPrint('Error decoding JSON: $e');
      }
    } else {
      debugPrint('Remote config data is null or empty.');
    }

    healthWebViewController = controller;
    final showFAQ = loadingUrl?.contains('/health/segmentV2') == true ||
        loadingUrl?.contains('p/health/transactions') == true;
    final showHelp = (loadingUrl?.contains('health') ?? false) &&
        !_isHeaderActionDisabled(loadingUrl, hideHeaderActionUrls) &&
        loadingUrl?.toLowerCase().contains("help") == false &&
        loadingUrl?.toLowerCase().contains("support") == false;
    final uri = WebUri(loadingUrl ?? '');
    String productType = uri.queryParameters['product_type'] ?? '';
    final helpUrl = productType == 'retail'
        ? Constants.RETAIL_HEALTH_HELP_PAGE
        : productType == Constants.TERM_LIFE_POLICY
            ? Constants.TERM_LIFE_HELP_HOME
            : Constants.ENTERPRISE_HELP_PAGE;

    List<AppBarAction> actionsWidget = [];
    if (showFAQ) {
      actionsWidget.add(AppBarAction(
        actionValue: 'HEALTH_FAQ_CLICKED',
        actionWidget: _faq(Constants.RETAIL_FAQ),
      ));
    } else if (showHelp) {
      actionsWidget.add(AppBarAction(
        actionValue: 'HEALTH_HELP_CLICKED',
        actionWidget: _helpView(helpUrl),
      ));
    }
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      webviewState?.addAppBarActions(actionsWidget);
    });
  }

  bool _isHeaderActionDisabled(
      String? loadingUrl, List<String>? hideHeaderActionUrls) {
    if (loadingUrl.isNotNullOrEmpty ||
        (hideHeaderActionUrls != null && hideHeaderActionUrls.isNotEmpty)) {
      return true;
    }
    for (String url in hideHeaderActionUrls!) {
      if (loadingUrl.containsIgnoreCase(url)) {
        return false;
      }
    }
    return true;
  }

  @override
  Future<Either<NotHandlingMethod, NavigationActionPolicy>>
      shouldOverrideUrlLoading(
          InAppWebViewController controller, NavigationAction action) async {
    String url = action.request.url.toString();
    checkAndSetIfPageContextIsUnmounted();
    if (!_isGMCPolicyLinked) {
      _isGMCPolicyLinked =
          url.equalIgnoreCase(Constants.HEALTH_SUCCESSFUL_ENROLLMENT_URL);
      if (_isGMCPolicyLinked)
        _stateProvider.notify(ObserverState.MyAccount_Refresh);
    }

    if (url.contains('health/pdf') ||
        url.contains('Retail%20Health%20Policy.pdf')) {
      _downloadPolicy(url, controller);
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    } else if (RemoteConfigInstance.instance
                .getData(RemoteConfigKeysSet.IS_NATIVE_PAYMENT_FLOW) &&
            url.containsIgnoreCase(Constants.PAYMENT_URL) ||
        (!Constants.IS_PROD &&
            kDebugMode &&
            (url.containsIgnoreCase("/p/health/transactionSuccess") ||
                url.containsIgnoreCase("/p/health/as/transactionSuccess")))) {
      // Health mock payment success condition
      if (!Constants.IS_PROD &&
          kDebugMode &&
          (url.containsIgnoreCase("/p/health/transactionSuccess") ||
              url.containsIgnoreCase("/p/health/as/transactionSuccess"))) {
        URLRequest? urlRequest =
            URLRequest(url: await blocInstance?.redirectWithNewCookies(url));
        if (urlRequest.url != null)
          healthWebViewController.loadUrl(urlRequest: urlRequest);
        return Future.value(Right(NavigationActionPolicy.ALLOW));
      }
      return Future.value(Right(NavigationActionPolicy.ALLOW));
    } else if (url.contains(Constants.MY_ACCOUNT_URL) ||
        url.equalIgnoreCase(Constants.webHomeUrl) ||
        url.startsWith(Constants.webHomeUtmUrl) ||
        url.equalIgnoreCase(Constants.BASE_URL) ||
        url.equalIgnoreCase(
            Constants.BASE_URL.substring(0, (Constants.BASE_URL.length - 1)))) {
      bool isPaymentNewTab =
          action.request.url?.queryParameters['payment_new_tab'] as bool? ??
              false;
      bool isGMCLinkUrlLoaded =
          action.request.url?.queryParameters['isGmcLinkUrlLoaded'] as bool? ??
              false;
      if (isGMCLinkUrlLoaded) {
        Navigator.pushNamedAndRemoveUntil(
            pageContext!, Routes.APP_HOME, (route) => false);
        return Future.value(Right(NavigationActionPolicy.CANCEL));
      }
      if (isPaymentNewTab) {
        Navigator.pushNamedAndRemoveUntil(
            pageContext!, Routes.APP_HOME, (route) => false);
        return Future.value(Right(NavigationActionPolicy.CANCEL));
      } else {
        Navigator.pop(
          pageContext!,
          WebViewResult(
            result: Result.RESULT_OK,
            data: _isGMCPolicyLinked
                ? {'is_from_policy_success': true}
                : isGMCLinkUrlLoaded
                    ? {"is_from_gmc_page": true}
                    : null,
          ),
        );
        _stateProvider.notify(ObserverState.MyAccount_Refresh);
        return Future.value(Right(NavigationActionPolicy.CANCEL));
      }
    } else {
      return _handleUrlInterceptions(controller, url);
    }
  }

  Future<Either<NotHandlingMethod, NavigationActionPolicy>>
      _handleUrlInterceptions(
          InAppWebViewController controller, String urlOverloading) async {
    debugPrint("webview intercept " + urlOverloading);

    Uri urlStr = Uri.parse(urlOverloading);
    if (urlStr.path.containsIgnoreCase('/claims/file_claim')) {
      Map<String, dynamic> qParams = {};
      if (urlStr.queryParameters.containsKey('acr_id')) {
        qParams['acr_id'] = urlStr.queryParameters['acr_id'];
      }
      if (urlStr.queryParameters.containsKey('load_from_draft')) {
        qParams['load_from_draft'] =
            urlStr.queryParameters['load_from_draft']?.equalsIgnoreCase('true');
      }
      if (urlStr.queryParameters.containsKey('incident_reason')) {
        qParams['incident_reason'] = urlStr.queryParameters['incident_reason'];
      }
      if (urlStr.queryParameters.containsKey('has_pending_payment')) {
        qParams['has_pending_payment'] = urlStr
            .queryParameters['has_pending_payment']
            ?.equalsIgnoreCase('true');
      }
      if (urlStr.queryParameters.containsKey('is_advance_cash_flow')) {
        qParams['is_advance_cash_flow'] = urlStr
            .queryParameters['is_advance_cash_flow']
            ?.equalsIgnoreCase('true');
      }
      if (urlStr.queryParameters.containsKey('is_claim_for_advance_cash')) {
        qParams['is_claim_for_advance_cash'] = urlStr
            .queryParameters['is_claim_for_advance_cash']
            ?.equalsIgnoreCase('true');
      }
      Navigator.pushNamed(pageContext!, Routes.FILE_HEALTH_FNOL,
          arguments: qParams);
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    } else if (urlStr.path.containsIgnoreCase('claims/track/status')) {
      Map<String, dynamic> qParams = {
        'claimId': urlStr.queryParameters['ahecClaimId']?.toString(),
        'policyId': urlStr.queryParameters['policyId'],
        'subClaimId': urlStr.queryParameters['subClaimId'].isNotNullOrEmpty ? urlStr.queryParameters['subClaimId'] : null,
        'claimType': urlStr.queryParameters['claimType'],
        'acrId': int.tryParse(urlStr.queryParameters['acrId'] ?? ''),
      };
      Navigator.pushNamed(
        pageContext!,
        Routes.IPD_TRACK_STATUS_PAGE,
        arguments: qParams,
      );
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    } else if (urlStr.path.containsIgnoreCase('claims/sample')) {
      Navigator.push(
          pageContext!,
          MaterialPageRoute(
              builder: (context) => BlocProvider<CeConfigBloc>(
                  create: (context) => CeConfigBloc(),
                  child: CEFNOLDocPage())));
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    } else if (urlStr.path.containsIgnoreCase('claims/acr_reject')) {
      Navigator.of(pageContext!).push(MaterialPageRoute(
          builder: (_) =>
              AdvanceCashReadyRejectPage('ac_ready', "claims education")));
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    } else if (urlStr.path.containsIgnoreCase('health/home')) {
    if (urlStr.queryParameters.containsKey('e-cards')) {
      Navigator.pushNamed(
        pageContext!,
        Routes.HEALTH_POLICY_CARDS,
        arguments: urlStr.queryParameters,
      );
    } else {
      Map<String, dynamic> qParams = {
        'policy_number': urlStr.queryParameters['policy_number'],
        'policy_id': urlStr.queryParameters['policy_id'],
        'proposal_id': urlStr.queryParameters['proposal_id'],
      };
      Navigator.pushNamed(
        pageContext!,
        Routes.HEALTH_HOME_PAGE,
        arguments: qParams,
      );
    }
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    } else if (urlStr.path.containsIgnoreCase('rapid_response_home')) {
      Navigator.pushNamed(
        pageContext!,
        Routes.RAPID_RESPONSE_HOME,
      );
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    } else if (urlStr.path.containsIgnoreCase('networkhospitals')) {
      Navigator.pushNamed(pageContext!, Routes.NETWORK_HOSPITALS_LIST);
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    } else if (urlStr.path.containsIgnoreCase('asset_policy_health')) {
      Navigator.pushNamed(
        pageContext!,
        Routes.ASSET_POLICY_HEALTH,
        arguments: urlStr.queryParameters,
      );
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    } else if (urlStr.path.equalsIgnoreCase(Urls.healthHomePage)) {
      Navigator.pop(pageContext!, WebViewResult(result: Result.RESULT_OK));
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    } else if (urlStr.toString().contains(Urls.ppeUrls)) {
      if (urlStr.toString().contains(Urls.ppeOverview)) {
        Map<String, dynamic> qParams = {};
        if (urlStr.queryParameters.containsKey('proposalId')) {
          qParams['proposalId'] = urlStr.queryParameters['proposalId'];
        }
        Navigator.pushNamedAndRemoveUntil(
            pageContext!,
            Routes.PPE_EDIT_OVERVIEW_SCREEN,
            arguments: {
              'proposal_id': qParams['proposalId'],
            },
            (route) => ((route.settings.name == Routes.APP_HOME)));
        return Future.value(Right(NavigationActionPolicy.CANCEL));
      } else if (urlStr.toString().contains(Urls.ppeSummary) ||
          urlStr.toString().contains(Urls.ppeTrack)) {
        Map<String, dynamic> qParams = {};
        if (urlStr.queryParameters.containsKey('proposalId')) {
          qParams['proposalId'] = urlStr.queryParameters['proposalId'];
        }
        Navigator.pushNamedAndRemoveUntil(
            pageContext!,
            Routes.PPE_EDIT_TRACKING_SCREEN,
            arguments: {
              'proposal_id': qParams['proposalId'],
            },
            (route) => ((route.settings.name == Routes.APP_HOME)));
        return Future.value(Right(NavigationActionPolicy.CANCEL));
      } else if (urlStr.toString().contains(Urls.ppeEdit)) {
        Map<String, dynamic> qParams = {};
        if (urlStr.queryParameters.containsKey('proposalId')) {
          qParams['proposalId'] = urlStr.queryParameters['proposalId'];
        }
        Navigator.pushNamedAndRemoveUntil(
            pageContext!,
            Routes.PPE_EDIT_SCREEN,
            arguments: {
              'proposal_id': qParams['proposalId'],
              'edit_type': EditType.MEMBER,
              'scroll_to_bottom': true
            },
            (route) => ((route.settings.name == Routes.APP_HOME)));
        return Future.value(Right(NavigationActionPolicy.CANCEL));
      }
    }
    // Ensure a default return value if no condition matches.
    return Future.value(Left(NotHandlingMethod()));
  }

  Future<bool> _healthVasServicesHandling(
      {required String url, String? partner}) async {
    final _healthVasServices =
        HealthVasServicesStandalone(context: pageContext!);
    if (url.containsIgnoreCase(Urls.docOnCallUrl)) {
      await _healthVasServices.navigateToVAS(VAS.doctorOnCall,
          triggerEvents: true);
      return true;
    } else if (url.containsIgnoreCase(Urls.labsUrl)) {
      if (partner?.containsIgnoreCase(healthians) ?? false) {
        await _healthVasServices.navigateToVAS(VAS.lab, isMedicine: false);
      } else {
        await _healthVasServices.navigateToVAS(VAS.lab, isMedicine: false);
      }
      return true;
    } else if (url.containsIgnoreCase(Urls.medicineUrl)) {
      await _healthVasServices.navigateToVAS(VAS.medicine, isMedicine: true);
      return true;
    } else if (url.containsIgnoreCase(oneMgTnC)) {
      final vasData = await VASData.fromJson(jsonDecode(RemoteConfigInstance
          .instance
          .getData(RemoteConfigKeysSet.HEALTH_VAS)));
      final service = vasData.services
          ?.where((element) => element.id.containsIgnoreCase(medicine))
          .firstOrNull;
      if (service != null) {
        final partner = service.partners
            ?.where((element) => element.id.containsIgnoreCase(oneMg))
            .firstOrNull;
        if (partner != null && partner.tnc.isNotNullOrEmpty) {
          Navigator.push(
            pageContext!,
            MaterialPageRoute(
              builder: (context) {
                return OneMGTnC(partner.tnc!);
              },
            ),
          );
        }
      }
      return true;
    } else {
      return false;
    }
  }

  Future<HealthHeaderDataModel?> checkGmcPolicyStatus(String? source) async {
    final response = await HealthHeaderApiRepo.instance.getHealthHeaderData();
    if (response.error == null) {
      if (source != null) {
        triggerAmplitudeEvent("end_enrollment_flow", {"source": source},
            feature: "webview");
      }
    }
    return response;
  }

  @override
  Future<bool> shouldSubtractKeypadSpacing(String url) async {
    return false;
  }

  Widget _helpView(String url) {
    return Container(
        padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 4.0),
        decoration: BoxDecoration(
            border: Border.all(color: color040222, width: 1.0),
            borderRadius: BorderRadius.circular(8.0)),
        margin: const EdgeInsets.only(bottom: 5.0, right: 20.0),
        child: GestureDetector(
            onTap: () {
              final redirectionUrl =
                  Util.getFormattedRedirectionUrl(url, false, true);
              Navigator.pushNamed(pageContext!, Routes.WEB_PAGE_V2, arguments: {
                'url': redirectionUrl,
              });
            },
            child: Row(children: [
              SvgPicture.asset("${assetImage}ic_health_call.svg",
                  height: 16.0, color: color040222),
              Padding(
                  padding: const EdgeInsets.only(left: 6.0),
                  child: TextEuclidSemiBold(help_text, textColor: color040222))
            ])));
  }

  Widget _faq(String url) => Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 4.0),
        child: SvgPicture.asset("${assetImage}ic_faq.svg"),
      );

  _downloadPolicy(String url, InAppWebViewController controller) {
    if (url.contains('abha_card')) {
      Downloader.downloadFileAndPreview(url, null, 'abha_card.pdf');
    } else {
      Downloader.download(url, controller: controller);
    }
  }

  @override
  Either<NotHandlingMethod, HandlingVoidMethod> onCreateWindow(
      InAppWebViewController controller, CreateWindowAction request) {
    return Left(NotHandlingMethod());
  }

  @override
  void onPageCompleteLoaded(InAppWebViewController controller) {
    // TODO: implement onPageCompleteLoaded
  }
}
