import 'dart:io';

import 'package:acko_flutter/common/util/disable_screenshot_mixin.dart';
import 'package:acko_flutter/common/view/Application.dart';
import 'package:acko_flutter/feature/healthlife_webview/constants/hl_webview_string_constants.dart';
import 'package:acko_flutter/util/TrackerManager.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_flutter/util/health/utils.dart';
import 'package:acko_web_view_module/bloc/states.dart';
import 'package:acko_web_view_module/common/custom_dartz.dart';
import 'package:acko_web_view_module/lob_contract_classes/view_contract.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_inappwebview/src/in_app_webview/in_app_webview_controller.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:utilities/constants/constants.dart';
import 'package:acko_core_utilities/deeplink_handler/DeeplinkHandler.dart';

class LifeWebViewLogic extends WebViewUIBase with DisableScreenshotMixin {
  late InAppWebViewController lifeWebViewController;
  bool _isScreenshotLockEnabled = false;
  String? lastLoadedURL = "";
  List<dynamic>? _scrollableUrlsMap;
  List<String> _scrollableUrls = [];

  @override
  void addJsHandler(InAppWebViewController controller) {}

  @override
  void blocStateListener(WebViewState state) {}

  @override
  Future<Either<NotHandlingMethod, bool>> canGoBack(
      InAppWebViewController controller) async {
    return Left(NotHandlingMethod());
  }

  @override
  void dispose() {}

  @override
  Future<Either<NotHandlingMethod, dynamic>> handleWebViewActions(
      args, InAppWebViewController controller) {
    return Future.value(Left(NotHandlingMethod()));
  }

  @override
  Either<NotHandlingMethod, HandlingVoidMethod> onAppBarActionPressed(
      String actionValue) {
    return Left(NotHandlingMethod());
  }

  @override
  void onCloseWindow(InAppWebViewController controller) {}

  @override
  void onConsoleMessage(
      InAppWebViewController controller, ConsoleMessage message) {
    debugPrint("=======> console message ${message.message}");
  }

  @override
  Either<NotHandlingMethod, HandlingVoidMethod> onDownloadStart(
      InAppWebViewController controller,
      DownloadStartRequest downloadStartRequest) {
    return Left(NotHandlingMethod());
  }

  @override
  void onLoadError(
      InAppWebViewController controller, Uri? url, int code, String message) {}

  @override
  void onLoadStart(InAppWebViewController controller, Uri? url) {}

  @override
  void onLoadStop(InAppWebViewController controller, Uri? url) {}

  @override
  void onPageCommitVisible(InAppWebViewController controller, Uri? url) {}

  @override
  void onWebViewCreated(InAppWebViewController controller) {
    lifeWebViewController = controller;
  }

  @override
  Future<Either<NotHandlingMethod, NavigationActionPolicy>>
      shouldOverrideUrlLoading(
          InAppWebViewController controller, NavigationAction action) async {
    checkAndSetIfPageContextIsUnmounted();
    String url = action.request.url.toString();
    if (url.contains('life/p/payment_success')) {
      if (Platform.isIOS && url.equalIgnoreCase(url)) {
        return Right(NavigationActionPolicy.ALLOW);
      }
      final redirectionUrl = Util.getFormattedRedirectionUrl(url, true, false);
      await Navigator.pushNamedAndRemoveUntil(
          pageContext!, Routes.WEB_PAGE_V2, (route) => route.isFirst,
          arguments: {"url": redirectionUrl});
      return Right(NavigationActionPolicy.CANCEL);
    } else if (url.contains('life/p/reproposal/payment_success')) {
      if (Platform.isIOS && url.equalIgnoreCase(url)) {
        return Right(NavigationActionPolicy.ALLOW);
      }
      final redirectionUrl = Util.getFormattedRedirectionUrl(url, false, true);
      Navigator.pushNamedAndRemoveUntil(
          pageContext!, Routes.WEB_PAGE_V2, (route) => route.isFirst,
          arguments: {"url": redirectionUrl});
      return Right(NavigationActionPolicy.CANCEL);
    } else {
      return _handleUrlInterceptions(controller, url);
    }
  }

  @override
  Future<bool> shouldSubtractKeypadSpacing(String url) async {
    return false;
  }

  Future<Either<NotHandlingMethod, NavigationActionPolicy>>
      _handleUrlInterceptions(
          InAppWebViewController controller, String urlOverloading) async {
    checkAndSetIfPageContextIsUnmounted();
    Uri urlStr = Uri.parse(urlOverloading);

    if (urlStr
        .toString()
        .contains(HLWebviewStringConstants.LIFE_PAGE_DOCUMENTS)) {
      final result = urlStr.toString().getRoute();
      if (result != null)
        SchedulerBinding.instance.addPostFrameCallback((timeStamp) {
          Navigator.pushNamedAndRemoveUntil(
            pageContext!,
            result['route'],
            (route) {
              return route.settings.name == Routes.APP_HOME;
            },
            arguments: result,
          );
        });

      return Future.value(Right(NavigationActionPolicy.CANCEL));
    }

    if (urlStr.toString().contains("/status/track")) {
      Map<String, dynamic> qParams = {};
      if (urlStr.queryParameters.containsKey('proposal_id')) {
        qParams['referenceId'] = urlStr.queryParameters['proposal_id'];
      }
      Navigator.pushReplacementNamed(
        pageContext!,
        Routes.P2I_STATUS_TRACKING_SCREEN,
        arguments: {
          "referenceId": qParams['referenceId'],
        },
      );
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    }

    if (urlStr.path.containsIgnoreCase(HLWebviewStringConstants.ASSESSMENT)) {
      final param = urlStr.queryParameters['category'];
      final String? proposalId = urlStr.queryParameters['ref_id'];
      final String? assessmentId = urlStr.queryParameters['assessment_id'];
      final String? source = urlStr.queryParameters['source'];
      final String testCategory =
          param.containsIgnoreCase('LAB') ? "LAB_TEST" : "HOME_TEST";

      TrackerManager.instance.instrumentUserAction(
          "assessment_details",
          {
            "redirection_url": urlOverloading,
            "reference_id": proposalId ?? "Empty",
            "category": param ?? "Empty",
            "assessment_id": assessmentId ?? "Empty",
            "source": source ?? "Empty"
          },
          specificPlatform: AnalyticsPlatform.SEGMENT_EVENT);

      if (urlStr.path.containsIgnoreCase(
          HLWebviewStringConstants.MEDICAL_EVALUATION_STATUS)) {
        String? proposalId = urlStr.queryParameters['proposal_id'];
        Navigator.pushNamedAndRemoveUntil(
          pageContext!,
          Routes.MEDICAL_EVALUATION_DETAILS,
          (route) => (route.isFirst),
          arguments: {"proposal_id": proposalId},
        );
        return Future.value(Right(NavigationActionPolicy.CANCEL));
      } else if (urlStr.path
          .containsIgnoreCase(HLWebviewStringConstants.PPMC_BOOKING_SCREEN)) {
        Navigator.pushNamedAndRemoveUntil(
          pageContext!,
          Routes.PPMC_BOOKING_SCREEN,
          (route) => (route.isFirst),
          arguments: urlStr.queryParameters,
        );
        return Future.value(Right(NavigationActionPolicy.CANCEL));
      } else if (urlStr.path.containsIgnoreCase(
          HLWebviewStringConstants.PPMC_TESTS_LANDING_SCREEN)) {
        Navigator.pushNamed(
          pageContext!,
          Routes.PPMC_TESTS_LANDING_SCREEN,
          arguments: urlStr.queryParameters,
        ).then((value) => controller.reload());
        return Future.value(Right(NavigationActionPolicy.CANCEL));
      } else if (urlStr.path.containsIgnoreCase(
          HLWebviewStringConstants.PPMC_BOOKING_DETAILS_SCREEN)) {
        if (urlStr.queryParameters.containsKey('policyType')) {
          Navigator.pushNamedAndRemoveUntil(
            pageContext!,
            Routes.PPMC_BOOKING_DETAILS_SCREEN,
            (route) => (route.isFirst),
            arguments: urlStr.queryParameters,
          );
        } else {
          Navigator.pushNamed(
            pageContext!,
            Routes.PPMC_BOOKING_DETAILS_SCREEN,
            arguments: urlStr.queryParameters,
          ).then((value) => controller.reload());
        }
        return Future.value(Right(NavigationActionPolicy.CANCEL));
      }
    } else if (urlStr.path
        .equalsIgnoreCase(HLWebviewStringConstants.PPMC_EVALUATION_STATUS)) {
      Navigator.pushNamed(
        pageContext!,
        Routes.MEDICAL_EVALUATION_DETAILS,
        arguments: urlStr.queryParameters,
      ).then((value) => controller.reload());
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    } else if (urlStr.path
        .equalsIgnoreCase(HLWebviewStringConstants.VMER_EVALUATION_STATUS)) {
      await Navigator.pushNamed(
        pageContext!,
        Routes.VMER_EVALUATION_DETAILS,
        arguments: urlStr.queryParameters,
      );
      await lifeWebViewController.reload();
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    } else if (urlStr.path
        .equalsIgnoreCase(HLWebviewStringConstants.VMER_SCHEDULE)) {
      if (urlStr.queryParameters.containsKey("proposal_id")) {
        await Navigator.pushNamed(
          pageContext!,
          Routes.VMER_SECRET_SCHEDULE_PAGE,
          arguments: urlStr.queryParameters,
        );
        await lifeWebViewController.reload();
      }
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    } else if (urlStr.queryParameters.containsKey("proposal_id") &&
        urlStr.path
            .containsIgnoreCase(HLWebviewStringConstants.LIFE_PURCHASED_PLAN)) {
      var proposalId = urlStr.queryParameters['proposal_id'];
      Navigator.pushNamed(
        pageContext!,
        Routes.LIFE_BENEFITS_PAGE,
        arguments: {"isPostPolicy": false, "proposal_id": proposalId},
      );
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    } else if (urlStr.path.containsIgnoreCase(
            HLWebviewStringConstants.LIFE_TERMS_CONDITIONS) ||
        urlStr.path.containsIgnoreCase(HLWebviewStringConstants.LIFE_SUPPORT) ||
        urlStr.path
            .containsIgnoreCase(HLWebviewStringConstants.SUPPORT_SOLUTIONS)) {
      if (Platform.isIOS) {
        return Future.value(Right(NavigationActionPolicy.ALLOW));
      }
      if (lastLoadedURL != urlStr.toString()) {
        lastLoadedURL = urlStr.toString();
        final redirectionUrl = Util.getFormattedRedirectionUrl(lastLoadedURL, false, true);
        await Navigator.pushNamed(
          pageContext!,
          Routes.WEB_PAGE_V2,
          arguments: {
            "url": redirectionUrl,
          },
        );
        await blocInstance?.setCookies(urlOverloading);
        lastLoadedURL = "";
      }
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    } else if (await makePhoneCall(urlOverloading) ||
        await handleMailTo(urlOverloading) ||
        WebUtils.getInstance.handleGoogleMapUrl(urlStr)) {
      return Future.value(Right(NavigationActionPolicy.CANCEL));
    }

    // Ensure a default return value if no condition matches.
    return Future.value(Left(NotHandlingMethod()));
  }

  checkAndSetIfPageContextIsUnmounted() {
    if ((pageContext?.mounted ?? false) == false) {
      pageContext = navigatorKey.currentContext!;
    }
  }

  @override
  Either<NotHandlingMethod, HandlingVoidMethod> onCreateWindow(
      InAppWebViewController controller, CreateWindowAction request) {
    return Left(NotHandlingMethod());
  }

  @override
  void onPageCompleteLoaded(InAppWebViewController controller) {
    // TODO: implement onPageCompleteLoaded
  }
}
