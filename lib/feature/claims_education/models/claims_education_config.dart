import 'package:utilities/constants/constants.dart';

class ClaimEducationConfig {
  final String? title;
  final BannerData? educationPendingBanner;
  final BannerData? educationCompletedBanner;
  final List<TileData>? tiles;
  bool? isEducationCompleted;

  ClaimEducationConfig({
    required this.title,
    this.educationPendingBanner,
    this.educationCompletedBanner,
    required this.tiles,
    this.isEducationCompleted,
  });

  factory ClaimEducationConfig.fromJson(Map<String, dynamic> json) {
    return ClaimEducationConfig(
      title: json['title'],
      educationPendingBanner: json['education_pending_banner'] != null
          ? BannerData.fromJson(json['education_pending_banner'])
          : null,
      educationCompletedBanner: json['education_completed_banner'] != null
          ? BannerData.fromJson(json['education_completed_banner'])
          : null,
      tiles: (json['tiles'] as List<dynamic>)
          .map((e) => TileData.fromJson(e))
          .toList(),
    );
  }

  static final defaultJson = {
    "title": "Select an option",
    "education_pending_banner": {
      "title": "Not sure how to raise a claim?",
      "subtitle": "Here’s a quick guide to help you",
      "imageUrl":
      "https://marketing.ackoassets.com/images/health/claim/ic_claim_education.svg",
      "redirectUrl":
      "${Constants.BASE_URL}gi/p/health/claim/how-to-claim?hide_app_bar=true"
    },
    "tiles": [
      {
        "iconUrl":
        "https://marketing.ackoassets.com/images/health/claim/raise_claim.svg",
        "text": "Raise a claim",
        "redirectUrl":
        "${Constants.BASE_URL}gi/p/health/claim/raise-claim?hide_app_bar=true"
      },
      {
        "iconUrl":
        "https://marketing.ackoassets.com/images/health/claim/view_claim.svg",
        "text": "View your claims",
        "redirectUrl":
        "${Constants.BASE_URL}gi/p/health/claim/view-claims?hide_app_bar=true"
      }
    ],
    "education_completed_banner": {
      "text": "Quick recap on raising a claim",
      "imageUrl":
      "https://marketing.ackoassets.com/images/health/claim/ic_claim_education.svg",
      "redirectUrl":
      "${Constants.BASE_URL}gi/p/health/claim/how-to-claim?hide_app_bar=true"
    }
  };

}

class BannerData {
  final String? title;
  final String? subtitle;
  final String? text;
  final String? imageUrl;
  final String? redirectUrl;

  BannerData({
    this.title,
    this.subtitle,
    this.text = '',
    required this.imageUrl,
    required this.redirectUrl,
  });

  factory BannerData.fromJson(Map<String, dynamic> json) {
    return BannerData(
      title: json['title'],
      subtitle: json['subtitle'],
      text: json['text'],
      imageUrl: json['imageUrl'],
      redirectUrl: json['redirectUrl'],
    );
  }
}

class TileData {
  final String? iconUrl;
  final String? text;
  final String? redirectUrl;
  final String? action;
  final String? id;

  TileData({
    required this.iconUrl,
    required this.text,
    required this.redirectUrl,
    this.id,
    this.action,
  });

  factory TileData.fromJson(Map<String, dynamic> json) {
    return TileData(
      iconUrl: json['iconUrl'],
      text: json['text'],
      redirectUrl: json['redirectUrl'],
      id: json['id'],
      action: json['action'],
    );
  }
}