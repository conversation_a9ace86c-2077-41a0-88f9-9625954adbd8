import 'package:acko_flutter/util/extensions.dart';

class ClaimsEducationParameterConfig {
  bool? isAcrEnabled;
  bool? hasAcrData;
  String? uhids;
  String? name;
  String? policyNumber;
  String? fromPage;

  ClaimsEducationParameterConfig(
      {
      this.isAcrEnabled = false,
      this.hasAcrData = false,
      this.uhids,
      this.name,
      this.policyNumber,
      this.fromPage});

  String? generateUrl(String? redirectUrl) {
    if (redirectUrl.isNullOrEmpty) return null;

    final queryParams = <String, String>{};

    if (isAcrEnabled != null) {
      queryParams['isAcrEnabled'] = isAcrEnabled.toString();
    }
    if (hasAcrData != null) {
      queryParams['hasAcrData'] = hasAcrData.toString();
    }
    if (uhids.isNotNullOrEmpty) {
      queryParams['uhids'] = Uri.encodeComponent(uhids!);
    }
    if (name.isNotNullOrEmpty) {
      queryParams['name'] = Uri.encodeComponent(name!);
    }
    if (policyNumber.isNotNullOrEmpty) {
      queryParams['policyNumber'] = policyNumber!;
    }
    if (fromPage.isNotNullOrEmpty) {
      queryParams['fromPage'] = fromPage!;
    }

    final queryString = queryParams.entries
        .map((entry) => '${entry.key}=${entry.value}')
        .join('&');

    return '$redirectUrl${redirectUrl!.contains('?') ? '&' : '?'}$queryString';
  }
}
