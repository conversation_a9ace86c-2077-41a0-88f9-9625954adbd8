import 'package:acko_flutter/common/view/layout_header.dart';
import 'package:acko_flutter/feature/claims_education/cubit/claims_education_cubit.dart';
import 'package:acko_flutter/feature/claims_education/repo/claims_education_repo.dart';
import 'package:acko_flutter/feature/claims_education/view/widgets/health_policy_claims_entry_widget.dart';
import 'package:acko_flutter/framework/pdp/health/common/constants.dart';
import 'package:acko_flutter/framework/pdp/health/repo/health_repo.dart';
import 'package:acko_flutter/util/health/health_constants.dart';
import 'package:design_module/utilities/hybrid_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class HealthClaimsOptionSelectionScreen extends StatelessWidget {
  final String? policyNumber;
  const HealthClaimsOptionSelectionScreen({super.key, this.policyNumber});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: BlocProvider(
        create: (context) => ClaimsEducationCubit(policyNumber: policyNumber, fromPage: 'quick_actions', page: 'Claims option select screen', claimesEducationRepo: ClaimsEducationRepository(), healthRepo: HealthRepo(AggregatedPageMode.policy)),
        child: Stack(children: [
          Padding(
            padding: const EdgeInsets.only(top: 140),
            child: Transform.scale(
              scale: 2,
              child: ClipRect(
                child: Align(
                  alignment: Alignment.topCenter,
                  child: HybridImage(
                    imageUrl: HealthConstants.claimsEduBgImage,
                    width: MediaQuery.sizeOf(context).width,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
          ),
          CommonHeaders.getAppBar(context, '', '', onBackPressed: () {
            Navigator.pop(context);
          }, onHelpPressed: () {
            CommonHeaders.handleHelpClick(
                context, null, "claim_landing_screen", null);
          },
              isHelpRequired: true,
              showFAQ: false,
              backgroundColor: Colors.transparent,
              displayBackButton: true),
          Padding(
              padding: const EdgeInsets.only(top: 80),
              child: HealthPolicyClaimsEntryWidget())
        ]),
      ),
    );
  }
}
