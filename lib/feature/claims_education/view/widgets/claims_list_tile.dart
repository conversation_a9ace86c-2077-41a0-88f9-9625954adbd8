import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/feature/claims_education/cubit/claims_education_cubit.dart';
import 'package:acko_flutter/feature/claims_education/models/claims_education_config.dart';
import 'package:acko_flutter/feature/claims_education/models/claims_education_parameter_config.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/constants/constants.dart';

class ClaimsListTile extends StatelessWidget {
  final TileData? tileData;
  final ClaimsEducationParameterConfig? parameterConfig;
  final bool popOnAction;

  const ClaimsListTile({
    Key? key,
    required this.tileData,
    required this.parameterConfig,
    this.popOnAction = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (tileData == null) return SizedBox.shrink();

    final _bloc = context.read<ClaimsEducationCubit>();

    return GestureDetector(
      onTap: () {
        _bloc.triggerTapClaimOptionSelectEvent(tapCta: tileData!.id ?? '');
        final url = parameterConfig?.generateUrl(tileData?.redirectUrl);
        if(url.isNullOrEmpty) return;
        Navigator.pushNamed(context, Routes.WEB_PAGE_V2,
            arguments: {'url': url}).then((value) {
              if(popOnAction) {
                Navigator.pop(context);
              }
        });
      },
      behavior: HitTestBehavior.opaque,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if(tileData!.iconUrl.isNotNullOrEmpty)
          Container(
            margin: const EdgeInsets.only(right: 16),
            width: 40,
            height: 40,
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: colorF5F5F5,
              borderRadius: BorderRadius.all(Radius.circular(8)),
            ),
            child: Center(
              child: SDUIImage(imageUrl: tileData!.iconUrl, width: 24, height: 24),
            ),
          ),
          if(tileData!.text.isNotNullOrEmpty)
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SDUIText(
                  value: tileData!.text,
                  textStyle: 'lMedium',
                  textColor: color121212,
                  maxLines: 2,
                ),
                Icon(Icons.arrow_forward_ios_rounded, size: 20, color: color121212),
              ],
            ),
          ),
        ],
      ),
    );
  }
}