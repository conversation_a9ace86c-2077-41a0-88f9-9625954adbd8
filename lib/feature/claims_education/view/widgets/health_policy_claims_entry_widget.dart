import 'package:acko_flutter/common/view/shimmer_view.dart';
import 'package:acko_flutter/feature/claims_education/cubit/claims_education_cubit.dart';
import 'package:acko_flutter/feature/claims_education/models/claims_education_config.dart';
import 'package:acko_flutter/feature/claims_education/models/claims_education_parameter_config.dart';
import 'package:acko_flutter/feature/claims_education/view/widgets/claim_education_banners.dart';
import 'package:acko_flutter/feature/claims_education/view/widgets/claims_list_tile.dart';
import 'package:design_module/utilities/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';


class HealthPolicyClaimsEntryWidget extends StatefulWidget {
  final bool popOnAction;
  const HealthPolicyClaimsEntryWidget({super.key, this.popOnAction = false});

  @override
  State<HealthPolicyClaimsEntryWidget> createState() => _HealthPolicyClaimsEntryWidgetState();
}

class _HealthPolicyClaimsEntryWidgetState extends State<HealthPolicyClaimsEntryWidget> {
  late ClaimsEducationCubit _cubit;

  @override
  void initState() {
    super.initState();
    _cubit = context.read<ClaimsEducationCubit>();
    _cubit.fetchEducationConfig();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ClaimsEducationCubit, ClaimsEducationState>(
      bloc: _cubit,
      builder: (context, state) {
        if (state is ClaimsEducationLoading) {
          return ShimmerView();
        } else if (state is ClaimsEducationLoaded) {
          _cubit.triggerViewClaimOptionSelectEvent();
          return _buildContent(state.claimEducationConfig, state.educationParameterConfig);
        } else return const SizedBox.shrink();
      },
    );
  }

  Widget _buildContent(ClaimEducationConfig config, ClaimsEducationParameterConfig? parameterConfig) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(20, 32, 20, 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SDUIText(
            value: config.title,
            textStyle: 'hSmall',
            textColor: color040222,
            maxLines: 2,
          ),
          if (!(config.isEducationCompleted ?? false)) ...[
            const SizedBox(height: 32),
            EducationPendingBanner(bannerData: config.educationPendingBanner, parameterConfig: parameterConfig, popOnAction: widget.popOnAction,),
          ],
          ListView.separated(
            padding: EdgeInsets.only(top: 32),
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              return ClaimsListTile(
                tileData: config.tiles?[index],
                parameterConfig: parameterConfig,
                popOnAction: widget.popOnAction,
              );
            },
            separatorBuilder: (context, index) {
              return Padding(
                child: Divider(color: colorE8E8E8),
                padding: const EdgeInsets.symmetric(vertical: 20),
              );
            },
            itemCount: config.tiles?.length ?? 0,
          ),
          const SizedBox(height: 32),
          if ((config.isEducationCompleted ?? false))
            EducationCompletedBanner(bannerData: config.educationCompletedBanner, parameterConfig: parameterConfig, popOnAction: widget.popOnAction,),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _cubit.close();
    super.dispose();
  }
}