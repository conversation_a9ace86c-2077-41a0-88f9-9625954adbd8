import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/feature/claims_education/cubit/claims_education_cubit.dart';
import 'package:acko_flutter/feature/claims_education/models/claims_education_config.dart';
import 'package:acko_flutter/feature/claims_education/models/claims_education_parameter_config.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/constants/constants.dart';

class EducationPendingBanner extends StatelessWidget {
  final BannerData? bannerData;
  final ClaimsEducationParameterConfig? parameterConfig;
  final bool popOnAction;

  const EducationPendingBanner(
      {Key? key, required this.bannerData, required this.parameterConfig, this.popOnAction = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (bannerData == null) return const SizedBox.shrink();

    final _bloc = context.read<ClaimsEducationCubit>();

    return GestureDetector(
      onTap: () {
        _bloc.triggerTapClaimOptionSelectEvent(tapCta: "quick_guide_pending");
        final url = parameterConfig?.generateUrl(bannerData?.redirectUrl);
        if(url.isNullOrEmpty) return;
        Navigator.pushNamed(context, Routes.WEB_PAGE_V2, arguments: {'url': url}).then((value) {
          if (popOnAction) {
            Navigator.pop(context);
          }
        });
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [Color(0xFFD0BDF4), Color(0xFFA9DDFE)],
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.all(1),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [colorEFE9FB, colorFFFFFF, colorE2F5FF],
            ),
            borderRadius: BorderRadius.circular(11),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if(bannerData!.imageUrl.isNotNullOrEmpty)
                SDUIImage(imageUrl: bannerData!.imageUrl, width: 42, height: 42),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if(bannerData!.title.isNotNullOrEmpty)
                      SDUIText(
                        value: bannerData!.title,
                        textStyle: 'h6',
                        textColor: color121212,
                        maxLines: 5,
                      ),
                    const SizedBox(height: 4,),
                    if(bannerData!.subtitle.isNotNullOrEmpty)
                      SDUIText(
                        value: bannerData!.subtitle,
                        textStyle: 'pXSmall',
                        textColor: color4B4B4B,
                        maxLines: 5,
                      ),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              Icon(Icons.arrow_forward_rounded, size: 20, color: color36354C,),
            ],
          ),
        ),
      ),
    );
  }
}

class EducationCompletedBanner extends StatelessWidget {
  final BannerData? bannerData;
  final ClaimsEducationParameterConfig? parameterConfig;
  final bool popOnAction;
  const EducationCompletedBanner(
      {Key? key, required this.bannerData, required this.parameterConfig, this.popOnAction = false})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (bannerData == null) return SizedBox.shrink();

    final _bloc = context.read<ClaimsEducationCubit>();

    return GestureDetector(
      onTap: () {
        _bloc.triggerTapClaimOptionSelectEvent(tapCta: "quick_guide_done");
        final url = parameterConfig?.generateUrl(bannerData?.redirectUrl);
        if(url.isNullOrEmpty) return;
        Navigator.pushNamed(context, Routes.WEB_PAGE_V2, arguments: {'url': url}).then((value) {
          if (popOnAction) {
            Navigator.pop(context);
          }
        });
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: [colorD0BDF4, colorA9DDFE],
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.all(1),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [colorEFE9FB, colorFFFFFF, colorE2F5FF],
            ),
            borderRadius: BorderRadius.circular(11),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if(bannerData!.imageUrl.isNotNullOrEmpty)
                SDUIImage(imageUrl: bannerData!.imageUrl, width: 32, height: 32),
              const SizedBox(width: 12),
              if(bannerData!.text.isNotNullOrEmpty)
                Expanded(
                  child: SDUIText(
                    value: bannerData!.text,
                    textStyle: 'p2',
                    textColor: color121212,
                    maxLines: 5,
                  ),
                ),
              const SizedBox(width: 12),
              Icon(Icons.arrow_forward_rounded, size: 20, color: color36354C),
            ],
          ),
        ),
      ),
    );
  }
}
