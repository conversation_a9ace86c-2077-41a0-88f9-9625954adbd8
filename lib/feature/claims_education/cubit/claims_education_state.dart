part of 'claims_education_cubit.dart';

@immutable
sealed class ClaimsEducationState {}

final class ClaimsEducationInitial extends ClaimsEducationState {}

class ClaimsEducationLoading extends ClaimsEducationState {}

class ClaimsEducationLoaded extends ClaimsEducationState {
  ClaimEducationConfig claimEducationConfig;
  ClaimsEducationParameterConfig? educationParameterConfig;
  ClaimsEducationLoaded({required this.claimEducationConfig, required this.educationParameterConfig});
}

class ClaimsEducationError extends ClaimsEducationState {
  final ErrorHandler? error;
  ClaimsEducationError({this.error});
}