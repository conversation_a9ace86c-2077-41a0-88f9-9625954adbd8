import 'package:acko_flutter/feature/claim/advance_cash/onboarding/data/AdvanceCashState.dart';
import 'package:acko_flutter/feature/claims_education/models/claims_education_config.dart';
import 'package:acko_flutter/feature/claims_education/models/claims_education_parameter_config.dart';
import 'package:acko_flutter/feature/claims_education/repo/claims_education_repo.dart';
import 'package:acko_flutter/framework/pdp/health/repo/health_repo.dart';
import 'package:acko_flutter/network/ApiResponse.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/health/health_constants.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/health_life/page_events/health_life_page_events.dart';
import 'package:analytics/events/health_life/tap_events/health_life_tap_events.dart';
import 'package:meta/meta.dart';
import 'package:policy_details/policy_details.dart';
import 'package:utilities/widgets/acko_safe_cubit.dart';
part 'claims_education_state.dart';

class ClaimsEducationCubit extends AckoSafeCubit<ClaimsEducationState> {
  final String? page;
  final String fromPage;
  final String? policyNumber;
  final HealthRepo healthRepo;
  final ClaimsEducationRepository claimesEducationRepo;
  final HealthAssetDetailsResponseInsured? selectedAsset;
  ClaimsEducationCubit(
      {this.page,
      this.policyNumber,
      this.selectedAsset,
      required this.healthRepo,
      required this.claimesEducationRepo,
      required this.fromPage})
      : super(ClaimsEducationInitial());

  Future<void> fetchEducationConfig() async {
    emit(ClaimsEducationLoading());
    await healthRepo.getPoliciesHeaderDetails();
    AdvanceCashDiscoveryState? acr = healthRepo.getAdvaceCashDiscoveryState();

    try {
      var (uhids, name) = selectedAsset?.getUhidsAndName() ?? (null, null);
      final config = await claimesEducationRepo.fetchClaimsEducationConfig();

      config.isEducationCompleted =
          await claimesEducationRepo.getClaimsEducationDataFromSharedPreferences();

      final ClaimsEducationParameterConfig parameterConfig =
          ClaimsEducationParameterConfig(
        isAcrEnabled: acr?.isEligibleForAdvanceCash ?? false,
        hasAcrData: acr?.advanceCashData != null,
        uhids: uhids,
        name: name,
        fromPage: fromPage,
        policyNumber: policyNumber,
      );

      emit(ClaimsEducationLoaded(
          claimEducationConfig: config,
          educationParameterConfig: parameterConfig));
    } catch (e) {
      emit(ClaimsEducationError(error: ErrorHandler(e.toString())));
    }
  }

  /// Analytics
  Future<void> triggerViewClaimOptionSelectEvent() async {
    await _sendAnalyticalEvent(
      event: HLPageEvents.VIEW_CLAIM_OPTION_SELECT,
    );
  }

  Future<void> triggerTapClaimOptionSelectEvent({
    required String tapCta,
  }) async {
    Map<String, dynamic> properties = {
      "tap_cta": tapCta,
    };

    await _sendAnalyticalEvent(
      event: HLTrackEvents.TAP_CLAIM_OPTION_SELECT,
      additionalProperties: properties,
    );
  }

  Future<void> _sendAnalyticalEvent({
    required dynamic event,
    Map<String, dynamic> additionalProperties = const {},
  }) async {
    String productType = HealthConstants.HEALTH_RETAIL;

    Map<String, dynamic> properties = {
      "policy_number": policyNumber,
      "product": productType,
      "platform": Util.getPlatform(),
      "from_page": fromPage,
      "page": page,
      ...additionalProperties,
    };

    AnalyticsTrackerManager.instance
        .sendEvent(event: event, properties: properties);
  }
}
