import 'package:acko_flutter/feature/claims_education/models/claims_education_config.dart';
import 'package:session_manager_module/SessionManager/session_manager_module.dart';
import 'package:session_manager_module/StorageManager/shared_preferences_storage.dart';
import 'package:utilities/remote_config/remote_config.dart';

class ClaimsEducationRepository {
  const ClaimsEducationRepository();

  Future<bool?> getClaimsEducationDataFromSharedPreferences() async =>
      await SessionManager.instance.storageManager.getBoolDataInPreferences(
          BoolDataSharedPreferenceKeys.CLAIMS_EDUCATION_COMPLETED);

  Future<ClaimEducationConfig> fetchClaimsEducationConfig() async {
    var jsonData = await RemoteConfigInstance.instance
            .getData(RemoteConfigKeysSet.CLAIMS_EDUCATION) ??
        ClaimEducationConfig.defaultJson;

    return ClaimEducationConfig.fromJson(jsonData);
  }
}
