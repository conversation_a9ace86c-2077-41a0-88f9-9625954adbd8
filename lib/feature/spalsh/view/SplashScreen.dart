import 'dart:io';

import 'package:acko_flutter/common/util/EventsHelper.dart';
import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/view/AckoText.dart';
import 'package:acko_flutter/common/view/flutter_splash_screen.dart';
import 'package:acko_flutter/feature/spalsh/bloc/SplashScreenBloc.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:utilities/constants/constants.dart';

import '../../../common/util/PreferenceHelper.dart';

class SplashScreen extends StatefulWidget {
  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with SingleTickerProviderStateMixin {
  SplashScreenState? _state;
  bool isVideoCompleted = false;

  @override
  void initState() {
    super.initState();
    EventsHelper.logFirebaseEvent('state', {'timer_start': 'true'});
    // Handle Android notifications for API > 33
    _handleNotificationPermission();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: color21202A,
        body: BlocConsumer<SplashScreenBloc, SplashScreenState>(
            listenWhen: (prev, current) =>
                current.type != SplashScreenSuccessType.SPLASH,
            listener: (context, state) {
              EventsHelper.logFirebaseEvent(
                'state',
                {'name': state.type.toString()},
              );

              _state = state;
              if (isVideoCompleted) {
                _handleNavigation(state);
              }
            },
            buildWhen: (prev, current) =>
                current.type == SplashScreenSuccessType.SPLASH,
            builder: (BuildContext context, SplashScreenState state) {
              return _getSplashScreenWidgets(state);
            }));
  }

  _handleNavigation(SplashScreenState state) {
    EventsHelper.logFirebaseEvent(
        'state', {'navigate_to_next': 'true', 'type': state.type.toString()});
    switch (state.type) {
      case SplashScreenSuccessType.LOGIN:
        _navigateToRoute(
          Routes.LOGIN_PAGE,
          context,
          params: state.params ?? {},
        );
        break;
      case SplashScreenSuccessType.LOGIN_LANDING_PAGE:
        _navigateToRoute(Routes.LOGIN_LANDING_PAGE, context);
        break;
      case SplashScreenSuccessType.HOME:
        _navigateToRoute(Routes.APP_HOME, context);
        break;
      case SplashScreenSuccessType.ON_BOARDING_QUESTIONS:
        _navigateToRoute(Routes.ONBOARDING_INITIAL_PAGE, context);
      case SplashScreenSuccessType.SPLASH || SplashScreenSuccessType.INIT_TIMER:
        AnalyticsTrackerManager.instance.sendEvent(
            event: PageLoadedConstants.APP_OPEN_SPLASH_SCREEN_LOADED,
            properties: {
              'platform': Util.getPlatform(),
            });
        return _getSplashScreenWidgets(state);
      case SplashScreenSuccessType.FORCED_UPDATE:
        _navigateToRoute(Routes.APP_UPDATE_PAGE, context);
        break;
    }
  }

// Handle Android notifications for API > 33
  void _handleNotificationPermission() async {
    if (Platform.isAndroid) {
      final status = await Permission.notification.status;
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      final sdkVersion = androidInfo.version.sdkInt;
      if (sdkVersion > 32 && status.isDenied) {
        await Permission.notification.request();
      }
    }
  }

  _getWidgets(String message, context) {
    List<Widget> widgetList = [];
    widgetList.add(SvgPicture.asset("assets/images/ic_splash_logo.svg"));
    widgetList.add(Padding(
        padding: const EdgeInsets.only(top: 20),
        child: TextEuclidMediumL2(message, textColor: Colors.white)));
    widgetList.add(Container(
      margin: EdgeInsets.only(top: 20),
      child: ElevatedButton(
          style: ButtonStyle(
              foregroundColor: MaterialStateProperty.all(Colors.white),
              backgroundColor: MaterialStateProperty.all(color74CE63),
              shape: MaterialStateProperty.all(RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.0)))),
          onPressed: () {
            BlocProvider.of<SplashScreenBloc>(context).navigateToLandingPage();
          },
          child: TextEuclidMediumL2("Retry")),
    ));
    return widgetList;
  }

  double _getSplashScreenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  double _getSplashScreenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  _navigateToRoute(String route, context,
      {Map<String, dynamic> params = const {}}) async {
    if (route == Routes.LOGIN_PAGE) {
      Navigator.pushReplacementNamed(context, route, arguments: {
        "is_user_logged_out": false,
        ...params,
      });
    } else if (route == Routes.APP_HOME) {
      Navigator.pushReplacementNamed(context, route);
    } else if (route == Routes.LOGIN_LANDING_PAGE) {
      setIsAppOpen(true);
      SplashScreenBloc bloc = BlocProvider.of(context);

      if (bloc.phoneNumber.isNotNullAndEmpty) {
        Navigator.pushReplacementNamed(context, Routes.LOGIN_PAGE, arguments: {
          "is_user_logged_out": false,
          "next": bloc.nextUrl,
          "phone_number": bloc.phoneNumber
        });
      } else {
        Navigator.pushReplacementNamed(context, Routes.LOGIN_LANDING_PAGE,
            arguments: {
              "is_user_logged_out": false,
              "next_url": bloc.nextUrl,
              "phone_number": bloc.phoneNumber
            });
      }
    } else {
      Navigator.pushReplacementNamed(context, route);
    }
  }

  Widget _getSplashScreenWidgets(SplashScreenState state) {
    if (state.message.isNotNullOrEmpty) {
      return _getErrorView(state.message!);
    }

    return FlutterSplashScreen(
      onActionCompleted: () {
        isVideoCompleted = true;
        EventsHelper.logFirebaseEvent('state', {'timer_end': 'true'});
        if (_state != null && mounted) {
          _handleNavigation(_state!);
        } else {
          EventsHelper.logFirebaseEvent(
              'state', {'timer_end': 'state not available'});
        }
      },
    );
  }

  Widget _getErrorView(String message) {
    return Container(
      width: MediaQuery.of(context).size.width,
      color: color21202A,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: _getWidgets(message, context),
      ),
    );
  }
}
