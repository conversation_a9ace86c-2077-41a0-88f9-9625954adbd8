import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:acko_core_utilities/acko_core_utilities.dart';
import 'package:acko_flutter/common/util/PreferenceHelper.dart';
import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/common/view/acko_text_config.dart';
import 'package:acko_flutter/feature/endorsement/domain/repository/hl_endorsement_repository.dart';
import 'package:acko_flutter/feature/profile/model/ProfleResponse.dart';
import 'package:acko_flutter/feature/spalsh/bloc/SplashScreenRepository.dart';
import 'package:acko_flutter/main.dart';
import 'package:acko_flutter/network/ApiService.dart';
import 'package:acko_flutter/network/interceptors/DioFirebasePerformanceInterceptor.dart';
import 'package:acko_flutter/network/interceptors/api_error_log_interceptor.dart';
import 'package:acko_flutter/network/interceptors/auth_interceptor.dart';
import 'package:acko_flutter/r2d2/events.dart';
import 'package:acko_flutter/r2d2/r2d2_helper.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/appIconUtility.dart';
import 'package:acko_flutter/util/deeplink/app_initial_deeplink_listener.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_flutter/util/health/health_constants.dart';
import 'package:acko_flutter/util/in_app_update_util.dart';
import 'package:acko_logger/acko_logger.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:design_module/design_module.dart';
import 'package:dio/dio.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:networking_module/network/rest/interceptors/log_interceptor.dart';
import 'package:networking_module/networking_module.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:sdui/sdui.dart';
import 'package:session_manager_module/session_manager.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/remote_config/model/app_configuration_dao.dart';
import 'package:utilities/remote_config/model/remote_config.dart';
// import 'package:utilities/constants/constants.dart';

import 'package:utilities/remote_config/remote_config.dart';
import 'package:utilities/widgets/acko_safe_cubit.dart';

import '../../../common/bloc/device_info_repo.dart';
import '../../../network/interceptors/api_logger_interceptor.dart';
import '../../../util/device_info_util/device_info_model.dart';
import '../../../util/device_info_util/device_util.dart';
import '../../login/bloc/LoginRepository.dart';

class SplashScreenBloc extends AckoSafeCubit<SplashScreenState>
    with AppStoredDeeplinkListener {
  late SplashScreenRepository _splashScreenRepository;

  SplashScreenBloc()
      : super(SplashScreenState(SplashScreenSuccessType.SPLASH)) {
    getApplicationData();
  }

  ///used in case of forced update
  bool blockUser = false;
  String? phoneNumber;
  String? nextUrl;

  AppConfig? appConfigData;

  getApplicationData() async {
    emit(SplashScreenState(SplashScreenSuccessType.INIT_TIMER));
    await initApplicationData();
    registerDevice();
    if (!blockUser) navigateToLandingPage();
  }

  checkForDeeplink() async {
    await getStoreDeeplink(
      maxTimeout: 1000,
      onDeeplinkCallback: (deeplink) {
        if ((deeplink?.value ?? "").isNotNullAndEmpty &&
            deeplink!.value!.containsIgnoreCase('login') &&
            (deeplink.queryParameters?['phone_number'] ?? "")
                .toString()
                .isNotNullAndEmpty) {
          phoneNumber = deeplink.queryParameters!['phone_number'];
          nextUrl = deeplink.queryParameters!['next_url'];
        }
      },
    );

    if(phoneNumber.isNotNullAndEmpty){
      await Constants
          .PLATFORM_CHANNEL
          .invokeMethod("clear_stored_deeplink");
    }
  }

  registerDevice() async {
    var fcmToken =
        await Constants.PLATFORM_CHANNEL.invokeMapMethod("get_fcm_token");
    DeviceInfoModel? deviceUUIDMap = await DeviceUtils().getDeviceInfo();
    final DeviceInfoRepository repo = DeviceInfoRepository();
    if (deviceUUIDMap != null) {
      await repo.registerDevice(deviceUUIDMap);
      setBoolPrefs(BoolDataSharedPreferenceKeys.DEVICE_REGISTERED, true);
      if (fcmToken != null && fcmToken["fcm_token"] != null) {
        await repo.updateFcmToken(fcmToken["fcm_token"], deviceUUIDMap.deviceId,
            fcmToken["notification_enabled"]);
      }
    }
  }

  Future<void> initApplicationData() async {
    ///calls which are required before subsequent calls
    await initConfigConstants();
    await initFirebaseServices();

    ///Background calls
    initGrowthBook();
    // checkAndRevertAppIcon();

    ///calls which are independent but needs to be awaited
    await Future.wait([
      initNetworkService(),
      initAnalytics(),
      checkForAppUpdate(),
    ]);

    attachNetworkPerformanceInterceptor();
  }

  attachNetworkPerformanceInterceptor() {
    AckoLoggerManager.instance.addCustomInterceptor(registerDioInterceptor);
  }

  registerDioInterceptor(Interceptor interceptor){
    NetworkingModule.instance.restService.addCustomInterceptors([interceptor]);
  }

  Future<void> initConfigConstants() async {
    final Map<dynamic, dynamic>? data =
        await Constants.PLATFORM_CHANNEL.invokeMethod("get_init_params");
    if (data != null) {
      Constants.BASE_URL = data["base_url"];
      Constants.CENTRAL_APP_BFF_BASE_URL = data["central_app_bff_base_url"];
      Constants.APP_VERSION = data["app_version"];
      Constants.BUILD_NUMBER = data["build_number"];
      Constants.HEALTH_CLAIMS_BASE = "${data["base_url"]}health-claims/";
      Constants.HEALTH_PRESENTATION_URL = "${data["base_url"]}present";
      Constants.HEALTH_BASE_URL_v1 = "${data["health_base_url"]}v1";
      Constants.HEALTH_BASE_URL_V2 = "${data["health_base_url"]}v2";
      Constants.BUILD_FLAVOR = data["build_flavor"];
      Constants.IOS_CER_DECRYPT_KEY =
          Platform.isIOS ? data["acko_cer_decrypt_key"] : null;
      Constants.IOS_CER_IV = Platform.isIOS ? data["acko_cer_iv"] : null;

      Util.adId = data[Constants.AD_ID];
      Util.idfaValue = data[Constants.IDFA_VALUE];
      Util.userId = data[Constants.KEY_USER_ID];
      Util.afId = data[Constants.AF_ID];
      Util.appleAppId = data[Constants.apple_app_id];

      BFF.initSDUIBaseURL(Constants.CENTRAL_APP_BFF_BASE_URL);

      try {
        await SessionManager.instance.initialize(
            baseURL: Constants.BASE_URL,
            cookieAPIEndpoint: ApiPath.USER_COOKIE_HEADER);
      } catch (e) {
        logException(e);
      }

      String? prevAppVersion = await getStringPrefs(
          StringDataSharedPreferenceKeys.PREVIOUS_APP_VERSION);
      if ((prevAppVersion == null || prevAppVersion.isEmpty) &&
          data['previous_app_version'] != null) {
        setStringPrefs(StringDataSharedPreferenceKeys.PREVIOUS_APP_VERSION,
            data['previous_app_version']);
      }
    }
  }

  Future<void> initNetworkService() async {
    initNetworkAndDesignModule();
    await ApiService.apiServiceInstance.initPersistentCookie();

    if ((RemoteConfigInstance.instance
                .getData(RemoteConfigKeysSet.ENABLE_SSL_PINNING_V3) ??
            true) &&
        ((Constants.BUILD_FLAVOR ?? "uat").containsIgnoreCase('prod') ||
            kReleaseMode)) {
      final appInfraUtil = AppInfraSecurityUtil();
      final sslCerData = await appInfraUtil.getSslPinningCertificateBytes();
      final hostExclusionList = appInfraUtil.getHostExclusionList();
      ApiService.apiServiceInstance
          .addSslSecurityAgent(sslCerData, hostExclusionList);
      NetworkingModule.instance
          .addSslSecurityAgent(sslCerData, hostExclusionList);
    }
  }

  Future<void> initGrowthBook() async {
    await RemoteConfigInstance.instance.initGrowthBookInstance();
  }

  Future<void> initFirebaseServices() async {
    await RemoteConfigInstance.instance.initFirebaseInstance();
    String? mobileNo =
        await getStringPrefs(StringDataSharedPreferenceKeys.MOBILE_NUMBER);
    if (mobileNo.isNotNullOrEmpty) {
      FirebaseCrashlytics.instance.setUserIdentifier(mobileNo!);
    }
    appConfigData = await fetchRemoteConfigData();
  }

  Future<void> initAnalytics() async {
    ///calls which are independent but needs to ne awaited
    await Future.wait([
      initAnalyticsManager(),
      initLoggerManager(),
      R2D2Helper().init(),
    ]);
    R2D2Events.instance.init();
  }

  Future<void> initLoggerManager() async {
    if (appConfigData != null) {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();

      await AckoLoggerManager.instance.initialize({
        'environment': Constants.LOGGER_ENV_NAME,
        'application_name': Constants.LOGGER_APPLICATION_NAME,
        'application_version': packageInfo.version,
        'coralogix_key': appConfigData?.coralogixKey,
      });
      await Util.attachUserPropertiesToLogger();
    }
  }

  Future<void> initAnalyticsManager() async {
    if (appConfigData != null) {
      await AnalyticsTrackerManager.instance.initTrackers({
        "androidSegmentWriteKey": appConfigData!.androidSegmentWriteKey,
        "iosSegmentWriteKey": appConfigData!.iosSegmentWriteKey,
        "amplitudeApiKey": appConfigData!.amplitudeApiKey,
        "firebaseAnalyticsConfig": {
          "af_id": Util.afId,
          "dev_key": Constants.afDevKey,
          "apple_app_id": Util.appleAppId,
        }
      });
      await attachUserProperties();
    }
  }

  Future<void> attachUserProperties() async {
    UserSessionModel userSessionModel =
        await SessionManager.instance.storageManager.getUserSessionDetails();
    Map<String, dynamic>? deviceId =
        await Constants.PLATFORM_CHANNEL.invokeMapMethod("get_device_id");
    Map<String, dynamic> properties = {};

    properties["trackerid"] = userSessionModel.trackerId;
    properties["acko_device_id"] =
        (deviceId != null) ? deviceId["device_id"] : null;
    properties["user_id"] = userSessionModel.userEkey;
    properties.removeNullOrEmptyValues();
    await AnalyticsTrackerManager.instance.attachUserProperties(properties);
  }

  Future<AppConfig?> fetchRemoteConfigData() async {
    AppConfig? data = await getAppDependencies();
    setURLs();
    if (isInDebugMode) setRemoteConfigConst();
    unawaited(HlEndorsementRepository().fetchEndorsementCopies());
    return data;
  }

  void initNetworkAndDesignModule() async {
    final enableOfflineMode = RemoteConfigInstance.instance.getGbBoolAsync(
      RemoteConfigKeysSet.ENABLE_OFFLINE_MODE,
      defaultValue: true,
    );
    UserSessionModel userSessionModel =
        await SessionManager.instance.storageManager.getUserSessionDetails();

    DesignModule.instance.init(config: AckoTextConfig.instance);
    DesignModuleConstants.setEnableImageCaching(
      enableOfflineMode,
    );

    NetworkingModule.init(
      baseUrl: Constants.BASE_URL,
      gqlHeadersCallback: getAppD2CTokenMap,
    );

    await _addRestServiceInterceptors(enableOfflineMode);
  }

  Future<Map<String, dynamic>> getAppD2CTokenMap() async {
    final UserSessionModel sessionModel =
        await SessionManager.instance.storageManager.getUserSessionDetails();
    return sessionModel.getAppD2CTokenMap();
  }

  Future<void> _addRestServiceInterceptors(
      Future<bool> enableOfflineMode) async {
    final _restService = NetworkingModule.instance.restService;

    // DO NOT MODIFY :  Required to bust cache versions, else app won't refresh
    final String cacheBustVersion = await RemoteConfigInstance.instance
        .getData(RemoteConfigKeysSet.ACKO_APP_CACHE_BUST_VERSION);

    /// Adding here as an aysnc call is requried to fetch the latest version.
    _restService.addCustomInterceptors([
      AuthInterceptor(),
      DioFirebasePerformanceInterceptor(),
      LogNetworkErrorInterceptor(),
    ]);

    if (kDebugMode) {
      _restService.addCustomInterceptors([
        APILogInterceptor(responseBody: false),
        APILoggerInterceptor(),
      ]);
    }
  }

  void checkAndRevertAppIcon() {
    AppIconUtility utility = AppIconUtility();
    utility.checkAndUpdateAppIcon();
  }

  void setURLs() {
    String? urlData =
        RemoteConfigInstance.instance.getData(RemoteConfigKeysSet.urlDataKey);

    if (urlData != null && urlData.isNotEmpty) {
      RemoteConfigUrl urls = RemoteConfigUrl.fromJson(jsonDecode(urlData));
      if (urls.policyDetails != null)
        Urls.policyDetailsUrl = urls.policyDetails;
    }
  }

  void setRemoteConfigConst() {
    String? data = RemoteConfigInstance.instance
        .getData(RemoteConfigKeysSet.healthSurveyKey);
    if (data.isNotNullOrEmpty) {
      RemoteConfigConstants.healthSurveyData =
          HealthSurveyData.fromJson(jsonDecode(data!));
    }
  }

  Future<void> checkForAppUpdate() async {
    if (Platform.isIOS) {
      InAppUpdateUtility utility = InAppUpdateUtility();
      UpdateType? updateType = await utility.checkForUpdates();
      if (updateType == UpdateType.forced) {
        blockUser = true;
        if (!isClosed)
          emit(SplashScreenState(SplashScreenSuccessType.FORCED_UPDATE));
      }
    }
    return;
  }

  Future<AppConfig?> getAppDependencies() async {
    try {
      final String appConfig = RemoteConfigInstance.instance
          .getData(RemoteConfigKeysSet.appDependencies);
      if (appConfig.isNotNullOrEmpty) {
        RemoteConfigConstants.configsData =
            AppConfig.fromJson(jsonDecode(appConfig));
      }
      var data = RemoteConfigConstants.configsData;
      if (data != null) {
        Constants.GOOGLE_PLACE_API_KEY = data.googlePlaceApiKey;
        HealthConstants.ONEMG_API_BASE_URL = data.onemgApiBaseUrl;
        HealthConstants.ONEMG_MEDICINE_URL = data.onemgMedicineUrl;
        HealthConstants.ONEMG_MEDICINE_KEY = data.onemgMedicineKey;
        HealthConstants.ONEMG_LABS_URL = data.onemgLabsUrl;
        HealthConstants.ONEMG_LABS_KEY = data.onemgLabsKey;
        HealthConstants.ONE_MG_HEADERS = data.onemgHeaders;
        if (data.googleMapsApiKey.isNotNullAndEmpty && Platform.isIOS) {
          try {
            Constants.PLATFORM_CHANNEL.invokeMethod("init_maps_sdk",
                {"google_maps_api_key": data.googleMapsApiKey!});
          } catch (e, s) {
            logException("mapsss exception ${e.toString()}", trace: s);
          }
        }
      }
      return data;
    } catch (e, stack) {
      FirebaseCrashlytics.instance.recordError(e, stack);
    }
    return null;
  }

  navigateToLandingPage() async {
    _splashScreenRepository = SplashScreenRepository();
    bool isAppOpened = await isAppOpen();
    bool didDisplayOnboardingQuestions =
        await didDisplayedOnboardingQuestions();
    String? userEkey =
        await SessionManager.instance.storageManager.getUserEkey();
    await handleUserLogout(userEkey);
    await _checkForProspectId(userEkey);
    _appUpdater2d2(isAppOpened);
    bool isLoggedIn = await isLogIn();
    if (!isLoggedIn) {
      await checkForDeeplink();
    }

    if (!isAppOpened || (!isLoggedIn && isAppOpened)) {
      await navigateToLoginPage(isAppOpened);
    } else if (!didDisplayOnboardingQuestions) {
      await navigateToOnboardingQuestionScreen(userEkey);
    } else {
      await navigateToHome(userEkey);
    }
  }

  _checkForProspectId(String? userId) async {
    bool isLoggedIn = await isLogIn();
    String? prospectNumber =
        await getStringPrefs(StringDataSharedPreferenceKeys.PROSPECT_ID);
    if (isLoggedIn &&
        userId.isNotNullAndEmpty &&
        prospectNumber.isNullOrEmpty) {
      final prospectId = await LoginRepository().getProspectId(userId!);
      String? prospectIdTemp =
          await getStringPrefs(StringDataSharedPreferenceKeys.PROSPECT_ID);

      /// Splashscreenbloc gets called twice
      if (prospectId.error == null && prospectIdTemp.isNullOrEmpty) {
        setStringPrefs(
            StringDataSharedPreferenceKeys.PROSPECT_ID, prospectId.id);
        var fcmToken =
            await Constants.PLATFORM_CHANNEL.invokeMapMethod("get_fcm_token");
        Constants.PLATFORM_CHANNEL.invokeMethod("webengage_login",
            {"prospect_id": prospectId.id, "fcm_token": fcmToken});
      }
    }
  }

  Future<void> navigateToLoginPage(bool isAppOpened) async {
    if (await Util.isNetworkConnected()) {
      await callHeaderApi(isAppOpened);
    } else {
      saveNoNetworkData("SPLASH_SCREEN");
      if (!isClosed)
        emit(SplashScreenState(SplashScreenSuccessType.SPLASH,
            message: noInternetMessage));
    }
  }

  Future<void> setUserPropertiesBeforeNavigation() async {
    await _setUserPropsInAnalytics();
    sendAppOpenEvent();
    _persistUserIdAndSetUserPref();
  }

  Future<void> navigateToOnboardingQuestionScreen(String? userId) async {
    await setUserPropertiesBeforeNavigation();
    if (!isClosed)
      emit(SplashScreenState(SplashScreenSuccessType.ON_BOARDING_QUESTIONS));
  }

  Future<void> navigateToHome(String? userId) async {
    await setUserPropertiesBeforeNavigation();
    if (!isClosed) emit(SplashScreenState(SplashScreenSuccessType.HOME));
  }

  Future<void> handleUserLogout(String? userId) async {
    bool isLoggedIn = await isLogIn();
    if (isLoggedIn && userId.isNullOrEmpty) {
      logException(Exception("userid:: user logged in but cookie null"));
      await setIsLoggedIn(false);
      await logoutHandling();
    }
  }

  Future<void> callHeaderApi(bool isAppOpened) async {
    ProfileResponse? response = await _splashScreenRepository.callHeaderApi();
    if (response != null) {
      if (response.error == null) {
        try {
          final trackerIDCookie = (await SessionManager.instance.storageManager
              .getUserSessionDetails())
              .trackerId;
          if (trackerIDCookie.isNotNullAndEmpty) {
            await setIsAppOpen(true);
            _appUpdater2d2(true);
          }
        } catch (error, stacktrace) {
          await FirebaseCrashlytics.instance.recordError(
              error, stacktrace); // Firebase crashlytics recording an error.
          Zone.current.handleUncaughtError(error, stacktrace);
        } finally {
          await setUserPropertiesBeforeNavigation();
          if (!isClosed)
            emit(SplashScreenState(SplashScreenSuccessType.LOGIN_LANDING_PAGE));
        }
      } else {
        if (!isClosed)
          emit(SplashScreenState(SplashScreenSuccessType.SPLASH,
              message: somethingWentWrong));
      }
    } else {
      logException(Exception("Header API response is null"));
    }
  }

  void _persistUserIdAndSetUserPref() async {
    try {
      final userEkey =
          await SessionManager.instance.storageManager.getUserEkey();
      if (userEkey.isNotNullAndEmpty) {
        await AnalyticsTrackerManager.instance
            .identifyUser(userEkey!, IdentifierType.userId);
        Map<String, dynamic> attributes = {
          'id': userEkey,
        };
        RemoteConfigInstance.instance.setGBAttributes(attributes);
      }
    } catch (error, stackTrace) {
      await FirebaseCrashlytics.instance.recordError(
          error, stackTrace); // Firebase crashlytics recording an error.
      Zone.current.handleUncaughtError(error, stackTrace);
    }
  }

  Future<void> _setUserPropsInAnalytics() async {
    /// Sometimes, for different reasons tracker_id is not getting set, Till now we are setting tracker_id as anonymous_id on segment only on the new user onboarding not in other cases.
    /// Setting tracker_id as anonymous_id here to make sure in all cases we set the segment anonymous_id (This logic moved from repository to bloc)
    String? trackerId = await Util.getTrackerId();
    String? phone =
        await getStringPrefs(StringDataSharedPreferenceKeys.MOBILE_NUMBER);
    await Future.wait([
      if (trackerId?.isNotNullAndEmpty ?? false)
        AnalyticsTrackerManager.instance
            .identifyUser(trackerId!, IdentifierType.anonymous),
      AnalyticsTrackerManager.instance
          .identifyUser(phone ?? '', IdentifierType.phoneNumber),
      AnalyticsTrackerManager.instance
          .identifyUser(Util.adId ?? '', IdentifierType.adId),
      AnalyticsTrackerManager.instance
          .identifyUser(Util.idfaValue ?? '', IdentifierType.idfa),
    ]);
  }

  void sendAppOpenEvent() {
    sendAppInstallEvent();
    AnalyticsTrackerManager.instance.sendEvent(
        event: PageLoadedConstants.APP_OPEN_SPLASH_SCREEN_LOADED,
        properties: {'platform': Util.getPlatform()});
    R2D2Events.instance.trackSplashScreenLoadEvent();

    setAppOpenCounter();
  }

  void sendAppInstallEvent() async {
    Constants.PLATFORM_CHANNEL.invokeMethod(Constants.APP_OPEN);
    if (Platform.isIOS) {
      final bool? isAlreadyInstalled =
          await getBoolPrefs(BoolDataSharedPreferenceKeys.IS_ALREADY_INSTALLED);
      if (!(isAlreadyInstalled ?? false)) {
        AnalyticsTrackerManager.instance.sendEvent(
            event: PageLoadedConstants.ACKO_APP_INSTALL, properties: {});
        await setBoolPrefs(
            BoolDataSharedPreferenceKeys.IS_ALREADY_INSTALLED, true);
      }
    }
  }

  void setAppOpenCounter() async {
    // Overlfow limit for Integer
    final int maxInt64 = double.maxFinite.toInt();
    final int? count =
        await getIntPrefs(IntDataSharedPreferenceKeys.APP_OPEN_COUNTER);

    /// resetting the count to 1 incase the integer would overflow
    final int counter = (count == null || count >= maxInt64) ? 1 : count + 1;
    await setIntPrefs(IntDataSharedPreferenceKeys.APP_OPEN_COUNTER, counter);
  }

  _appUpdater2d2(bool isAppOpened) async {
    if (isAppOpened) {
      String? prevAppVersion = await getStringPrefs(
          StringDataSharedPreferenceKeys.PREVIOUS_APP_VERSION);
      if (prevAppVersion == null || prevAppVersion.isEmpty) {
        R2D2Events.instance.trackAppUpdateEvent(Constants.APP_INSTALL_SUCCESS);
      }
    }
  }
}

class SplashScreenState {
  SplashScreenSuccessType type;
  String? message;
  Map<String, dynamic>? params;

  SplashScreenState(this.type, {this.message, this.params});
}

enum SplashScreenSuccessType {
  LOGIN,
  LOGIN_LANDING_PAGE,
  HOME,
  SPLASH,
  INIT_TIMER,
  FORCED_UPDATE,
  ON_BOARDING_QUESTIONS
}
