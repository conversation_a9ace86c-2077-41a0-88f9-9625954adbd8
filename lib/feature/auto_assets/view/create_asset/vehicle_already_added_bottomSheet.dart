// ignore_for_file: must_be_immutable

import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/view/AckoText.dart';
import 'package:acko_flutter/feature/asset_sdui/bloc/asset_sdui_bloc.dart';
import 'package:analytics/analytics.dart';
import 'package:analytics/events/tap_events.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:utilities/state_provider/StateProvider.dart';

import '../../../../common/util/strings.dart';
import '../../../../r2d2/events.dart';
import 'package:utilities/constants/constants.dart';
import '../../../../util/Utility.dart';
import '../../../../util/health/utils.dart';
import '../../model/detail_vehicle_asset_model.dart';

class VehicleAlreadyAddedBottomSheetContent extends StatelessWidget {
  VehicleAlreadyAddedBottomSheetContent(
      {Key? key, required this.ctx, required this.regNumber, this.vehicleList})
      : super(key: key);
  final BuildContext ctx;
  final String regNumber;
  List<DetailVehicleAssetModel>? vehicleList;
  DetailVehicleAssetModel? _detailVehicleModel;

  @override
  Widget build(BuildContext context) {
    if (vehicleList != null && vehicleList!.isEmpty) {
      _closeBottomsheetWithError();
      return SizedBox();
    }
    _detailVehicleModel = vehicleList!.first;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 32.0),
      width: MediaQuery.of(context).size.width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: 100,
            width: 100,
            margin: EdgeInsets.only(top: 32, bottom: 24),
            child: SvgPicture.asset(Util.getAssetImage(
                assetName: (_detailVehicleModel?.vehicle?.isCar ?? false)
                    ? 'car.svg'
                    : 'bike.svg')),
          ),
          TextEuclidMedium14(
            vehicle_has_been_added.replaceAll('vehicle',
                '${(_detailVehicleModel?.vehicle?.isCar ?? false) ? 'car' : 'bike'}'),
            textSize: 18.0,
            textColor: color040222,
            weight: FontWeight.w700,
            height: 1.3,
            textAlign: TextAlign.center,
          ),
          SizedBox(
            height: 64,
          ),
          SizedBox(
            width: MediaQuery.of(context).size.width,
            child: OutlinedButton(
              onPressed: _navigateToAssetDetailsPage,
              style: ButtonStyle(
                side: MaterialStateProperty.all(BorderSide(color: color040222)),
                shape: MaterialStateProperty.all(RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(
                      8.0,
                    ),
                    side: BorderSide(
                        color: color040222,
                        width: 1.0,
                        style: BorderStyle.solid))),
                padding: MaterialStateProperty.all(
                    EdgeInsets.symmetric(vertical: 16.0, horizontal: 32.0)),
              ),
              child: TextEuclidMedium14(
                go_to_vehicle_details.replaceAll('vehicle',
                    '${(_detailVehicleModel?.vehicle?.isCar ?? false) ? 'car' : 'bike'}'),
                textSize: 16.0,
                textColor: color040222,
                textAlign: TextAlign.center,
              ),
            ),
          ),
          SizedBox(
            height: 32,
          ),
          InkWell(
            onTap: _closeBottomSheet,
            child: TextEuclidRegular14(
              add_another_vehicle,
              textColor: color1C73E8,
            ),
          ),
        ],
      ),
    );
  }

  _closeBottomsheetWithError() {
    _closeBottomSheet();
    UiUtils.getInstance
        .showToast(somethingWentWrong, toastLength: Toast.LENGTH_LONG);
  }

  _closeBottomSheet() {
    AnalyticsTrackerManager.instance.sendEvent(
        event: TapConstants.TAP_BTN_ADD_ANOTHER_ASSET, properties: {});
    if (_detailVehicleModel != null)
      R2D2Events.instance.trackAssetManagementButtonTappedEvents(
          'tap_btn_add_another_asset', 'create_asset',
          regNumber: _detailVehicleModel?.vehicle?.assetNumber);
    Navigator.pop(ctx);
  }

  _navigateToAssetDetailsPage() {
    AnalyticsTrackerManager.instance.sendEvent(
        event: TapConstants.TAP_BTN_ASSET_ALREADY_EXISTS,
        properties: {
          "registration_number": _detailVehicleModel?.vehicle?.assetNumber,
          "vehicle_make": _detailVehicleModel?.vehicle?.makeName,
          "vehicle_model": _detailVehicleModel?.vehicle?.makeModel,
          "vehicle_variant": _detailVehicleModel?.vehicle?.vehicleType,
        });
    R2D2Events.instance.trackAssetManagementButtonTappedEvents(
        'tap_btn_asset_already_exists', 'create_asset',
        regNumber: _detailVehicleModel?.vehicle?.assetNumber);

    Navigator.pushReplacementNamed(ctx, Routes.SINGLE_ASSET_VIEW, arguments: {
      "assetId": _detailVehicleModel?.assetId,
      "regNumber": _detailVehicleModel?.vehicle?.assetNumber,
      "context": ctx
    });
  }
}
