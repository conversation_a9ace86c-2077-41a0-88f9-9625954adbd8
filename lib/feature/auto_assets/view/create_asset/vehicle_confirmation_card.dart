// ignore_for_file: must_be_immutable

import 'package:acko_flutter/common/view/AckoText.dart';
import 'package:acko_flutter/common/view/Application.dart';
import 'package:acko_flutter/feature/acko_services/utilities/utility.dart';
import 'package:acko_flutter/feature/asset_sdui/bloc/asset_sdui_bloc.dart';
import 'package:acko_flutter/feature/auto_assets/bloc/create_asset_bloc.dart';
import 'package:acko_flutter/r2d2/events.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/analytics.dart';
import 'package:analytics/events/card_loaded_events.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:analytics/events/tap_events.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:session_manager_module/session_manager.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/remote_config/remote_config.dart';
import 'package:utilities/state_provider/StateProvider.dart';

import '../../../../common/util/PreferenceHelper.dart';
import '../../../../common/util/color_constants.dart';
import '../../../../common/util/strings.dart';
import '../../bloc/auto_assets_controller_bloc.dart';
import '../../model/asset_search_model.dart';
import '../../model/detail_vehicle_asset_model.dart';
import '../common/vehicle_card_widget.dart';

class VehicleConfirmationCard extends StatefulWidget {
  final VehicleAssetModel model;
  String? source;
  bool gradientBg;
  String? vehicleConfirmationCustomTitle;
  VoidCallback popBack;
  VehicleConfirmationCard(
      {Key? key,
      required this.model,
      required this.buildContext,
      this.source,
      required this.userAssetControllerBloc,
      this.gradientBg = true,
      required this.popBack,
      this.vehicleConfirmationCustomTitle})
      : super(key: key);
  final UserAssetsControllerBloc userAssetControllerBloc;
  final BuildContext buildContext;

  @override
  State<VehicleConfirmationCard> createState() => _VehicleConfirmationCardState();
}

class _VehicleConfirmationCardState extends State<VehicleConfirmationCard> {
  SearchAssetBloc? _bloc;

  @override
  Widget build(BuildContext context) {
    _bloc = BlocProvider.of<SearchAssetBloc>(context);
    final _size = MediaQuery.of(context).size;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0),
      child: Center(
        child: Container(
          width: _size.width,
          decoration: BoxDecoration(
              gradient: widget.gradientBg
                  ? LinearGradient(
                      colors: [colorEFE9FB, colorE3FAFC],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomRight)
                  : null,
              color: colorFFFFFF,
              borderRadius: BorderRadius.circular(16.0)),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 40),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: _size.width / 3,
                height: _size.width / 3,
                padding: const EdgeInsets.all(8),
                decoration:
                    BoxDecoration(shape: BoxShape.circle, color: Colors.white),
                child: SvgPicture.asset(Util.getAssetImage(
                    assetName:
                        (widget.model.isCar ?? false) ? 'car.svg' : 'bike.svg')),
              ),
              SizedBox(
                height: 40,
              ),
              TextEuclidMedium14(
                widget.vehicleConfirmationCustomTitle.isNotNullOrEmpty
                    ? widget.vehicleConfirmationCustomTitle ?? ''
                    : vehicleConfirmationTitle(widget.model.isCar ?? false),
                textColor: color000000,
                textSize: 18.0,
                textAlign: TextAlign.center,
                height: 1.3,
              ),
              SizedBox(
                height: 24,
              ),
              VehicleCardWidget(
                cardWidth: _size.width,
                isSlidable: false,
                withoutOutlineCard: true,
                model: widget.model.toDetailVehicleAssetModel(),
                onTap: (id) {
                  // _navigateToVehicleDetailedPage(context);
                },
              ),
              SizedBox(
                height: 24,
              ),
              OutlinedButton(
                onPressed: () {
                  _onConfirmationButtonTapped(context);
                },
                child: Center(
                  child: Text(
                    confirm_cta_txt,
                    style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: colorFFFFFF),
                  ),
                ),
                style: ButtonStyle(
                  backgroundColor: MaterialStateProperty.all<Color>(
                    color000000,
                  ),
                  shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                      RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.0),
                  )),
                  padding: MaterialStateProperty.all(
                      EdgeInsets.symmetric(vertical: 14.0, horizontal: 20.0)),
                ),
              ),
              SizedBox(
                height: 12,
              ),
              InkWell(
                onTap: () {
                  AnalyticsTrackerManager.instance.sendEvent(
                      event: TapConstants.TAP_BTN_ASSET_NOT_YOUR_VEHICLE,
                      properties: {"registration_number": widget.model.assetNumber});

                  R2D2Events.instance.trackAssetManagementButtonTappedEvents(
                      'tap_btn_add_asset_not_your_vehicle', 'create_asset',
                      regNumber: widget.model.assetNumber);
                  _bloc?.resetToInitState();
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 12.0),
                  child: TextEuclidMediumL2(
                    not_your_vehicle,
                    textSize: 14.0,
                    weight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _onConfirmationButtonTapped(context) async {
    AnalyticsTrackerManager.instance
        .sendEvent(event: TapConstants.TAP_BTN_CONFIRM_ADD_ASSET, properties: {
      "registration_number": widget.model.assetNumber,
      "vehicle_make": widget.model.makeName,
      "vehicle_model":
          (widget.model.makeModel ?? "").replaceAll(widget.model.makeName ?? "", ''),
      "vehicle_variant": widget.model.vehicleType,
      'from_page': widget.source ?? 'asset_management',
      "type": _getType(widget.model.vehicleType ?? "car"),
      'registration_year': widget.model.registration_year,
      'registration_month': widget.model.registration_month
    });

    R2D2Events.instance.trackAssetManagementButtonTappedEvents(
        'tap_btn_confirm_add_asset', widget.source ?? 'asset_management',
        regNumber: widget.model.assetNumber,
        extraInfo: {
          'vehicle_make': widget.model.makeName,
          'vehicle_model':
              (widget.model.makeModel ?? "").replaceAll(widget.model.makeName ?? "", ''),
          'vehicle_variant': widget.model.vehicleType,
          "type": _getType(widget.model.vehicleType ?? "car"),
          'registration_year': widget.model.registration_year,
          'registration_month': widget.model.registration_month
        });
    setBoolPrefs(
        BoolDataSharedPreferenceKeys.MANAGE_ASSETS_BOTTOM_SHEET_SHOWN, true);

    _bloc?.changeStateToLoading();


    DetailVehicleAssetModel _detailVehicleModel =
        await widget.userAssetControllerBloc.addAsset(widget.model.assetNumber);
    if (_detailVehicleModel.error == null) {
      // todo: confirm it
      if (widget.source == 'fastag') {
        AnalyticsTrackerManager.instance.sendEvent(
            event: CardLoadedConstants.TRACK_EVENT_COMPLETE,
            properties: {
              "name": "add_vehicle_from_fastag",
              "journey": widget.source ?? 'asset_management',
              "platform": Util.getPlatform(),
              "product": "app_vas"
            });
      }
      _navigateToVehicleDetailedPage(_detailVehicleModel);
    } else {
      _bloc?.handleError(_detailVehicleModel.error?.message,
          _detailVehicleModel.error?.subTitle);
    }
  }

  _navigateToVehicleDetailedPage(
      DetailVehicleAssetModel detailVehicleModel) async {
    AnalyticsTrackerManager.instance.sendEvent(
        event: PageLoadedConstants.ADD_ASSET_SUCCESS_SCREEN_LOADED,
        properties: {
          'registration_number': widget.model.assetNumber,
          'vehicle_make': widget.model.makeName,
          'vehicle_model':
              (widget.model.makeModel ?? "").replaceAll(widget.model.makeName ?? "", ''),
          'vehicle_variant': widget.model.vehicleType,
          'from_page': widget.source ?? 'home'
        });

    R2D2Events.instance.trackAssetManagementButtonTappedEvents(
        'add_asset_success_screen_loaded', 'asset_detail_page',
        regNumber: widget.model.assetNumber,
        extraInfo: {
          'vehicle_make': widget.model.makeName,
          'vehicle_model':
              (widget.model.makeModel ?? "").replaceAll(widget.model.makeName ?? "", ''),
          'vehicle_variant': widget.model.vehicleType,
        });

    bool useV10Design = (await RemoteConfigInstance.instance.getGbAsyncData(RemoteConfigKeysSet.APP_IA_VERSION)).toString().equalsIgnoreCase("app_v10");
    if(!useV10Design){
      Navigator.pushReplacementNamed(widget.buildContext, Routes.SINGLE_ASSET_VIEW,
          arguments: {
            "showNewAssetAnimation": true,
            "assetId": detailVehicleModel.assetId,
            "regNumber": detailVehicleModel.vehicle?.assetNumber,
            "context": context
          });
    }else{
      StateProvider().notify(ObserverState.NEW_ASSET_ADDED, data: {
        "assetId": detailVehicleModel.assetId,
        "regNumber": detailVehicleModel.vehicle?.assetNumber,
      });
      widget.popBack!();
      StateProvider().notify(ObserverState.CHANGE_TAB, data: {"index": 1});
    }
  }

  _getType(String vehicleType) {
    bool isCar = Utility.getVehicleType(vehicleType) != 0;
    return isCar ? "car" : 'bike';
  }
}
