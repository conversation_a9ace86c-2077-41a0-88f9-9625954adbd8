import 'package:acko_flutter/network/ApiResponse.dart';
import 'package:design_module/utilities/hide_scroll_glow_effect.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:utilities/widgets/acko_page_ui/model/acko_error_data.dart';
import 'package:utilities/widgets/acko_page_ui/model/acko_loaded_data_model.dart';
import 'package:utilities/widgets/acko_page_ui/stateful/acko_stateful_ui.dart';

import '../../../common/view/ErrorWidget.dart';
import '../../acko_services/ui/acko_service_loading_screen.dart';
import '../bloc/adaptive_sdui_bloc.dart';
import '../parser/reusable_sdui_page_parser.dart';

class AckoAdaptiveSduiScreen extends StatefulWidget {
  @override
  State<AckoAdaptiveSduiScreen> createState() => _AckoAdaptiveSduiScreenState();
}

class _AckoAdaptiveSduiScreenState
    extends AckoUiStatefulState<AckoAdaptiveSduiScreen, AckoAdaptiveSduiBloc>
    with ErrorViewRetryCallBack {
  ReusableSduiPageParser sduiParser = ReusableSduiPageParser();

  @override
  void onRetry() {
    BlocProvider.of<AckoAdaptiveSduiBloc>(context).refresh();
  }

  @override
  Widget pageContentWrapperView(Widget pageContent) {
    return Scaffold(
      body: pageContent,
    );
  }

  @override
  Widget errorView(AckoErrorDataModel? error) {
    return ErrorPage(
      errorHandler: ErrorHandler(error),
      retryCallbackFunc: this,
    );
  }

  @override
  Widget pageContent(AckoLoadedDataModel loadedData) {
    List<Widget> widgets = sduiParser.fromJson(loadedData.data!);

    return RefreshIndicator(
      onRefresh: () async {
        if (mounted) {
          context.read<AckoAdaptiveSduiBloc>().refresh();
        }
      },
      child: ListView.builder(
        physics: const ClampingScrollPhysics(),
        padding: EdgeInsets.zero,
        shrinkWrap: true,
        itemCount: widgets.length,
        itemBuilder: (context, index) => widgets[index],
      ),
    );
  }

  @override
  Widget loadingView() {
    return AckoServiceLoadingScreen();
  }
}
