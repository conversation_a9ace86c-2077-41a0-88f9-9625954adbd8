import 'package:flutter/material.dart';
import 'package:sdui/sdui.dart';

class HsrpParser extends SDUIBaseParser {
  @override
  List<Widget> fromJson(Map<String, dynamic> json,
      {SDUIBaseParser? parser, int? widgetPosition, String? key}) {
    List<Widget> hsrpPageWidgets = [];

    if (json['widgets'] != null) {
      for (var element in (json['widgets'] as List)) {

        Widget widget = SDUIParser.getInstance().fromJson(element);
        if (widget != SDUINull()) {
          hsrpPageWidgets.add(widget);
        }
      }
    }

    return hsrpPageWidgets;
  }
}
