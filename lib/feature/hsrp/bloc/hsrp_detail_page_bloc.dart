import 'package:sdui/sdui.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/utilities.dart';
import 'package:utilities/widgets/acko_page_ui/model/acko_loaded_data_model.dart';
import 'package:utilities/widgets/acko_safe_cubit.dart';

class HsrpDetailPageBloc extends AckoSafeCubit<AckoCubitStates> {
  final BaseRepository _baseRepository;
  final Map<String, dynamic> params;
  String? regNum;

  HsrpDetailPageBloc({required this.params, BaseRepository? baseRepository}) :
        _baseRepository = baseRepository ?? BaseRepository(),
        super(AckoLoadingState()) {
    _getPageView();
  }

  refresh() async {
    await _getPageView();
  }

  Future<void> _getPageView() async {
    emit(AckoLoadingState());
    regNum = params['reg_num'];
    String url = ApiPath.HSRP_DETAIL_PAGE(regNum!);
    /// call repository and get json
    ResponseWrapper response = await _baseRepository.getResponse(url);

    if (response.error == null && response.data != null && response.data!['widgets'] != null) {
        emit(AckoLoadedState(loadedData: AckoLoadedDataModel(data: response.data)));
    } else {
      emit(AckoErrorState());
    }
  }
}
