import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/feature/auto_assets/view/create_asset/error_state.dart';
import 'package:acko_flutter/feature/hsrp/parser/hsrp_parser.dart';
import 'package:acko_flutter/feature/vas-sdui/gradient_header_scaffold.dart';
import 'package:acko_flutter/feature/vas-sdui/view/widgets/vas_loading_view.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:utilities/widgets/acko_page_ui/model/acko_error_data.dart';
import 'package:utilities/widgets/acko_page_ui/model/acko_loaded_data_model.dart';
import 'package:utilities/widgets/acko_page_ui/stateful/acko_stateful_ui.dart';
import '../bloc/hsrp_detail_page_bloc.dart';

class HsrpDetailPage extends StatefulWidget {
  @override
  State<HsrpDetailPage> createState() => _HsrpDetailPageState();
}

class _HsrpDetailPageState extends AckoUiStatefulState<HsrpDetailPage, HsrpDetailPageBloc>{
  HsrpDetailPageBloc? _bloc;

  @override
  initState(){
    super.initState();
    AnalyticsTrackerManager.instance.sendEvent(
      event: PageLoadedConstants.APP_VIEW_PAGE,
      properties: {
        "page_name" : "HSRP RC Details",
      },
    );
    _bloc = context.read<HsrpDetailPageBloc>();
  }

  @override
  Widget loadingView() {
    return VasLoadingScreen(
      text: matching_your_vehicle_hsrp,
      pageName: 'HsrpDetailPage',
    );
  }

  @override
  Widget errorView(AckoErrorDataModel? error) {
    return CreateVehicleErrorState(
      onRetryTapped: (){
        _bloc?.refresh();
      }, registrationNumber: _bloc?.regNum ?? "",
    );
  }

  @override
  Widget pageContent(AckoLoadedDataModel loadedData) {
    if (loadedData.data == null) return const SizedBox.shrink();

    final List<Widget> widgets = HsrpParser().fromJson(loadedData.data!);

    return SingleChildScrollView(
      child: Center(
        child: Column(
          children: widgets,
        ),
      ),
    );
  }

  @override
  Widget pageContentWrapperView(Widget stateUi) {
    return GradientHeaderScaffold(
      onLeadingIconTapped: () => Navigator.pop(context),
      body: stateUi,
    );
  }
}
