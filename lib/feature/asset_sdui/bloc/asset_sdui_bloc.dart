import 'dart:async';

import 'package:acko_flutter/common/util/PreferenceHelper.dart';
import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/feature/acko_services/ui/acko_service_common_header.dart';
import 'package:acko_flutter/feature/asset_sdui/parser/custom_grid_tile.dart';
import 'package:acko_flutter/feature/auto_assets/model/assets_documents_model.dart';
import 'package:acko_flutter/travel_insurance/shared/util/enum.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/card_loaded_events.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:analytics/events/tap_events.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:networking_module/util/etag.dart';
import 'package:sdui/sdui.dart';
import 'package:session_manager_module/StorageManager/shared_preferences_storage.dart';
import 'package:utilities/remote_config/remote_config.dart';
import 'package:utilities/utilities.dart';

import '../../../common/bloc/acko_safe_bloc.dart';
import '../../../r2d2/events.dart';
import '../../../util/Utility.dart';
import '../../../util/file_picker/file_picker.dart';
import '../../../util/health/utils.dart';
import '../../acko_services/helpers/mixins/rate_limit_check_mixin.dart';
import '../../auto_assets/model/puc_model.dart';
import '../../auto_assets/repository/single_asset_management_repository.dart';
import '../model/asset_ui_model.dart';
import '../parser/asset_parser.dart';

class AssetSDUIBloc extends SafeAckoBloc<AssetSDUIBlocState>
    with LocationHeaderMixin, RateLimitCallerMixin {
  final BaseRepository _repo = BaseRepository();
  AssetUiModel commonDataStoreAssetModel = AssetUiModel();
  late AssetParser assetParser;
  String? assetId, registrationNumber;
  String? assetGroupId, userId;
  bool apiCallInProgress = false;
  Map<String, Widget?> asyncWidgets = {};
  Map<String, Widget?> refreshAsyncWidgets = {};
  bool vasServiceTapped = false;
  List<Widget> emptyAssetViewWidgets = List.empty(growable: true);
  SingleAssetManagementRepository _repository =
      SingleAssetManagementRepository();
  StreamController<String> updateIdListenerCallback =
      StreamController<String>.broadcast();
  String lastLoadedAsset = "";
  String? phone;
  int? assetsCount;
  bool useV10Design = false;


  /// For uploading documents
  Map<String, AssetUiModel?> data = {};
  Map<String, bool> apiCallInProgressMap = {};

  AssetSDUIBloc(this.assetId, this.registrationNumber)
      : super(AssetLoadingState()) {
    assetParser = AssetParser(
        onAssetTapped,
        onAssetDocumentTapped,
        onVasServiceTapped,
        commonDataStoreIdsLoaded,
        updateIdListenerCallback,
        refreshVasTapped,
    );
    if (this.assetId.isNotNullAndEmpty) {
      lastLoadedAsset = this.assetId!;
      AnalyticsTrackerManager.instance
          .sendEvent(event: TapConstants.TAP_BTN_INTERACTION, properties: {
        'app_tab_name': 'no_tab',
        'from_page': 'asset_overview_page',
        'type': 'default',
        'cta_text': 'asset id ${this.assetId}',
        'journey': 'Asset loaded',
      });
      _initAssetData();
    } else {
      emit(LoadDefaultAssetState());
    }
  }

  showEmptyAssetView() async {
    await setV10DesignFlag();
    if (emptyAssetViewWidgets.isNotEmpty) {
      emit(EmptyAssetState());
      return;
    }
    if (apiCallInProgress) return;
    apiCallInProgress = true;
    String url = "/app-config/auto/asset-details/empty-view?useV10Design=$useV10Design";
    ResponseWrapper response = await _repo.getResponse(url);
    apiCallInProgress = false;
    if (response.error == null) {
      if (response.data != null && response.data!['widgets'] != null) {
        emptyAssetViewWidgets = assetParser.fromEmptyAssetJson(response.data!);
        emit(EmptyAssetState());
      } else {
        emit(AssetErrorState("An error occurred in sdui repository"));
      }
    } else {
      emit(AssetErrorState("An error occurred in sdui repository"));
    }
  }

  assetDataReceived(Map<String, dynamic> json) {
    if (json['assets'] != null && json['assets'] is List) {
      assetId = (json['assets'] as List)[0]["id"].toString();
      registrationNumber = (json['assets'] as List)[0]["regNo"];
      emit(AssetLoadingState());
      _initAssetData();
    }
  }

  _initAssetData() {
    _fetchAssetData(assetId!);
  }

  setAssetsCount(Map<String, dynamic>? assetData) {
    assetsCount = assetData?['assets']?.length;
  }

  /// Cache implementation for fetching updated data in the background
  Future<void> fetchCacheRefresh(ResponseWrapper response,
      {bool isAsyncVas = false}) async {
    if (await ConnectionStatus.instance.isNetworkAvaialable() &&
        response.statusCode == 304 &&
        response.fromCache!) {
      final etag = createEtag(response.data);
      if (isAsyncVas) {
        refreshVasServices(refreshCache: true, etag: etag);
      } else {
        _fetchAssetData(assetId!, refreshCache: true, etag: etag);
      }
    } else {
      return;
    }
  }

  Future<void> setV10DesignFlag() async {
    useV10Design = (await RemoteConfigInstance.instance.getGbAsyncData(RemoteConfigKeysSet.APP_IA_VERSION)).toString().equalsIgnoreCase("app_v10");
    assetParser.useV10Design = useV10Design;
  }

  _fetchAssetData(String assetId,
      {bool docRefresh = false,
      bool refreshCache = false,
      String? etag}) async {
    await setV10DesignFlag();
    AssetUiModel assetUiModel = AssetUiModel();
    if (!docRefresh) {
      apiCallInProgressMap[assetId] = apiCallInProgress;
      apiCallInProgress = true;
      emit(AssetLoadingState());
    }

    /// call repository and get json
    String url = "/app-config/auto/asset-details?assetId=$assetId&useV10Design=$useV10Design";
    if (registrationNumber.isNotNullAndEmpty) {
      url += '&regNo=$registrationNumber';
    }

    await ensureLocationHeadersInitialised();

    ResponseWrapper response = await _repo.getResponse(url,
        etag: etag,
        refreshCache: refreshCache,
        requestHeaders: locationDetails?.toJson());
    apiCallInProgress = false;
    if (response.error == null) {
      /// create widget here in the bloc itself and pass it to UI for rendering.
      if (response.data != null) {
        if (response.data!['vehicleModel'] != null) {
          sendAssetPageLoadedEvent(response.data!['vehicleModel']);
        }
        if (response.data!['headerSection'] != null) {
          final List<Widget> widgets =
              assetParser.fromJson(response.data!['headerSection']);
          if (widgets.isNotEmpty) {
            assetUiModel.headerSection = widgets;
          }
        }
        if (response.data!['detailsSection'] != null) {
          final List<Widget> widgets =
              assetParser.parseDetailsSection(response.data!['detailsSection']);

          if (widgets.isNotEmpty) {
            assetUiModel.detailsSection = widgets;
          }
        }
        if (response.data!['assetGroupId'] != null &&
            response.data!['assetGroupId'].toString().isNotNullOrEmpty) {
          assetGroupId = response.data!['assetGroupId'];
        }
        if (response.data!['userId'] != null &&
            response.data!['userId'].toString().isNotNullOrEmpty) {
          userId = response.data!['userId'];
        }
        data[assetId] = assetUiModel;
        emit(AssetLoadedState());

        fetchCacheRefresh(response);
      } else {
        emit(AssetErrorState("An error occurred in sdui repository"));
      }
    } else {
      emit(AssetErrorState("An error occurred in sdui repository"));
    }
  }

  sendAssetPageLoadedEvent(Map<String, dynamic>? vehicleModel) async {
    if (vehicleModel != null) {
      if (phone.isNullOrEmpty) {
        phone =
            await getStringPrefs(StringDataSharedPreferenceKeys.MOBILE_NUMBER);
      }
      AnalyticsTrackerManager.instance
          .sendEvent(event: PageLoadedConstants.ASSET_PAGE_LOADED, properties: {
        "from_page": 'asset_management',
        "registration_number": vehicleModel['assetNumber'],
        "phone": phone,
        "registration_year": vehicleModel['registrationYear'],
        "type": vehicleModel['isCar'] ? 'car' : 'bike',
        'vehicle_make': vehicleModel['makeName'],
        'vehicle_model': (vehicleModel['makeModel'] != null &&
                vehicleModel['makeName'] != null)
            ? vehicleModel['makeModel']
                .toString()
                .replaceAll(vehicleModel['makeName']!, '')
                .trim()
            : null,
        'vehicle_variant': vehicleModel['vehicleType'],
        'mmv': vehicleModel['makeModel'],
        'pped': vehicleModel['previousPolicyExpiryDate'],
        if (assetsCount != null) "asset_count": assetsCount,
      });
    }
  }

  uploadDocument(String type, FileInfo document, String? expiry) async {
    emit(ShowLoadingState());
    bool uploadSuccess = false;

    AssetDocument documentGroup = AssetDocument(documentName: type);
    if (assetGroupId.isNullOrEmpty) {
      AssetsDocumentsModel model = await _repository.uploadDocumentToNewGroup(
          documentGroup, document, expiry ?? "", userId ?? "");
      emit(StopLoadingState());
      if (model.error == null) {
        await _repository.attachGroupIdToAsset(assetId!, model.groupId ?? "");
        assetGroupId = model.groupId;
        UiUtils.getInstance.showToast("Document uploaded successfully!",
            toastLength: Toast.LENGTH_LONG);
        refresh(docRefresh: true);
        uploadSuccess = true;
      } else {
        UiUtils.getInstance
            .showToast(something_went_wrong, toastLength: Toast.LENGTH_LONG);
      }
    } else {
      ///Patch request
      AssetsDocumentsModel model =
          await _repository.uploadDocumentToExistingGroup(
              documentGroup, document, assetGroupId ?? "", expiry ?? "");
      emit(StopLoadingState());
      if (model.error == null) {
        UiUtils.getInstance.showToast("Document uploaded successfully!",
            toastLength: Toast.LENGTH_LONG);
        refresh(docRefresh: true);
        uploadSuccess = true;
      } else {
        UiUtils.getInstance
            .showToast(something_went_wrong, toastLength: Toast.LENGTH_LONG);
      }
    }

    AnalyticsTrackerManager.instance.sendEvent(
        event: CardLoadedConstants.ASSET_DOC_UPLOAD_SUCCESS_TOAST_LOADED,
        properties: {
          'documents': documentGroup.documentName,
          'registration_number': registrationNumber,
        });

    R2D2Events.instance.trackAssetManagementButtonTappedEvents(
        'asset_doc_upload_success_toast_loaded', 'asset_detailed_view',
        regNumber: registrationNumber);
  }

  fetchPuc(bool isPucPresent) async {
    PucModel? responseModel =
        await _repository.getPucData('$assetId', force: true);
    if (responseModel != null && responseModel.error == null) {
      if (isPucPresent) {
        if (responseModel.expiryDate != null &&
            Util.isDateExpired(responseModel.expiryDate!)) {
          emit(AssetPucUpdatedState(unable_to_get_latest_puc_details,
              'assets/images/ic_red_cross.svg', true));
        } else {
          refresh();
          emit(AssetPucUpdatedState(updated_puc_details,
              'assets/images/ic_green_success_tick.svg', false));
        }
      } else {
        refresh();
        emit(AssetPucUpdatedState(updated_puc_details,
            'assets/images/ic_green_success_tick.svg', false));
      }
    } else {
      emit(AssetPucUpdatedState(unable_to_get_latest_puc_details,
          'assets/images/ic_red_cross.svg', true));
    }
  }

  onAssetDocumentTapped(String type, Map<String, dynamic>? info) async {
    AnalyticsTrackerManager.instance
        .sendEvent(event: TapConstants.TAP_BTN_INTERACTION, properties: {
      'app_tab_name': 'no_tab',
      'from_page': 'asset_overview_page',
      'type': 'upload_btn',
      'cta_text': '$type',
      'journey': 'Auto',
      'platform': Util.getPlatform(),
    });
    switch (type) {
      case "registration_certificate":
      case "driving_license":
      case "puc_certificate":
      case "insurance_document":
        if (info == null ||
            info["documentId"] == null ||
            (info["isDeleted"] ?? false)) {
          emit(UploadDocumentTapped(type));
        } else {
          AssetDocument assetDocument = AssetDocument(
            documentId: info["documentId"],
            documentName: info["documentName"],
            isDeleted: info["isDeleted"],
            url: info["url"],
            docUploadState: info["docUploadState"] == "LOADED"
                ? DocumentUploadStates.LOADED
                : DocumentUploadStates.EMPTY,
            fileExtension: info["fileExtension"],
          );

          emit(ShowDocumentAssetState(assetDocument));
        }
        break;
      // case 'puc_certificate':
      //   bool pucPresent = false;
      //   if (info != null && info["expiry_date"] != null) {
      //     DateTime expiryDate = DateTime.parse(info['expiry_date']);
      //     AssetDocument assetDocument = AssetDocument(
      //       documentId: info["id"],
      //       documentName: type,
      //       url: info["document_url"],
      //       docUploadState: DocumentUploadStates.LOADED,
      //       fileExtension: 'pdf',
      //     );
      //     if (Util.isDateExpired(expiryDate)) {
      //       emit(PucUpdateRequestedState(true));
      //     } else {
      //       emit(ShowDocumentAssetState(assetDocument));
      //     }
      //   } else {
      //     emit(PucUpdateRequestedState(false));
      //   }
      //   break;
    }
  }

  refreshVasServices({bool refreshCache = false, String? etag}) {
    vasServiceTapped = false;
    asyncWidgets.forEach((key, value) {
      getAsyncWidget(key, force: true, refreshCache: refreshCache, etag: etag);
    });
  }

  onVasServiceTapped() {
    vasServiceTapped = true;
  }

  void onAssetTapped(String id, {bool swipped = false}) {
    final isAddAction = id.equalsIgnoreCase("add");
    final eventType = swipped ? 'switch' : 'button';
    AnalyticsTrackerManager.instance.sendEvent(
      event: TapConstants.TAP_BTN_INTERACTION,
      properties: {
        'app_tab_name': 'no_tab',
        'from_page': 'asset_overview_page',
        'type': eventType,
        'cta_text': 'asset id $id',
        'journey': "Asset loaded",
      },
    );

    lastLoadedAsset = id;

    if (isAddAction) return;

    assetId = id;
    updateIdListenerCallback.add(id);
    refresh();
    emit(AssetIdUpdatedState());
  }


  refreshVasTapped(String? identifier) async {
    if (identifier.isNullOrEmpty && assetId.isNotNullOrEmpty) return;
    String url = asyncWidgets.keys
        .where((element) =>
            element.contains(assetId!) && element.contains(identifier!))
        .first;

    Uri uri = Uri.parse(url);

    String? regNo = uri.queryParameters['regNo'];

    if (await isRateLimitExceeded(regNo ?? registrationNumber, getRateLimitType(identifier!))) return;

    Widget? widget = asyncWidgets[url];
    if (widget is CustomAssetGridTile) {
      widget.showShimmer = true;
    }
    refreshAsyncWidgets[url] = widget;
    asyncWidgets.remove(url);

    ///Show loading state
    emit(AsyncWidgetsUpdated());

    if (identifier == VasServices.challan.name) {
      getAsyncWidget(url, force: true, refreshCache: true, fetchCached: false);
    } else if (identifier == VasServices.fastag.name) {
      getAsyncWidget(url, force: true, refreshCache: true, fetchCached: false);
    } else if (identifier == VasServices.puc.name) {
      getAsyncWidget(url, force: true, refreshCache: true, fetchCached: false);
    }
  }

  getRateLimitType(String identifier) {
    if (identifier == VasServices.challan.name) {
      return ServiceType.CHALLAN.name;
    } else if (identifier == VasServices.puc.name) {
      return ServiceType.PUC_VALIDITY.name;
    } else {
      return VasServices.fastag.name;
    }
  }

  getAsyncWidget(String url,
      {bool force = false,
      bool refreshCache = false,
      String? etag,
      bool fetchCached = true,
      String source = "DEFAULT",
      String useCase = "cached-if-exist"}) async {
    if (url.contains('document')) {
      refreshCache = true;
      force = true;
    }
    if (asyncWidgets.containsKey(url) && !force) {
      return;
    }
    String currentUrl = url;
    if (!fetchCached) {
      if (url.contains(VasServices.fastag.name)) {
        currentUrl = (currentUrl.contains('?'))
            ? '$currentUrl&fetchCached=false'
            : '$currentUrl?fetchCached=false';
      } else if (url.contains(VasServices.challan.name)) {
        currentUrl = (currentUrl.contains('?'))
            ? '$currentUrl&fetchCached=$fetchCached'
            : '$currentUrl?fetchCached=$fetchCached';
      } else if (url.contains(VasServices.puc.name)) {
        currentUrl = (currentUrl.contains('?'))
            ? '$currentUrl&fetchCached=$fetchCached'
            : '$currentUrl?fetchCached=$fetchCached';
      }
    }

    await ensureLocationHeadersInitialised();

    final response = await _repo.getResponse(currentUrl,
        refreshCache: refreshCache,
        etag: etag,
        requestHeaders: locationDetails?.toJson());
    if (response.error != null) {
      asyncWidgets[url] = null;
    } else {
      try {
        asyncWidgets[url] = assetParser.fromJson(response.data!).first;
        if (asyncWidgets[url] is ContainerWidget &&
            (asyncWidgets[url] as ContainerWidget).children.isEmpty &&
            ((asyncWidgets[url] as ContainerWidget).height ?? 0) == 0 &&
            ((asyncWidgets[url] as ContainerWidget).width ?? 0) == 0) {
          asyncWidgets[url] = null;
        }
      } catch (e) {
        asyncWidgets[url] = null;
      }
    }
    refreshAsyncWidgets.remove(url);
    emit(AsyncWidgetsUpdated());

    if (url.contains('auto/asset-details/vas') && !url.contains('fastag'))
      fetchCacheRefresh(response, isAsyncVas: true);
  }

  Future<bool> isRateLimitExceeded(registrationNumber, serviceType) async {
    Map<String, dynamic>? rateLimitExceededComponent = await getRateLimitComponent(
        registrationNumber: registrationNumber,
        serviceType: serviceType,
        source: RateLimitSources.ASSET_PAGE.name,
        useCase: RateLimitUseCases.latest.name
    );

    if (rateLimitExceededComponent != null) {
      emit(RateLimitExceeded(rateLimitExceededComponent));
      return true;
    }

    return false;
  }

  commonDataStoreIdsLoaded(List<String> ids) {
    for (String id in ids) {
      if (!data.containsKey(id)) {
        data[id] = null;
      }
    }
    emit(AssetLoadedState());
  }

  removeDocument(AssetDocument? document, BuildContext context) async {
    AnalyticsTrackerManager.instance.sendEvent(
        event: TapConstants.TAP_BTN_CONFIRM_REMOVE_ASSET_DOC,
        properties: {
          'documents': document?.documentName,
          'registration_number': registrationNumber,
        });
    await R2D2Events.instance.trackAssetManagementButtonTappedEvents(
      'tap_btn_confirm_remove_asset_doc',
      'asset_detailed_view',
      regNumber: registrationNumber,
    );

    bool _isDeleteSuccessFull =
        await _repository.removeDoc(document?.documentId ?? "");
    if (!_isDeleteSuccessFull) {
      UiUtils.getInstance
          .showToast(somethingWentWrong, toastLength: Toast.LENGTH_LONG);
      return;
    }
    refresh(docRefresh: true);
    AnalyticsTrackerManager.instance.sendEvent(
        event: CardLoadedConstants.ASSET_DOC_REMOVED_SUCCESS_TOAST_LOADED,
        properties: {
          'documents': document?.documentName,
          'registration_number': registrationNumber,
        });
    await R2D2Events.instance.trackAssetManagementButtonTappedEvents(
        'asset_doc_removed_success_toast_loaded', 'asset_detailed_view',
        regNumber: registrationNumber);
  }

  refreshPage({String? registrationNumber, String? assetId}){
    this.assetId = assetId;
    this.registrationNumber = registrationNumber;
    assetGroupId = null;
    data.clear();
    userId = null;
    apiCallInProgress = false;
    asyncWidgets.clear();
    refreshAsyncWidgets.clear();
    lastLoadedAsset = "";
    vasServiceTapped = false;
    emptyAssetViewWidgets.clear();
    lastLoadedAsset = "";
    assetsCount = null;
    if (this.assetId.isNotNullAndEmpty) {
      lastLoadedAsset = this.assetId!;
      AnalyticsTrackerManager.instance
          .sendEvent(event: TapConstants.TAP_BTN_INTERACTION, properties: {
        'app_tab_name': 'no_tab',
        'from_page': 'asset_overview_page',
        'type': 'default',
        'cta_text': 'asset id ${this.assetId}',
        'journey': 'Asset loaded',
      });
      _initAssetData();
    } else {
      emit(LoadDefaultAssetState());
    }
  }

  Future<void> refresh({bool docRefresh = false}) async {
    emit((docRefresh ? DocRefreshState() : AssetLoadingState()));
    if (docRefresh) {
      String url = '';
      asyncWidgets.forEach((key, value) {
        if (key.contains('auto/asset-details/document-section')) {
          url = key;
        }
      });
      asyncWidgets[url] = null;
      getAsyncWidget(url, refreshCache: true, force: true);
      return;
    }

    await _fetchAssetData(
        docRefresh: docRefresh, assetId!, refreshCache: false);
  }

  selectorDataReceived(Map<String, dynamic> json) async{
    await setV10DesignFlag();
    if (assetId == null) return;
    final selectorWidget = assetParser.getSelectorWidget(json, assetId!);
    if (selectorWidget != null) {
      commonDataStoreAssetModel.selectorSection = selectorWidget;
      emit(AssetLoadedState());
    }
  }

  basicInfoDataReceived(Map<String, dynamic> json) {
    final basicInfoWidget = assetParser.getBasicInfoWidget(json);
    if (basicInfoWidget != null) {
      commonDataStoreAssetModel.basicInfoSection = basicInfoWidget;
      emit(AssetLoadedState());
    }
  }
}

@immutable
abstract class AssetSDUIBlocState {}

class AssetLoadingState extends AssetSDUIBlocState {}

class AssetEmptyState extends AssetSDUIBlocState {}

class AssetIdUpdatedState extends AssetSDUIBlocState {}

class AddNewAssetTappedState extends AssetSDUIBlocState {}

class PucUpdateRequestedState extends AssetSDUIBlocState {
  final bool pucPresent;

  PucUpdateRequestedState(this.pucPresent);
}

class UploadDocumentTapped extends AssetSDUIBlocState {
  final String type;

  UploadDocumentTapped(this.type);
}

class ShowDocumentAssetState extends AssetSDUIBlocState {
  final AssetDocument assetDocument;

  ShowDocumentAssetState(this.assetDocument);
}

class AssetPucUpdatedState extends AssetSDUIBlocState {
  final String text;
  final String image;
  final bool failed;

  AssetPucUpdatedState(this.text, this.image, this.failed);
}

class EmptyAssetState extends AssetSDUIBlocState {}

class LoadDefaultAssetState extends AssetSDUIBlocState {}

class ShowLoadingState extends AssetSDUIBlocState {}

class StopLoadingState extends AssetSDUIBlocState {}

class DocRefreshState extends AssetSDUIBlocState {}

class AsyncWidgetsUpdated extends AssetSDUIBlocState {}

class RateLimitExceeded extends AssetSDUIBlocState {
  final Map<String, dynamic>? rateLimitPayload;
  RateLimitExceeded(this.rateLimitPayload);
}

class AssetLoadedState extends AssetSDUIBlocState {
  AssetLoadedState();
}

class AssetErrorState extends AssetSDUIBlocState {
  final String errorMessage;

  AssetErrorState(this.errorMessage);
}
