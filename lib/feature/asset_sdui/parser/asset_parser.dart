import 'dart:async';

import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/util/screen_size_helper.dart';
import 'package:flutter/material.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/core/string_extensions.dart';

import 'asset_circular_widget_selector.dart';
import 'asset_content.dart';
import 'asset_document_section_parser.dart';
import 'asset_policy_view_parser.dart';
import 'asset_vas_status_cards.dart';
import 'async_asset_document_card.dart';
import 'custom_grid_tile.dart';

class AssetParser extends SDUIBaseParser {
  bool useV10Design = false;
  final VoidCallback onVasServiceTapped;
  final Function(String) onAssetTapped;
  final Function(String, Map<String, dynamic>?) onAssetDocumentTapped;
  final Function(List<String>) commonDataStoreIdsLoaded;
  final StreamController<String>? updateIdListenerCallback;
  final Function(String?) refreshVasTapped;

  AssetParser(
      this.onAssetTapped,
      this.onAssetDocumentTapped,
      this.onVasServiceTapped,
      this.commonDataStoreIdsLoaded,
      this.updateIdListenerCallback,
      this.refreshVasTapped,
      );

  @override
  fromJson(Map<String, dynamic> json,
      {SDUIBaseParser? parser, int? widgetPosition, String? key}) {
    List<Widget> coveragePageWidgets = [];
    if (json['widgets'] != null) {
      for (var element in (json['widgets'] as List)) {
        String type = element['\$type'];
        late Widget widget;
        if (type.equalsIgnoreCase('AssetDocumentSectionParser')) {
          widget = AssetDocumentSectionParser()
            ..onAssetDocumentTapped = onAssetDocumentTapped
            ..fromJson(element);
        } else if (type.equalsIgnoreCase("component.grid.GradientGridTile")) {
          widget = CustomAssetGridTile()
            ..onVasServiceTapped = onVasServiceTapped
            ..onRefreshVasTapped = refreshVasTapped
            ..fromJson(element);
        } else if (type.equalsIgnoreCase("AssetVasStatusCards")) {
          widget = AssetVasStatusCards()..fromJson(element);
        } else if (type.equalsIgnoreCase("AssetProposalPolicyView")) {
          widget = AssetProposalPolicyView()..fromJson(element);
        } else if (type.equalsIgnoreCase("AsyncAssetDocumentCard")) {
          widget = AsyncAssetDocumentCard()..fromJson(element);
        } else {
          widget = SDUIParser.getInstance().fromJson(element);
        }
        coveragePageWidgets.add(widget);
      }
    }
    return coveragePageWidgets;
  }

  @override
  List<Widget> fromEmptyAssetJson(Map<String, dynamic> json,
      {SDUIBaseParser? parser, int? widgetPosition, String? key}) {
    List<Widget> coveragePageWidgets = [];
    if (json['widgets'] != null) {
      for (var element in (json['widgets'] as List)) {
        String? type = element['\$type'];
        late Widget contentWidget;
        contentWidget = SDUIParser.getInstance().fromJson(element);
        if (contentWidget != SDUINull()) coveragePageWidgets.add(contentWidget);
      }
    }
    return coveragePageWidgets;
  }

  parseDetailsSection(Map<String, dynamic> data) {
    final List<Widget> lightWidgets = fromJson(data['lightSection']);

    final List<Widget> darkSection = fromJson(data['darkSection']);
    return useV10Design
        ? [
            Container(
              width: getScreenWidth(),
              decoration: BoxDecoration(
                  color: color070707,
                  borderRadius:
                      BorderRadius.vertical(bottom: Radius.circular(24))),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: darkSection,
              ),
            ),
            Container(
              width: getScreenWidth(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: lightWidgets,
              ),
            )
          ]
        :
            [
              ...darkSection,
              Container(
                color: Colors.white,
                width: getScreenWidth(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: lightWidgets,
                ),
              )
            ]
          ;
  }

  getSelectorWidget(Map<String, dynamic> json, String defaultId) {
    json['defaultId'] = defaultId;
    if (json['\$type'].toString().equalsIgnoreCase("CircularWidgetSelector")) {
      return Container(
        color: useV10Design ? color070707 : Colors.transparent,
        child: AssetCircularWidgetSelector()
          ..onAssetTapped = onAssetTapped
          ..commonDataStoreIdsLoaded = commonDataStoreIdsLoaded
          ..updateIdListenerCallback = updateIdListenerCallback
          ..disableCenterListOntap = useV10Design
          ..fromJson(json),
      );
    }
    return null;
  }

  getBasicInfoWidget(Map<String, dynamic> json) {
    if (json['\$type'].toString().equalsIgnoreCase("AssetContentWidgets")) {
      return AssetContentWidgets()..fromJson(json);
    }

    return null;
  }
}
