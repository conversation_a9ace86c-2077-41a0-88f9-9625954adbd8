import 'package:acko_flutter/feature/asset_sdui/bloc/asset_sdui_bloc.dart';
import 'package:design_module/utilities/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';

import '../../../common/widgets/shimmer.dart';

class AssetVasStatusCards extends SDUIStatefulWidget {
  EdgeInsets? padding;
  EdgeInsets? margin;
  double? width;
  double? height;
  List<String>? urlsList;

  @override
  fromJson(Map<String, dynamic>? json) {
    if (json == null || json["attributes"] == null) return;
    padding = getEdgeInsets(json["attributes"]["padding"]);
    margin = getEdgeInsets(json["attributes"]["margin"]);
    width = checkDouble(json["attributes"]["width"]);
    height = checkDouble(json["attributes"]["height"]);
    if (json["attributes"]['urlsList'] != null) {
      urlsList = [];
      json["attributes"]['urlsList'].forEach((url) {
        urlsList!.add(url);
      });
    }
  }

  @override
  State<StatefulWidget> createState() => _AssetVasStatusCardsState();
}

class _AssetVasStatusCardsState extends State<AssetVasStatusCards>
    with AutomaticKeepAliveClientMixin {
  late AssetSDUIBloc bloc;

  @override
  void initState() {
    bloc = BlocProvider.of<AssetSDUIBloc>(context);
    _initUrls();
    super.initState();
  }

  _initUrls() {
    if ((widget.urlsList ?? []).isEmpty) return;
    widget.urlsList!.forEach((url) {
      bloc.getAsyncWidget(url);
    });
  }

  @override
  Widget build(BuildContext context) {
    // if((widget.urlsList??[]).isEmpty)return SizedBox.shrink();
    return BlocBuilder<AssetSDUIBloc, AssetSDUIBlocState>(
      buildWhen: (prev, state) => state is AsyncWidgetsUpdated,
      builder: (context, state) {
        bool allWidgetsAreEmpty = true;
        widget.urlsList!.forEach((element) {
          if (!bloc.asyncWidgets.containsKey(element) ||
              (bloc.asyncWidgets[element] != null)) {
            allWidgetsAreEmpty = false;
          }
        });

        if (allWidgetsAreEmpty) {
          return SizedBox.shrink();
        }

        return FittedBox(
          fit: BoxFit.scaleDown,
          child: Container(
            width: checkDouble(widget.width, context: context),
            padding: widget.padding,
            margin: widget.margin,
            child: Column(
              children: _buildRows(widget.urlsList!),
            ),
          ),
        );
      },
    );
  }

  List<Widget> _buildRows(List<String> urls) {
    final List<Widget> rows = [];

    for (var i = 0; i < urls.length; i += 2) {
      final isLastRow = i + 2 >= urls.length;

      rows.add(
        Padding(
          padding: EdgeInsets.only(top: i == 0 ? 0 : 8),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              _getWidget(urls[i], true),
              if (i + 1 < urls.length)
                _getWidget(urls[i + 1], false),
            ],
          ),
        ),
      );
    }

    return rows;
  }

  Widget _getWidget(String url, bool addPadding) {
    return (!bloc.asyncWidgets.containsKey(url))
        ? _loadingState(addPadding ? 10 : 0, url)
        : ((bloc.asyncWidgets[url] == null)
            ? SizedBox.shrink()
            : Container(
                margin: EdgeInsets.only(right: addPadding ? 10 : 0),
                child: bloc.asyncWidgets[url]!,
              ));
  }

  _loadingState(double rightPadding, String url) {
    if (bloc.refreshAsyncWidgets.containsKey(url)) {
      return Padding(
        key: UniqueKey(),
        padding: EdgeInsets.only(right: rightPadding),
        child: bloc.refreshAsyncWidgets[url],
      );
    }

    return Padding(
      key: UniqueKey(),
      padding: EdgeInsets.only(top: 12.0, right: rightPadding),
      child: Shimmer.fromColors(
        child: FittedBox(
          child: Container(
            height: 135,
            width: MediaQuery.of(context).size.width * 0.48,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16), color: Colors.white),
          ),
        ),
        baseColor: color1B1B1B,
        highlightColor: color272727,
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
