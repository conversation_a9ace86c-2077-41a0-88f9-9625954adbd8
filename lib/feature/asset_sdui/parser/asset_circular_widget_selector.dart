import 'dart:async';

import 'package:flutter/material.dart';
import 'package:sdui/sdui.dart';

class AssetCircularWidgetSelector extends CircularWidgetSelector {
  Function(String)? onAssetTapped;
  Function(List<String>)? commonDataStoreIdsLoaded;
  StreamController<String>? updateIdListenerCallback;
  bool disableCenterListOntap = false;

  @override
  onWidgetTapped(BuildContext context, {properties}) {
    if (onAssetTapped != null) {
      onAssetTapped!(properties!["id"]);
    }
    return super.onWidgetTapped(context, properties: properties);
  }

  @override
  fromJson(Map<String, dynamic>? json) {
    super.fromJson(json);
    if (updateIdListenerCallback != null) {
      updateIdListener = updateIdListenerCallback;
    }
    List<String> idsList = List.empty(growable: true);

    if (circularWidgets != null && circularWidgets!.length > 1) {
      String selectedId = defaultId ?? circularWidgets!.first.id;
      final element = circularWidgets!.where((e) => e.id == selectedId).firstOrNull;
      if(element != null){
        circularWidgets!.removeWhere((e) => e.id == selectedId);
        if (disableCenterListOntap) {
          circularWidgets!.insert(0, element);
        } else {
          int index = circularWidgets!.length ~/ 2;
          circularWidgets!.insert(index, element);
        }
      }
    }

    circularWidgets?.forEach((element) {
      idsList.add(element.id);
    });
    if (commonDataStoreIdsLoaded != null) {
      commonDataStoreIdsLoaded!(idsList);
    }
  }
}
