import 'package:acko_flutter/common/util/scroll_fold_mixin.dart';
import 'package:acko_flutter/common/view/FullPageLoader.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/asset_view/asset_view_shimmer/asset_view_shimmers.dart';
import 'package:acko_flutter/travel_insurance/shared/util/enum.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:analytics/events/tap_events.dart';
import 'package:design_module/utilities/color_constants.dart';
import 'package:design_module/utilities/hide_scroll_glow_effect.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/state_provider/StateProvider.dart';

import '../../../common/util/color_constants.dart' as colorConstants;
import '../../../common/util/custom_file_picker.dart';
import '../../../common/view/ErrorWidget.dart';
import '../../../common/widgets/custom_indexed_stack.dart';
import '../../../common/widgets/shimmer.dart';
import '../../../r2d2/events.dart';
import '../../acko_services/ui/acko_service_loading_screen.dart';
import '../../auto_assets/model/assets_documents_model.dart';
import '../../auto_assets/model/detail_vehicle_asset_model.dart';
import '../../auto_assets/view/common/get_time_expiry_bottom_sheet.dart';
import '../../home_view_util.dart';
import '../bloc/asset_sdui_bloc.dart';

class AssetSduiView extends StatefulWidget {
  const AssetSduiView({super.key});
  @override
  State<AssetSduiView> createState() => _AssetSduiViewState();
}

class _AssetSduiViewState extends State<AssetSduiView>
    with
        AutomaticKeepAliveClientMixin,
        ErrorViewRetryCallBack,
        StateListener,
        ScrollFoldMixin,
        TickerProviderStateMixin {
  late AssetSDUIBloc _bloc;
  late CommonDataStoreBloc commonDataStoreBloc;
  final FullPageLoader _fullPageLoader = FullPageLoader.instance;
  TabController? _tabController;
  int currentIndex = 0;
  StateProvider _stateProvider = StateProvider();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _bloc = BlocProvider.of(context);
    _updateV10DesignFlag();
    _stateProvider.subscribe(this);
    commonDataStoreBloc = BlocProvider.of(context);
    initCommonDataStoreUIData();
    _bloc.setAssetsCount(commonDataStoreBloc.getPositionedDataFromMap("auto.assets.data"));
    WidgetsBinding.instance.addPostFrameCallback((_) {
      addScrollFoldListener(_scrollController, 'vehicle');
    });
  }

  _updateV10DesignFlag(){
    _bloc.setV10DesignFlag().then((_){
      setState(() {});
    });
  }

  @override
  void dispose() {
    _bloc.updateIdListenerCallback.close();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      backgroundColor: _bloc.useV10Design ? Colors.white : colorConstants.color101010,
      body: BlocConsumer<AssetSDUIBloc, AssetSDUIBlocState>(
        listener: (context, state) async {
          if (currentIndex != _bloc.assetId && _tabController != null) {
            _switchTab(_bloc.assetId!);
          }
          if (state is ShowDocumentAssetState) {
            await _showAssetDocument(state, context);
          } else if (state is UploadDocumentTapped) {
            uploadDocumentTapped(state.type);
          } else if (state is ShowLoadingState) {
            _fullPageLoader.showFullPageLoader(context);
          } else if (state is StopLoadingState) {
            _fullPageLoader.dismissFullPageLoader(context);
          } else if (state is PucUpdateRequestedState) {
            _onPucRefreshTapped(state.pucPresent);
          } else if (state is AssetPucUpdatedState) {
            ScaffoldMessenger.of(context).removeCurrentSnackBar();
            HomeViewUtil.sharedInstance.showSnackBar(state.text, context,
                image: state.image, secondDuration: 1);
          } else if (state is RateLimitExceeded) {
            NavigationAction.fromJson(state.rateLimitPayload)..executeAction(context);
          } else if (state is EmptyAssetState){
            _sendEmptyAssetViewEvent();
          }
        },
        builder: (context, state) {
          if (state is LoadDefaultAssetState) {
            return _defaultAssetState();
          } else if (state is EmptyAssetState) {
            return _emptyAssetView();
          }
          return RefreshIndicator(
            onRefresh: () => _bloc.refresh(),
            child: ScrollConfiguration(
              behavior: HideScrollGlowEffect(),
              child: ListView(
                controller: _scrollController,
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                children: [
                  Container(
                    width: MediaQuery.sizeOf(context).width,
                    color: _bloc.useV10Design ? colorConstants.color070707 : Colors.transparent,
                    child: Column(children: [
                      if(!_bloc.useV10Design)
                      _headerSection(state),
                      _basicInfoSection(),
                      if(_bloc.useV10Design)
                      _selectorSection(),
                    ],),
                  ),
                  _detailsSection(state),
                  if(_bloc.useV10Design)
                    SizedBox(height: 72,) /// To avoid bottom navigation bar overlap
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  _sendEmptyAssetViewEvent(){
    AnalyticsTrackerManager.instance.sendEvent(
        event: PageLoadedConstants.APP_VIEW_PAGE,
        properties: {
          "status": "zero state",
          'page_name': 'you - vehicle'
        });
  }

  _emptyAssetView() {
    return BlocConsumer<CommonDataStoreBloc, CommonDataStoreBlocStates>(
        listener: (context, state) {
          final data =
              commonDataStoreBloc.getPositionedDataFromMap("auto.assets.data");

          if (data == null) {
            if (commonDataStoreBloc.getPositionedDataFromMap("auto") != null) {
              _bloc.showEmptyAssetView();
            }
            return;
          }

          _bloc.assetDataReceived(data);
          initCommonDataStoreUIData();
        },
        listenWhen: (prev, state) => state is LobDataUpdated,
        buildWhen: (prev, state) => state is LobDataUpdated,
        builder: (context, state) {
          return SingleChildScrollView(
            physics: ClampingScrollPhysics(),
            child: Column(children: [
              _bloc.emptyAssetViewWidgets.first
            ],),
          );
        });
  }

  _defaultAssetState() {
    return BlocConsumer<CommonDataStoreBloc, CommonDataStoreBlocStates>(
        listener: (context, state) {
          final data =
              commonDataStoreBloc.getPositionedDataFromMap("auto.assets.data");

          if (data == null) {
            if (commonDataStoreBloc.getPositionedDataFromMap("auto") != null) {
              _bloc.showEmptyAssetView();
            }
            return;
          }

          _bloc.assetDataReceived(data);
          initCommonDataStoreUIData();
        },
        listenWhen: (prev, state) => state is LobDataUpdated,
        buildWhen: (prev, state) => state is LobDataUpdated,
        builder: (context, state) {
          return _loadingView();
        });
  }

  _loadingView() {
    return ScrollConfiguration(
      behavior: HideScrollGlowEffect(),
      child: ListView(
        padding: EdgeInsets.zero,
        shrinkWrap: true,
        children: [
          Container(
            width: MediaQuery.sizeOf(context).width,
            color: _bloc.useV10Design ? colorConstants.color070707 : Colors.transparent,
            child: Column(children: [
              _basicInfoShimmerView(),
              if(_bloc.useV10Design)
                _selectorShimmerView(),
            ],),
          ),
          _detailsSectionShimmerView(),
        ],
      ),
    );
  }

  _switchTab(String assetId) {
    if (_tabController == null) return;
    int index = _bloc.data.keys.toList().indexOf(assetId);
    if (index != currentIndex) {
      setState(() {
        currentIndex = index;
        _tabController!.index = index;
      });
    }
  }

  Future<void> _showAssetDocument(
      ShowDocumentAssetState state, BuildContext context) async {
    final res = await Navigator.pushNamed(
      context,
      Routes.ASSET_DOCUMENT_VIEWER,
      arguments: {
        "asset_doc": state.assetDocument,
        "vehicle_model": DetailVehicleAssetModel(
            vehicle: Vehicle(assetNumber: _bloc.registrationNumber)),
      },
    ).then((value) {
      if (value == "remove") {
        _removeDoc(state.assetDocument);
      }
    });
  }

  _removeDoc(AssetDocument doc) async {
    _fullPageLoader.showFullPageLoader(context);
    await _bloc.removeDocument(doc, context);
    _fullPageLoader.dismissFullPageLoader(context);
  }

  _onPucRefreshTapped(bool pucPresent) async {
    _fullPageLoader.showFullPageLoader(context);
    await _bloc.fetchPuc(pucPresent);
    _fullPageLoader.dismissFullPageLoader(context);
  }

  uploadDocumentTapped(String type) async {
    final fileInfo = await DocumentUploadFilePicker.instance.pickFile(context);
    if (fileInfo == null) return;
    AnalyticsTrackerManager.instance
        .sendEvent(event: TapConstants.TAP_BTN_DOC_UPLOAD_SOURCE, properties: {
      'registration_number': _bloc.registrationNumber,
    });
    R2D2Events.instance.trackAssetManagementButtonTappedEvents(
      'tap_btn_doc_upload_source',
      'asset_detailed_view',
      regNumber: _bloc.registrationNumber,
    );
    String? expiryTimeStamp;
    if (type.equalsIgnoreCase('insurance_document')) {
      expiryTimeStamp = await getExpiryTimestampBottomsheet();
    }
    _bloc.uploadDocument(type, fileInfo, expiryTimeStamp);
  }

  Future<String?> getExpiryTimestampBottomsheet() async {
    var response = await showModalBottomSheet(
      barrierColor: color040222.withOpacity(0.7),
      context: context,
      backgroundColor: Colors.white,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(15.0)),
      ),
      builder: (context) {
        return TimeExpiryBottomSheet(
          ctx: context,
          isPuc: false,
        );
      },
    );

    return (response != null && response is int) ? response.toString() : '';
  }

  void initCommonDataStoreUIData() {
    if (_bloc.assetId == null) {
      final data =
          commonDataStoreBloc.getPositionedDataFromMap("auto.assets.data");
      if (data != null) {
        _bloc.assetDataReceived(data);
        initCommonDataStoreUIData();
      } else {
        if (commonDataStoreBloc.getPositionedDataFromMap("auto") != null) {
          _bloc.showEmptyAssetView();
        }
      }
      return;
    }
    final selectorData =
        commonDataStoreBloc.getPositionedDataFromMap("auto.assets.selector");
    final basicInfoData = commonDataStoreBloc
        .getPositionedDataFromMap("auto.assets.basic_info_v2");

    if (selectorData != null) {
      _bloc.selectorDataReceived(selectorData);
    }

    if (basicInfoData != null) {
      _bloc.basicInfoDataReceived(basicInfoData);
    }
  }

  _headerSection(AssetSDUIBlocState state) {
    return Container(
      color: Color(0xff101010),
      margin: const EdgeInsets.only(top: 48, bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          IconButton(
            icon: Icon(
              Icons.arrow_back_ios_new_rounded,
              color: Colors.white,
              size: 20,
            ),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
          if(!_bloc.useV10Design) ... [
            _selectorSection(),
            Container(
              margin: const EdgeInsets.only(right: 20),
              child: _addButton(),
            ),
          ]

        ],
      ),
    );
  }

  _addButton() => GestureDetector(
    onTap: () {
      AnalyticsTrackerManager.instance.sendEvent(
          event: TapConstants.TAP_BTN_ADD_NEW_ASSET,
          properties: {
            "from_page": "asset",
            "journey": "rto_info",
            "platform": "app",
            "product": "auto"
          });
      R2D2Events.instance.trackAssetManagementButtonTappedEvents(
          'tap_btn_add_new_asset', 'asset_page');
      Navigator.pushNamed(
        context,
        Routes.CREATE_VEHICLE_ASSET,
      );
    },
    child: SDUIImage(
      imageUrl: 'assets/images/add_member.svg',
      width: 40,
      height: 40,
    ),
  );

  _selectorSection() {
    return BlocConsumer<CommonDataStoreBloc, CommonDataStoreBlocStates>(
        listener: (context, state) {
          final data = commonDataStoreBloc
              .getPositionedDataFromMap("auto.assets.selector");
          if (data != null) {
            _bloc.selectorDataReceived(data);
          }
        },
        listenWhen: (prev, state) => state is LobDataUpdated,
        buildWhen: (prev, state) => state is LobDataUpdated,
        builder: (context, state) {
          final selectorData = commonDataStoreBloc
              .getPositionedDataFromMap("auto.assets.selector");

          if (selectorData == null) {
            if (commonDataStoreBloc.getPositionedDataFromMap("auto") != null) {
              return SizedBox.shrink();
            } else {
              return _selectorShimmerView();
            }
          }
          return BlocBuilder<AssetSDUIBloc, AssetSDUIBlocState>(
              builder: (context, state) {
            if (_bloc.commonDataStoreAssetModel.selectorSection == null) {
              return SizedBox.shrink();
            }
            return Container(
              color: _bloc.useV10Design ? colorConstants.color070707 : Colors.transparent,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  _bloc.commonDataStoreAssetModel.selectorSection!,
                if(_bloc.useV10Design)
                  Padding(
                    padding: const EdgeInsets.only(left: 8),
                    child: _addButton(),
                  ),
              ],),
            );
          });
        });
  }

  _basicInfoSection() {
    return BlocConsumer<CommonDataStoreBloc, CommonDataStoreBlocStates>(
        listener: (context, state) {
          final data = commonDataStoreBloc
              .getPositionedDataFromMap("auto.assets.basic_info_v2");
          if (data != null) {
            _bloc.basicInfoDataReceived(data);
          }
        },
        listenWhen: (prev, state) => state is LobDataUpdated,
        buildWhen: (prev, state) => state is LobDataUpdated,
        builder: (context, state) {
          final basicInfoData = commonDataStoreBloc
              .getPositionedDataFromMap("auto.assets.basic_info_v2");

          if (basicInfoData == null) {
            if (commonDataStoreBloc.getPositionedDataFromMap("auto") != null) {
              return SizedBox.shrink();
            } else {
              return _basicInfoShimmerView();
            }
          }

          return BlocConsumer<AssetSDUIBloc, AssetSDUIBlocState>(
              listener: (prev, state) {
                if (state is AssetLoadedState) {
                  setState(() {});
                }
              },
              listenWhen: (prev, state) => (state is AssetLoadedState),
              buildWhen: (prev, state) => (state is AssetLoadedState),
              builder: (context, state) {
                if (_bloc.commonDataStoreAssetModel.basicInfoSection == null) {
                  return SizedBox.shrink();
                }
                return GestureDetector(
                    onHorizontalDragEnd: _handleSwipeGesture,
                    child: _bloc.commonDataStoreAssetModel.basicInfoSection!);
              });
        });
  }

  _handleSwipeGesture(dragDetail) {
    if (dragDetail.primaryVelocity! < -10) {
      _pageSwipped(leftSwipped: true);
    } else if (dragDetail.primaryVelocity! > 10) {
      _pageSwipped(leftSwipped: false);
    }
  }

  _basicInfoShimmerView() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Shimmer.fromColors(
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.45,
                  height: 28,
                  decoration: BoxDecoration(
                    color: colorFFFFFF,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                baseColor: color1B1B1B,
                highlightColor: color272727,
              ),
              const SizedBox(
                height: 12,
              ),
              Shimmer.fromColors(
                child: Container(
                  width: MediaQuery.of(context).size.width * 0.3,
                  height: 20,
                  decoration: BoxDecoration(
                    color: colorFFFFFF,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                baseColor: color1B1B1B,
                highlightColor: color272727,
              ),
            ],
          ),
          Shimmer.fromColors(
            child: Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: colorFFFFFF,
              ),
            ),
            baseColor: color1B1B1B,
            highlightColor: color272727,
          ),
        ],
      ),
    );
  }

  _selectorShimmerView() {
    return Shimmer.fromColors(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: colorFFFFFF,
            ),
          ),
          SizedBox(
            width: 8,
          ),
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: colorFFFFFF,
            ),
          ),
          SizedBox(
            width: 8,
          ),
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: colorFFFFFF,
            ),
          )
        ],
      ),
      baseColor: color1B1B1B,
      highlightColor: color272727,
    );
  }

  Widget _detailsSectionShimmerView() {
    return Container(
      color: _bloc.useV10Design ? colorConstants.color070707 : Colors.transparent,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Padding(
            padding:
                const EdgeInsets.only(top: 12.0, left: 16, right: 16, bottom: 8),
            child: Shimmer.fromColors(
              child: Container(
                width: MediaQuery.of(context).size.width,
                height: 90,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16), color: Colors.white),
              ),
              baseColor: color1B1B1B,
              highlightColor: color272727,
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Row(
                children: [
                  Padding(
                    padding: EdgeInsets.only(top: 12, right: 8),
                    child: Shimmer.fromColors(
                      child: FittedBox(
                        child: Container(
                          height: 100,
                          width: MediaQuery.of(context).size.width * 0.48,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              color: Colors.white),
                        ),
                      ),
                      baseColor: color1B1B1B,
                      highlightColor: color272727,
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                      top: 12.0,
                    ),
                    child: Shimmer.fromColors(
                      child: FittedBox(
                        child: Container(
                          height: 100,
                          width: MediaQuery.of(context).size.width * 0.48,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              color: Colors.white),
                        ),
                      ),
                      baseColor: color1B1B1B,
                      highlightColor: color272727,
                    ),
                  )
                ],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 12.0, left: 16, right: 16),
            child: Shimmer.fromColors(
              child: Container(
                width: MediaQuery.of(context).size.width,
                height: 200,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16), color: Colors.white),
              ),
              baseColor: color1B1B1B,
              highlightColor: color272727,
            ),
          ),
          Container(
            width: MediaQuery.of(context).size.width,
            color: colorFFFFFF,
            child: Column(
              children: [
                PolicyStatusShimmer(),
                HealthEducationalShimmer(),
                MoreActionsShimmer(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  _detailsSection(AssetSDUIBlocState state) {
    if (_bloc.data[_bloc.assetId] == null ||
        _bloc.data[_bloc.assetId]!.detailsSection == null) {
      return _detailsSectionShimmerView();
    }
    _initTabBarController();

    ///Indexed stack takes size of the largest page and smaller pages ends with a lot of empty space
    ///CustomIndexedStack fixes that issue
    return CustomIndexedStack(
      index: _tabController!.index,
      children: _bloc.data.keys.map((e) {
        return BlocBuilder<AssetSDUIBloc, AssetSDUIBlocState>(
          builder: (prev, state) {
            if (_bloc.data[e] == null ||
                _bloc.data[e]!.detailsSection == null) {
              return _detailsSectionShimmerView();
            }
            return GestureDetector(
              onHorizontalDragEnd: _handleSwipeGesture,
              child: Container(
                color: Colors.transparent,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: _bloc.data[e]!.detailsSection!,
                ),
              ),
            );
          },
        );
      }).toList(),
    );
  }

  _pageSwipped({bool leftSwipped = false}) {
    ///left swipe -> increment right swipe - decrement
    if (leftSwipped && _bloc.data.keys.toList().length > 1) {
      if (currentIndex < (_bloc.data.keys.toList().length - 1)) {
        final assetId = _bloc.data.keys.toList()[currentIndex + 1];
        _bloc.onAssetTapped(assetId, swipped: true);
      }
    } else {
      if (currentIndex > 0) {
        final assetId = _bloc.data.keys.toList()[currentIndex - 1];
        _bloc.onAssetTapped(assetId, swipped: true);
      }
    }
  }

  _initTabBarController() {
    if (_tabController != null) {
      if (_tabController!.length == _bloc.data.length) {
        return;
      }
    }

    _tabController = TabController(vsync: this, length: _bloc.data.length);
  }

  @override
  bool get wantKeepAlive => true;

  @override
  onRetry() {
    _bloc.refresh();
  }

  @override
  void onStateChanged(ObserverState state, {data}) {
    if (state == ObserverState.FASTAG_RECHARGE_UPDATE) {
      _bloc.refreshVasTapped(VasServices.fastag.name);
    }else if (state == ObserverState.NEW_ASSET_ADDED && data["assetId"] != null){
      _refreshAssetData();
      Future.delayed(Duration(seconds: 1), (){
        setState(() {
          _bloc.onAssetTapped(data["assetId"].toString());
        });
      });
    }else if(state == ObserverState.REFRESH_ASSETS){
      _refreshAssetData();
    }
  }

  void _refreshAssetData() {
    _tabController = null;
    _bloc.refreshPage();
    initCommonDataStoreUIData();
    _bloc.setAssetsCount(commonDataStoreBloc.getPositionedDataFromMap("auto.assets.data"));
  }
}
