import 'dart:convert';

import 'package:acko_logger/acko_logger.dart';
import 'package:acko_logger/events/error/api_error.dart';
import 'package:analytics/analytics.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:dio/dio.dart';
import 'package:networking_module/util/curl_representation.dart';

class LogNetworkErrorInterceptor extends Interceptor {
  @override
  void onError(DioException error, ErrorInterceptorHandler handler) {
    final List<String> extractedPath = error.requestOptions.path.split('?');

    Map<String, dynamic> eventObj = {
      "path": extractedPath.firstOrNull,
      "error_message": error.message?.toString(),
      'error_response': error.response?.data?.toString(),
      "request": jsonEncode(error.requestOptions.data),
      "type": error.requestOptions.method,
      "failure_reason": error.response?.statusMessage?.toString(),
      "failure_status": error.response?.statusCode?.toString(),
      "exception_name": error.type.toString(),
      "stack_trace": error.stackTrace.toString(),
      "curl": cURLRepresentation(error.requestOptions),
      'headers': jsonEncode(error.requestOptions.headers),
      'query_params': (extractedPath.length > 1) ? extractedPath[1] : null,
    };

    AnalyticsTrackerManager.instance.sendEvent(
      event: PageLoadedConstants.NETWORK_API_ERROR,
      properties: eventObj,
    );

    AckoLoggerManager.instance.logError(event: ApiErrorEvent(
        path: extractedPath.firstOrNull,
        errorMessage: error.message?.toString() ?? "",
        errorResponse: error.response?.data?.toString(),
    request: jsonEncode(error.requestOptions.data),
    type: error.requestOptions.method,
    failureReason: error.response?.statusMessage?.toString(),
    failureStatus: error.response?.statusCode?.toString(),
    exceptionName: error.type.toString(),
    curl: cURLRepresentation(error.requestOptions),
    headers: error.requestOptions.headers,
    queryParams: (extractedPath.length > 1) ? extractedPath[1] : null,
    ));

    super.onError(error, handler);
  }
}
