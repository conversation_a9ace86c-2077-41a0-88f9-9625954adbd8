import 'dart:convert';

import 'package:rxdart/rxdart.dart';
import '../common/util/strings.dart';
import '../feature/auto_assets/model/auto_assets_list_model.dart';
import '../feature/auto_assets/model/detail_vehicle_asset_model.dart';
import '../network/ApiResponse.dart';
import '../network/ApiService.dart';
import 'package:utilities/constants/constants.dart';
import '../util/Utility.dart';
import 'package:dio/dio.dart';
import 'package:acko_flutter/util/extensions.dart';
import '../../../common/model/BaseModel.dart';

class ApiRepo {
  bool _isApiCallInProgress = false;
  final _assetsStream = BehaviorSubject<List<DetailVehicleAssetModel>?>();

  get dataUpdateSubscription => _assetsStream.asBroadcastStream().listen;

  updateData(List<DetailVehicleAssetModel>? data) {
    _assetsStream.add(data);
  }

  clearData() => _assetsStream.add(null);

  Future<AutoAssetListModel> getAssetData(bool force) async {
    if (_assetsStream.valueOrNull != null && !force) {
      return AutoAssetListModel(vehiclesList: _assetsStream.value);
    }
    final _response = await _getAssetsListFromApi();
    _assetsStream.add(_response.vehiclesList ?? []);
    return _response;
  }

  Future<AutoAssetListModel> _getAssetsListFromApi() async {
    if (_isApiCallInProgress) {
      ///To prevent race condition
      while (_isApiCallInProgress) {
        await Future.delayed(Duration(milliseconds: 100));
      }

      _isApiCallInProgress = false;
      if (_assetsStream.valueOrNull != null) {
        return AutoAssetListModel(vehiclesList: _assetsStream.value);
      }
    }

    _isApiCallInProgress = true;

    ApiResponse response = await ApiService.apiServiceInstance.getApiRequest(
      ApiPath.VEHICLE_ASSETS_DEFAULT_URL + "?strict_asset_number=false",
    );

    AutoAssetListModel? model;
    if (response.statusCode == 200) {
      model = AutoAssetListModel.fromJson(response.data);
    } else {
      ErrorHandler _err = ErrorHandler(response.error);
      try {
        _err.message = response.data['error_code'] ?? '';
      } catch (e) {}
      model = AutoAssetListModel.error(_err);
    }

    _isApiCallInProgress = false;
    return model;
  }

  Future<void> generateLead(
      {String? phone, DetailVehicleAssetModel? model}) async {
    String? _leadIntentDate = Util.getLeadGenerationDate(
        model?.vehicle?.previousPolicyExpiryDate ?? "");

    Map<String, dynamic> _leadGenerationData = {
      "phone": phone,
      "product": (model?.vehicle?.isCar ?? true) ? 'car' : 'bike',
      "registration_number": (model?.vehicle?.assetNumber),
      "source": "APP_ASSET",
      "make": model?.vehicle?.makeName ?? "",
      "model": (model?.vehicle?.makeModel ?? "")
          .replaceAll(model?.vehicle?.makeName ?? "", "")
          .trim(),
      "lead_intent_date": _leadIntentDate,
    };

    String apiKey = Constants.lead_generation_api_keys["APP_ASSET"] ?? "";

    await ApiService.apiServiceInstance.postApiRequest(
        baseUrl: Constants.ACKO_LEAD_CREATION_URL,
        ApiPath.LEAD_GENERATION_URL,
        request: jsonEncode(_leadGenerationData),
        headers: <String, String>{
          'Content-Type': 'application/json',
          'apiKey': apiKey
        });
  }

  Future<DetailVehicleAssetModel> createAsset(String regNumber) async {
    ApiResponse response = await ApiService.apiServiceInstance.postApiRequest(
        ApiPath.VEHICLE_ASSETS_CREATE_URL,
        headers: {"content-type": "application/json"},
        request: jsonEncode({"asset_number": regNumber}));

    DetailVehicleAssetModel? model;
    if (response.statusCode == 200) {
      model = DetailVehicleAssetModel.fromJson(response.data);
    } else {
      ErrorHandler _err = ErrorHandler(response.error);
      try {
        _err.message = (response.error?.error as DioException)
            .response
            ?.data["error_code"];
        _err.subTitle = (response.error?.error as DioException)
            .response
            ?.data["error_message"];
        if (_err.subTitle.isNullOrEmpty) _err.subTitle = somethingWentWrong;
      } catch (e) {}
      model = DetailVehicleAssetModel.error(_err);
    }

    return model;
  }

  Future<BaseModel> removeAsset(int assetId) async {
    ApiResponse response = await ApiService.apiServiceInstance.deleteApiRequest(
      ApiPath.VEHICLE_ASSETS_DEFAULT_URL + '/$assetId',
      headers: {"content-type": "application/json"},
    );
    BaseModel baseModel = BaseModel();
    if (response.error != null) {
      try {
        ErrorHandler _err = ErrorHandler(response.error);
        _err.message = (response.error?.error as DioException)
            .response
            ?.data["error_code"];
        _err.subTitle = (response.error?.error as DioException)
            .response
            ?.data["error_message"];

        if (_err.subTitle.isNullOrEmpty) _err.subTitle = somethingWentWrong;
        baseModel.error = _err;
      } catch (e) {}
    }
    return baseModel;
  }
}
