import 'package:acko_flutter/common/model/UserPropertiesModel.dart';
import 'package:acko_flutter/r2d2/events.dart';
import 'package:acko_flutter/util/analytic_util.dart';
import 'package:analytics/trackers/segment/segment_tracker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_segment/flutter_segment.dart';

///Use AnalyticsTrackerManager
@deprecated
class TrackerManager {
  // Making this Manager a Singleton class
  static TrackerManager? _instance;

  TrackerManager._();

  static TrackerManager get instance {
    return _instance ??= TrackerManager._();
  }

  // Future<bool> initialize(AppConfig data) async {
  //   try {
  //     await Segment.config(
  //         options: SegmentConfig(
  //             writeKey: Platform.isAndroid
  //                 ? data.androidSegmentWriteKey!
  //                 : data.iosSegmentWriteKey!,
  //             trackApplicationLifecycleEvents: true,
  //             amplitudeIntegrationEnabled: true,
  //             debug: kDebugMode));
  //     await Amplitude.getInstance().init(data.amplitudeApiKey!);
  //     _isAnalyticsPlatformsInitialized = true;
  //     return Future.value(true);
  //   } catch (ex, stack) {
  //     FirebaseCrashlytics.instance.recordError(ex, stack);
  //     return Future.value(false);
  //   }
  // }

  // identifyUser(String userId) async {
  //   if (_isAnalyticsPlatformsInitialized) {
  //     await Segment.identify(userId: userId);
  //     await Amplitude.getInstance().setUserId(userId);
  //   }
  // }
  void instrumentUserAction(String? event, Map<String, dynamic> properties,
      {String feature = "health",
      AnalyticsPlatform specificPlatform = AnalyticsPlatform.SEGMENT_EVENT}) {
    // filter null values
    properties.removeWhere((key, value) => value == null);
    if (SegmentTracker.isAnalyticsPlatformsInitialized) {
      debugPrint("analytics-instrumentation- $event $properties");
      switch (specificPlatform) {
        case AnalyticsPlatform.SEGMENT_SCREEN_TRACK:
        case AnalyticsPlatform.SEGMENT_EVENT:
          _triggerSegmentEventOrScreen(event, properties,
              (specificPlatform == AnalyticsPlatform.SEGMENT_SCREEN_TRACK));
          break;
        case AnalyticsPlatform.FIREBASE:
          _triggerFirebaseEvent(event, properties);
          break;
        case AnalyticsPlatform.R2D2:
          R2D2Events.instance.sendHealthEvents(event, properties, feature);
          break;
        case AnalyticsPlatform.TRACK_USER_PROPERTIES:
          AnalyticsUtil.instance.setUserProperties(
              UserPropertiesModel(phone: properties['phone']));
          break;
        case AnalyticsPlatform.AMPLITUDE_EVENT:
          if (event != null) _triggerAmplitudeEvent(event, properties, feature);
          break;
      }
    }
  }

  void _triggerAmplitudeEvent(
      String event, Map<String, dynamic> properties, String feature) {
    R2D2Events.instance.sendHealthEvents(event, properties, feature);
   // Amplitude.getInstance().logEvent(event, eventProperties: properties);
  }

  void _triggerSegmentEventOrScreen(
      String? title, Map<String, dynamic> properties, bool isScreenTrack) {
    if (title != null) {
      if (isScreenTrack) {
        Segment.screen(screenName: title, properties: properties);
      } else {
        Segment.track(eventName: title, properties: properties);
      }
    }
  }

  // void _pushUserPropertyOnSegment(Map<String, dynamic> propValues) {
  //   Segment.identify(traits: propValues);
  // }
  //
  // void _pushUserPropertyOnAmplitude(Map<String, dynamic> propValues) {
  //   Amplitude.getInstance().setUserProperties(propValues);
  // }

  void _triggerFirebaseEvent(String? event, Map<String, dynamic> properties) {
    /* var eventDetails = <String, dynamic>{
    event: properties
  };
  Constants.PLATFORM_CHANNEL.invokeMethod("firebase_events",<String, dynamic>{
    "event":eventDetails});*/
  }

// Future<void> resetAnalytics() async {
//   await Segment.reset();
// }
//
// Future<void> setAnonymousIds(String anonymousId) async {
//   if (SegmentTracker.isAnalyticsPlatformsInitialized) {
//     debugPrint("analytics-anonymousID $anonymousId");
//     await Segment.identify(traits: {"anonymousId": anonymousId});
//   }
// }
}

enum AnalyticsPlatform {
  FIREBASE,
  R2D2,
  SEGMENT_EVENT,
  SEGMENT_SCREEN_TRACK,
  TRACK_USER_PROPERTIES,
  AMPLITUDE_EVENT
}

//name, page, path, platform, product, proposal_id, referrer, search, title, type, url

void triggerAmplitudeEvent(String? event, Map<String, dynamic> properties,
    {String feature = "health"}) {
  // filter null values
  /*properties.removeWhere((key, value) => value == null);
  if (event != null) {
    TrackerManager.instance.instrumentUserAction(event, properties,
        specificPlatform: AnalyticsPlatform.AMPLITUDE_EVENT, feature: feature);
  }*/
}
