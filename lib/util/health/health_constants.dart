import 'package:acko_flutter/common/util/strings.dart';
import 'package:flutter/material.dart';
import 'package:utilities/constants/constants.dart';

class HealthRemoteConfigConstants {
  static const String VAS = "value_added_services",
      HEALTH_STATIC_STRINGS = "health_static_strings",
      HEALTH_CLAIM_DOCUMENTS = "health_claim_documents_v2",
      APP_DEPENDENCIES = "app_dependencies_v2",
      CE_SETTLEMENT_DETAILS = "ce_settlement_content",
      CALAMITY_CONFIG = "calamity_config",
      FEATURE_FLAG_FITNESS_RETAIL = "feature_flag_fitness_retail",
      LIFE_RAISE_CLAIMS_DOCS = "life_raise_claims_docs",
      LIFE_RAISE_CLAIMS_DOCS_V2 = "life_raise_claims_docs_v2";
}

enum ValueAddedServices { mfine, oneMG, healthians }

/*extension VASPartnerDetails on ValueAddedServices {
  String partnerLogo() {
    switch (this) {
      case ValueAddedServices.mfine:
        return "img_health_mfine.webp";
      case ValueAddedServices.oneMG:
      case ValueAddedServices.healthians:
        return "img_health_1mg.webp";
    }
  }
}*/

enum VAS {
  doctorOnCall("doctor-on-call"),
  lab("book-lab-tests"),
  medicine("buy-medicines");

  final String id;
  const VAS(this.id);
}

enum VASVendorId {
  mfine("m-fine"),
  oneMG("one-mg");

  final String id;
  const VASVendorId(this.id);
}

extension VASDetails on VAS {
  String getVASImage() {
    switch (this) {
      case VAS.doctorOnCall:
        return "img_health_doc_on_call.svg";
      case VAS.lab:
        return "img_health_lab.svg";
      case VAS.medicine:
        return "img_health_medicine.svg";
    }
  }

  String getVASCardImage() {
    switch (this) {
      case VAS.doctorOnCall:
        return "ic_health_policy_benefit_doc.svg";
      case VAS.lab:
        return "ic_health_policy_benefit_lab.svg";
      case VAS.medicine:
        return "ic_health_policy_benefit_medicine.svg";
    }
  }
}

class HealthPlatformChannel {
  static const HEALTH_CTA_CLICKED = "health_cta_clicked";
}

class HealthConstants {
  static String? ONE_MG_LABS_REDIRECT_URL;
  static String? ONE_MG_PHARMACY_REDIRECT_URL;
  static String? ONEMG_API_BASE_URL;
  static String? ONEMG_LABS_URL;
  static String? ONEMG_MEDICINE_URL;
  static String? ONEMG_LABS_KEY;
  static String? ONEMG_MEDICINE_KEY;
  static Map<String, dynamic>? ONE_MG_HEADERS;
  static const String BASE = "base";
  static const String RETAIL_PRODUCT = "retail";
  static const String HEALTH_RETAIL = "health_retail";
  static const String ENTERPRISE_PRODUCT = "enterprise";

  /*GMC("enterprise"),
  RETAIL("retail"),
  AROGYA_SANJEEVNI("arogya_sanjeevni"),
  AROGYA_SANJEEVNI_V2("arogya_sanjeevni_v2");*/
  static const String AS_PRODUCT_ID =
      "Arogya Sanjeevani Policy Acko General Insurance Limited";
  static const String ASP_PRODUCT = "asp_retail";
  static const String ASP_DISPLAY_NAME = 'Acko Arogya Sanjeevani Plan';
  static const String AS_PRODUCT = "arogya_sanjeevni";
  static const String PARAM_API_KEY = "merchant_key";
  static const String PARAM_REDIRECT_URL = "redirect_url";
  static const String PARAM_USER_ID = "user_id";
  static const String PARAM_NUMBER = "number";
  static const String PARAM_NAME = "name";
  static const String PARAM_TITLE = "title";
  static const String PARAM_SOURCE = "source";
  static const PHARMACY_REDIRECT_URL = "https://www.1mg.com/";
  static const LABS_REDIRECT_URL = "https://www.1mglabs.com/";
  static const String ONEMG_GENERATE_HASH_API = "generate_hash";
  static const String ONEMG_GENERATE_HASH_NEW = "api/v6/b2b/generate_hash";
  static const String URL_REGEX =
      r'https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)';
  static const String PAN_REGEX = "[A-Z]{5}[0-9]{4}[A-Z]{1}";
  static const String EMAIL_REGEX =
      r'^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$';
  static const String AMOUNT_REGEX = r'₹[0-9,]*[\.]*[0-9]*';
  static const String WORD_HIGHLIGHT_REGEX = r'₹[0-9,]*[\.]*[0-9]*';
  static const String PREPOSTSTRING = 'pre_post_hospitalisation';
  static const String ACR_CANCEL_STATUS = "CANCELLED";
  static const String ACR_TREATMENT_DONE_STATUS = "TREATMENT_DONE";
  static const String ACR_CLAIM_SUBMISSION_PENDING_STATE =
      "claim-submission-pending";
  static const String ACR_CANCELLED_STATE = "cancelled";
  static const String ACR_PAYMENT_PENDING_STATE = "payment-pending-acko";
  static const String dates_error = "dates_error";
  static const String slots_error = "slots_error";
  static const String login = "Login";
  static const String loginWithAuthorizedNumber =
      "Login with authorized number to continue";
  static const String unauthorizedAccess = "Unauthorized access";
  static const String unauthorizedAccessMessage =
      "We're sorry. You don't have access to this page.";
  static const LINK_GMC_BANNER_IMAGE = "https://marketing.ackoassets.com/images/health/asset/Hospital_Network.png";
  static const String HEALTH_ASSET_VIEW_BG = "https://auto.ackoassets.com/central-app/features/app_ia_v10/health_bg.png";
  static const String HEALTH_PWILO_CARD_BG = 'https://marketing.ackoassets.com/images/health/asset/pwilo_bg_grad.png';
  static const claimsEduBgImage = 'https://marketing.ackoassets.com/images/health/claim/education_grad_bg.png';

  static const GMC_LINK_DEEPLINK = 'acko.com://health-page/gmc-link';

  static bool appHomeRoutePredicate(Route<dynamic> route) =>
      route.settings.name == Routes.APP_HOME;

  static bool homeRoutePredicate(Route<dynamic> route) =>
      route.settings.name == Routes.HEALTH_HOME_PAGE ||
      route.settings.name == Routes.ADVANCE_CASH_DETAIL ||
      route.settings.name == Routes.HEALTH_CLAIMS_LIST_PAGE ||
      route.settings.name == Routes.APP_HOME ||
      route.settings.name == Routes.FNOL_INTRO ||
      route.settings.name == Routes.ASSET_POLICY_HEALTH;

  static bool webRoutePredicate(Route<dynamic> route) =>
      route.settings.name == Routes.MEDICAL_EVALUATION_DETAILS ||
      route.settings.name == Routes.WEB_PAGE ||
      route.settings.name == Routes.HEALTH_HOME_PAGE ||
      route.settings.name == Routes.APP_HOME;

  static bool ppmcRoutePredicate(Route<dynamic> route) =>
      route.settings.name == Routes.AHC_LANDING ||
      route.settings.name == Routes.MEDICAL_EVALUATION_DETAILS ||
      route.settings.name == Routes.PPMC_BOOKING_DETAILS_SCREEN ||
      route.settings.name == Routes.P2I_STATUS_TRACKING_SCREEN ||
      route.settings.name == Routes.WEB_PAGE ||
      route.settings.name == Routes.HEALTH_HOME_PAGE ||
      route.settings.name == Routes.APP_HOME;

  static bool vmerTimelineRoute(Route<dynamic> route) =>
      route.settings.name == Routes.VMER_EVALUATION_DETAILS ||
      route.settings.name == Routes.VMER_TIMER_PAGE ||
      route.settings.name == Routes.WEB_PAGE_V2;
}

class LinkPolicyState {
  static const String ENROLLMENT = "ENROLLMENT";
  static const String ENDORSEMENT = "ENDORSEMENT";
}

enum FNOLPage {
  MedicalVisit,
  MemberSelection,
  FNOLCoverPage,
  PrePostHospitalization,
  TreatmentSearchPage,
  TreatmentCategoryPage,
  HealthPolicyPlanPage,
  DocUpload,
  PayoutMember,
  KYCPage,
  PayoutModule,
  CategoryChooser,
  ClaimReviewPage,
  ClaimDeficiencyPage
}

extension ClaimScreenProperties on FNOLPage {
  bool hideTopHeader(bool isAdvanceCash) =>
      this == FNOLPage.ClaimDeficiencyPage ||
      this == FNOLPage.ClaimReviewPage ||
      isAdvanceCash;
}

enum ClaimReviewSection { treatmentInfo, documents, accountDetails }

extension ReviewItemDetails on ClaimReviewSection {
  String getIcon() {
    switch (this) {
      case ClaimReviewSection.treatmentInfo:
        return "ic_treatment_stroke.svg";
      case ClaimReviewSection.documents:
        return "ic_document_stroke.svg";
      case ClaimReviewSection.accountDetails:
        return "ic_bank_stroke.svg";
    }
  }

  String getTitle() {
    switch (this) {
      case ClaimReviewSection.treatmentInfo:
        return treatment_info;
      case ClaimReviewSection.documents:
        return claim_document;
      case ClaimReviewSection.accountDetails:
        return claim_bankDetails;
    }
  }

  bool isEditable(bool isEditable) {
    switch (this) {
      case ClaimReviewSection.treatmentInfo:
        return isEditable;
      case ClaimReviewSection.documents:
        return true;
      case ClaimReviewSection.accountDetails:
        return true;
    }
  }

  FNOLPage getCtaPage() {
    switch (this) {
      case ClaimReviewSection.treatmentInfo:
        return FNOLPage.MedicalVisit;
      case ClaimReviewSection.documents:
        return FNOLPage.DocUpload;
      case ClaimReviewSection.accountDetails:
        return FNOLPage.PayoutMember;
    }
  }
}

enum HealthNavigationPage {
  AS_POLICY,
  AS_POLICY_V2,
  HEALTH_RETAIL_PURCHASE,
  GMC_LINKING,
  HEALTH_HOME,
  ASSET_VIEW,
  POLICY_VIEW,
  E_CARDS,
  LOAD_MERGED_PDP,
  PHARMACY,
  LABS,
  DOCTOR_ON_CALL,
  EDIT_POLICY,
  REGISTER_CLAIM,
  LOAD_WEB_PAGE,
  NETWORK_HOSPITALS,
  GMC_UPSELL,
  LIFE_CROSS_SELL,
}

extension HealthNav on String {
  HealthNavigationPage getHealthNavigationPage() {
    switch (this) {
      case "as-policy":
        return HealthNavigationPage.AS_POLICY;
      case "as-policy-v2":
        return HealthNavigationPage.AS_POLICY_V2;
      case "retail-purchase":
        return HealthNavigationPage.HEALTH_RETAIL_PURCHASE;
      case "gmc-link":
        return HealthNavigationPage.GMC_LINKING;
      case "network-hospitals":
        return HealthNavigationPage.NETWORK_HOSPITALS;
      case "web":
        return HealthNavigationPage.LOAD_WEB_PAGE;
      case "claim":
        return HealthNavigationPage.REGISTER_CLAIM;
      case 'asset':
        return HealthNavigationPage.ASSET_VIEW;
      case 'policy-view':
        return HealthNavigationPage.POLICY_VIEW;
      case 'e-cards':
        return HealthNavigationPage.E_CARDS;
      case "doctor-consultation":
        return HealthNavigationPage.DOCTOR_ON_CALL;
      case "labs":
        return HealthNavigationPage.LABS;
      case "pharmacy":
        return HealthNavigationPage.PHARMACY;
      case "edit-policy":
        return HealthNavigationPage.EDIT_POLICY;
      case "gmc_upsell":
        return HealthNavigationPage.GMC_UPSELL;
      case "life_cross_sell":
        return HealthNavigationPage.LIFE_CROSS_SELL;
    }
    return HealthNavigationPage.HEALTH_HOME;
  }
}
