class StoredDeeplink {
  final String? value;
  final DeeplinkSource? source;
  Map<String, dynamic>? queryParameters;
  final UtmParams? utmParams;

  StoredDeeplink({
    this.value,
    this.source,
    this.queryParameters,
    this.utmParams,
  }) {
    this.queryParameters =
        value != null ? Uri.parse(value!).queryParameters : null;
  }

  factory StoredDeeplink.fromJson(Map? json) {
    return StoredDeeplink(
      value: json?['value'] as String?,
      source: DeeplinkSource.fromString(json?['source'] as String? ?? ''),
      utmParams: json?['utmParams'] != null
          ? UtmParams.fromJson(json?['utmParams'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, String> toJson() {
    final map = <String, String>{};
    if (value != null) map['value'] = value!;
    if (source != null) map['source'] = source!.value;
    if (utmParams != null) {
      map.addAll(utmParams!.toJson());
    }
    return map;
  }
}

class UtmParams {
  String? utmUrl;
  String? utmSource;
  String? utmCampaign;
  String? utmTerm;
  String? utmMedium;
  String? utmContent;

  UtmParams({
    this.utmUrl,
    this.utmSource,
    this.utmCampaign,
    this.utmTerm,
    this.utmMedium,
    this.utmContent,
  });

  factory UtmParams.fromJson(Map<String, dynamic> json) {
    return UtmParams(
      utmUrl: json['utmUrl'] as String?,
      utmSource: json['utmSource'] as String?,
      utmCampaign: json['utmCampaign'] as String?,
      utmTerm: json['utmTerm'] as String?,
      utmMedium: json['utmMedium'] as String?,
      utmContent: json['utmContent'] as String?,
    );
  }

  Map<String, String> toJson() {
    final map = <String, String>{};
    if (utmUrl != null) map['utmUrl'] = utmUrl!;
    if (utmSource != null) map['utmSource'] = utmSource!;
    if (utmCampaign != null) map['utmCampaign'] = utmCampaign!;
    if (utmTerm != null) map['utmTerm'] = utmTerm!;
    if (utmMedium != null) map['utmMedium'] = utmMedium!;
    if (utmContent != null) map['utmContent'] = utmContent!;
    return map;
  }
}

enum DeeplinkSource {
  APPS_FLYER,
  INTENT,
  PUSH_NOTIFICATION,
  FLUTTER;

  String get value => name;

  static DeeplinkSource fromString(String value) {
    return switch (value) {
      'PUSH_NOTIFICATION' => PUSH_NOTIFICATION,
      'APPS_FLYER' => APPS_FLYER,
      'FLUTTER' => FLUTTER,
      _ => INTENT,
    };
  }
}
