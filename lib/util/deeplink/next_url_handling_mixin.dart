import 'package:acko_core_utilities/deeplink_handler/DeeplinkHandler.dart';
import 'package:flutter/material.dart';

/// Mixin for handling next URL navigation in widgets
mixin NextUrlHandlingMixin<T extends StatefulWidget> on State<T> {

  void handleNextUrlNavigation(Map<dynamic, dynamic>? params) {
    if (params?.isNotEmpty == true && params!.containsKey("nextUrl")) {
      final String? nextUrl = params["nextUrl"];
      if (nextUrl != null) {
        final Map<String, dynamic>? routeMap = nextUrl.getRoute();
        if (routeMap != null) {
          WidgetsBinding.instance.addPostFrameCallback((_){
            Navigator.pushNamed(
              context,
              routeMap['route'],
              arguments: routeMap,
            );
          });
        }
      }
    }
  }
}