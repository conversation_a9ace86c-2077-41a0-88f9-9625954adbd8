import 'dart:async';

import 'package:acko_flutter/util/deeplink/stored_deeplink.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_logger/acko_logger.dart';
import 'package:acko_logger/events/error/app_exception.dart';
import 'package:acko_logger/events/info/app_debug_info.dart';
import 'package:flutter/services.dart';
import 'package:utilities/constants/constants.dart';

mixin AppStoredDeeplinkListener<T> {
  static const loggerConstants = (
    page: 'AppStoredDeeplinkListener',
    emitCallback: 'emitCallback',
    afLinkJourney: 'AF_LINK',
    deeplinkInfo: 'getStoreDeeplink invoked',
    appsFlyerInfo: 'appsflyer callback invoked',
    errorMessage: 'AppsFlyer initialization failed',
  );

  Future<void> getStoreDeeplink({
    required Function(StoredDeeplink? deeplink) onDeeplinkCallback,
    int maxTimeout = 500,
  }) async {
    bool waitForAppsFlyer = false;

    /// Abstraction for Appsflyer Initialisation and deeplink callbacks
    Future<void> emitCallback() async {
      final deeplinkInfo = await _fetchDeeplink();
      if (deeplinkInfo != null) {
        AckoLoggerManager.instance.logInfo(
            event: AppDebugInfoEvent(
          page: loggerConstants.page,
          infoMessage: loggerConstants.deeplinkInfo,
          journey: loggerConstants.emitCallback,
          data: deeplinkInfo.toJson(),
        ));
        onDeeplinkCallback(deeplinkInfo);
      } else {
        waitForAppsFlyer = true;
      }
    }

    /// Timer object, provides a callback afer [maxTimeout] if waitForAppsFlyer is `true`
    Timer? t;

    /// Local method channel to listen to AF initialisation from Native side.
    Future<void> setMethodCallHandler(MethodCall call) async {
      switch (call.method) {
        case Constants.AF_LINK:
          waitForAppsFlyer = false;

          if (call.arguments is Map) {
            if (call.arguments['status'] == 'ERROR') {
              AckoLoggerManager.instance.logError(
                  event: AppExceptionEvent(
                page: loggerConstants.page,
                errorMessage: loggerConstants.errorMessage,
                journey: loggerConstants.afLinkJourney,
                data: {'args': call.arguments?.toString()},
              ));
            } else {
              AckoLoggerManager.instance.logInfo(
                  event: AppDebugInfoEvent(
                page: loggerConstants.page,
                infoMessage: loggerConstants.appsFlyerInfo,
                journey: loggerConstants.afLinkJourney,
                data: {'args': call.arguments?.toString()},
              ));
            }

            if (t?.isActive ?? false) {
              t?.cancel();
            }
            await emitCallback();
          }
      }
    }

    /// Fetch intial deeplink, emit `appsFlyerInstialization` and `onDeeplinkCallback`
    ///  callback if AF is initlaized irrespecitve of AF inisitalization.
    await emitCallback();

    if (waitForAppsFlyer) {
      waitForAppsFlyer = false;
      Constants.LOCAL_TO_SCREEN_PLATFORM_CHANNEL
          .setMethodCallHandler(setMethodCallHandler);

      t = Timer(
        Duration(milliseconds: maxTimeout),
        () async {
          await emitCallback();
          if (t!.isActive) t.cancel();
        },
      );
    }
  }

  Future<StoredDeeplink?> _fetchDeeplink() async {
    final StoredDeeplink deeplinkInfo = StoredDeeplink.fromJson(
        (await Constants.PLATFORM_CHANNEL.invokeMapMethod("launch_deeplink")) ??
            {});
    if (deeplinkInfo.value.isNotNullOrEmpty) {
      return deeplinkInfo;
    } else {
      return null;
    }
  }
}
