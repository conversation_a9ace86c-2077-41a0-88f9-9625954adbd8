import 'package:acko_flutter/travel_insurance/shared/util/event/travel_event.dart';
import 'package:utilities/constants/constants.dart';

import 'Utility.dart';

class TravelUrls {
  static String initJourney =
      Constants.BASE_URL + 'travel/api/v1/international/journey/purchase/init';
  static String homeWidget =
      Constants.BASE_URL + 'travel/api/v1/international/home/<USER>';

  static String travelMWeb(String utmMedium) =>
      Constants.BASE_URL +
      'p/travel/international-insurance?platform=${Util.getPlatform()}&utm_campaign=${TravelEvent.utm_campaign}&utm_medium=${utmMedium.isEmpty ? TravelEvent.utm_medium : utmMedium}&utm_source=${TravelEvent.utm_source.isEmpty ? 'direct' : TravelEvent.utm_source}';

  static String pdp(String policyId) =>
      Constants.BASE_URL +
      'travel/api/v1/international/pdp?policy_id=$policyId';

  static String policyCancelInit(String policyId) =>
      Constants.BASE_URL +
      'travel/api/v1/international/policy/$policyId/cancel/init';

  static String policyCancelConfirm(String policyId) =>
      Constants.BASE_URL +
      'travel/api/v1/international/policy/$policyId/cancel/confirm';

  static String benefit(String policyId) =>
      Constants.BASE_URL +
      'travel/api/v1/international/policy/covers?policy_id=$policyId';

  static String updateInsured(String policyId) =>
      Constants.BASE_URL +
      'travel/api/v1/international/policy/$policyId/update_insured';

  static String draftPolicy(String proposalId) =>
      Constants.BASE_URL +
      'travel/api/v1/international/proposal/$proposalId/payment_verify';

  static String cancelPolicy = Constants.BASE_URL +
      'travel/api/v1/international/proposal/cancel_transaction';

  static String endorse(String policyId) =>
      Constants.BASE_URL +
      'travel/api/v1/international/policy/$policyId/endorse';

  static String endorsePayment(String policyId) =>
      '${Constants.BASE_URL}travel/api/v1/international/policy/$policyId/endorse/confirm';
  static String endorsePaymentVerify =
      '${Constants.BASE_URL}travel/api/v1/international/endorsement/payment/verify';

  static String downloadPolicy =
      '${Constants.BASE_URL}travel/api/v1/international/pdf';

  static String claimConfig(String policyId, String damageId) =>
      '${Constants.BASE_URL}travel/api/v1/international/policy/$policyId/$damageId/claim_config';

  static String claimExpectation(String policyId) =>
      '${Constants.BASE_URL}travel/api/v1/international/policy/$policyId/expectation_setting';

  static String claimInitiate(String policyNumber) =>
      '${Constants.BASE_URL}travel/api/v1/international/claim/$policyNumber/create';

  static String travelClaimState(String policyId) =>
      '${Constants.BASE_URL}travel/api/v1/international/claim/$policyId/current_state';

  static String claimStateChange =
      '${Constants.BASE_URL}travel/api/v1/international/claim/event';

  static String claimUpdate(String machineId) =>
      '${Constants.BASE_URL}travel/api/v1/international/claim/$machineId/update';

  static String claimUploadDocument(String machineId, String claimNumber,
          String documentType, String taskId) =>
      '${Constants.BASE_URL}travel/api/v1/international/claim/$machineId/document?claim_request_number=$claimNumber&document_type=$documentType&task_id=$taskId';

  static String claimRemoveDocument(String machineId) =>
      '${Constants.BASE_URL}travel/api/v1/international/claim/$machineId/document';

  static String claimTracking(String claimNumber) =>
      '${Constants.BASE_URL}travel/api/v1/international/claim/$claimNumber/tracking';

  static String visaApplication =
      '${Constants.BASE_URL}p/travel/visa-application';

  static String visaAssistance =
      '${Constants.BASE_URL}travel/api/v1/vas/visa_assistance/v2f/applications';

  static String claimPolicies(String claimNumber) =>
      '${Constants.BASE_URL}travel/api/v1/international/claim/$claimNumber';

  static String helpWidget =
      '${Constants.BASE_URL}travel/api/v1/international/faqs';

      

  static String flightPassHelp =
      '${Constants.BASE_URL}travel/api/v1/travel/pass/faqs';

  static String cashlessHospitals(
          int pageNo, int pageSize, String matchingText) =>
      '${Constants.BASE_URL}travel/api/v1/international/cashless_hospitals?pageNo=$pageNo&pageSize=$pageSize&hospitalName=$matchingText';

  static String countryDetails(int pageNo, int pageSize, String matchingText) =>
      '${Constants.BASE_URL}travel/api/v1/international/country_details?pageNo=$pageNo&pageSize=$pageSize&text=$matchingText';

  static String cashlessHospitalsByCountryData() =>
      '${Constants.BASE_URL}travel/api/v1/international/cashless_hospitals/country_data';

  static String flightPassPDP(String policyId) =>
      Constants.BASE_URL +
      'travel/api/v1/travel/travel_pass_pdp?policy_id=$policyId';

  static String passClaimExpectation(String policyId, String perilId,
          String claimModeId, String damageId) =>
      '${Constants.BASE_URL}travel/api/v1/international/pass/policy/$policyId/expectation_setting?peril_id=$perilId&claim_mode_id=$claimModeId&damage_id=$damageId';

  static String domesticAirportList =
      Constants.BASE_URL + 'travel/api/v1/travel/airports/list';

  static String addFlight =
      Constants.BASE_URL + 'travel/api/v1/travel/flights/add';
  static String fetchFlights(String policyId) =>
      Constants.BASE_URL +
      'travel/api/v1/travel/flights/fetch_all?policy_id=$policyId';

  static String getCouponDetail(String policyId, couponId) =>
      Constants.BASE_URL +
      'travel/api/v1/travel/policy/coupon?policy_id=$policyId&coupon_id=$couponId';

  static String unlockCoupon =
      Constants.BASE_URL + 'travel/api/v1/travel/unlock_coupon';

  static String getAllPassClaims(String policyNumber) =>
      Constants.BASE_URL +
      'travel/api/v1/international/claim/flight_pass/all_claims?policy_number=$policyNumber';

  static String getCoverageDetails(String policyId, String coverageType) =>
      Constants.BASE_URL +
      'travel/api/v1/travel/policy/cover_details?policy_id=$policyId&coverage_type=$coverageType';

  static String dialCodeList = Constants.BASE_URL +
      'travel//api/v1/international/claim/country_dial_code';

      static String myTrips =
      '${Constants.BASE_URL}travel/api/v1/travel/my_trips';

  static String diseaseList = Constants.BASE_URL +
      'travel/api/v1/international/medical/pre-existing-diseases';

  static String coverage(String policyId) =>
      Constants.BASE_URL +
      'travel/api/v1/international/policy/covers/v2?policy_id=$policyId';
      static String pdpAlerts = "/pages/alerts/travel_pdp/";
}
