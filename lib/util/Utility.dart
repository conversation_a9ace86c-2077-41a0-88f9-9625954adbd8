import 'dart:collection';
import 'dart:convert';
import 'dart:core';
import 'dart:io';
import 'dart:math';
import 'dart:ui';

import 'package:acko_flutter/common/util/PermissionService.dart';
import 'package:acko_flutter/common/util/PreferenceHelper.dart' as prefs;
import 'package:acko_flutter/common/util/PreferenceHelper.dart';
import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/common/view/AckoText.dart';
import 'package:acko_flutter/common/view/FullPageLoader.dart';
import 'package:acko_flutter/common/view/RoundedWidget.dart';
import 'package:acko_flutter/common/view/router.dart';
import 'package:acko_flutter/common/view/sizing_bottom_sheet_widget.dart';
import 'package:acko_flutter/common/widgets/shimmer.dart';
import 'package:acko_flutter/feature/acko_services/ui/acko_service_loading_screen.dart';
import 'package:acko_flutter/feature/app_policy_page/bloc/tab_bar_bloc.dart';
import 'package:acko_flutter/feature/app_policy_page/model/policy_list_response.dart';
import 'package:acko_flutter/feature/app_policy_page/navigation/tabInternalNavigator.dart';
import 'package:acko_flutter/feature/app_policy_page/utils/CommonDataStore.dart';
import 'package:acko_flutter/feature/app_policy_page/utils/common_ui_widget.dart';
import 'package:acko_flutter/feature/healthlife_webview/health_webview.dart';
import 'package:acko_flutter/feature/healthlife_webview/life_webview.dart';
import 'package:acko_flutter/feature/my_account/model/MyAccountItem.dart';
import 'package:acko_flutter/network/ApiService.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_logger/acko_logger.dart';
import 'package:acko_logger/model/user_context_model.dart';
import 'package:acko_web_view_module/web_view_registers.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dio/dio.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart' as rendering;
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:intl/intl.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:session_manager_module/session_manager.dart';
import 'package:sprintf/sprintf.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/remote_config/model/remote_config.dart';
import 'package:utilities/remote_config/remote_config.dart';

import '../common/bloc/device_info_repo.dart';
import '../feature/acko_services/ui/acko_service_common_header.dart';
import '../feature/acko_services/ui/unlock_acko_health_benefits_bottom_sheet.dart';
import '../feature/base_webview/view/base_web_view.dart';
import '../feature/central_webview/view/central_webview.dart';
import '../feature/home/<USER>/home_utils.dart';
import '../feature/login/bloc/LoginRepository.dart';
import '../feature/travel_webview/travel_web_view_logic.dart';
import '../main.dart';
import 'health/utils.dart';

///Todo : Clean up this class
class Util {
  static bool isPageLinkRequired = false;
  static String? adId = "0";
  static String? idfaValue = "0";
  static String? userId = "";
  static String? afId = '';
  static String? appleAppId = "";

  static String getFormattedDate(String utcDate) {
    //var dateTime = DateFormat.MMMd().parse(utcDate, true);
    //return dateTime.toLocal().toString();
    return utcDate.substring(0, 10);
  }

  static getInitials(String userName, {bool needWords = false}) {
    if (userName.isNotNullOrEmpty && userName.trim().isNotNullOrEmpty) {
      List<String> wordsOfName = userName.trim().split(' ').toList();
      return (needWords)
          ? wordsOfName
          : (wordsOfName.length > 1)
              ? '${wordsOfName[0][0].toUpperCase() + wordsOfName[wordsOfName.length - 1][0].toUpperCase()}'
              : '${wordsOfName[0][0].toUpperCase()}';
    } else {
      return '';
    }
  }

  static String convertDateTimeToUTC(DateTime dateTime) {
    return DateFormat('yyyy-dd-MM HH:mm:ss').format(dateTime);
  }

  static DateTime convertUTCToDateTime(String utcString) {
    return DateFormat('yyyy-dd-MM HH:mm:ss').parse(utcString);
  }

  static Future<void> attachUserPropertiesToLogger() async {
    final userSessionModel =
        await SessionManager.instance.storageManager.getUserSessionDetails();

    if (userSessionModel.userEkey?.isNotNullAndEmpty ?? false) {
      final deviceId =
          await Constants.PLATFORM_CHANNEL.invokeMapMethod("get_device_id");
      PackageInfo packageInfo = await PackageInfo.fromPlatform();

      final Map<String, String> metaData = {
        "trackerid": userSessionModel.trackerId ?? "",
        "acko_device_id": deviceId?['device_id'] ?? "",
        "app_platform": (Platform.isAndroid) ? 'android':'ios',
        "app_version": packageInfo.version,
      };

      final String emailId = await SessionManager.instance.storageManager
          .getStringDataInPreferences(StringDataSharedPreferenceKeys.EMAIL_ID);

      metaData.removeNullOrEmptyValues();

      AckoLoggerManager.instance.setUserContext(UserContextModel(
        phoneNumber: userSessionModel.phone!,
        userId: userSessionModel.userEkey!,
        email: emailId,
        metaData: metaData,
      ));
    }
  }

  static String getChatTime(String date) {
    return DateFormat('dd MMM | hh:mm a').format(
        DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").parse(date, true).toLocal());
  }

  static void exitApp() {
    if (Platform.isIOS) {
      exit(0);
    } else {
      SystemNavigator.pop();
    }
  }

  static bool isDateVaild(String? date) {
    return (DateTime.tryParse(date ?? "") != null);
  }

  static String getLeadGenerationDate(String? date) {
    ///Date in 27-Aug-2022 format
    try {
      List<String> _dateList = (date ?? "").trim().split("/");
      if (_dateList.length < 2)
        return DateFormat("yyyy-MM-dd").format(DateTime.now());
      DateTime _newDate = DateTime(int.parse(_dateList[2]),
          getMonthIndexFromString(_dateList[1]), int.parse(_dateList[0]));
      return DateFormat("yyyy-MM-dd").format(_newDate);
    } catch (e) {}
    return DateFormat("yyyy-MM-dd").format(DateTime.now());
  }

  static int getMonthIndexFromString(String month) {
    int monthIndex = 1;

    switch (month.toLowerCase().trim()) {
      case "jan":
        monthIndex = 1;
        break;
      case "feb":
        monthIndex = 2;
        break;
      case "mar":
        monthIndex = 3;
        break;
      case "apr":
        monthIndex = 4;
        break;
      case "may":
        monthIndex = 5;
        break;
      case "jun":
        monthIndex = 6;
        break;
      case "jul":
        monthIndex = 7;
        break;
      case "aug":
        monthIndex = 8;
        break;
      case "sep":
        monthIndex = 9;
        break;
      case "oct":
        monthIndex = 10;
        break;
      case "nov":
        monthIndex = 11;
        break;
      case "dec":
        monthIndex = 12;
        break;
    }

    return monthIndex;
  }

  static String getAckoServicesTime(String date) {
    return DateFormat('dd-MMM-yyyy | hh:mm a')
        .format(DateFormat("yyyy-MM-dd HH:mm:ss").parse(date, true).toLocal());
  }

  //TODO: Rename if to match the time format instead of flow name
  //TODO: Check if the format for date can be uniform across the app
  static String getTransactionTime(String? date, {bool isUtc = false}) {
    if (date.isNullOrEmpty) return "";

    try {
      final formattedDate = DateFormat('dd MMM yyyy | hh:mm a').format(
          DateFormat("yyyy-MM-dd'T'HH:mm:ss").parse(date!, isUtc).toLocal());
      return formattedDate;
    } catch (error) {
      return '';
    }
  }

  static String getClaimTime(String date) {
    return DateFormat('dd MMM yyyy • hh:mm a').format(
        DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'").parse(date, true).toLocal());
  }

  static String getChatUpdateTime(String date) {
    String dateSubString = date.substring(0, 19);
    try {
      return DateFormat('dd MMM | hh:mm a').format(
          DateFormat("yyyy-MM-dd'T'HH:mm:ss")
              .parse(dateSubString, true)
              .toLocal());
    } catch (error) {
      debugPrint("DATE FORMAT EXCEPTION " + error.toString());
    }
    return date;
  }

  static String? getCid() {
    return adId == null || adId == '0' ? userId : adId;
  }

  static Future<bool> isNetworkConnected() async {
    List<ConnectivityResult> connectivityResult =
        await (Connectivity().checkConnectivity());
    if (connectivityResult.contains(ConnectivityResult.none)) {
      return false;
    }
    return true;
  }

  static Future<bool> _checkInternetConnection() async {
    bool hasInternetAccess = false;
    try {
      final result = await InternetAddress.lookup('www.acko.com');
      if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
        hasInternetAccess = true;
      }
    } on SocketException catch (_) {
      hasInternetAccess = false;
    }
    return Future.value(hasInternetAccess);
  }

  static checkIfUserIsPresentInLocation(List locations) {
    bool userInLocation = false;
    if (Constants.USER_LOCATION_DATA != null && locations.isNotEmpty) {
      Constants.USER_LOCATION_DATA!.forEach((element) {
        locations.forEach((loc) {
          String location = loc.toString();
          bool _temp = (element.locality ?? "")
                  .toLowerCase()
                  .contains(location.toLowerCase()) ||
              (element.subAdministrativeArea ?? "")
                  .toLowerCase()
                  .contains(location.toLowerCase());
          if (_temp) userInLocation = _temp;
        });
      });
    }
    return userInLocation;
  }

  static void preCacheImages(BuildContext context) {
    PRE_CACHED_IMAGES_LIST.forEach((imageUrl) {
      try {
        precacheImage(CachedNetworkImageProvider(imageUrl), context)
            .onError((error, stackTrace) {
          ///When app opens & internet is not there it throws error
        });
      } catch (e) {}
    });
  }

  static askForAppPermission(Permission permission) async {
    PermissionStatus permissionGranted = await permission.request();

    if (permissionGranted == PermissionStatus.permanentlyDenied ||
        (permissionGranted == PermissionStatus.denied && Platform.isIOS)) {
      UiUtils.getInstance
          .showToast(enable_permissions, toastLength: Toast.LENGTH_LONG);
      return;
    } else if (!permissionGranted.name.equalsIgnoreCase("granted")) {
      permissionGranted = await permission.request();
    }

    if (!permissionGranted.name.equalsIgnoreCase("granted")) {
      UiUtils.getInstance
          .showToast(enable_permissions, toastLength: Toast.LENGTH_LONG);
    }
  }

  static checkAndShowSlowInternetToast() async {
    if (!(await isNetworkConnected())) return;
    bool canShowSlowInternetToast = true;
    try {
      canShowSlowInternetToast = RemoteConfigInstance.instance
          .getData(RemoteConfigKeysSet.showSlowInternetToast);
    } catch (e) {
      debugPrint("Remote Config exception: " + e.toString());
    }
    if (!canShowSlowInternetToast) return;
    bool? isInternetSlow;
    Future.wait([
      ApiService.apiServiceInstance
          .getApiRequest(Constants.INTERNET_SPEED_TEST_FILE_100KB,
              baseUrl: Constants.INTERNET_SPEED_TEST_FILE_100KB_BASE_URL)
          .then((response) {
        if (isInternetSlow == null && response.statusCode == 200)
          isInternetSlow = false;
      }),
      Future.delayed(Duration(seconds: 4), () {
        if (isInternetSlow == null) {
          isInternetSlow = true;
          Fluttertoast.showToast(
              msg: slowInternetError,
              toastLength: Toast.LENGTH_LONG,
              gravity: ToastGravity.BOTTOM,
              backgroundColor: colorF6F7FB,
              textColor: color737F8B,
              fontSize: 13.0);
        }
      })
    ]);
  }

  static Future<bool> canShowBookCarService() async {
    var carServiceConfig = await RemoteConfigInstance.instance
        .getGbAsyncData(RemoteConfigKeysSet.BOOK_CAR_SERVICE);
    if (!carServiceConfig["visible"]) {
      return false;
    }
    return checkIfUserIsPresentInLocation(carServiceConfig["cities"]);
  }

  static Future<File?> getFileFromInternet(
      String? url, String extension) async {
    if (url.isNullOrEmpty) return null;
    try {
      final response = await Dio()
          .get(url!, options: Options(responseType: ResponseType.bytes));
      final documentDirectory = await getApplicationDocumentsDirectory();
      final file = File(
          '${documentDirectory.path}/${DateTime.now().millisecondsSinceEpoch}.${extension.trim()}');
      file.writeAsBytesSync(response.data);
      return file;
    } catch (e) {}
    return null;
  }

  static Future<bool> isPhysicalDevice() async {
    bool isPhysicalDevice = false;
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (((Platform.isIOS && (await deviceInfo.iosInfo).isPhysicalDevice)) ||
        (Platform.isAndroid &&
            (await deviceInfo.androidInfo).isPhysicalDevice)) {
      isPhysicalDevice = true;
    }
    return isPhysicalDevice;
  }

  static void startLoading(BuildContext context) async {
    FullPageLoader.instance.showFullPageLoader(context);
  }

  static void stopLoading(BuildContext context) async {
    FullPageLoader.instance.dismissFullPageLoader(context);
  }

  static String getFormattedDateZ(String date) {
    return DateFormat('d MMM yy').format(
        DateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").parse(date, true).toLocal());
  }

  static String getFormattedPolicyDetailsDate(String date) {
    return DateFormat('d MMM yy')
        .format(DateFormat("dd-MM-yyyy hh:mm:ss").parse(date, true).toLocal());
  }

  static String? getCustomDate(String? date) {
    String? formattedDate;
    try {
      if (date != null) {
        String dateSubString = date.substring(0, 10);
        formattedDate = DateFormat.MMMd().format(
            DateFormat("yyyy-MM-dd").parse(dateSubString, true).toLocal());
      }
    } catch (error) {
      debugPrint("DATE FORMAT EXCEPTION " + error.toString());
    }
    return formattedDate;
  }

  static String fullFormatDate(String date) {
    try {
      return DateFormat('dd MMM yyyy')
          .format(DateFormat("yyyy-MM-dd").parse(date, true).toLocal());
    } catch (e) {
      return date;
    }
  }

  static String getYYYYMMDD(String date) {
    try {
      return DateFormat('yyyy-MM-dd').format(DateFormat("MMM dd yyyy").parse(
        date,
      ));
    } catch (e) {
      return date;
    }
  }

  static String getMMMDDYYYY(String date) {
    try {
      return DateFormat('MMM dd, yyyy').format(DateFormat("yyyy-MM-dd").parse(
        date,
      ));
    } catch (e) {
      return date;
    }
  }

  static String getExpiryLabel(String date) {
    return calculateDifference(date) > 0 ? "Expires" : "Expired";
  }

  static String formatDate(String date) => DateFormat.MMMd().format(
      DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS").parse(date, true).toLocal());

  static int calculateDifference(String date) {
    DateTime now = DateTime.now();
    DateTime parsedDate = DateTime.parse(date).toLocal();
    int days = DateTime(parsedDate.year, parsedDate.month, parsedDate.day)
        .difference(DateTime(now.year, now.month, now.day))
        .inDays;
    // debugPrint("difference in days " + days.toString());
    return days;
  }

  static int calculateDifferenceForPolicyDetails(String date) {
    DateTime now = DateTime.now();
    DateTime parsedDate =
        DateFormat('dd-MM-yyyy hh:mm:ss').parse(date).toLocal();
    int days = DateTime(parsedDate.year, parsedDate.month, parsedDate.day)
        .difference(DateTime(now.year, now.month, now.day))
        .inDays;
    debugPrint("difference in days " + days.toString());
    return days;
  }

  static String? dayMonthFormattedDate(int days) {
    try {
      DateTime now = DateTime.now();
      DateTime newDate = now.add(Duration(days: days));
      return mmmDDyyyyFormatDate(newDate);
    } catch (e) {
      return null;
    }
  }

  static String getTodayDate(int duration) {
    return DateFormat('yyyy-MM-dd')
        .format(DateTime.now().subtract(Duration(days: duration)).toLocal());
  }

  static String mmmDDyyyyFormatDate(DateTime date) {
    return DateFormat('MMM dd yyyy').format(date.toLocal());
  }

  static String getDateMonthFormatDate(String date) {
    return DateFormat('dd MMM')
        .format(DateFormat('yyyy-MM-dd').parse(date, true));
  }

  static String getmmmDDyyyyFormatDate(String date) {
    return DateFormat('MMMM dd yyyy')
        .format(DateFormat('dd-MMM-yyyy').parse(date, true));
  }

  static String? getFormattedDateFromDateString(
      String? date, String inputFormat, String outputFormat) {
    if (date == null) return null;
    var dateTime = DateFormat(inputFormat).parse(date);
    return DateFormat(outputFormat).format(dateTime);
  }

  static String? getFormattedDateString(String? date, String outputFormat) {
    if (date == null) return null;
    // Parse the input date string to a DateTime object
    DateTime dateTime = DateTime.parse(date);

    // Define the output format
    DateFormat formatter = DateFormat(outputFormat);

    // Format the DateTime object to the desired format
    String formattedDate = formatter.format(dateTime);

    return formattedDate;
  }

  static String? lastVisitTimeFormatter(String date) {
    try {
      return DateFormat('MMM dd, h:mm a')
          .format(DateFormat("yyyy-MM-dd hh:mm:ss").parse(date, true));
    } catch (error, stacktrace) {
      debugPrint("EXCEPTION : ${error.toString()}");
      FirebaseCrashlytics.instance.recordError(error, stacktrace);
      return null;
    }
  }

  static String? getDateFormatFromEpoch(int epoch) {
    try {
      var dt = DateTime.fromMillisecondsSinceEpoch(epoch);
      return DateFormat("yyyy-MM-dd hh:mm:ss").format(dt);
    } catch (error, stacktrace) {
      debugPrint("EXCEPTION : ${error.toString()}");
      FirebaseCrashlytics.instance.recordError(error, stacktrace);
      return null;
    }
  }

  static String getMODateFormat(String date) {
    return DateFormat("yyyy-MM-dd").format(
        DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS").parse(date, true).toLocal());
  }

  static bool notExpired(String? createdDate, int days) {
    return createdDate != null
        ? DateTime.now().difference(DateTime.parse(createdDate)).inDays <= days
        : true;
  }

  static bool isDateExpired(DateTime date) {
    return DateTime.now().compareTo(date) > 0;
  }

  static String getPriceInCurrency(int? number) {
    var formatter = NumberFormat.decimalPattern(
      'HI',
    );
    if (number != null) {
      return '$rupeeSymbol ${formatter.format(number)}';
    } else {
      return '0';
    }
  }

  static Future<String?> getTrackerId() async {
    try {
      String? trackerId =
          await getStringPrefs(StringDataSharedPreferenceKeys.TRACKER_ID);
      if ((trackerId ?? "").trim().isNullOrEmpty) {
        ///For backward compatibility and already logged in users
        String? _cookieTrackerId = (await SessionManager.instance.storageManager
                .getUserSessionDetails())
            .trackerId;
        if (_cookieTrackerId.isNotNullAndEmpty) {
          setStringPrefs(
              StringDataSharedPreferenceKeys.TRACKER_ID, _cookieTrackerId);
          trackerId = _cookieTrackerId;
        }
      }
      return trackerId;
    } catch (e, s) {
      logException(e, trace: s);
    }
    return null;
  }

  static Future<void> updateTrackerIdInSharedPref(String? trackerId) async {
    if (trackerId.isNullOrEmpty) return;
    await setStringPrefs(StringDataSharedPreferenceKeys.TRACKER_ID, trackerId);
    return;
  }

  static String getHomeAssetImage({required String? assetName}) {
    return '$assetImageHome$assetName';
  }

  static String getAssetImage({required String? assetName}) {
    return '$assetImage$assetName';
  }

  static String getAssetImageFromPath({required String? assetName}) {
    return '$assetName';
  }

  static Future<bool> showHomeAppRating() async {
    String? ratingData;

    try {
      ratingData =
          RemoteConfigInstance.instance.getData(RemoteConfigKeysSet.appRating);
    } catch (e) {
      debugPrint("Remote Config exception: " + e.toString());
    }

    if (ratingData == null) {
      ratingData = RemoteConfigDefaultValues.APP_RATING_DATA;
    }

    AppRatingRemoteConfig config =
        AppRatingRemoteConfig.fromJson(jsonDecode(ratingData));

    if (config.showOldAppRating != null && config.showOldAppRating == true ||
        (config.showHomeAppRating != null &&
            config.showHomeAppRating == true)) {
      return true;
    } else {
      return false;
    }
  }

  static Future<bool> canShowRefer() async {
    bool? showRefer = await prefs
        .getBoolPrefs(BoolDataSharedPreferenceKeys.PREFS_SHOW_REFER_SCREEN);
    if (showRefer != null && showRefer == true) {
      bool ratingData = false;

      try {
        ratingData = RemoteConfigInstance.instance
            .getData(RemoteConfigKeysSet.showReferOnHome);
      } catch (e) {
        debugPrint("Remote Config exception: " + e.toString());
      }
      return ratingData;
    } else {
      return false;
    }
  }

  static showFNOLInAppRating() async {
    String? ratingData;
    try {
      ratingData =
          RemoteConfigInstance.instance.getData(RemoteConfigKeysSet.appRating);
    } catch (e) {
      debugPrint("Remote Config exception: " + e.toString());
    }

    if (ratingData == null) {
      ratingData = RemoteConfigDefaultValues.APP_RATING_DATA;
    }

    AppRatingRemoteConfig config =
        AppRatingRemoteConfig.fromJson(jsonDecode(ratingData));

    if (config.showFnolInAppRating != null &&
        config.showFnolInAppRating == true) {
      showInAppReview();
    }
  }

  static Future<bool> showSuccessPageAppRating() async {
    String? ratingData;

    try {
      ratingData =
          RemoteConfigInstance.instance.getData(RemoteConfigKeysSet.appRating);
    } catch (e) {
      debugPrint("Remote Config exception: " + e.toString());
    }

    if (ratingData == null) {
      ratingData = RemoteConfigDefaultValues.APP_RATING_DATA;
    }

    AppRatingRemoteConfig config =
        AppRatingRemoteConfig.fromJson(jsonDecode(ratingData));
    if (config.showOldAppRating != null && config.showOldAppRating == true) {
      return true;
    } else {
      return false;
    }
  }

  static void showInAppReview() {
    debugPrint("Show In app review called");
    Constants.PLATFORM_CHANNEL.invokeMethod(Constants.SHOW_IN_APP_REVIEW_API);
  }

  static bool isEmailValid(String email) {
    RegExp regExp = new RegExp(
      r"^[a-zA-Z0-9.a-zA-Z0-9!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+",
      caseSensitive: false,
    );
    return (regExp.hasMatch(email));
  }

  static bool isNameValid(String name) {
    RegExp regExp = new RegExp(
      r"^[a-zA-Z ]{2,30}$",
      caseSensitive: false,
    );
    return (regExp.hasMatch(name.trim()));
  }

  static bool validatePhoneNumber(String phone) {
    var exp = RegExp("[2-9][0-9]{9}");
    RegExpMatch? match = exp.firstMatch(phone);
    if (match != null) {
      return true;
    } else {
      return false;
    }
  }

  static String? getMaskedBankAccount(String? accountNumber) {
    if (accountNumber == null || accountNumber.length < 6) {
      return accountNumber;
    } else {
      int length = accountNumber.length - 5;
      return accountNumber.replaceRange(0, length, _getXMask(length));
    }
  }

  static String _getXMask(int length) {
    String mask = '';
    for (int i = 0; i < length; i++) {
      mask = mask + 'X';
    }
    return mask;
  }

  static bool isChassisNumberValid(String chassis) {
    RegExp regExp = new RegExp(
      r"^[a-zA-Z0-9_\\+\\-\\*\\.\\#\\&@\\$]{5,25}$",
      caseSensitive: false,
    );
    return (regExp.hasMatch(chassis));
  }

  static void showToast(String s) {
    Fluttertoast.showToast(
        msg: s,
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        backgroundColor: colorF6F7FB,
        textColor: color737F8B,
        fontSize: 13.0);
  }

  static Widget bottomBannerWidget(String assetName, String t1, String t2) {
    return Container(
      margin: EdgeInsets.only(left: 40, right: 40, top: 30, bottom: 30),
      child: Row(
        children: [
          Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
                color: colorF3F7FF, borderRadius: BorderRadius.circular(28)),
            child: SvgPicture.asset(assetName),
          ),
          Container(width: 12),
          Expanded(
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              TextEuclidMediumL2(t1),
              Container(height: 6),
              TextEuclidMediumL2(
                t2,
                textSize: 10.0,
                textColor: color555A68,
              )
            ]),
          )
        ],
      ),
    );
  }

  static Widget commonStatusWidget(
      PolicesStatus item, Color cardBackground, String? vehicleNumber,
      {GestureTapCallback? onTap}) {
    return InkWell(
      key: Key(Util.getKey(
          myAccountScreen, 'policy_status_${item.actionName}_$vehicleNumber')),
      onTap: item.actionName != null ? onTap : null,
      child: RoundedWidget(
        radius: 12.0,
        color: item.backgroundColor == null
            ? cardBackground
            : item.backgroundColor,
        child: Padding(
          padding: EdgeInsets.all(
            12.0,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    item.title != null
                        ? Padding(
                            child: TextEuclidSemiBold(
                              item.title!,
                              textColor: item.titleColor ?? color040222,
                              textSize: 13.0,
                            ),
                            padding: EdgeInsets.only(bottom: 5),
                          )
                        : Container(),
                    Row(
                      children: <Widget>[
                        item.subTitle != null
                            ? Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.only(
                                    right: 10.0,
                                  ),
                                  child: TextEuclidMediumL2(
                                    item.subTitle!,
                                    textColor: item.subTitleColor == null
                                        ? color453960
                                        : item.subTitleColor,
                                    textSize: 12.0,
                                  ),
                                ),
                              )
                            : Container(),
                        item.offerLabel != null
                            ? TextEuclidMediumL2(
                                item.offerLabel!,
                                textColor: color7C47E1,
                              )
                            : Container()
                      ],
                    ),
                    item.price != null
                        ? Row(
                            children: <Widget>[
                              TextEuclidBoldL2(
                                item.price!,
                                textColor: color3EB753,
                                textSize: 10.0,
                              ),
                              item.offerPrice != null
                                  ? Padding(
                                      padding: const EdgeInsets.only(left: 9),
                                      child: TextEuclidMediumL2(
                                          item.offerPrice!,
                                          textDecoration:
                                              TextDecoration.lineThrough,
                                          textColor: colorB9AFD0),
                                    )
                                  : Container()
                            ],
                          )
                        : Container()
                  ],
                ),
              ),
              item.actionName != null
                  ? Container(
                      margin: EdgeInsets.only(left: 10.0),
                      child: getRoundedBorderWidget(
                        color: color040222,
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              vertical: 6.0, horizontal: 12.0),
                          child: TextEuclidSemiBold(
                            item.actionName!,
                            textColor: color040222,
                            textSize: 13.0,
                          ),
                        ),
                      ),
                    )
                  : Container(),
            ],
          ),
        ),
      ),
    );
  }

  static Widget getImage(String image, Color color) {
    return Container(
      child: Container(
          width: 20,
          height: 20,
          margin: EdgeInsets.all(10),
          child: SvgPicture.asset(
            image,
          )),
      decoration: BoxDecoration(shape: BoxShape.circle, color: color),
    );
  }

  ///Todo : remove from here
  static showElectronicPolicies(BuildContext context) async {
    TabBarBloc _bloc = TabBarBloc.withoutInitCode();
    CommonUIWidgets? _commonUIWidgets = CommonUIWidgets(context, _bloc);
    var electronicPolicyList =
        CommonDataStore.sharedInstance.electronicPolicyList();

    if (electronicPolicyList.isNotEmpty) {
      showModalBottomSheet(
          isScrollControlled: true,
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(top: Radius.circular(15.0))),
          context: context,
          builder: (BuildContext bc) {
            return SizingBottomSheetWidget(
              builder: (context, index) {
                return InkWell(
                    onTap: () {
                      if (electronicPolicyList[index].claimUrl != null) {
                        _bloc.handleRoutingBasedOnUrl(
                            context, electronicPolicyList[index].claimUrl!);
                      }
                    },
                    child: Wrap(children: [
                      _commonUIWidgets
                          .getPolicyCard(electronicPolicyList[index])
                    ]));
              },
              itemHeight: 91,
              itemsCount: electronicPolicyList.length,
              title: claim_title,
              isTwoLineTitle: true,
            );
          });
    }
  }

  static showExploreHealthPlansBottomSheet(context, String serviceName) {
    showModalBottomSheet(
        barrierColor: color040222.withOpacity(0.7),
        isScrollControlled: true,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.vertical(top: Radius.circular(15.0))),
        context: context,
        builder: (BuildContext bc) {
          return UnlockAckoHealthBenefitsBottomSheet(
            screenName: 'explore_screen',
            serviceName: serviceName,
          );
        });
  }

  static Widget getDashedLine(
      {double height = 1.0, Color color = colorD6D9E0}) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        final boxWidth = constraints.constrainWidth();
        final dashWidth = 10.0;
        final dashHeight = height;
        final dashCount = (boxWidth / (2 * dashWidth)).floor();
        return Flex(
          children: List.generate(dashCount, (_) {
            return SizedBox(
              width: dashWidth,
              height: dashHeight,
              child: DecoratedBox(
                decoration: BoxDecoration(color: color),
              ),
            );
          }),
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          direction: Axis.horizontal,
        );
      },
    );
  }

  static Widget circularWidget(double width, double height, Widget child,
      {Color color = colorD6D9E0}) {
    return Container(
      width: width,
      height: height,
      child: Center(child: child),
      decoration: BoxDecoration(shape: BoxShape.circle, color: color),
    );
  }

  static String getDomainName(Uri uri) {
    String? domain = uri.host;
    if (domain.startsWith("www.")) {
      return domain.substring(3);
    } else if (!domain.startsWith(".")) {
      return "." + domain;
    } else {
      return domain;
    }
  }

  static bool isTfInScrollableWeb(String? url) {
    if (url == null) {
      return false;
    } else {
      return url.contains(
              'lp/new-car-comprehensive/checkout/review?proposal_id') ||
          (Platform.isAndroid && url.contains('health-product/memberdetail')) ||
          (Platform.isAndroid &&
              url.contains('health-product/networkhospitals')) ||
          (Platform.isAndroid && url.contains('p/health'));
    }
  }

  static bool shouldSubtractKeypadSpacing(
      String? url, List<dynamic>? scrollableUrlsMap) {
    if (url == null || scrollableUrlsMap == null)
      return false;
    else {
      return scrollableUrlsMap.any((element) =>
          url.contains(element['url']) &&
          element.containsKey('cut_keypad_height') &&
          element['cut_keypad_height']);
    }
  }

  static bool checkForBottomSheetUrl(
      String? url, List<String>? scrollableUrls) {
    if (url == null || scrollableUrls == null) {
      return false;
    } else {
      bool urlPresent = false;
      scrollableUrls.forEach((url) {
        if (url.containsIgnoreCase(url)) {
          urlPresent = true;
          return;
        }
      });
      return urlPresent;
    }
  }

  static void initializeWebViewSingletonClasses() {
    if (WebViewRegisters.lobs.isEmpty)
      WebViewRegisters.instance.initRegisters([
        CentralWebViewLogic(),
        BaseWebViewLogic(),
        HealthWebViewLogic(),
        LifeWebViewLogic(),
        TravelWebViewLogic(),
      ]);
  }

  static String? getFormattedRedirectionUrl(
      String? url, bool hideAppBar, bool hideHeader) {
    if (url.isNullOrEmpty) {
      return null;
    } else {
      Uri deepLinkUri = Uri.parse(url!);
      Map<String, dynamic> queryParameters = HashMap();
      if (!deepLinkUri.queryParameters.containsKey("hide_app_bar")) {
        queryParameters["hide_app_bar"] = '$hideAppBar';
      }
      if (!deepLinkUri.queryParameters.containsKey("hide_header")) {
        queryParameters["hide_header"] = '$hideHeader';
      }
      String redirectionUrl = Uri(
              scheme: deepLinkUri.scheme,
              path: deepLinkUri.path,
              host: deepLinkUri.host,
              queryParameters: <String, dynamic>{}
                ..addAll(deepLinkUri.queryParameters)
                ..addAll(queryParameters))
          .toString();
      return redirectionUrl;
    }
  }

  static String getKey(String? screen, String text) {
    /*debugPrint(('Key =====' + text + '_' + (screen ?? 'Submit'))
        .replaceAll(' ', '_')
        .toLowerCase());*/
    return (text + '_' + (screen ?? 'Submit'))
        .replaceAll(' ', '_')
        .toLowerCase();
  }

  static ValueKey? getValueKey(String? screen, String? text) {
    if (text == null) {
      return null;
    }
    return ValueKey(getKey(screen, text));
  }

  static openFileOnBrowser(String url) {
    if (Platform.isAndroid) {
      Constants.PLATFORM_CHANNEL.invokeMethod("open_url_intent", {"url": url});
    } else {
      Constants.PLATFORM_CHANNEL.invokeMethod("open_browser", url);
    }
  }

  static openFile(String filePath) async {
    if (Platform.isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      final sdkVersion = androidInfo.version.sdkInt;
      if (sdkVersion > 29) {
        Constants.PLATFORM_CHANNEL
            .invokeMethod("open_file", {"path": filePath});
      } else {
        PermissionsService.requestPermissionWithNoResult(Permission.storage,
            onPermissionResponse: (status) {
          if (status) {
            Constants.PLATFORM_CHANNEL
                .invokeMethod("open_file", {"path": filePath});
          } else {
            Fluttertoast.showToast(
                msg: "Storage permission is required to download policy",
                toastLength: Toast.LENGTH_SHORT,
                gravity: ToastGravity.BOTTOM,
                backgroundColor: colorF6F7FB,
                textColor: color737F8B,
                fontSize: 13.0);
          }
        });
      }
    } else {
      if (await File(filePath).exists()) {
        Constants.PLATFORM_CHANNEL
            .invokeMethod("open_file", {"path": filePath});
      }
    }
  }

  static Map getTempMoHeader() {
    Map<String, String> headers = Map();
    headers["x-user-id"] = "2";
    headers["x-tracker-id"] = "2";
    return headers;
  }

  static void deleteFile(String path) async {
    try {
      final file = File(path);
      await file.delete();
      debugPrint("DELETED SUCCESSFULLY");
    } catch (e) {
      debugPrint(" ERROR IN DELETING FILE: $e");
    }
  }

  static Widget messageLoader(BuildContext context) {
    return Center(
      child: AckoServiceLoadingScreen(),
    );
  }

  static void snackBar(Map data, BuildContext context) {
    List<Widget> children = [];
    if (data['icon'] != null) {
      children.add(Container(
        margin: EdgeInsets.only(left: 12, right: 12),
        child: SvgPicture.asset(
          Util.getAssetImage(assetName: data['icon']),
          height: 24,
          width: 24,
          color: Colors.white,
        ),
      ));
    }
    if (data['icon_no_color'] != null) {
      children.add(Container(
        margin: EdgeInsets.only(left: 12, right: 12),
        child: SvgPicture.asset(
          Util.getAssetImage(assetName: data['icon_no_color']),
          height: 24,
          width: 24,
        ),
      ));
    }
    if (data['message'] != null) {
      children.add(Expanded(
          child: TextEuclidRegular14(
        data['message'],
        textColor: Colors.white,
      )));

      final snackBar = SnackBar(
        behavior: SnackBarBehavior.floating,
        margin: EdgeInsets.all(20),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0)),
        content: Row(
          children: children,
        ),
        duration: Duration(seconds: 3),
        backgroundColor: color040222,
      );

      ScaffoldMessenger.of(context).showSnackBar(snackBar);
    }
  }

  getVariant(int? variant, String? fuelType) {
    if (fuelType == null) {
      return '';
    }
    bool isElectric = fuelType.equalIgnoreCase('electric');
    if (isElectric) {
      String cc = '';
      if (variant != null) {
        cc = (variant / 1000).toString();
        if (cc.length > 3) {
          cc = cc.substring(0, 3);
        }
        return ' ${cc}kWh';
      } else {
        return '';
      }
    } else {
      return variant != null ? ' ${variant}CC' : '';
    }
  }

  static getMaskedName(String? input) {
    if (input == null || input.isEmpty || input.length == 1) {
      return input;
    } else {
      StringBuffer buffer = StringBuffer();
      List<String> inputCharacters = input.split('');
      for (int i = 0; i < inputCharacters.length; i++) {
        if (inputCharacters[i] == ' ') {
          buffer.write(inputCharacters[i]);
        } else if (i % 2 == 0) {
          buffer.write('*');
        } else {
          buffer.write(inputCharacters[i]);
        }
      }
      return buffer.toString();
    }
  }

  static String getPlatform() {
    return Platform.isAndroid ? 'app_android' : 'app_ios';
  }

  static Map<String, dynamic> getAsyncWidgetJson(String url, String id) {
    return {
      "attributes": {
        "url": url,
        "id": id,
      },
    };
  }

  static Future<void> setCookieValue(CookieManager cookieManager,
      {required WebUri url,
      required String name,
      required String value,
      String? domain,
      String path = "/",
      bool webviewV2 = true}) async {
    try {
      await cookieManager.setCookie(
          url: url, name: name, value: value, path: path, domain: domain);
    } catch (e) {
      logException(e, trace: StackTrace.current);
    }
  }

  static String? getServiceTypeRoute(ServiceType serviceType) {
    final serviceTypeRoutes = {
      ServiceType.VALUATION: Routes.VERIFY_DETAILS,
      ServiceType.CHALLAN: Routes.PENDING_CHALLAN,
      ServiceType.PUC_VALIDITY: Routes.PUC_VALIDITY,
      ServiceType.RTO: Routes.RTO_DETAILS,
      ServiceType.FASTAG_RECHARGE: Routes.FASTAG_DECISION_PAGE,
    };
    final route = serviceTypeRoutes[serviceType];

    return route ?? null;
  }

  /// write code to have correct events
  static String getPolicySubCardType(PolicyStatusSubCards statusCard) {
    if ((statusCard.type == 'pending_policy' &&
            statusCard.title?.contains('KYC') == true) ||
        statusCard.type == "KYC") return 'kyc_pending';
    return statusCard.type ?? '';
  }

  static Map<String, dynamic> getPolicySubCardProperties(
    PolicyStatusSubCards statusCard,
    String? phone,
    bool isPolicyExpired,
    String? productType,
    String? sourceType, {
    bool isHomePage = false,
  }) {
    String type = getPolicySubCardType(statusCard);
    String? product = productType ?? statusCard.product;
    if (product == auto) {
      product =
          statusCard.redirectUrl?.contains('car') == true ? 'car' : 'bike';
    }
    Map<String, dynamic> properties = {
      "from_page": HomePageUtils.getTabName(isHomePage),
      'product': product ?? 'car',
      'proposal_number': statusCard.id ?? '',
      'journey': type,
      'platform': Util.getPlatform(),
      'policy_status': isPolicyExpired ? 'expired' : 'active',
      'policy_number': statusCard.id ?? '',
      "visit_entry_tag": "entry",
      "source_type": sourceType ?? '',
    };
    return properties;
  }

  static String getHomeTabString(int index) {
    switch (index) {
      case 0:
        return 'home';
      case 1:
        return 'policy';
      case 2:
        return 'explore';
      case 3:
        return 'support';
      default:
        return '';
    }
  }
}

extension StringCompare on String {
  bool equalIgnoreCase(String otherString) =>
      this.toLowerCase() == otherString.toLowerCase();
}

extension FormatDeepLink on String {
  String formatLink() => this.startsWith("https")
      ? this
      : this.startsWith("/")
          ? Constants.BASE_URL + this.replaceFirst("/", "")
          : Constants.BASE_URL + this;
}

extension StringCasingExtension on String {
  String toCapitalized() =>
      length > 0 ? '${this[0].toUpperCase()}${substring(1).toLowerCase()}' : '';

  String toTitleCase() => replaceAll(RegExp(' +'), ' ')
      .split(' ')
      .map((str) => str.toCapitalized())
      .join(' ');

  String toSentenceCase() {
    if (this.isNullOrEmpty) return this;
    return this[0].toUpperCase() + this.substring(1).toLowerCase();
  }

  String snakeCaseToHumanReadable() {
    return this.split('_').map((word) {
      return word.substring(0, 1).toUpperCase() + word.substring(1);
    }).join(' ');
  }

  String calculateAge() {
    DateTime birthDate = DateTime.parse(this);
    DateTime currentDate = DateTime.now();

    int age = currentDate.year - birthDate.year;
    if (currentDate.month < birthDate.month ||
        (currentDate.month == birthDate.month &&
            currentDate.day < birthDate.day)) {
      age--;
    }

    return age.toString();
  }

  String extractNumber() {
    return replaceAll(RegExp(r'[^0-9]'), '');
  }
}

extension DateFormatting on String {
  String formatDate(String date) =>
      DateFormat.yMMMd().format(DateTime.parse(date));
}

extension Round on double {
  double roundToPrecision(int n) {
    int fac = pow(10, n) as int;
    return (this * fac).round() / fac;
  }
}

extension FileExtension on String {
  String? extension() {
    var lastIndex = this.lastIndexOf(".") + 1;
    if (lastIndex != 0) {
      return this.substring(lastIndex, this.length);
    }
    return null;
  }
}

extension CapExtension on String {
  String toCapitalCase() {
    var lowerCased = this.toLowerCase();
    return lowerCased[0].toUpperCase() + lowerCased.substring(1);
  }

  String get inCaps => '${this[0].toUpperCase()}${this.substring(1)}';

  String get allInCaps => this.toUpperCase();
}

extension TrimSpaceExtension on String {
  String trimSpacesInString() {
    return this.replaceAll(" ", '');
  }
}

class FileChooserCallback {
  void filesChosen(List<File> files) {
    for (File f in files) debugPrint("file chosen=============>>${f.path}");
  }

  void error(String err) {
    debugPrint(err);
  }
}

extension StringFormatExtension on String {
  String? format(List arguments) => sprintf(this, arguments);
}

enum Result { RESULT_OK, RESULT_CANCEL, RESULT_BACK }

enum AssetHeaderSources { vas_challan, vas_puc, vas_rto }

enum AssetHeaderUseCases { latest }

enum SearchApiSources { vas_fastag, rto }

enum RateLimitSources { ASSET_PAGE, CREATE_ASSET, LOOKUP_SCREEN }

enum RateLimitUseCases { latest }

Widget getShimmer() {
  return Container(
      margin: EdgeInsets.only(top: 30.0, left: 20.0, right: 20.0),
      child: Column(crossAxisAlignment: CrossAxisAlignment.stretch, children: [
        _getShimmerItem(),
        Padding(
          padding: EdgeInsets.only(top: 30.0, bottom: 30.0),
          child: _getShimmerItem(),
        ),
        _getShimmerItem(),
        Padding(
          padding: EdgeInsets.only(top: 30.0, bottom: 30.0),
          child: _getShimmerItem(),
        ),
      ]));
}

Widget _getShimmerItem() {
  return Shimmer.fromColors(
      child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Container(
                width: 160,
                height: 10.0,
                color: Colors.white,
                margin: EdgeInsets.only(left: 20.0, bottom: 10.0)),
            Container(
                margin: EdgeInsets.only(left: 20.0, bottom: 10.0),
                width: 80,
                height: 10.0,
                color: Colors.white),
            Container(
                margin: EdgeInsets.only(left: 20.0),
                width: 40.0,
                height: 10.0,
                color: Colors.white)
          ]),
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!);
}

Widget lockdownDelayWidget() {
  return Container(
    padding: const EdgeInsets.all(15.0),
    decoration: BoxDecoration(
      color: colorFFEEEC,
      borderRadius: BorderRadius.circular(8.0),
    ),
    child: Row(
      children: [
        Container(
          width: 30,
          height: 30,
          child: SvgPicture.asset(
              Util.getAssetImage(assetName: 'ic_hour_glass.svg')),
        ),
        Container(width: 15),
        Expanded(
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            TextEuclidSemiBold(Constants.lockdownDelayData.title),
            Container(height: 5),
            TextEuclidMediumL2(
              Constants.lockdownDelayData.subtitle,
              textColor: color282E33,
              letterSpacing: 0.3,
              textSize: 10.0,
            )
          ]),
        )
      ],
    ),
  );
}

getLockdownDelayData() async {
  // final FirebaseRemoteConfig remoteConfig = FirebaseRemoteConfig.instance;
  try {
    // await remoteConfig.fetchAndActivate();
    final lockdownDelayJson = RemoteConfigInstance.instance
        .getData(RemoteConfigKeysSet.lockdown_delay);
    Constants.lockdownDelayData =
        LockdownDelayData.fromJson(jsonDecode(lockdownDelayJson));
  } catch (e) {
    debugPrint("Remote Config exception: " + e.toString());
  }
}

double distanceBetween(
  double startLatitude,
  double startLongitude,
  double endLatitude,
  double endLongitude,
) {
  var earthRadius = 6378137.0;
  var dLat = _toRadians(endLatitude - startLatitude);
  var dLon = _toRadians(endLongitude - startLongitude);

  var a = pow(sin(dLat / 2), 2) +
      pow(sin(dLon / 2), 2) *
          cos(_toRadians(startLatitude)) *
          cos(_toRadians(endLatitude));
  var c = 2 * asin(sqrt(a));

  return earthRadius * c;
}

_toRadians(double degree) {
  return degree * pi / 180;
}

logoutHandling() async {
  var deviceId =
      await Constants.PLATFORM_CHANNEL.invokeMapMethod("get_device_id");
  if (deviceId != null) {
    await DeviceInfoRepository().logoutUser(deviceId['device_id']);
  }

  globalCommonDataStoreBloc = null;

  ///The following is used in iOS and android .
  Constants.PLATFORM_CHANNEL.invokeMethod("log_out");

  ///The following is not used in the iOS app , its used only in android

  Constants.PLATFORM_CHANNEL.invokeMethod("user_logged_out");
  await LoginRepository().logoutUser();
  await SessionManager.instance.handleLogout();
}

Decoration? getLinearGradient(List<Color>? colors, AlignmentGeometry begin,
    AlignmentGeometry end, double? radius) {
  if (colors == null) {
    return null;
  }
  return BoxDecoration(
      borderRadius: BorderRadius.circular(radius ?? 0),
      gradient: LinearGradient(colors: colors, begin: begin, end: end));
}

List<PermissionData> setPermissionData() {
  List<PermissionData> _permission = [];
  _permission
    ..add(PermissionData(
        image: "assets/images/ic_camera_permission.svg",
        title: "Access camera",
        description: "Provide documents via phone camera."))
    ..add(PermissionData(
        title: "Enable location",
        image: "assets/images/ic_location_permission.svg",
        description: "Vehicle inspection at your doorstep."))
    ..add(PermissionData(
        image: "assets/images/ic_gallery_permission.svg",
        title: "Access photos/media",
        description: "Upload documents from your gallery."))
    ..add(PermissionData(
        image: "assets/images/ic_calendar_permission.svg",
        title: "Access calendar",
        description: "Timely renewal reminders."));

  return _permission;
}

bool isList(dynamic value) => (value is List);

Future<Uint8List?> createImageFromWidget(BuildContext context, Widget widget,
    {Duration? wait, Size? logicalSize, Size? imageSize}) async {
  final repaintBoundary = rendering.RenderRepaintBoundary();

  logicalSize ??=
      View.of(context).physicalSize / View.of(context).devicePixelRatio;
  imageSize ??= View.of(context).physicalSize;

  assert(logicalSize.aspectRatio == imageSize.aspectRatio,
      'logicalSize and imageSize must not be the same');

  final renderView = rendering.RenderView(
      child: rendering.RenderPositionedBox(
          alignment: Alignment.center, child: repaintBoundary),
      configuration: rendering.ViewConfiguration(
        physicalConstraints: BoxConstraints.tight(logicalSize),
        logicalConstraints: BoxConstraints.tight(logicalSize),
        devicePixelRatio: 1,
      ),
      view: View.of(context) //PlatformDispatcher.instance.views.first,
      );

  final pipelineOwner = rendering.PipelineOwner();
  final buildOwner = BuildOwner(focusManager: FocusManager());

  pipelineOwner.rootNode = renderView;
  renderView.prepareInitialFrame();

  final rootElement = RenderObjectToWidgetAdapter<RenderBox>(
      container: repaintBoundary,
      child: Directionality(
        textDirection: rendering.TextDirection.ltr,
        child: widget,
      )).attachToRenderTree(buildOwner);

  buildOwner.buildScope(rootElement);

  if (wait != null) {
    await Future.delayed(wait);
  }

  buildOwner
    ..buildScope(rootElement)
    ..finalizeTree();

  pipelineOwner
    ..flushLayout()
    ..flushCompositingBits()
    ..flushPaint();

  final image = await repaintBoundary.toImage(
      pixelRatio: imageSize.width / logicalSize.width);
  final byteData = await image.toByteData(format: ImageByteFormat.png);

  return byteData?.buffer.asUint8List();
}

class AckoDataDefaultResponse {
  Map<String, dynamic>? component;

  AckoDataDefaultResponse({this.component});

  AckoDataDefaultResponse.fromJson(Map<String, dynamic> json) {
    component = json['component'] != null ? json['component'] : null;
  }
}
