import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/view/AckoText.dart';
import 'package:acko_flutter/common/view/dotted_border.dart';
import 'package:acko_flutter/travel_insurance/pdp/home/<USER>/pdp_home_bloc_singleton.dart';
import 'package:acko_flutter/travel_insurance/pdp/policy_detail/bloc/policy_detail_bloc.dart';
import 'package:acko_flutter/travel_insurance/shared/extension/num_extension.dart';
import 'package:acko_flutter/travel_insurance/shared/extension/string_extension.dart';
import 'package:acko_flutter/travel_insurance/shared/util/event/travel_event.dart';
import 'package:acko_flutter/travel_insurance/shared/util/event/travel_event_key.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:collection/collection.dart';
import 'package:design_module/design_module.dart';
import 'package:design_module/typography/typography.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:utilities/constants/constants.dart';

import '../../../../shared/util/enum.dart';
import '../../../home/<USER>/pdp_model.dart';
import '../../../nominee/view/widget/nominee_widget.dart';
import '../widget/traveller_data_widget.dart';

class PolicyDetailPage extends StatefulWidget {
  final PdpModel model;

  const PolicyDetailPage({Key? key, required this.model}) : super(key: key);

  @override
  State<PolicyDetailPage> createState() => _PolicyDetailPageState();
}

class _PolicyDetailPageState extends State<PolicyDetailPage> {
  @override
  void initState() {
    _eventViewPolicy();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _getPolicyDetails(),
          32.spaceHeight,
          _getTripDetailsBox(),
          32.spaceHeight,
          _getTravellersList(),
          32.spaceHeight,
          _cancelPolicy(),
        ],
      ),
    );
  }

  _getPolicyDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        hSmallText.text(
          'Policy details',
          textColor: color121212,
        ),
        if (widget.model.policyDetails?.sumInsured.isNotNullOrEmpty ??
            false) ...[
          Padding(
            padding: EdgeInsets.only(top: 16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                TextEuclidRegular14(
                  'Coverage amount',
                  textColor: color5B5675,
                ),
                52.spaceWidth,
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      h4BoldText.text(
                        widget.model.policyDetails?.sumInsured ?? '-',
                      ),
                      p2Text.text(
                        'per person',
                        textAlign: TextAlign.right,
                        textColor: color121212,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          16.spaceHeight,
          Divider(
            thickness: 1.0,
            height: 10.0,
            color: colorE7E7F0,
          ),
        ],
        TravellerDataWidget(
          title: 'Policy number',
          data: widget.model.policyDetails?.policyNumber,
        ),
      ],
    );
  }

  _getTripDetailsBox() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        hSmallText.text(
          'Trip details',
          textColor: color121212,
        ),
        TravellerDataWidget(
            title: 'Where',
            data: widget.model.tripDetails?.countryList?.join(', ')),
        16.spaceHeight,
        Divider(
          thickness: 1.0,
          height: 10.0,
          color: colorE7E7F0,
        ),
        TravellerDataWidget(
          title: 'From',
          data: _startDate(),
        ),
        16.spaceHeight,
        Divider(
          thickness: 1.0,
          height: 10.0,
          color: colorE7E7F0,
        ),
        TravellerDataWidget(
          title: 'To',
          data: _endDate(),
        ),
        _editPolicy(),
      ],
    );
  }

  String _startDate() {
    String startDate = widget.model.tripDetails?.startDate?.value
            ?.dateFormat('dd-MM-yyyy', 'dd MMM yyyy') ??
        '';
    return startDate;
  }

  String _endDate() {
    String endDate = widget.model.tripDetails?.endDate?.value
            .dateFormat('dd-MM-yyyy', 'dd MMM yyyy') ??
        '';
    return endDate;
  }

  String _getCountriesListString() {
    String _countryText = '';
    List<String?> countryList = widget.model.tripDetails?.countryList ?? [];
    _countryText = countryList[0] ?? '';
    if (countryList.length > 1)
      _countryText = _countryText +
          ", ${countryList[1]}" +
          ((countryList.length > 2) ? ' +${countryList.length - 2}' : '');

    return _countryText;
  }

  _getTravellersList() {
    return Column(
      children: (widget.model.insuredDetailList ?? [])
          .mapIndexed((index, data) => _getTravellerDetailCard(data, index))
          .toList(),
    );
  }

  Widget _getTravellerDetailCard(InsuredDetailList insured, int index) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        hSmallText.text(
          'Traveler - ${insured.name?.value ?? ''}',
          textColor: color121212,
        ),
        TravellerDataWidget(
          title: 'Date of birth',
          data: (insured.dob?.value.isNullOrEmpty ?? false)
              ? 'N/A'
              : insured.dob?.value,
        ),
        16.spaceHeight,
        Divider(
          thickness: 1.0,
          height: 10.0,
          color: colorE7E7F0,
        ),
        TravellerDataWidget(
          title: 'Mobile number',
          data: (insured.phone?.value.isNullOrEmpty ?? false)
              ? 'N/A'
              : insured.phone?.value,
        ),
        16.spaceHeight,
        Divider(
          thickness: 1.0,
          height: 10.0,
          color: colorE7E7F0,
        ),
        TravellerDataWidget(
          title: 'E-mail',
          data: (insured.email?.value.isNullOrEmpty ?? false)
              ? 'N/A'
              : insured.email?.value,
        ),
        16.spaceHeight,
        Divider(
          thickness: 1.0,
          height: 10.0,
          color: colorE7E7F0,
        ),
        TravellerDataWidget(
          title: 'Passport number',
          data: (insured.passport?.value.isNullOrEmpty ?? false)
              ? 'N/A'
              : insured.passport?.value,
        ),
        16.spaceHeight,
        Divider(
          thickness: 1.0,
          height: 10.0,
          color: colorE7E7F0,
        ),
        TravellerDataWidget(
          title: 'Medical conditions',
          data: insured.medicalConditions?.value ?? 'N/A',
        ),
        32.spaceHeight,
        t2SemiBoldText.text(
          'Nominee',
          textColor: color121212,
        ),
        16.spaceHeight,
        if ((insured.nominee?.name?.value ?? '').isEmpty)
          InkWell(
            onTap: () => insured.canEndorse ? _addNomineeSheet(insured) : null,
            child: SizedBox(
              width: double.infinity,
              child: DottedBorder(
                padding: EdgeInsets.all(12),
                color: colorDEDEDE,
                radius: Radius.circular(12),
                borderType: BorderType.RRect,
                dashPattern: [4, 1],
                child: Center(
                  child: Column(
                    children: [
                      Container(
                        padding: EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: colorF5F5F5,
                        ),
                        child: Icon(
                          Icons.add,
                          size: 24,
                        ),
                      ),
                      8.spaceHeight,
                      t4Text.text(
                        'Nominee not added',
                        textColor: color121212,
                      ),
                      4.spaceHeight,
                      t4Text.text(
                        'Add nominee',
                        textColor: color1B73E8,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        if (insured.nominee != null) ...[
          if ((insured.nominee?.name?.value ?? "").isNotNullOrEmpty)
            TravellerDataWidget(
                title: 'Name', data: insured.nominee?.name?.value ?? ""),
          if ((insured.nominee?.relationship?.value ?? "").isNotNullOrEmpty)
            TravellerDataWidget(
                title: 'Relationship',
                data: insured.nominee?.relationship?.value ?? ""),
          if ((insured.nominee?.phone?.value ?? "").isNotNullOrEmpty)
            TravellerDataWidget(
                title: 'Mobile number',
                data: insured.nominee?.phone?.value ?? ""),
        ],
        _editPolicy(),
        32.spaceHeight,
      ],
    );
  }

  Widget _editPolicy() {
    if (widget.model.policyDetails?.canEdit ?? false) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          28.spaceHeight,
          InkWell(
            onTap: () {
              Navigator.pushNamed(context, Routes.EDIT_POLICY);
            },
            child: t4Text.text(
              'Edit details',
              textColor: color1B73E8,
            ),
          ),
        ],
      );
    } else {
      return SizedBox.shrink();
    }
  }

  Widget _cancelPolicy() {
    if (widget.model.policyDetails?.canCancel ?? false) {
      return Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            InkWell(
              onTap: () => PdpHomeBlocSingleton.blocInstance.apiCancelInit(),
              child: t4Text.text(
                'Cancel policy',
                textColor: colorD83D37,
              ),
            ),
            32.spaceHeight,
          ],
        ),
      );
    } else {
      return SizedBox.shrink();
    }
  }

  void _addNomineeSheet(InsuredDetailList? insured) async {
    await showModalBottomSheet(
      context: context,
      builder: (bsc) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: MultiBlocProvider(
          providers: [
            BlocProvider.value(
              value: PdpHomeBlocSingleton.blocInstance,
            ),
            BlocProvider(
              create: (context) => PolicyDetailBloc(),
            ),
          ],
          child: NomineeWidget(
            isWidget: false,
            travellerDetail: insured!,
          ),
        ),
      ),
      isDismissible: false,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
    );
  }

  void _eventViewPolicy() {
    TravelEvent.push(
      eventName: TravelEventkey.view_page_int_policy_details,
      keyValue: {
        EventKey.page: 'int_travel_policy_details_page',
        EventKey.from_page: 'int_travel_view_page_pdp'
      },
    );
  }
}
