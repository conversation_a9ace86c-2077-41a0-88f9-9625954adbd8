import 'dart:math' as math;

import 'package:acko_core_utilities/deeplink_handler/DeeplinkHandler.dart';
import 'package:acko_flutter/common/util/AckoTextStyle.dart';
import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/view/AckoText.dart';
import 'package:acko_flutter/feature/acko_services/ui/acko_service_loading_screen.dart';
import 'package:acko_flutter/travel_insurance/pdp/home/<USER>/pdp_home_bloc_singleton.dart';
import 'package:acko_flutter/travel_insurance/pdp/home/<USER>/sheet/add_passport_sheet.dart';
import 'package:acko_flutter/travel_insurance/pdp/home/<USER>/widget/overlay_widget.dart';
import 'package:acko_flutter/travel_insurance/pdp/home/<USER>/widget/pdp_alert_widget.dart';
import 'package:acko_flutter/travel_insurance/pdp/home/<USER>/widget/pending_card_widget.dart';
import 'package:acko_flutter/travel_insurance/pdp/home/<USER>/widget/policy_action_grid.dart';
import 'package:acko_flutter/travel_insurance/pdp/home/<USER>/widget/travel_policy_header_view.dart';
import 'package:acko_flutter/travel_insurance/pdp/plan_benefit/bloc/plan_benefit_bloc.dart';
import 'package:acko_flutter/travel_insurance/pdp/plan_benefit/view/widgets/pdp_coverage_tab.dart';
import 'package:acko_flutter/travel_insurance/pdp/policy_detail/view/page/policy_detail_page.dart';
import 'package:acko_flutter/travel_insurance/shared/extension/num_extension.dart';
import 'package:acko_flutter/travel_insurance/shared/extension/string_extension.dart';
import 'package:acko_flutter/travel_insurance/shared/util/enum.dart';
import 'package:acko_flutter/travel_insurance/shared/util/event/travel_event.dart';
import 'package:acko_flutter/travel_insurance/shared/util/event/travel_event_key.dart';
import 'package:acko_flutter/travel_insurance/shared/util/travel_utility.dart';
import 'package:acko_flutter/travel_insurance/shared/view/widget/button_widget.dart';
import 'package:acko_flutter/util/health/utils.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:growthbook_sdk_flutter/growthbook_sdk_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:policy_details/policy_details.dart';
import 'package:sdui/sdui.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/core/string_extensions.dart';
import 'package:utilities/remote_config/remote_config.dart';

import '../../../../../common/util/scroll_indicator.dart';
import '../../../../../common/util/strings.dart';
import '../../../../../common/view/FullPageLoader.dart';
import '../../../../../feature/acko_services/ui/acko_services_initial_screen.dart';
import '../../../../../util/Utility.dart';
import '../../../../claim/damages/model/claim_damage_model.dart';
import '../../../../shared/library/flutter_linkify/flutter_linkify.dart';
import '../../../../shared/view/widget/expansion_widget.dart';
import '../../../fnol/view/sheet/fnol_option_sheet.dart';
import '../../../fnol/view/sheet/fnol_sheet.dart';
import '../../../fnol/view/sheet/old_fnol_option_sheet.dart';
import '../../../policy_detail/bloc/policy_detail_bloc.dart';
import '../../bloc/pdp_home_bloc.dart';
import '../../model/pdp_model.dart';

class PdpHomePage extends StatefulWidget {
  final String policyId;
  final String? utmParam;
  final bool toClaim;
  final bool toEdit;

  PdpHomePage({
    Key? key,
    required this.policyId,
    this.utmParam,
    this.toClaim = false,
    this.toEdit = false,
  }) : super(key: key);

  @override
  State<PdpHomePage> createState() => _PdpHomePageState();
}

class _PdpHomePageState extends State<PdpHomePage> with RouteAware {
  final ScrollController _scrollController = ScrollController();
  String eventFromPage = 'int_travel_landing_screen';
  String eventPage = 'int_travel_pdp';
  OverlayEntry? overlay;
  OverlayState? overlayState;
  int _pageViewIndex = 0;
  GrowthBookSDK? _gb;
  bool _enableTravelClaim = false;
  bool _enableTravelPdpAlerts = false;
  late PlanBenefitBloc _benefitsBloc;

  @override
  void initState() {
    super.initState();
    TravelEvent.assignUtm(widget.utmParam);
    _pushViewEvent();
    _enableTravelClaim = RemoteConfigInstance.instance
            .getData(RemoteConfigKeysSet.enableTravelClaim) ??
        false;
    _enableTravelPdpAlerts = RemoteConfigInstance.instance
            .getData(RemoteConfigKeysSet.enableTravelPdpAlerts) ??
        false;
    PdpHomeBlocSingleton.blocInstance.policyId = widget.policyId;
    PdpHomeBlocSingleton.blocInstance.apiPdpHome();
    _benefitsBloc = BlocProvider.of<PlanBenefitBloc>(context);
    _benefitsBloc.policyId = widget.policyId;
    _benefitsBloc.apiPlanCoverage();
    if (!Constants.IS_PROD) {
      overlayState = Overlay.of(context);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _insertOverlay(context);
        overlayState?.insert(overlay!);
      });
    }
  }

  @override
  void dispose() {
    overlay?.remove();
    super.dispose();
  }

  void _insertOverlay(BuildContext context) {
    overlay = OverlayEntry(
      builder: (context) => OverlayWidget(),
    );
  }

  void _handleFnolResponse(ClaimDamageData? data) {
    if (data != null) {
      if (data.claimModeList?.length == 1) {
        if (data.claimModeList?.first.id == 'cashless_treatment' ||
            data.claimModeList?.first.damageList?.length == 1) {
          //move to expectation setting page
          _navigateClaimExpectation(
              policyId: widget.policyId,
              claimType: data.claimModeList!.first.id!,
              damageId: data.claimModeList!.first.damageList!.first.id!,
              perilId: data.perilId!);
        } else {
          _navigateClaimDamage(
            claimModeList: data.claimModeList!.first,
            title: data.title!,
            subTitle: data.subTitle ?? '',
            iconUrl: data.iconUrl!,
            policyId: widget.policyId,
            perilId: data.perilId!,
          );
        }
      } else if (data.claimModeList?.length == 2) {
        _showFnolOptionSheet(data.claimModeList!, data);
      }
    }
  }

  void _navigateClaimDamage({
    required ClaimDamageModeList claimModeList,
    required String title,
    required String subTitle,
    required String iconUrl,
    required String policyId,
    required String perilId,
  }) {
    Navigator.pushNamed(context, Routes.CLAIM_DAMAGES, arguments: {
      'claimModeList': claimModeList,
      'title': title,
      'subTitle': subTitle,
      'iconUrl': iconUrl,
      'policyId': widget.policyId,
      'perilId': perilId,
      'policyNumber': PdpHomeBlocSingleton
              .blocInstance.pdpData?.policyDetails?.policyNumber ??
          '',
    });
  }

  void _navigateClaimExpectation({
    required String policyId,
    required String claimType,
    required String damageId,
    required String perilId,
  }) {
    Navigator.pushNamed(
      context,
      Routes.CLAIM_EXPECTATION,
      arguments: {
        'policyId': widget.policyId,
        'policyNumber': PdpHomeBlocSingleton
                .blocInstance.pdpData?.policyDetails?.policyNumber ??
            '',
        'claimType': claimType,
        'damageId': damageId,
        'perilId': perilId
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        body: RefreshIndicator(
          onRefresh: () async {
            PdpHomeBlocSingleton.blocInstance.apiPdpHome();
          },
          child: BlocConsumer<PdpHomeBloc, PdpHomeState>(
            listenWhen: (previous, current) =>
                current is PolicyCancelInitData ||
                current is PolicyCancelInitError ||
                current is PolicyCancelInitLoading ||
                current is PdpHomeData ||
                current is PolicyDownloadStart ||
                current is PolicyDownloadEnd ||
                current is ClaimDamageLoading ||
                current is ClaimDamageFailure ||
                current is ClaimDamageSuccess,
            listener: (context, state) {
              if (state is PolicyCancelInitLoading ||
                  state is PolicyDownloadStart ||
                  state is ClaimDamageLoading) {
                FullPageLoader.instance.showFullPageLoader(context);
              } else if (state is PolicyDownloadEnd) {
                FullPageLoader.instance.dismissFullPageLoader(context);
                if (state.hasError ?? false) {
                  TravelUtility.showSnackbar(
                      context: context,
                      msg: state.messageOrPdfLink ?? no_policy_link_error);
                } else {
                  _downloadOrSharePolicy(
                      state.messageOrPdfLink!, state.policyAction!);
                }
              } else if (state is PolicyCancelInitError) {
                FullPageLoader.instance.dismissFullPageLoader(context);
                TravelUtility.showSnackbar(context: context, msg: state.error);
              } else if (state is PolicyCancelInitData) {
                FullPageLoader.instance.dismissFullPageLoader(context);
                Navigator.pushNamed(context, Routes.REQUEST_CANCELLATION,
                    arguments: {
                      'requestPolicyCancel': state.requestPolicyCancel
                    });
              } else if (state is PdpHomeData) {
                if (state.pdpModel.policyDetails?.status ==
                        PolicyStatus.COMPLIENCE_PENDING &&
                    state.pdpModel.expiredSubTitle == null &&
                    state.pdpModel.insuredDetailList!.every(
                        (insured) => insured.passport!.canEndorse == true)) {
                  _passportSheet();
                } else if (widget.toClaim) {
                  Navigator.pushReplacementNamed(context, Routes.PERIL,
                      arguments: {
                        'perilList': PdpHomeBlocSingleton
                                .blocInstance.pdpData?.perilListRefresh ??
                            [],
                        'policyId': widget.policyId,
                      });
                } else if (widget.toEdit) {
                  Navigator.pushReplacementNamed(context, Routes.EDIT_POLICY);
                }
              } else if (state is ClaimDamageFailure) {
                FullPageLoader.instance.dismissFullPageLoader(context);
                TravelUtility.showSnackbar(
                    context: context, msg: state.errorMessage!);
              } else if (state is ClaimDamageSuccess) {
                FullPageLoader.instance.dismissFullPageLoader(context);
                _handleFnolResponse(state.model.data);
              }
            },
            buildWhen: (previous, current) =>
                current is PdpHomeLoading ||
                current is PdpHomeData ||
                current is PdpHomeError,
            builder: (context, state) {
              if (state is PdpHomeLoading || state is PolicyCancelInitLoading) {
                return Center(child: AckoServiceLoadingScreen());
              } else if (state is PdpHomeData) {
                return ListView(
                  padding: EdgeInsets.zero,
                  children: [
                    _buildHeader(),
                    TabBar(
                      labelPadding: const EdgeInsets.symmetric(
                        vertical: 20,
                      ),
                      onTap: (value) {
                        PdpHomeBlocSingleton.blocInstance
                            .updateSelectedIndex(value);

                        HapticFeedback.mediumImpact();
                      },
                      indicatorWeight: 4,
                      indicatorSize: TabBarIndicatorSize.tab,
                      enableFeedback: true,
                      indicatorColor: color121212,
                      labelColor: color121212,
                      splashFactory: NoSplash.splashFactory,
                      dividerHeight: 1,
                      dividerColor: colorE8E8E8,
                      unselectedLabelStyle: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                        color: color121212,
                        fontFamily: 'euclid_circularB_regular',
                      ),
                      labelStyle: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: color121212,
                        fontFamily: 'euclid_circularB_regular',
                      ),
                      tabs: [
                        const Text(
                          StringsLocale.overviewEn,
                        ),
                        Container(
                          width: double.infinity,
                          padding: EdgeInsets.zero,
                          height: 22,
                          decoration: const BoxDecoration(
                            border: Border(
                              right: BorderSide(
                                color: colorE8E8E8,
                              ),
                            ),
                          ),
                          child: const Text(
                            StringsLocale.coveragesEn,
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 32,
                    ),
                    BlocBuilder(
                      bloc: PdpHomeBlocSingleton.blocInstance,
                      builder: (index, state) {
                        if (PdpHomeBlocSingleton.blocInstance.selectedTab ==
                            0) {
                          return PolicyDetailPage(
                            model: PdpHomeBlocSingleton.blocInstance.pdpData!,
                          );
                        } else {
                          return PdpCoverageTab();
                        }
                      },
                    ),
                  ],
                );
              } else if (state is PdpHomeError) {
                return Center(
                  child: AckoServicesIntiailScreen(
                    isOutlinedButton: true,
                    title: something_went_wrong,
                    imgUrl: Util.getAssetImage(assetName: 'ic_bucket_drop.svg'),
                    btnTitle: try_again,
                    onTap: () {
                      PdpHomeBlocSingleton.blocInstance.apiPdpHome();
                    },
                  ),
                );
              }
              return SizedBox();
            },
          ),
        ),
      ),
    );
  }

  Container _buildHeader() {
    PdpModel pdpData = PdpHomeBlocSingleton.blocInstance.pdpData!;

    return Container(
      decoration: const BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.bottomLeft,
          end: Alignment.topRight,
          colors: [
            Color(0xFF121212),
            Color(0xFF121212),
            Color(0xFF7B18c9),
          ],
        ),
      ),
      child: ListView(
        padding: EdgeInsets.zero,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        children: [
          const PDPAppBar(),
          if (_enableTravelPdpAlerts) PdpAlertWidget(policyId: widget.policyId),
          Container(
            margin: const EdgeInsets.fromLTRB(16, 0, 16, 8),
            padding: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  colorFFFFFF.withOpacity(0.024),
                  colorFFFFFF.withOpacity(0.12),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              border: Border(
                left: BorderSide(
                  color: colorFFFFFF.withOpacity(0.16),
                ),
                top: BorderSide(
                  color: colorFFFFFF.withOpacity(0.16),
                ),
              ),
              borderRadius: const BorderRadius.all(
                Radius.circular(
                  16,
                ),
              ),
            ),
            child: Column(
              children: [
                TravelPolicyViewHeaderText(
                  bgColor: Color(0xFFEBFBEE),
                  headerText: PdpHomeBlocSingleton.blocInstance.formatCountry(),
                  subHeaderText: pdpData.expiredSubTitle ??
                      PdpHomeBlocSingleton.blocInstance.formatTripDetail(),
                  policyStatusText:
                      PdpHomeBlocSingleton.blocInstance.pdpDateFormat(),
                  statusColor: color0B753E,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SDUIText(
                              value: pdpData.policyDetails!.sumInsured
                                      .isNotNullAndEmpty
                                  ? 'Medical coverage'
                                  : 'Plan selected',
                              textColor: colorFFFFFF.withOpacity(0.6),
                              textStyle: 'c1',
                            ),
                            SDUIText(
                              value: pdpData.policyDetails!.sumInsured
                                      .isNotNullAndEmpty
                                  ? '${pdpData.policyDetails!.sumInsured!}/person'
                                  : (pdpData.policyDetails!.selectedPlan ??
                                      '-'),
                              textColor: colorFFFFFF,
                              textStyle: 'lSmall',
                              alignment: TextAlign.left,
                              maxLines: 2,
                            ),
                          ],
                        ),
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          SDUIText(
                            value: StringsLocale.policyNumberEn,
                            textColor: colorFFFFFF.withOpacity(0.6),
                            textStyle: 'c1',
                          ),
                          SDUIText(
                            value: pdpData.policyDetails!.policyNumber,
                            textColor: colorFFFFFF,
                            textStyle: 'lSmall',
                            alignment: TextAlign.right,
                            maxLines: 2,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          PolicyActionGrid(
            actionList:
                PdpHomeBlocSingleton.blocInstance.pdpData?.actionListRefresh ??
                    [],
            policyId: widget.policyId,
          ),
          SizedBox(
            height: 88,
            child: PageView.builder(
              allowImplicitScrolling: true,
              onPageChanged: (value) => setState(() {
                _pageViewIndex = value.toInt();
              }),
              itemCount: PdpHomeBlocSingleton
                      .blocInstance.pdpData?.pendingActions?.length ??
                  0,
              scrollDirection: Axis.horizontal,
              itemBuilder: (context, index) {
                PendingActions? detail = PdpHomeBlocSingleton
                    .blocInstance.pdpData?.pendingActions?[index];

                return detail == null
                    ? SizedBox()
                    : PendingCardWidget(
                        cardDetail: detail,
                        onTap: () => _onClickPendingAction(detail),
                      );
              },
            ),
          ),
          const SizedBox(
            height: 24,
          ),
        ],
      ),
    );
  }

  void _onClickPendingAction(PendingActions detail) {
    if (detail.category == 'phone') {
      _eventTabEmgContact();
      _saveContact();
    } else if (detail.category == 'insured_endorsement') {
      _passportSheet();
    } else if (detail.category == 'redirection') {
      if (detail.redirectUrl != null) {
        Navigator.pushNamed(context, Routes.WEB_PAGE, arguments: {
          'url': detail.redirectUrl,
          'hide_app_bar': true,
        });
      }
    } else if (detail.category == 'deeplink') {
      if (detail.redirectUrl != null) {
        final param = detail.redirectUrl?.getRoute();
        Navigator.pushNamed(context, param!['route'], arguments: param);
      }
    }
  }

  Container _buildInjuryTag(Note? note) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10, horizontal: 16),
      decoration: BoxDecoration(
        color: note?.backgroundColor?.getColorByHex(),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(
        children: [
          SvgPicture.network(note?.iconUrl ?? ''),
          12.spaceWidth,
          TextEuclidRegular(
            note?.text ?? '',
            textSize: 12.0,
            textColor: color040222,
          )
        ],
      ),
    );
  }

  Column _buildBanner() {
    return Column(
      children: [
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          controller: _scrollController,
          child: Row(
            mainAxisSize: MainAxisSize.max,
            children: List.generate(
              PdpHomeBlocSingleton
                      .blocInstance.pdpData?.quickBannerList?.length ??
                  0,
              (index) {
                QuickBannerList banner = PdpHomeBlocSingleton
                    .blocInstance.pdpData!.quickBannerList![index];
                return GestureDetector(
                  onTap: () {
                    _eventTabBanner(banner.storyList!.first.title!);
                    Navigator.pushNamed(context, Routes.COVER_STORY,
                        arguments: {'storyList': banner.storyList});
                  },
                  child: Container(
                    height: 90,
                    padding: EdgeInsets.symmetric(horizontal: 12),
                    width: MediaQuery.of(context).size.width - 28,
                    margin:
                        EdgeInsets.only(left: index == 0 ? 14 : 0, right: 14),
                    decoration: BoxDecoration(
                      //color: color0091DD,
                      borderRadius: BorderRadius.circular(12),
                      image: DecorationImage(
                        fit: BoxFit.fitWidth,
                        image: AssetImage(
                          'banner_bg'.pngAsset,
                        ),
                      ),
                    ),
                    child: Stack(
                      children: [
                        Center(
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              Container(
                                width: 58,
                                height: 58,
                                margin: EdgeInsets.symmetric(horizontal: 14),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                ),
                                child: Stack(
                                  alignment: Alignment.center,
                                  children: [
                                    Image.asset(
                                      'circular_bg'.pngAsset,
                                      width: 58,
                                      height: 58,
                                    ),
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(200),
                                      child: SvgPicture.network(
                                        banner.iconUrl!,
                                        fit: BoxFit.fill,
                                        height: 50,
                                        width: 50,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Expanded(
                                child: TextEuclidSemiBold14(
                                  banner.text!,
                                  textColor: color040222,
                                  maxLines: 2,
                                ),
                              ),
                              32.spaceWidth,
                              SvgPicture.asset(
                                'assets/images/ic_arrow_forward.svg',
                                height: 14,
                                color: color5B5675,
                              ),
                              24.spaceWidth
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),
        16.spaceHeight,
        ScrollIndicator(
          width: 80,
          indicatorWidth: 25,
          scrollController: _scrollController,
        )
      ],
    );
  }

  Padding _buildFaq() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          8.spaceHeight,
          TextEuclidSemiBold(
            'Get more answers',
            textColor: color040222,
            textSize: 16.0,
          ),
          16.spaceHeight,
          ListView.separated(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              return ExpansionWidget(
                titleBuilder: (double animationValue,
                        double easeInValue,
                        bool isExpanded,
                        dynamic Function({bool animated}) toggleFunction) =>
                    InkWell(
                  onTap: () => toggleFunction(animated: true),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 24),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextEuclidSemiBold(
                            PdpHomeBlocSingleton.blocInstance.pdpData!.faq!
                                .faqList![index].question!,
                            textSize: 16.0,
                            weight: FontWeight.w600,
                            textColor: color040222,
                          ),
                        ),
                        18.spaceWidth,
                        Transform.rotate(
                          angle: math.pi * animationValue,
                          child: Icon(
                            Icons.expand_more,
                            size: 24,
                            color: color36354C,
                          ),
                          alignment: Alignment.center,
                        )
                      ],
                    ),
                  ),
                ),
                content: Padding(
                  padding: const EdgeInsets.only(bottom: 24),
                  child: Linkify(
                    onOpen: (link) async =>
                        await launchUrl(Uri.parse(link.url)),
                    text: PdpHomeBlocSingleton
                        .blocInstance.pdpData!.faq!.faqList![index].answer!,
                    style: TextStyleInterRegular(
                      fontSize: 14.0,
                      fontWeight: FontWeight.w400,
                      color: color5B5675,
                    ),
                    linkStyle: TextStyleInterMediumL1(
                      fontSize: 14.0,
                      fontWeight: FontWeight.w500,
                      color: color1B73E8,
                      decoration: TextDecoration.none,
                    ),
                  ),
                ),
                initiallyExpanded: index == 0,
              );
            },
            padding: EdgeInsets.zero,
            separatorBuilder: (context, index) => Divider(
              color: colorE7E7F0,
              height: 1,
              thickness: 1.5,
            ),
            itemCount:
                (PdpHomeBlocSingleton.blocInstance.pdpData?.faq?.faqList ?? [])
                    .take(3)
                    .length,
          ),
          16.spaceHeight,
          Align(
            alignment: Alignment.centerRight,
            child: InkWell(
              onTap: () => Navigator.pushNamed(context, Routes.TRAVEL_HELP),
              child: TextEuclidMediumL16(
                'View all',
                textColor: color1B73E8,
              ),
            ),
          )
        ],
      ),
    );
  }

  Column _buildHelp() {
    return Column(
      children: [
        TextEuclidSemiBold18(
          'Need more help?',
          textColor: color040222,
        ),
        8.spaceHeight,
        TextEuclidRegular(
          'Find answers to your questions and get help',
          textSize: 12.0,
          textColor: color5B5675,
        ),
        10.spaceHeight,
        Row(
          children: [
            Transform.translate(
              offset: Offset(-12, 0),
              child: SvgPicture.asset('help_left'.svgAsset),
            ),
            Spacer(),
            Flexible(
              flex: 2,
              child: ButtonWidget(
                buttonText: 'Get help',
                textColor: colorFFFFFF,
                buttonColor: color040222,
                buttonState: ButtonState.active,
                onTap: () {
                  _eventTabFaq();
                  Navigator.pushNamed(context, Routes.TRAVEL_HELP, arguments: {
                    'faq': PdpHomeBlocSingleton.blocInstance.pdpData!.faq,
                  });
                },
              ),
            ),
            Spacer(),
            Transform.translate(
              offset: Offset(12, 0),
              child: SvgPicture.asset('help_right'.svgAsset),
            ),
          ],
        )
      ],
    );
  }

  void _onFnolClick(List<ClaimModeList>? claimList) {
    if ((claimList?.length ?? 0) == 1) {
      _validateFnol(claimList!.first);
    } else {
      showModalBottomSheet(
        barrierColor: color040222.withOpacity(0.7),
        isScrollControlled: true,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(15.0),
          ),
        ),
        context: context,
        builder: (BuildContext context) {
          return OldFnolOptionSheet(
            claimList: claimList!,
            onClick: (ClaimModeList claim) {
              Navigator.pop(context);
              _validateFnol(claim);
            },
          );
        },
      );
    }
  }

  void _openFnolSheet(ClaimModeList claim) {
    showModalBottomSheet(
      barrierColor: color040222.withOpacity(0.7),
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(15.0),
        ),
      ),
      context: context,
      builder: (BuildContext bc) {
        return FnolSheet(
          claim: claim,
        );
      },
    );
  }

  void _validateFnol(ClaimModeList claim) {
    if ((claim.fnol?.iconUrl ?? '').isEmpty) {
      _openFnolSheet(claim);
    } else {
      Navigator.pushNamed(context, Routes.FNOL, arguments: {'fnolData': claim});
    }
  }

  void _downloadOrSharePolicy(String pdfLink, PolicyAction policyAction) async {
    FullPageLoader.instance.showFullPageLoader(context);
    String dirloc = (await getApplicationDocumentsDirectory()).path;
    String path = '$dirloc/${DateTime.now().microsecondsSinceEpoch}.pdf';
    await Dio().download(pdfLink, path);

    FullPageLoader.instance.dismissFullPageLoader(context);
    if (policyAction == PolicyAction.download_policy) {
      Util.openFile(path);
    } else {
      await Constants.PLATFORM_CHANNEL
          .invokeMethod("share_document", {"path": path});
    }
  }

  void _saveContact() async {
    final status = await Permission.contacts.request();
    if (await Permission.contacts.request().isGranted) {
      final _platform_channel = const MethodChannel("com.acko.contact");
      final isContactAdded =
          await _platform_channel.invokeMethod("save_contact", {
        "name": 'Acko Travel Emergency Helpline',
        "phone": PdpHomeBlocSingleton.blocInstance.pdpData?.pendingActions
            ?.firstWhere((contact) => contact.category == 'phone')
            .value,
      });
      if (isContactAdded) {
        TravelUtility.showSnackbar(
            context: context,
            msg: "Saved in your phonebook as 'Acko Travel Emergency Helpline'",
            isError: false);
      }
    } else {
      TravelUtility.showSnackbar(
          context: context,
          msg: 'Please allow contact permission to save in your phonebook',
          isError: true);
    }
  }

  void _showFnolOptionSheet(
    List<ClaimDamageModeList> claimList,
    ClaimDamageData? data,
  ) {
    showModalBottomSheet(
      barrierColor: color040222.withOpacity(0.7),
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(15.0),
        ),
      ),
      context: context,
      builder: (BuildContext bottomSheetContext) {
        return FnolOptionSheet(
          claimList: claimList,
          onClick: (claim) {
            Navigator.pop(context);
            if (claim.id == 'cashless_treatment' ||
                claim.damageList!.length == 1) {
              _navigateClaimExpectation(
                policyId: widget.policyId,
                claimType: claim.id!,
                damageId: claim.damageList!.first.id!,
                perilId: data!.perilId!,
              );
            } else {
              _navigateClaimDamage(
                claimModeList: claim,
                title: data!.title!,
                subTitle: data.subTitle ?? '',
                iconUrl: data.iconUrl!,
                policyId: widget.policyId,
                perilId: data.perilId!,
              );
            }
          },
        );
      },
    );
  }

  void _pushViewEvent() {
    TravelEvent.push(
      eventName: TravelEventkey.view_page_int_pdp,
      keyValue: {
        EventKey.page: eventPage,
        EventKey.from_page: eventFromPage,
      },
    );
  }

  void _passportSheet() async {
    await showModalBottomSheet(
      context: context,
      builder: (_) => BlocProvider.value(
        value: context.read<PolicyDetailBloc>(),
        child: AddPassportSheet(
          travellerList:
              PdpHomeBlocSingleton.blocInstance.pdpData?.insuredDetailList ??
                  [],
        ),
      ),
      isDismissible: false,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
    );
  }

  void _eventTabPolicyDetail() {
    TravelEvent.push(
      eventName: TravelEventkey.tap_btn_int_view_policy_details,
      keyValue: {EventKey.page: eventPage, EventKey.from_page: eventFromPage},
    );
  }

  void _eventTabEmgContact() {
    TravelEvent.push(
      eventName: TravelEventkey.tap_btn_int_save_emergency_contact,
      keyValue: {
        EventKey.page: eventPage,
        EventKey.from_page: eventFromPage,
        EventKey.title: PdpHomeBlocSingleton
            .blocInstance.pdpData!.pendingActions!.first.value,
      },
    );
  }

  void _eventTabFaq() {
    TravelEvent.push(
      eventName: TravelEventkey.tap_btn_int_get_help,
      keyValue: {
        EventKey.page: eventPage,
        EventKey.from_page: eventFromPage,
      },
    );
  }

  void _eventTabBanner(String category) {
    TravelEvent.push(
      eventName: TravelEventkey.tap_btn_int_how_to_banner,
      keyValue: {
        EventKey.page: eventPage,
        EventKey.from_page: eventFromPage,
        EventKey.category: category,
      },
    );
  }
}
