import 'package:acko_flutter/util/Constants.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';

class PdpAlertWidget extends StatelessWidget {
  final String policyId;
  const PdpAlertWidget({required this.policyId, super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider<AsyncWidgetCubit>(
      create: (context) => AsyncWidgetCubit(),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12).copyWith(
          top: 0,
        ),
        child: AsyncWidget(
          url: '${TravelUrls.pdpAlerts}$policyId',
          id: "travel_pdp_alerts",
          widgetJson: Util.getAsyncWidgetJson(
              '${TravelUrls.pdpAlerts}$policyId', "travel_pdp_alerts"),
          shimmerWidget: SizedBox.shrink(),
        ),
      ),
    );
  }
}
