import 'package:acko_flutter/travel_insurance/pdp/policy_detail/model/country_model.dart';
import 'package:acko_flutter/travel_insurance/shared/library/enum_to_string/enum_to_string.dart';
import 'package:acko_flutter/travel_insurance/shared/model/travel_base_model.dart';
import 'package:networking_module/util/error/failure.dart';

import '../../../shared/util/enum.dart';

class PdpModel extends TravelBaseModel {
  bool? primary;
  bool? complianceNudge;
  TripDetails? tripDetails;
  PolicyDetails? policyDetails;
  List<InsuredDetailList>? insuredDetailList;
  List<String>? relationshipList;
  List<String>? genderList;
  List<PendingActions>? pendingActions;
  List<ActionList>? actionList;
  List<ActionList>? actionListRefresh;
  List<PerilListRefresh>? perilListRefresh;
  List<PerilList>? perilList;
  Note? addonNote;
  Note? declaration;
  List<QuickBannerList>? quickBannerList;
  Faq? faq;
  String? expiredSubTitle;

  PdpModel({
    this.primary,
    this.complianceNudge,
    this.tripDetails,
    this.policyDetails,
    this.insuredDetailList,
    this.pendingActions,
    this.relationshipList,
    this.genderList,
    this.actionList,
    this.actionListRefresh,
    this.perilListRefresh,
    this.perilList,
    this.addonNote,
    this.declaration,
    this.quickBannerList,
    this.faq,
    this.expiredSubTitle,
  });

  PdpModel.error(Failure? error) {
    this.error = error;
  }

  PdpModel.fromJson(Map<String, dynamic> json) {
    this.code = json['code'];
    this.message = json['message'];
    json = json['data'];

    complianceNudge = json['compliance_nudge'] ?? true;
    primary = json['primary'];
    relationshipList = json['relationship_list'].cast<String>();
    genderList =
        json['gender_list'] == null ? null : json['gender_list'].cast<String>();
    tripDetails = json['editable_trip_details'] != null
        ? new TripDetails.fromJson(json['editable_trip_details'])
        : null;
    policyDetails = json['policy_details'] != null
        ? new PolicyDetails.fromJson(json['policy_details'])
        : null;
    if (json['insured_details_list'] != null) {
      insuredDetailList = <InsuredDetailList>[];
      json['insured_details_list'].forEach((v) {
        insuredDetailList!.add(new InsuredDetailList.fromJson(v));
      });
    }
    if (json['pending_actions'] != null) {
      pendingActions = <PendingActions>[];
      json['pending_actions'].forEach((v) {
        pendingActions!.add(new PendingActions.fromJson(v));
      });
    }
    if (json['action_list'] != null) {
      actionList = <ActionList>[];
      json['action_list'].forEach((v) {
        actionList!.add(new ActionList.fromJson(v));
      });
    }
    if (json['action_list_refresh'] != null) {
      actionListRefresh = <ActionList>[];
      json['action_list_refresh'].forEach((v) {
        actionListRefresh!.add(new ActionList.fromJson(v));
      });
    }

    if (json['peril_list_refresh'] != null) {
      perilListRefresh = <PerilListRefresh>[];
      json['peril_list_refresh'].forEach((v) {
        perilListRefresh!.add(new PerilListRefresh.fromJson(v));
      });
    }
    if (json['peril_list'] != null) {
      perilList = <PerilList>[];
      json['peril_list'].forEach((v) {
        perilList!.add(new PerilList.fromJson(v));
      });
    }
    addonNote = json['addon_note'] != null
        ? new Note.fromJson(json['addon_note'])
        : null;
    declaration = json['declaration'] != null
        ? new Note.fromJson(json['declaration'])
        : null;
    if (json['quick_banner_list'] != null) {
      quickBannerList = <QuickBannerList>[];
      json['quick_banner_list'].forEach((v) {
        quickBannerList!.add(new QuickBannerList.fromJson(v));
      });
    }
    faq = json['faq'] != null ? new Faq.fromJson(json['faq']) : null;
    expiredSubTitle = json['expired_sub_title'];
  }
}

class TripDetails {
  List<String>? countryList;
  CountryListWithCode? countryListWithCode;
  EndorseValue? startDate;
  EndorseValue? endDate;

  TripDetails(
      {this.countryList,
      this.countryListWithCode,
      this.startDate,
      this.endDate});

  TripDetails.fromJson(Map<String, dynamic> json) {
    countryList = json['country_list']['value'].cast<String>();
    countryListWithCode = json['country_list_with_code'] != null
        ? CountryListWithCode.fromJson(json['country_list_with_code'])
        : null;
    startDate = EndorseValue.fromJson(json['start_date']);
    endDate = EndorseValue.fromJson(json['end_date']);
  }
}

class PolicyDetails {
  String? policyId;
  String? policyNumber;
  String? pdfLink;
  String? purchaseDate;
  PolicyStatus? status;
  String? riskStartDate;
  String? riskEndDate;
  String? sumInsured;
  String? selectedPlan;
  bool? canEdit;
  bool? canCancel;
  String? planType;

  PolicyDetails(
      {this.policyId,
      this.policyNumber,
      this.pdfLink,
      this.purchaseDate,
      this.status,
      this.riskStartDate,
      this.riskEndDate,
      this.sumInsured,
      this.canEdit,
      this.canCancel,
      this.selectedPlan,
      this.planType,
      });

  PolicyDetails.fromJson(Map<String, dynamic> json) {
    policyId = json['policy_id'];
    policyNumber = json['policy_number'];
    pdfLink = json['pdf_link'];
    purchaseDate = json['purchase_date'];
    status = EnumToString.fromString(
      PolicyStatus.values,
      json['status'] ?? 'INVALID',
    );
    riskStartDate = json['risk_start_date'];
    riskEndDate = json['risk_end_date'];
    sumInsured = json['medical_sum_insured'];
    canEdit = json['can_edit'];
    canCancel = json['can_cancel'];
    planType = json['plan_type'];
    selectedPlan = json['plans_selected'];
  }
}

class InsuredDetailList {
  String? id;
  EndorseValue? name;
  EndorseValue? dob;
  EndorseValue? phone;
  EndorseValue? passport;
  EndorseValue? gender;
  EndorseValue? medicalConditions;
  bool? healthStatus;
  bool? loggedIn;
  EndorseValue? email;
  Nominee? nominee;
  late bool primary;
  late bool canEndorse;

  InsuredDetailList({
    this.nominee,
    this.primary = false,
    this.canEndorse = false,
    this.id,
    this.name,
    this.dob,
    this.phone,
    this.healthStatus,
    this.email,
    this.gender,
    this.medicalConditions,
    this.loggedIn,
  });

  InsuredDetailList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = EndorseValue.fromJson(json['name']);
    passport = json['passport_number'] != null
        ? EndorseValue.fromJson(json['passport_number'])
        : null;
    gender = json['gender'] == null
        ? EndorseValue(value: null, canEndorse: false)
        : EndorseValue.fromJson(json['gender']);
    dob = EndorseValue.fromJson(json['dob']);
    phone = EndorseValue.fromJson(json['phone']);
    medicalConditions = json['medical_conditions'] != null
        ? EndorseValue.fromJson(json['medical_conditions'])
        : null;
    healthStatus = json['health_status'];
    loggedIn = json['logged_in'];
    email = json['email'] == null
        ? EndorseValue(value: "", canEndorse: false)
        : EndorseValue.fromJson(json['email']);
    nominee =
        json['nominee'] != null ? new Nominee.fromJson(json['nominee']) : null;
    primary = json['primary'] ?? false;
    canEndorse = json['can_endorse'] ?? true;
  }
}

class CountryListWithCode {
  List<CountryModel>? countryObjectList;
  bool canEndorse = false;

  CountryListWithCode({this.countryObjectList, this.canEndorse = false});

  CountryListWithCode.fromJson(Map<String, dynamic> json) {
    if (json['value'] != null) {
      countryObjectList = <CountryModel>[];
      json['value'].forEach((v) {
        countryObjectList!.add(CountryModel.fromJson(v));
      });
    }
    canEndorse = json['can_endorse'] ?? false;
  }
}

class EndorseValue {
  String? value;
  bool canEndorse = false;

  EndorseValue({this.value, this.canEndorse = false});

  EndorseValue.fromJson(Map<String, dynamic> json) {
    value = json['value'];
    canEndorse = json['can_endorse'] ?? false;
  }
}

class Nominee {
  String? id;
  EndorseValue? name;
  EndorseValue? relationship;
  EndorseValue? phone;

  Nominee({this.name, this.relationship, this.phone});

  Nominee.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = EndorseValue.fromJson(json['name']);
    relationship = EndorseValue.fromJson(json['relationship']);
    phone = EndorseValue.fromJson(json['phone']);
  }
}

class PendingActions {
  String? iconUrl;
  String? text;
  String? value;
  String? category;
  String? cta;
  String? redirectUrl;

  PendingActions({
    this.iconUrl,
    this.text,
    this.value,
    this.category,
    this.cta,
    this.redirectUrl,
  });

  PendingActions.fromJson(Map<String, dynamic> json) {
    iconUrl = json['icon_url'];
    text = json['text'];
    value = json['value'];
    category = json['category'];
    redirectUrl = json['redirect_url'];
    cta = json['cta'] ?? 'Add';
  }
}

class ActionList {
  String? iconUrl;
  String? disabledText;
  String? text;
  PolicyAction? id;
  late bool disabled;

  ActionList(
      {this.iconUrl,
      this.text,
      this.id,
      this.disabled = false,
      this.disabledText});

  ActionList.fromJson(Map<String, dynamic> json) {
    iconUrl = json['icon_url'];
    text = json['text'];
    disabledText = json['disabled_text'];
    disabled = json['disabled'] ?? false;
    id = EnumToString.fromString(
      PolicyAction.values,
      json['id'] ?? 'none',
    );
  }
}

class PerilList {
  String? id;
  String? iconUrl;
  String? name;
  List<ClaimModeList>? claimModeList;

  PerilList({this.iconUrl, this.name});

  PerilList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    iconUrl = json['icon_url'];
    name = json['name'];
    if (json['claim_mode_list'] != null) {
      claimModeList = <ClaimModeList>[];
      json['claim_mode_list'].forEach((v) {
        claimModeList!.add(new ClaimModeList.fromJson(v));
      });
    }
  }
}

class ClaimModeList {
  String? id;
  String? name;
  String? action;
  FnolData? fnol;

  ClaimModeList({this.id, this.name, this.action, this.fnol});

  ClaimModeList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    action = json['action'];
    fnol = json['fnol'] != null
        ? new FnolData.fromJson(json['fnol']['data'])
        : null;
  }
}

class FnolData {
  String? iconUrl;
  String? header;
  String? description;
  Note? note;
  EmailCta? emailCta;
  EmailCta? callCta;
  HowItWorks? howItWorks;
  RequiredDocs? requiredDocs;

  FnolData(
      {this.iconUrl,
      this.header,
      this.description,
      this.note,
      this.emailCta,
      this.callCta,
      this.howItWorks,
      this.requiredDocs});

  FnolData.fromJson(Map<String, dynamic> json) {
    iconUrl = json['icon_url'];
    header = json['header'];
    description = json['description'];
    note = json['note'] != null ? new Note.fromJson(json['note']) : null;
    emailCta = json['email_cta'] != null
        ? new EmailCta.fromJson(json['email_cta'])
        : null;
    callCta = json['call_cta'] != null
        ? new EmailCta.fromJson(json['call_cta'])
        : null;
    howItWorks = json['how_it_works'] != null
        ? new HowItWorks.fromJson(json['how_it_works'])
        : null;
    requiredDocs = json['required_docs'] != null
        ? new RequiredDocs.fromJson(json['required_docs'])
        : null;
  }
}

class EmailCta {
  bool? show;
  String? text;
  String? value;

  EmailCta({this.show, this.text, this.value});

  EmailCta.fromJson(Map<String, dynamic> json) {
    show = json['show'];
    text = json['text'];
    value = json['value'];
  }
}

class HowItWorks {
  String? header;
  String? description;

  HowItWorks({this.header, this.description});

  HowItWorks.fromJson(Map<String, dynamic> json) {
    header = json['header'];
    description = json['description'];
  }
}

class RequiredDocs {
  String? title;
  List<String>? items;

  RequiredDocs({this.title, this.items});

  RequiredDocs.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    items = json['items'].cast<String>();
  }
}

class Note {
  String? iconUrl;
  String? text;
  String? backgroundColor;

  Note({this.iconUrl, this.text, this.backgroundColor});

  Note.fromJson(Map<String, dynamic> json) {
    iconUrl = json['icon_url'];
    text = json['text'];
    backgroundColor = json['background_color'];
  }
}

class QuickBannerList {
  String? text;
  String? iconUrl;
  List<StoryList>? storyList;

  QuickBannerList({this.text, this.iconUrl, this.storyList});

  QuickBannerList.fromJson(Map<String, dynamic> json) {
    text = json['text'];
    iconUrl = json['icon_url'];
    if (json['story_list'] != null) {
      storyList = <StoryList>[];
      json['story_list'].forEach((v) {
        storyList!.add(new StoryList.fromJson(v));
      });
    }
  }
}

class StoryList {
  String? heading;
  String? title;
  String? backgroundImageUrl;
  String? subTitle;
  String? ctaText;

  StoryList(
      {this.heading,
      this.title,
      this.backgroundImageUrl,
      this.subTitle,
      this.ctaText});

  StoryList.fromJson(Map<String, dynamic> json) {
    heading = json['heading'];
    title = json['title'];
    backgroundImageUrl = json['background_image_url'];
    subTitle = json['sub_title'];
    ctaText = json['cta_text'];
  }
}

class Faq {
  String? pageTitle;
  String? pageSubTitle;
  String? ctaText;
  String? phoneNumber;
  String? bodyTitle;
  List<FaqList>? faqList;

  Faq(
      {this.pageTitle,
      this.pageSubTitle,
      this.ctaText,
      this.phoneNumber,
      this.bodyTitle,
      this.faqList});

  Faq.fromJson(Map<String, dynamic> json) {
    pageTitle = json['page_title'];
    pageSubTitle = json['page_sub_title'];
    ctaText = json['cta_text'];
    phoneNumber = json['phone_number'];
    bodyTitle = json['body_title'];
    if (json['faq_list'] != null) {
      faqList = <FaqList>[];
      json['faq_list'].forEach((v) {
        faqList!.add(new FaqList.fromJson(v));
      });
    }
  }
}

class FaqList {
  String? question;
  String? answer;

  FaqList({this.question, this.answer});

  FaqList.fromJson(Map<String, dynamic> json) {
    question = json['question'];
    answer = json['answer'];
  }
}

class PerilListRefresh {
  String? perilTitle;
  String? perilSubTitle;
  List<PerilList>? perils;

  PerilListRefresh({this.perilTitle, this.perilSubTitle, this.perils});

  PerilListRefresh.fromJson(Map<String, dynamic> json) {
    perilTitle = json['peril_title'];
    perilSubTitle = json['peril_sub_title'];
    if (json['perils'] != null) {
      perils = <PerilList>[];
      json['perils'].forEach((v) {
        perils!.add(new PerilList.fromJson(v));
      });
    }
  }
}
