import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/view/AckoText.dart';
import 'package:acko_flutter/travel_insurance/shared/extension/date_extension.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../../../common/view/toast.dart';
import '../../../model/date_range_picker/date_picker_model.dart';
import '../../../model/date_range_picker/day_config_model.dart';

class DateRangePickerWidget extends StatefulWidget {
  final Function(DateTime? startDate) startDate;
  final Function(DateTime? endDate) endDate;
  final List<DateTime>? initialDateRange;
  final ValueNotifier<String> selectedDateWidgetNotifier;
  final int? minRangeDifference;
  final int? maxRangeDifference;
  final bool? freezeStartDate;
  final bool? freezeEndDate;

  const DateRangePickerWidget({
    required this.startDate,
    required this.endDate,
    required this.selectedDateWidgetNotifier,
    this.initialDateRange,
    this.minRangeDifference = 1,
    this.maxRangeDifference = 180,
    this.freezeStartDate = false,
    this.freezeEndDate = false,
  });

  @override
  State<DateRangePickerWidget> createState() => _DateRangePickerWidgetState();
}

class _DateRangePickerWidgetState extends State<DateRangePickerWidget> {
  var widgetList = <DatePickerModel>[];
  final weekList = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];

  //final List<DateTime> selectedDateList = [];
  DateTime? _selectedStartDate;
  DateTime? _selectedEndDate;
  late DateTime minDate;
  late DateTime maxDate;

  DateTime addOneMonth(DateTime k) {
    int year = k.year;
    int month = k.month + 1;
    int day = k.day;

    // Adjust for year overflow
    if (month > 12) {
      month = 1;
      year += 1;
    }

    // Handle month length overflow (e.g., Feb 30 -> Feb 28/29)
    int lastDayOfTargetMonth = DateTime(year, month + 1, 0).day;
    if (day > lastDayOfTargetMonth) {
      day = lastDayOfTargetMonth;
    }

    return DateTime(year, month, day, k.hour, k.minute, k.second, k.millisecond,
        k.microsecond);
  }

  @override
  void initState() {
    super.initState();
    minDate =
        DateTime.now().add(Duration(days: widget.minRangeDifference ?? 1));
    maxDate =
        DateTime.now().add(Duration(days: widget.maxRangeDifference ?? 180));
    if (widget.initialDateRange != null &&
        (widget.initialDateRange ?? []).isNotEmpty) {
      _selectedStartDate = widget.initialDateRange?.first ?? null;
      _selectedEndDate = widget.initialDateRange?.last ?? null;
      maxDate = (_selectedStartDate ?? DateTime.now())
          .add(Duration(days: widget.maxRangeDifference ?? 180));

      WidgetsBinding.instance.endOfFrame
          .then((value) => widget.selectedDateWidgetNotifier.value = 'end');
    }
  }

  @override
  Widget build(BuildContext context) {
    widgetList.clear();
    var weekWidgetList = List<Widget>.generate(
      weekList.length,
      (index) => Center(
        child: TextEuclidRegular(
          weekList[index],
          textSize: 16.0,
          textColor: color5B5675,
          weight: FontWeight.w400,
        ),
      ),
    );

    for (DateTime k = DateTime.now();
        k.isBefore(maxDate.add(Duration(days: k.day)));
        k = addOneMonth(k)) {
      //add first month widget
      DateTime dateTime = DateTime(k.year, k.month, 1);
      int weekDay = dateTime.weekday;
      var dayList = <Widget>[];
      dayList.addAll(weekWidgetList);
      if (weekDay != 7) {
        for (var i = 0; i < weekDay; i++) {
          dayList.add(const SizedBox.shrink());
        }
      }
      int days = DateUtils.getDaysInMonth(dateTime.year, dateTime.month);
      for (var i = 1; i <= days; i++) {
        dayList.add(
          InkWell(
            onTap: () {
              if (DateTime(k.year, k.month, i).isSameOrBefore(DateTime.now())) {
                //AckoToast.show('Past and today\'s date not allowed');
              } else if (DateTime(k.year, k.month, i)
                      .difference(_selectedStartDate ?? DateTime.now())
                      .inDays >=
                  (widget.maxRangeDifference ?? 180)) {
                AckoToast.show(
                    'Max ${widget.maxRangeDifference ?? 180} allowed');
              } else {
                DateTime selectedDateTime =
                    DateTime(dateTime.year, dateTime.month, i);

                if (widget.selectedDateWidgetNotifier.value == 'start') {
                  if (_selectedEndDate != null &&
                      selectedDateTime.isSameDay(_selectedEndDate!)) {
                    widget.endDate(null);
                    _selectedEndDate = null;
                  }
                  _selectedStartDate = selectedDateTime;
                  widget.startDate(selectedDateTime);
                  widget.selectedDateWidgetNotifier.value = 'end';
                  //update max date + 180 on first date selection
                  maxDate = (_selectedStartDate ?? DateTime.now())
                      .add(Duration(days: widget.maxRangeDifference ?? 180));
                } else {
                  if (_selectedStartDate != null &&
                      selectedDateTime.isSameOrBefore(
                          _selectedStartDate ?? DateTime.now())) {
                    if (!widget.freezeStartDate!) {
                      _selectedStartDate = selectedDateTime;
                      widget.startDate(selectedDateTime);
                      widget.endDate(null);
                      _selectedEndDate = null;
                    }
                  } else {
                    _selectedEndDate = selectedDateTime;
                    widget.endDate(selectedDateTime);
                  }
                  widget.selectedDateWidgetNotifier.value =
                      _selectedStartDate == null ? 'start' : 'end';
                }
                setState(() {});
              }
            },
            child: DecoratedBox(
              decoration: _decoration(i, dateTime.month, dateTime.year) ??
                  const BoxDecoration(),
              child: Center(
                child: TextEuclidRegular14(
                  i.toString(),
                  textColor: DateTime(k.year, k.month, i)
                              .isSameOrBefore(DateTime.now()) ||
                          DateTime(k.year, k.month, i)
                                  .difference(
                                      _selectedStartDate ?? DateTime.now())
                                  .inDays >=
                              (widget.maxRangeDifference ?? 180)
                      ? Colors.grey
                      : _dayConfig(DateTime(dateTime.year, dateTime.month, i))
                          .textColor,
                  textSize:
                      _dayConfig(DateTime(dateTime.year, dateTime.month, i))
                          .textSize,
                  weight: _dayConfig(DateTime(dateTime.year, dateTime.month, i))
                      .fontWeight,
                ),
              ),
            ),
          ),
        );
      }
      int lastWeekDay =
          DateTime(DateTime.now().year, dateTime.month, days).weekday;
      for (var i = 0; i < lastWeekDay + 1; i++) {
        if (i >= lastWeekDay) {
          dayList.add(const SizedBox.shrink());
        }
      }
      String monthName = DateFormat('MMMM').format(DateTime(
          0, dateTime.month > 12 ? dateTime.month - 12 : dateTime.month));
      widgetList.add(DatePickerModel(
        monthName: '$monthName ${dateTime.year}',
        days: dayList,
      ));
    }
    return ListView.builder(
        itemCount: widgetList.length,
        shrinkWrap: true,
        itemBuilder: (context, listIndex) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: listIndex == 0
                    ? EdgeInsets.only(bottom: 10)
                    : const EdgeInsets.symmetric(vertical: 10),
                child: TextEuclidSemiBold(
                  widgetList[listIndex].monthName ?? '',
                  textColor: color040222,
                  weight: FontWeight.w600,
                  textSize: 16.0,
                ),
              ),
              GridView.builder(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 7,
                  mainAxisSpacing: 5,
                ),
                itemBuilder: (BuildContext context, int index) {
                  return widgetList[listIndex].days![index];
                },
                itemCount: widgetList[listIndex].days?.length ?? 0,
              ),
            ],
          );
        });
  }

  DayConfigModel _dayConfig(DateTime dateTime) {
    if (_selectedStartDate == null && _selectedEndDate == null) {
      return DayConfigModel();
    } else if ((_selectedStartDate ?? DateTime.now()).isSameDay(dateTime) ||
        (_selectedEndDate ?? DateTime.now()).isSameDay(dateTime)) {
      return DayConfigModel(
        fontWeight: FontWeight.w600,
        textColor: Colors.white,
        textSize: 16.0,
      );
    } else {
      return DayConfigModel();
    }
  }

  BoxDecoration? _decoration(int day, int month, int year) {
    DateTime dateTime = DateTime(year, month, day);
    if ((_selectedStartDate ?? DateTime.now()).isSameDay(dateTime) ||
        (_selectedEndDate ?? DateTime.now()).isSameDay(dateTime)) {
      return BoxDecoration(
        color: (_selectedStartDate ?? DateTime.now()).isSameDay(dateTime) &&
                widget.freezeStartDate!
            ? colorE0E0E8
            : color0FA457,
        borderRadius: BorderRadius.circular(6),
      );
    } else if (dateTime.isAfter(_selectedStartDate ?? DateTime.now()) &&
        dateTime.isBefore(_selectedEndDate ?? DateTime.now())) {
      return BoxDecoration(
        color: colorEBFBEE,
      );
    } else {
      return null;
    }
  }
}
