#!/bin/bash

# Exit immediately if any command fails
set -e

echo "Running flutter clean & pub get in root project..."
if [ -f "pubspec.yaml" ]; then
  rm -f pubspec.lock
  flutter clean
  flutter pub get
else
  echo "No pubspec.yaml found in root project. Skipping."
fi

MODULES_DIR="./modules"
PLUGINS_DIR="$MODULES_DIR/plugins"

# Validate existence
if [ ! -d "$MODULES_DIR" ]; then
  echo "Modules directory not found: $MODULES_DIR"
  exit 1
fi

# Process all immediate subfolders in modules/, except plugins/
for module in "$MODULES_DIR"/*; do
  if [ -d "$module" ] && [ "$(basename "$module")" != "plugins" ]; then
    if [ -f "$module/pubspec.yaml" ]; then
      echo "Processing module: $module"
      (
        cd "$module"
        rm -f pubspec.lock
        flutter clean
        flutter pub get
      )
    else
      echo "Skipping $module: pubspec.yaml not found."
    fi
  fi
done

# Now process each plugin in modules/plugins/
if [ -d "$PLUGINS_DIR" ]; then
  for plugin in "$PLUGINS_DIR"/*; do
    if [ -d "$plugin" ] && [ -f "$plugin/pubspec.yaml" ]; then
      echo "Processing plugin: $plugin"
      (
        cd "$plugin"
        rm -f pubspec.lock
        flutter clean
        flutter pub get
      )
    else
      echo "Skipping $plugin: pubspec.yaml not found or not a directory."
    fi
  done
fi

echo "Pub get for all valid modules and plugins successful! ✅"