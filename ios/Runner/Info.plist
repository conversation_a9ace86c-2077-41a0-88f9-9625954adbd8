<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>AMPLITUDE_API_KEY</key>
	<string>$(AMPLITUDE_API_KEY)</string>
	<key>APIKey</key>
	<string>eyJhbGciOiJSU0EtU0hBMjU2IiwidmVyIjoiMSJ9.eyJ2ZXIiOiIzIiwiZW5kcG9pbnRzIjp7ImF1dGh6IjoiaHR0cHM6Ly93d3cuYW1hem9uLmNvbS9hcC9vYSIsInRva2VuRXhjaGFuZ2UiOiJodHRwczovL2FwaS5hbWF6b24uY29tL2F1dGgvbzIvdG9rZW4ifSwiY2xpZW50SWQiOiJhbXpuMS5hcHBsaWNhdGlvbi1vYTItY2xpZW50LmVlMzJlODdkMGEwMjQ2NWY5ZjEyNGVlNjNiM2Y4YTNhIiwiYXBwRmFtaWx5SWQiOiJhbXpuMS5hcHBsaWNhdGlvbi5mMDU0MTdiOWMwZTk0YTcwYjRkOGU0ODcxNjI4MmUzMSIsImJ1bmRsZVNlZWRJZCI6InRlY2guYWNrby5hcHBsZSIsImJ1bmRsZUlkIjoidGVjaC5hY2tvLmFwcGxlIiwiaXNzIjoiQW1hem9uIiwidHlwZSI6IkFQSUtleSIsImFwcFZhcmlhbnRJZCI6ImFtem4xLmFwcGxpY2F0aW9uLWNsaWVudC5mZmFkZmZlMjkyZDE0NGZhYmNiYTM5NTU5MDA3ODBiZSIsInRydXN0UG9vbCI6bnVsbCwiYXBwSWQiOiJhbXpuMS5hcHBsaWNhdGlvbi1jbGllbnQuZmZhZGZmZTI5MmQxNDRmYWJjYmEzOTU1OTAwNzgwYmUiLCJpZCI6IjE1MDMxODQ0LTJlOTItNDdjMS1hYzFmLTIxMGNkYzJjMmIxNSIsImlhdCI6IjE3NDQyNzQzNzc4NDcifQ==.Jgr+/Qa7tdkqEPrns6LQlF9xnD5Z+zzSrAlzID3ZONlmj0AOJjBCBEAK/MFwpP7/yI8D8jRGXDpGcpePjadDNiIe4Yr/0gX13WWsDWKRcRwamxlOBbGMIkZZbmsCLiH/cbP7y2v635rekrXNmV4ndz7HcWnuEEneTGSm1gQHiNH1jBxng9LJof1+4QqPTNuUCzLyPf4Uh8PzixwIDHnixQFPg8n4FcYqeUFiTFJpDjEOaAIB7iYDrsg+zJalZjt8+QSXPvumL48VS65b6U56F5jC9hFs9PrxhprxcG8YTQhY2VXmfbNrjeHr2QYPMg1IZ6VzrceEMx5Kyh6C8Bb9Pw==</string>
	<key>APP_ENVIRONMENT</key>
	<string>$(APP_ENVIRONMENT)</string>
	<key>BASE_URL</key>
	<string>$(BASE_URL)</string>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>$(DISPLAY_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIcons</key>
	<dict>
		<key>CFBundleAlternateIcons</key>
		<dict>
			<key>AppIcon-1</key>
			<dict>
				<key>CFBundleIconFiles</key>
				<array>
					<string>logo1</string>
				</array>
				<key>UIPrerenderedIcon</key>
				<false/>
			</dict>
		</dict>
	</dict>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>$(PRODUCT_NAME)</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>amzn-$(PRODUCT_BUNDLE_IDENTIFIER)</string>
				<string>juspay-$(PRODUCT_BUNDLE_IDENTIFIER)</string>
				<string>$(PRODUCT_BUNDLE_IDENTIFIER).cred</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLName</key>
			<string>Bundle ID</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.acko.apple</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLName</key>
			<string>acko</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>acko</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLName</key>
			<string>com.acko.apple</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>ackoapp</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLName</key>
			<string>facebook</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb388958348845668</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>FacebookAppID</key>
	<string>388958348845668</string>
	<key>FacebookDisplayName</key>
	<string>Acko Insurance</string>
	<key>FlutterDeepLinkingEnabled</key>
	<false/>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationCategoryType</key>
	<string></string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>blob</string>
		<string>tel</string>
		<string>telprompt</string>
		<string>credpay</string>
		<string>phonepe</string>
		<string>paytmmp</string>
		<string>tez</string>
		<string>paytm</string>
		<string>file</string>
		<string>bhim</string>
		<string>devtools</string>
		<string>cydia</string>
		<string>comgooglemaps</string>
		<string>myairtel</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>Play using while you are hiking</string>
	<key>NSCalendarsUsageDescription</key>
	<string>Acko requires your permission to update your calendar when you book sessions on the app.</string>
	<key>NSCameraUsageDescription</key>
	<string>Acko requires access to your camera for video consultations and to upload prescriptions</string>
	<key>NSContactsUsageDescription</key>
	<string>Acko required permission to save contact in phone book.</string>
	<key>NSFaceIDUsageDescription</key>
	<string>Enabling Face ID allows you quick and secure access to your medical data.</string>
	<key>NSHealthShareUsageDescription</key>
	<string>Acko requires your permission to read health and fitness data.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Acko requires your location in order to personalize your services, show doctors near you and curated products available for you.</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Acko requires your location in order to personalize your services, show doctors near you and curated products available for you.</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Acko requires your location in order to personalize your services, show doctors near you and curated products available for you.</string>
	<key>NSMotionUsageDescription</key>
    <string>Acko uses motion data to improve self inspection flow for purchase car insurance.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Acko requires micro phone permission to have seemless online medical evaluations</string>
	<key>NSMotionUsageDescription</key>
	<string>Acko requires motion access.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Acko requires the access for uploading images</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Acko requires the access for uploading images and prescriptions</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>Acko requires speech recognition</string>
	<key>NSUserTrackingUsageDescription</key>
	<string>Your data will be used to deliver personalized ads to you</string>
	<key>PUSH_KEY_FOR_PUBNUB</key>
	<string>$(PUSH_KEY_FOR_PUBNUB)</string>
	<key>SEGMENT_WRITE_KEY</key>
	<string>$(SEGMENT_WRITE_KEY)</string>
	<key>SUB_KEY_FOR_PUBNUB</key>
	<string>$(SUB_KEY_FOR_PUBNUB)</string>
	<key>UIAppFonts</key>
	<array>
		<string>Inter-Thin.ttf</string>
		<string>Inter-SemiBold.ttf</string>
		<string>Inter-Regular.ttf</string>
		<string>Inter-Medium.ttf</string>
		<string>Inter-Light.ttf</string>
		<string>Inter-ExtraLight.ttf</string>
		<string>Inter-ExtraBold.ttf</string>
		<string>Inter-Bold.ttf</string>
		<string>Inter-Black.ttf</string>
	</array>
	<key>UIApplicationShortcutItems</key>
	<array>
		<dict>
			<key>UIApplicationShortcutItemIconFile</key>
			<string>shortcutPoliciesUpdated</string>
			<key>UIApplicationShortcutItemTitle</key>
			<string>View policies</string>
			<key>UIApplicationShortcutItemType</key>
			<string>shortcutsPolicies</string>
			<key>UIApplicationShortcutItemUserInfo</key>
			<dict>
				<key>shortcutDeeplink</key>
				<string>acko.com://coverages_page_sdui?endpoint=/coverages&amp;utm_source=policies_app_shortcut</string>
			</dict>
		</dict>
		<dict>
			<key>UIApplicationShortcutItemIconFile</key>
			<string>shortcutChallanUpdated</string>
			<key>UIApplicationShortcutItemTitle</key>
			<string>Check challans</string>
			<key>UIApplicationShortcutItemType</key>
			<string>shortcutsChallan</string>
			<key>UIApplicationShortcutItemUserInfo</key>
			<dict>
				<key>shortcutDeeplink</key>
				<string>acko.com://check_challan?utm_source=challan_app_shortcut</string>
			</dict>
		</dict>
		<dict>
			<key>UIApplicationShortcutItemIconFile</key>
			<string>shortcutFastagUpdated</string>
			<key>UIApplicationShortcutItemTitle</key>
			<string>Recharge FASTag</string>
			<key>UIApplicationShortcutItemType</key>
			<string>shortcutsFastag</string>
			<key>UIApplicationShortcutItemUserInfo</key>
			<dict>
				<key>shortcutDeeplink</key>
				<string>acko.com://fastag_lookup?utm_source=fastag_app_shortcut</string>
			</dict>
		</dict>
		<dict>
			<key>UIApplicationShortcutItemIconFile</key>
			<string>shortcutAmbulanceUpdated</string>
			<key>UIApplicationShortcutItemTitle</key>
			<string>Book an ambulance</string>
			<key>UIApplicationShortcutItemType</key>
			<string>shortcutsAmbulance</string>
			<key>UIApplicationShortcutItemUserInfo</key>
			<dict>
				<key>shortcutDeeplink</key>
				<string>acko.com://rapid_response_home?utm_source=ambulance_app_shortcut</string>
			</dict>
		</dict>
	</array>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchScreen</key>
	<dict>
		<key>UIColorName</key>
		<string>splashColor</string>
		<key>UIImageName</key>
		<string>LaunchImage</string>
	</dict>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen.storyboard</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
	</array>
	<key>UIUserInterfaceStyle</key>
	<string>Light</string>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>WEGEnvironment</key>
	<string>IN</string>
	<key>WEGLicenseCode</key>
	<string>$(WEBENGAGE_LICENSE_KEY)</string>
	<key>WEGLogLevel</key>
	<string>VERBOSE</string>
	<key>base_Url</key>
	<string>$(base_Url)</string>
	<key>buildFlavor</key>
	<string>$(buildFlavor)</string>
	<key>cashless_claim_url</key>
	<string>$(cashless_claim_url)</string>
	<key>central_app_bff_base_url</key>
	<string>$(central_app_bff_base_url)</string>
	<key>health_base_url</key>
	<string>$(health_base_url)</string>
	<key>kTCCServiceMediaLibrary</key>
	<string>Acko requires access to music. </string>
	<key>max_image_size_key</key>
	<string>$(max_image_size_key)</string>
	<key>one_mg_api_base_url</key>
	<string>$(one_mg_api_base_url)</string>
	<key>one_mg_labs_key</key>
	<string>$(one_mg_labs_key)</string>
	<key>one_mg_labs_url</key>
	<string>$(one_mg_labs_url)</string>
	<key>one_mg_meds_key</key>
	<string>$(one_mg_meds_key)</string>
	<key>one_mg_meds_url</key>
	<string>$(one_mg_meds_url)</string>
	<key>web_base_url</key>
	<string>$(web_base_url)</string>
</dict>
</plist>
