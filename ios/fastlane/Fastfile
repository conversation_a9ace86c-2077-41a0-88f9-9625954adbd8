default_platform(:ios)

DEVELOPER_APP_ID = ENV["DEVELOPER_APP_ID"]
DEVELOPER_APP_IDENTIFIER = ENV["DEVELOPER_APP_IDENTIFIER"]
PROVISIONING_PROFILE_SPECIFIER = ENV["PROVISIONING_PROFILE_SPECIFIER"]
TEMP_KEYCHAIN_USER = ENV["TEMP_KEYCHAIN_USER"]
TEMP_KEYCHAIN_PASSWORD = ENV["TEMP_KEYCHAIN_PASSWORD"]
FIREBASE_CLI_TOKEN = ENV["FIREBASE_CLI_TOKEN"]
FIREBASE_APP_ID = ENV["FIREBASE_APP_ID"]
GITHUB_BUILD_NUMBER = ENV['GITHUB_BUILD_NUMBER']
CUSTOM_MESSAGE = ENV['CUSTOM_MESSAGE']
IOS_FASTLANE_UPLOAD_KEY_ID = ENV["IOS_FASTLANE_UPLOAD_KEY_ID"]
IOS_FASTLANE_UPLOAD_ISSUER_ID = ENV["IOS_FASTLANE_UPLOAD_ISSUER_ID"]

def delete_temp_keychain(name)
  delete_keychain(
    name: name
  ) if File.exist? File.expand_path("~/Library/Keychains/#{name}-db")
end

def create_temp_keychain(name, password)
  create_keychain(
    name: name,
    password: password,
    unlock: false,
    timeout: 0
  )
end

def ensure_temp_keychain(name, password)
  delete_temp_keychain(name)
  create_temp_keychain(name, password)
end

platform :ios do

  lane :firebase_uat_deploy do |options|
    keychain_name = TEMP_KEYCHAIN_USER
    keychain_password = TEMP_KEYCHAIN_PASSWORD
    ensure_temp_keychain(keychain_name, keychain_password)

    firebase_app_distribution(
          app: FIREBASE_APP_ID,
          groups: "acko-app-testers",
          release_notes: CUSTOM_MESSAGE,
          service_credentials_file: "./acko-sandbox-6afe8a6c76df.json",
          firebase_cli_path: "/usr/local/bin/firebase",
          firebase_cli_token: FIREBASE_CLI_TOKEN,
          ipa_path: "/Users/<USER>/work/AckoFlutter/AckoFlutter/build/ios/ipa/ACKO_UAT.ipa"
      )

    delete_temp_keychain(keychain_name)
  end

  lane :firebase_prod_deploy do |options|
    keychain_name = TEMP_KEYCHAIN_USER
    keychain_password = TEMP_KEYCHAIN_PASSWORD
    ensure_temp_keychain(keychain_name, keychain_password)

    firebase_app_distribution(
          app: FIREBASE_APP_ID,
          groups: "acko-app-testers",
          release_notes: CUSTOM_MESSAGE,
          service_credentials_file: "./acko-sandbox-6afe8a6c76df.json",
          firebase_cli_path: "/usr/local/bin/firebase",
          firebase_cli_token: FIREBASE_CLI_TOKEN,
          ipa_path: "/Users/<USER>/work/AckoFlutter/AckoFlutter/build/ios/ipa/ACKO.ipa"
      )

    delete_temp_keychain(keychain_name)
  end

  lane :testflight_deploy do
    keychain_name = TEMP_KEYCHAIN_USER
    keychain_password = TEMP_KEYCHAIN_PASSWORD
    ensure_temp_keychain(keychain_name, keychain_password)

    api_key = app_store_connect_api_key(
      key_id: "#{IOS_FASTLANE_UPLOAD_KEY_ID}",
      issuer_id: "#{IOS_FASTLANE_UPLOAD_ISSUER_ID}",
      key_filepath: "/Users/<USER>/work/AckoFlutter/AckoFlutter/ios/fastlane/AuthKey_QUJWYRT7ZS.p8"
    )

    pilot(
      api_key: api_key,
      app_identifier: "#{DEVELOPER_APP_IDENTIFIER}",
      skip_waiting_for_build_processing: true,
      skip_submission: true,
      ipa: "/Users/<USER>/work/AckoFlutter/AckoFlutter/build/ios/ipa/ACKO.ipa"
    )

    delete_temp_keychain(keychain_name)
  end
end