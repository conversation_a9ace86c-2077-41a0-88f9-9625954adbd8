$minDeploymentVersion = '14.0'
# Uncomment this line to define a global platform for your project
platform :ios, $minDeploymentVersion

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  use_frameworks!
pod 'DKImagePickerController'
#JUSPAY SDK
pod 'HyperSDK','*******'
pod 'FBSDKCoreKit', '~> 18.0'
pod 'HyperAPay', '*******'
pod 'GoogleTagManager'
pod 'Firebase/DynamicLinks'
pod 'WebEngage','6.4.3'
  use_modular_headers!

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
end


# ServiceExtension Target
target 'NotificationService' do
    platform :ios, $minDeploymentVersion
    use_frameworks!
    pod 'WebEngageBannerPush'
end

# ContentExtension Target
target 'NotificationViewController' do
  platform :ios, $minDeploymentVersion
  use_frameworks!
  pod 'WebEngageAppEx/ContentExtension'
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
            config.build_settings['ENABLE_BITCODE'] = 'NO'
          end
    if target.name === "WebEngageAppEx"
          target.build_configurations.each do |config|
            config.build_settings['SWIFT_INSTALL_OBJC_HEADER'] = 'NO'
          end
        end
    # 🔧 Strip bitcode from AmazonPay binary
    binary_path = './Pods/HyperAPay/AmazonPayHardenediOSSDK.xcframework/ios-arm64_armv7/AmazonPayHardenediOSSDK.framework/AmazonPayHardenediOSSDK'
    if File.exist?(binary_path)
        puts "⚙️ Stripping bitcode from #{binary_path}"
        system("xcrun bitcode_strip -r #{binary_path} -o #{binary_path}")
    else
        puts "❗ AmazonPay SDK binary not found at: #{binary_path}"
    end
    flutter_additional_ios_build_settings(target)
    target.build_configurations.each do |config|
      config.build_settings['CODE_SIGNING_REQUIRED'] = "NO"
      config.build_settings['CODE_SIGNING_ALLOWED'] = "NO"
    end
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = $minDeploymentVersion

      config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
      '$(inherited)',
      ## dart: PermissionGroup.calendar
       'PERMISSION_EVENTS=1',
      ## dart: PermissionGroup.reminders
      # 'PERMISSION_REMINDERS=1',
      ## dart: PermissionGroup.contacts
      # 'PERMISSION_CONTACTS=1',
      ## dart: PermissionGroup.camera
      'PERMISSION_CAMERA=1',
      ## dart: PermissionGroup.microphone
      'PERMISSION_MICROPHONE=1',
      ## dart: PermissionGroup.speech
      # 'PERMISSION_SPEECH_RECOGNIZER=1',
      ## dart: PermissionGroup.photos
      'PERMISSION_PHOTOS=1',
      ## dart: [PermissionGroup.location, PermissionGroup.locationAlways, PermissionGroup.locationWhenInUse]
      'PERMISSION_LOCATION=1',
      ## dart: PermissionGroup.notification
      'PERMISSION_NOTIFICATIONS=1',
      ## dart: PermissionGroup.mediaLibrary
      'PERMISSION_MEDIA_LIBRARY=1',
      ## dart: PermissionGroup.contacts
      'PERMISSION_CONTACTS=1',
      ## dart: PermissionGroup.sensors
      # 'PERMISSION_SENSORS=1',
      ## dart: PermissionGroup.bluetooth
      # 'PERMISSION_BLUETOOTH=1',
      ## dart: PermissionGroup.appTrackingTransparency
      # 'PERMISSION_APP_TRACKING_TRANSPARENCY=1',
      ## dart: PermissionGroup.criticalAlerts
      # 'PERMISSION_CRITICAL_ALERTS=1'
      ]
      end
    target.build_configurations.each do |config|
      xcconfig_relative_path = "Pods/Target Support Files/#{target.name}/#{target.name}.#{config.name}.xcconfig"
      file_path = Pathname.new(File.expand_path(xcconfig_relative_path))
      next unless File.file?(file_path)

      configuration = Xcodeproj::Config.new(file_path)
      next if configuration.attributes['LIBRARY_SEARCH_PATHS'].nil?

      configuration.attributes['LIBRARY_SEARCH_PATHS'].sub! 'DT_TOOLCHAIN_DIR', 'TOOLCHAIN_DIR'
      configuration.save_as(file_path)
    end
  end
  fuse_path = "./Pods/HyperSDK/Fuse.rb"
  clean_assets = true # Pass true to re-download all the assets
  if File.exist?(fuse_path)
    if system("ruby", fuse_path.to_s, clean_assets.to_s)
    end
  end
end
