## For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
#
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
# Default value: -Xmx1024m -XX:MaxPermSize=256m
# org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
#
# When configured, Grad<PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. For more details, visit
# https://developer.android.com/r/tools/gradle-multi-project-decoupled-projects
# org.gradle.parallel=true
#Mon Apr 08 21:35:49 IST 2024
BUILD_TOOLS_VERSION=24.0.3
GROUP=com.bignerdranch.android
SUPPORT_VERSION=24.2.1
VERSION_CODE=9
VERSION_NAME=3.0.0-RC1
#android.bundle.enableUncompressedNativeLibs=false
android.enableJetifier=true
#android.enableR8=true /// Added by default from
android.injected.testOnly=false
android.useAndroidX=true
firebasePerformanceInstrumentationEnabled=true
kapt.incremental.apt=true
kotlin.code.style=official
org.gradle.jvmargs=-Xmx4096M -Dkotlin.daemon.jvm.options="-Xmx4096M"
org.gradle.daemon=false
