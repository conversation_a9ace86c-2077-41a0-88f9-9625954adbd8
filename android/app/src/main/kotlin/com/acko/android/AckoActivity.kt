package com.acko.android

import `in`.juspay.services.HyperServices
import android.app.Activity
import android.app.AlertDialog
import android.app.PendingIntent
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.PackageManager
import android.graphics.Rect
import android.net.Uri
import android.content.pm.ShortcutInfo
import android.content.pm.ShortcutManager
import android.os.Bundle
import android.os.Handler
import android.os.RemoteException
import android.util.Log
import android.view.View
import android.view.ViewTreeObserver
import android.widget.Toast

import androidx.annotation.NonNull
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.LifecycleObserver
import androidx.multidex.MultiDex
import com.acko.android.feature.home.data.model.AppUpdateData
import com.acko.android.feature.home.ui.*
import com.acko.android.feature.smsretrievalapi.OTPRetrievalListener
import com.acko.android.feature.smsretrievalapi.SmsRetrievalReceiver
import com.acko.android.flutter.*
import com.acko.android.health.HealthApp
import com.acko.android.util.CommonUtil
import com.acko.android.util.Constants
import com.acko.android.util.Contact
import com.acko.android.util.FilePicker
import com.acko.android.util.FridaDetection
import com.acko.android.util.Logger
import com.acko.android.util.ext.SharedPreference
import com.acko.android.util.ext.SharedPreference.get
import com.android.installreferrer.api.InstallReferrerClient
import com.android.installreferrer.api.InstallReferrerStateListener
import com.android.installreferrer.api.ReferrerDetails
import com.appsflyer.AppsFlyerConversionListener
import com.appsflyer.AppsFlyerLib
import com.appsflyer.deeplink.DeepLinkResult
import com.facebook.applinks.AppLinkData
import com.google.android.gms.analytics.CampaignTrackingReceiver
import com.google.android.gms.auth.api.credentials.Credential
import com.google.android.gms.auth.api.credentials.Credentials
import com.google.android.gms.auth.api.credentials.HintRequest
import com.google.android.gms.auth.api.phone.SmsRetriever
import com.google.android.play.core.appupdate.AppUpdateInfo
import com.google.android.play.core.appupdate.AppUpdateManager
import com.google.android.play.core.appupdate.AppUpdateManagerFactory
import com.google.android.play.core.install.InstallState
import com.google.android.play.core.install.InstallStateUpdatedListener
import com.google.android.play.core.install.model.ActivityResult
import com.google.android.play.core.install.model.AppUpdateType
import com.google.android.play.core.install.model.InstallStatus
import com.google.android.play.core.install.model.UpdateAvailability
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson

import com.scottyab.rootbeer.RootBeer
import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.FlutterEngineCache
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.*
import org.json.JSONException
import org.json.JSONObject
import org.koin.androidx.viewmodel.ext.android.viewModel
import java.io.File
import java.util.*
import kotlin.math.roundToInt
import kotlin.system.exitProcess
import android.R.attr.data
import android.os.Build
import android.graphics.drawable.Icon
import androidx.annotation.StringRes
import androidx.annotation.DrawableRes
import com.acko.android.util.AnalyticsHelper
import com.acko.android.util.DeeplinkSource
import com.acko.android.util.StoredDeeplink


///Interface, Method channel communication

class AckoActivity : FlutterFragmentActivity(),
    InstallStateUpdatedListener, OTPRetrievalListener, EventCommunicator, LifecycleObserver {

    private var localMethodChannel: MethodChannel? = null
    private var methodChannel: MethodChannel? = null
    private var filePicker: FilePicker? = null
    private var contact: Contact? = null
    private var isMyAccountLoaded = false
    private var fbInit: HashMap<String, String>? = null
    private var isAppOpen: Boolean = false
    private var conversionData: HashMap<String, String>? = null
    private val viewModel: MainActivityViewModel by viewModel()
    private var appUpdateManager: AppUpdateManager? = null
    private var smsRetrievalReceiver: SmsRetrievalReceiver? = null
    private var referrerUrl: String? = "organic"
    private var handler = Handler()
    private var isKeyboardOpen = false

    var keyboardListener = ViewTreeObserver.OnGlobalLayoutListener {
        val newValue = isKeyboardOpen()
        handler.removeCallbacksAndMessages(null)
        if (newValue != isKeyboardOpen) {
            isKeyboardOpen = newValue
            localMethodChannelExt?.invokeMethod("is_keyboard_open", isKeyboardOpen)
            /*   handler.postDelayed({
               }, 20)*/
        }
    }

    companion object {
        private lateinit var flutterEngineInstance: FlutterEngine
        private const val REQUEST_CODE_FLEXI_UPDATE = 1234
        private const val REQUEST_CODE_IMMEDIATE_UPDATE = 1235
        private const val PREF_CHECK_INSTALL_REFERRER = "PREF_CHECK_INSTALL_REFERRER"
        const val PHONE_NUMBER_PICKER_HINT = 1123
        var appsFlyerInitialized = false
        var storedDeepLinkUntilNextAppOpen: StoredDeeplink? = null

        var appsFlyerDeeplinkMediaSource: String? = null
        fun getFlutterEngine(): FlutterEngine? {
            return if (::flutterEngineInstance.isInitialized)
                flutterEngineInstance
            else null
        }
    }

    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        flutterEngineInstance = flutterEngine
        configureMethodChannels(flutterEngine, this)
        methodChannel = methodChannelExt
        localMethodChannel = localMethodChannelExt
        filePicker = FilePicker(this, flutterEngine)
        contact = Contact(this, flutterEngine)
        DownloadCompletedBroadcastReceiver.channel = localMethodChannel
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (intent != null) {
            setNotificationEvent(intent)
            setDeeplink(intent)
        }
        createShortcuts()
        safetyCheck()
        appUpdateManagerInit()
        initialize()
        deleteWebViewCache()
    }

    private fun initialize() {
        CoroutineScope(Dispatchers.IO).launch {
            if (HealthApp.getInstance() == null && application is AckoApplication) {
                HealthApp.init(application)
            }


            appUpdateManagerInit()
            HealthApp.getInstance()?.configureRemoteConfig()
            registerConversionListener()
            subscribeDeeplink()

            /*
            Starting  Appsflyer service only once all SDK requirements and
            callbacks such as the deeplink listener are set. This is required
            for deferred deeplinks to work properly.
            */
            startAppsflyerSDKSession()
            revertActivity()
        }
    }

    fun Activity.revertActivity() {
//        DefaultAppIconActivity
//        AckoActivity
        if (getPreferences(MODE_PRIVATE).getBoolean(
                AckoActivity.PREF_CHECK_INSTALL_REFERRER,
                false
            )
        ) {

            this.packageManager.setComponentEnabledSetting(
                ComponentName(this,
                    "com.acko.android.DefaultAppIconActivity"),
                PackageManager.COMPONENT_ENABLED_STATE_DISABLED,
                PackageManager.DONT_KILL_APP)
            this.packageManager.setComponentEnabledSetting(
                ComponentName(
                    this,
                    "com.acko.android.AckoActivity"
                ),
                PackageManager.COMPONENT_ENABLED_STATE_ENABLED,
                PackageManager.DONT_KILL_APP
            )
        }
    }
    private fun appUpdateManagerInit() {
        checkInstallReferrer()
        appUpdateManager = AppUpdateManagerFactory.create(this)
        appUpdateManager?.registerListener(this)

        appUpdateManager?.appUpdateInfo?.addOnFailureListener {
            if (it != null) FirebaseCrashlytics.getInstance().recordException(it)
        }
        checkForUpdate()

        supportFragmentManager.registerFragmentLifecycleCallbacks(object :
            FragmentManager.FragmentLifecycleCallbacks() {
            override fun onFragmentCreated(
                fm: FragmentManager,
                f: Fragment,
                savedInstanceState: Bundle?
            ) {
                super.onFragmentCreated(fm, f, savedInstanceState)
            }
        }, true)

        handleObservables()
    }

    private fun isKeyboardOpen(): Boolean {
        val visibleBounds = Rect()
        val root = findViewById<View>(android.R.id.content)
        root.getWindowVisibleDisplayFrame(visibleBounds)
        val heightDiff = root.height - visibleBounds.height()
        val marginOfError = (0.1f * root.height).roundToInt()
        Log.i("dashboard", "heights $heightDiff $marginOfError")
        return heightDiff > marginOfError
    }

//    override fun onBackPressed() {added animations
//        if (supportFragmentManager.backStackEntryCount > 0) {
//            val profileFragment =
//                supportFragmentManager.findFragmentByTag("PROFILE_FRAGMENT") as FlutterFragment?
//            if (profileFragment != null) {
//                profileFragment.onBackPressed()
//            } else {
//                supportFragmentManager.popBackStack()
//            }
//        }else{
//            super.onBackPressed();
//        }
//    }

    private fun checkForUpdate() {
        appUpdateManager?.appUpdateInfo?.addOnSuccessListener {
            if (it.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE) {
                viewModel.updateInfo = it
                viewModel.updateAvailable.value = true
            } else viewModel.updateAvailable.value = false
        }
    }

    override fun onStop() {
        super.onStop()
        storedDeepLinkUntilNextAppOpen = null
    }

    private fun handleObservables() {
        CoroutineScope(Dispatchers.Main).launch {
            viewModel.updateAvailable.observe(this@AckoActivity) {
                if (viewModel.updateAvailable.hasActiveObservers() && it != null && it) {
                    fetchRemoteData()
                }
            }

            viewModel.updateAppRattingPrefs.observe(this@AckoActivity) { ratingPrefs ->
                if (viewModel.updateAppRattingPrefs.hasActiveObservers() && ratingPrefs != null) {
                    methodChannel?.invokeMethod(
                        "app_rating_prefs",
                        HashMap<String, Any>().apply {
                            put("should_display_home_ratings", ratingPrefs.isHomeRatingViewShown)
                            put("is_user_rating_done", ratingPrefs.isUserRated)
                        }
                    )
                }
            }

            viewModel.trackAppRatingEvents.observe(this@AckoActivity) { event ->
                if (viewModel.trackAppRatingEvents.hasActiveObservers() && event != null) {
                    event.feedback?.let {
                        if (it.isNotEmpty()) {
                            popupSnackbarForUpdate(
                                shouldUpdateApp = false,
                                message = "Thanks for submitting your feedback",
                                snackbarDisplayLength = 5
                            )
                        }
                    }

                    methodChannel?.invokeMethod(
                        "app_rating_event",
                        HashMap<String, Any>().apply {
                            event.event?.let { put("event", it) }
                            event.pageTitle?.let { put("page_title", it) }
                            event.feedback?.let { put("user_feedback", it) }
                        }
                    )
                }
            }
        }
    }

    /**
     * Fetch a welcome message from the Remote Config service, and then activate it.
     */
    private fun fetchRemoteData() {
        val remoteConfig = HealthApp.getInstance().remoteConfig
        remoteConfig?.fetchAndActivate()?.addOnCompleteListener(this) {
            val updateString = remoteConfig.getString("in_app_update")
            viewModel.appUpdateRemoteData =
                Gson().fromJson(updateString, AppUpdateData::class.java)

            if (viewModel.appUpdateRemoteData != null) {
                if (viewModel.appUpdateRemoteData?.isImmediateUpdate!! &&
                    BuildConfig.VERSION_NAME >= viewModel.appUpdateRemoteData?.appUpdateMinimumVersion!! &&
                    BuildConfig.VERSION_NAME < viewModel.appUpdateRemoteData?.appCurrentVersion!!
                ) {
                    viewModel.flexiUpdate = false
                    startInAppUpdate(
                        viewModel.updateInfo,
                        viewModel.appUpdateRemoteData?.isImmediateUpdate!!
                    )
                    methodChannel?.invokeMethod(
                        Constants.IS_APP_UPDATE,
                        Constants.FORCE_UPDATE_TRIGGERED
                    )
                } else {
                    viewModel.flexiUpdate = true;
                    if (isMyAccountLoaded) {
                        showInAppUpdate()
                    }
                }
            }
        }
    }

    private fun checkInstallReferrer() {
        if (getPreferences(MODE_PRIVATE).getBoolean(
                AckoActivity.PREF_CHECK_INSTALL_REFERRER,
                false
            )
        ) {
            return
        }
        val referrerClient = InstallReferrerClient.newBuilder(this).build()
        GlobalScope.launch(Dispatchers.IO) {
            getInstallReferrerFromClient(referrerClient)
        } // Non-blocking coroutine
    }

    private fun getInstallReferrerFromClient(referrerClient: InstallReferrerClient) {
        referrerClient.startConnection(object : InstallReferrerStateListener {
            override fun onInstallReferrerSetupFinished(responseCode: Int) {
                when (responseCode) {
                    InstallReferrerClient.InstallReferrerResponse.OK -> {
                        val response: ReferrerDetails? = try {
                            referrerClient.installReferrer
                        } catch (e: RemoteException) {
                            e.printStackTrace()
                            null
                        }
                        referrerUrl = response?.installReferrer

                        referrerUrl?.let {
                            trackInstallReferrer(it)
                        }
                        // End the connection
                        referrerClient.endConnection()
                    }

                    InstallReferrerClient.InstallReferrerResponse.FEATURE_NOT_SUPPORTED -> {
                    }

                    InstallReferrerClient.InstallReferrerResponse.SERVICE_UNAVAILABLE -> {
                    }
                }
            }

            override fun onInstallReferrerServiceDisconnected() {
                // Do nothing
            }
        })
    }

    override fun onResume() {
        super.onResume()
        onActivityResume()
    }

    private fun onActivityResume() {
        provideRootLayout(this).viewTreeObserver.addOnGlobalLayoutListener(keyboardListener)
        appUpdateManager?.appUpdateInfo?.addOnSuccessListener {
            when {
                it.updateAvailability() == UpdateAvailability.DEVELOPER_TRIGGERED_UPDATE_IN_PROGRESS &&
                        viewModel.appUpdateRemoteData != null && viewModel.appUpdateRemoteData?.isImmediateUpdate!! ->
                    startInAppUpdate(it, true)

                it.installStatus() == InstallStatus.DOWNLOADED -> popupSnackbarForUpdate(
                    shouldUpdateApp = false,
                    message = "Your update is downloaded",
                    actionString = "RESTART",
                    snackbarDisplayLength = 10000
                )
            }
        }
    }

    // Tracker for Classic GA (call this if you are using Classic GA only)
    private fun trackInstallReferrer(referrerUrl: String) {
        CoroutineScope(Dispatchers.Main).launch {
            val receiver = CampaignTrackingReceiver()
            val intent = Intent("com.android.vending.INSTALL_REFERRER")
            intent.putExtra("referrer", referrerUrl)
            receiver.onReceive(AckoApplication.getContext(), intent)
        }
    }

    override fun onNewIntent(intent: Intent) {

        setIntent(intent)
        super.onNewIntent(intent)
//        flutterFragment?.onNewIntent(intent)

        setNotificationEvent(intent)
        if (!isLaunchFromHistory()) {
            setDeeplink(intent)
        }
    }

    private fun setNotificationEvent(intent: Intent) {
        val intentBundle = intent.extras
        if (intentBundle != null && intentBundle.containsKey("source") && !intentBundle.containsKey(
                "is_notification_clicked"
            )
        )
            intentBundle.putBoolean("is_notification_clicked", true)
        if (intentBundle != null && intentBundle.containsKey("is_notification_clicked")) {
            val param = getEventParams(intentBundle);
            if (intentBundle.getBoolean("is_notification_clicked", false)) {
                val bundle = Bundle()
                bundle.putString("lob", intentBundle.getString("lob"))
                bundle.putString("notifcation_name", intentBundle.getString("name"))
                FirebaseAnalytics.getInstance(this).logEvent("notification_clicked", bundle)
                Handler().postDelayed({
                    methodChannel?.invokeMethod(
                        "send_events",
                        hashMapOf(
                            "event_name" to "tap_btn_push_notification",
                            param
                        )
                    )
                    val statusUpdateMap: HashMap<String, Any?>? = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        intentBundle.getSerializable("communication_service_notification_status_update", HashMap::class.java) as? HashMap<String, Any?>
                    } else {
                        intentBundle.getSerializable("communication_service_notification_status_update") as? HashMap<String, Any?>
                    }
                    statusUpdateMap?.let {
                        methodChannel?.invokeMethod(
                            "communication_service_notification_status_update",
                            it
                        )
                    }

                }, 3000)
            }
        }
    }

    private fun getEventParams(intentBundle: Bundle) = "parameters" to hashMapOf(
        "product" to intentBundle.getString("lob"),
        "name" to intentBundle.getString("name"),
        "type" to intentBundle.getString("type"),
        "platform" to "Android",
        "deeplink_url" to intentBundle.getString("deeplink")
    )

    private fun registerConversionListener() {
        Logger.e("AppsFlyer", "conversion listener called")
        val conversionDataListener = object : AppsFlyerConversionListener {
            override fun onConversionDataSuccess(data: MutableMap<String, Any>?) {
                data?.let { cvData ->
                    appsFlyerInitialized = true
                    if (cvData.containsKey(Constants.IS_FIRST_LAUNCH) && cvData[Constants.IS_FIRST_LAUNCH].toString() == "true") {
                        val formattedData: MutableMap<String, String> = HashMap()
                        formattedData["install_time"] =
                            data["install_time"]?.toString() ?: Date().time.toString()

                        data["click_time"]?.let {
                            formattedData["click_time"] = it.toString()
                        }
                        var mediaSource = data["media_source"]?.toString() ?: "organic"
                        formattedData[
                            "media_source"] = mediaSource
                        appsFlyerDeeplinkMediaSource = mediaSource
                        var mediaCamaign = data["campaign"]?.toString() ?: "organic"
                        formattedData["campaign"] = mediaCamaign

                        val params = Bundle().apply {
                            putString("mediaSource", mediaSource)
                            putString("mediaCampaign", mediaCamaign)
                        }

                        FirebaseAnalytics.getInstance(applicationContext).logEvent("uac_deeplink_info", params)

                        if (mediaSource in "googleadwords_int") {
                            HealthApp.getInstance().remoteConfig?.fetchAndActivate()?.addOnCompleteListener {
                                val sanDeeplinkJsonString = HealthApp.getInstance().remoteConfig.getString("san_deeplink")
                                val sanDeeplinkJson: JSONObject = try{
                                    JSONObject(sanDeeplinkJsonString)
                                }catch (e: Exception){
                                    JSONObject()
                                }
                                when {
                                    mediaCamaign.lowercase().contains("car") -> {
                                        val deeplink = sanDeeplinkJson.optString("car", "acko.com://app_home?home_tab=car")
                                        storedDeepLinkUntilNextAppOpen =  StoredDeeplink(deeplink, DeeplinkSource.APPS_FLYER)

                                        val params = Bundle().apply {
                                            putString("mediaSource", mediaSource)
                                            putString("mediaCampaign", mediaCamaign)
                                            putString("lob", "car")
                                            putString("deeplink", deeplink)
                                        }

                                        FirebaseAnalytics.getInstance(applicationContext).logEvent("uac_deeplink_parsed", params)
                                    }

                                    mediaCamaign.lowercase().contains("bike") -> {
                                        val deeplink = sanDeeplinkJson.optString("bike", "acko.com://app_home?home_tab=bike")
                                        storedDeepLinkUntilNextAppOpen =  StoredDeeplink(deeplink, DeeplinkSource.APPS_FLYER)
                                        val params = Bundle().apply {
                                            putString("mediaSource", mediaSource)
                                            putString("mediaCampaign", mediaCamaign)
                                            putString("lob", "bike")
                                            putString("deeplink", deeplink)
                                        }

                                        FirebaseAnalytics.getInstance(applicationContext).logEvent("uac_deeplink_parsed", params)
                                    }

                                    mediaCamaign.lowercase().contains("health") -> {
                                        val deeplink = sanDeeplinkJson.optString("health", "acko.com://app_home?home_tab=health")
                                        storedDeepLinkUntilNextAppOpen =  StoredDeeplink(deeplink, DeeplinkSource.APPS_FLYER)
                                        val params = Bundle().apply {
                                            putString("mediaSource", mediaSource)
                                            putString("mediaCampaign", mediaCamaign)
                                            putString("lob", "health")
                                            putString("deeplink", deeplink)
                                        }

                                        FirebaseAnalytics.getInstance(applicationContext).logEvent("uac_deeplink_parsed", params)
                                    }

                                    mediaCamaign.lowercase().contains("life") -> {
                                        val deeplink = sanDeeplinkJson.optString("life", "acko.com://app_home?home_tab=life")
                                        storedDeepLinkUntilNextAppOpen =  StoredDeeplink(deeplink, DeeplinkSource.APPS_FLYER)
                                        val params = Bundle().apply {
                                            putString("mediaSource", mediaSource)
                                            putString("mediaCampaign", mediaCamaign)
                                            putString("lob", "life")
                                            putString("deeplink", deeplink)
                                        }

                                        FirebaseAnalytics.getInstance(applicationContext).logEvent("uac_deeplink_parsed", params)
                                    }

                                    mediaCamaign.lowercase().contains("travel") -> {
                                        val deeplink = sanDeeplinkJson.optString("travel", "acko.com://app_home?home_tab=travel")
                                        storedDeepLinkUntilNextAppOpen =  StoredDeeplink(deeplink, DeeplinkSource.APPS_FLYER)
                                        val params = Bundle().apply {
                                            putString("mediaSource", mediaSource)
                                            putString("mediaCampaign", mediaCamaign)
                                            putString("lob", "travel")
                                            putString("deeplink", deeplink)
                                        }

                                        FirebaseAnalytics.getInstance(applicationContext).logEvent("uac_deeplink_parsed", params)
                                    }
                                    mediaCamaign.lowercase().contains("abha") -> {
                                        val deeplink = sanDeeplinkJson.optString("abha", "https://www.acko.com/abha_members")
                                        storedDeepLinkUntilNextAppOpen =  StoredDeeplink(deeplink, DeeplinkSource.APPS_FLYER)
                                        val params = Bundle().apply {
                                            putString("mediaSource", mediaSource)
                                            putString("mediaCampaign", mediaCamaign)
                                            putString("lob", "health")
                                            putString("deeplink", deeplink)
                                        }

                                        FirebaseAnalytics.getInstance(applicationContext).logEvent("uac_deeplink_parsed", params)
                                    }
                                    mediaCamaign.lowercase().contains("challan") -> {
                                        val deeplink = sanDeeplinkJson.optString("challan", "acko.com://check_challan")
                                        storedDeepLinkUntilNextAppOpen =  StoredDeeplink(deeplink, DeeplinkSource.APPS_FLYER)
                                        val params = Bundle().apply {
                                            putString("mediaSource", mediaSource)
                                            putString("mediaCampaign", mediaCamaign)
                                            putString("lob", "central")
                                            putString("deeplink", deeplink)
                                        }

                                        FirebaseAnalytics.getInstance(applicationContext).logEvent("uac_deeplink_parsed", params)
                                    }
                                }
                            }
                        }
                        data["af_status"]?.let {
                            formattedData["install_type"]
                        }
                        sendInstallToFirebase(formattedData)
                    }

                }
            }

            override fun onConversionDataFail(error: String?) {
                Logger.e("AppsFlyer", "error onAttributionFailure :  $error")
            }

            override fun onAppOpenAttribution(data: MutableMap<String, String>?) {
                Logger.e("AppsFlyer", "app open attribute:  $data")

                data?.let { cvData ->
                    Logger.e("AppsFlyer", "app open attribute:  $cvData")
                    // processDeepLinkData(cvData)
                }
            }

            override fun onAttributionFailure(error: String?) {
                Logger.e("AppsFlyer", "error onAttributionFailure :  $error")
            }

        }
        AppsFlyerLib.getInstance().registerConversionListener(this, conversionDataListener)

    }

    private fun  sendInstallToFirebase(formattedData: MutableMap<String, String>) {
        val prefs = SharedPreference.appPrefs(this, false)
        val uuid: String? = prefs[Constants.PREF_UUID]
        val adId: String? = prefs[Constants.PREF_AD_ID]
        val bundle = Bundle()
        formattedData.forEach { (key, value) -> bundle.putString(key, value) }
        bundle.putString("ad_id", (adId ?: uuid))
        bundle.putString("uu_id", uuid)
        FirebaseAnalytics.getInstance(this).logEvent("install", bundle)
    }

    private fun startAppsflyerSDKSession(){
        AppsFlyerLib.getInstance().start(AckoApplication.getContext())
    }


    private fun subscribeDeeplink() {
        AppsFlyerLib.getInstance().subscribeForDeepLink { deepLinkResult ->
            sendAppsFlyerInitEvent(deepLinkResult)
            val dlStatus = deepLinkResult.status
            Logger.d("AppsFlyer", "deeplink $deepLinkResult")
            if (dlStatus == DeepLinkResult.Status.FOUND) {
                if (!isLaunchFromHistory() && deepLinkResult.deepLink != null && !deepLinkResult.deepLink.deepLinkValue.isNullOrEmpty()) {
                    runOnUiThread {
                        handleDeeplink(
                            deepLinkResult.deepLink.deepLinkValue,
                            source = DeeplinkSource.APPS_FLYER
                        )
                    }
                }
            } else {
                getDefferedDeeplinkFromFb()
            }
        }
    }

    private fun getDefferedDeeplinkFromFb() {
        AppLinkData.fetchDeferredAppLinkData(
            this
        ) { it ->
            Log.d("AppsFlyer", "received deferred deeplink from fb target $it")
            val map: HashMap<String, String> = HashMap()
            map["status"] = "fb_init_success"
            map["event_name"] = "facebook_initialised"
            if (it?.appLinkData != null)
                map["app_link_data"] = it.appLinkData.toString()
            it?.argumentBundle?.keySet()?.forEach { key ->
                if (it.argumentBundle?.get(key) != null)
                    map[key] = it.argumentBundle?.get(key).toString()
            }

            it?.targetUri?.let {
                Logger.d("AppsFlyer", "received deferred deeplink from fb target $it")
                triggerUdl(it)
                map["deep_link_value"] = it.toString()
            }
            if (isAppOpen) {
                runOnUiThread {
                    localMethodChannelExt?.invokeMethod(Constants.AF_LINK, map)
                    methodChannel?.invokeMethod(Constants.AF_LINK, map)
                }
            } else {
                fbInit = map;
            }
        }

    }

    private fun triggerUdl(targetUrl: Uri) {
        if (!CommonUtil.isOneLink(targetUrl.toString())) {
            handleDeeplink(targetUrl.toString(), source = DeeplinkSource.APPS_FLYER)
        }
        val intent = Intent()
        intent.data = targetUrl
        intent.action = Intent.ACTION_VIEW
        AppsFlyerLib.getInstance().performOnDeepLinking(intent, this)
    }


    /**
     * BEGIN **** Deeplink Related methods and operations ***
     */

    /**
     * calls open page if Home page is loaded, else stores it in `storedDeepLinkUntilNextAppOpen`
     */
    private fun handleDeeplink(deepLink: String?, source: DeeplinkSource?) {
        deepLink?.let {
            if (isMyAccountLoaded) {
                openPage(it)
            } else {
                storedDeepLinkUntilNextAppOpen = StoredDeeplink(deepLink, source)
            }
        }
    }

    /**
     * Only responsible to pass the final deeplink by invoking the flutter method channel
     */
    private fun openPage(link: String) {
        methodChannel?.invokeMethod(
            "open_page",
            link
        )
    }

    /**
     * Responsible for Extracting deeplink from app link and Notification Intents
     */
    private fun setDeeplink(dataIntent: Intent) {
        var deepLinkData: String? = null
        val isPushNotification = dataIntent.getBooleanExtra("is_notification_clicked", false)
        val link = dataIntent.getStringExtra(getString(R.string.deeplink))
        when {
            !link.isNullOrEmpty() -> deepLinkData = link
            !dataIntent.dataString.isNullOrEmpty() && !CommonUtil.isOneLink(dataIntent.dataString!!) ->
                deepLinkData = dataIntent.dataString!!
        }
        deepLinkData?.let {
            handleDeeplink(
                deepLinkData,
                if (isPushNotification) DeeplinkSource.PUSH_NOTIFICATION else DeeplinkSource.INTENT
            )
        }
    }

    /**
     * If a valid stored deeplink exists and the client requests for it explicitly before openPage is called,
     * It is assumed that the client has used the deeplink value and there is no pending deeplink to be opened.
     */
    fun launchDeeplink(): StoredDeeplink? {
        var link: StoredDeeplink? = null
        storedDeepLinkUntilNextAppOpen?.let {
            link = it
//            storedDeepLinkUntilNextAppOpen = null
        }
        return link
    }

    /**
     * Get info on stored deeplinik, without calling launch.
     */
    fun getStoredDeeplink(): Map<String, String>? {
        return storedDeepLinkUntilNextAppOpen?.toHashMap();
    }

    fun clearStoredDeeplink() {
        storedDeepLinkUntilNextAppOpen = null;
    }

    /**
     * Usually used by the client to set a pending deeplink to be opened after app home page has loaded
     */
    fun setStoredDeeplink(deeplink : String, source : DeeplinkSource) : Map<String, Any> {
        storedDeepLinkUntilNextAppOpen = StoredDeeplink(deeplink, source = source)
        return storedDeepLinkUntilNextAppOpen!!.toHashMap()
    }

    private fun createShortcuts() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N_MR1) {
            val shortcutManager = getSystemService(Context.SHORTCUT_SERVICE) as ShortcutManager

            val rapidResponseShortcut = buildShortcut(
                shortcutId = Constants.ShortcutConstants.RapidResponse.id,
                shortLabel = R.string.ambulance_short_label,
                longLabel = R.string.ambulance_long_label,
                shortcutIcon = R.drawable.ambulance_app_icon_2x_gray,
                deeplinkValue = Constants.ShortcutConstants.RapidResponse.deepLink)

            val fastagShortcut = buildShortcut(
                shortcutId = Constants.ShortcutConstants.Fastag.id,
                shortLabel = R.string.fastag_short_label,
                longLabel = R.string.fastag_long_label,
                shortcutIcon = R.drawable.fastag_app_icon_2x_gray,
                deeplinkValue = Constants.ShortcutConstants.Fastag.deepLink)


            val challanShortcut = buildShortcut(
                shortcutId = Constants.ShortcutConstants.Challan.id,
                shortLabel = R.string.challan_short_label,
                longLabel = R.string.challan_long_label,
                shortcutIcon = R.drawable.challan_app_icon_2x_gray,
                deeplinkValue = Constants.ShortcutConstants.Challan.deepLink)

            val coveragesShortcut = buildShortcut(
                shortcutId = Constants.ShortcutConstants.Coverages.id,
                shortLabel = R.string.policies_short_label,
                longLabel = R.string.policies_long_label,
                shortcutIcon = R.drawable.policies_app_icon_2x_gray,
                deeplinkValue = Constants.ShortcutConstants.Coverages.deepLink)

            shortcutManager?.dynamicShortcuts = listOf(rapidResponseShortcut,fastagShortcut,challanShortcut,coveragesShortcut,  )
        }
    }

    private fun buildShortcut(shortcutId: String,
                               @StringRes shortLabel: Int,
                               @StringRes longLabel: Int,
                               @DrawableRes shortcutIcon: Int,
                               deeplinkValue: String) : ShortcutInfo {
        return ShortcutInfo.Builder(this, shortcutId)
            .setShortLabel(getString(shortLabel))
            .setLongLabel(getString(longLabel))
            .setIcon(Icon.createWithResource(this, shortcutIcon))
            .setIntent(
                Intent(Intent.ACTION_VIEW).setClassName(
                    this,
                    AckoActivity::class.java.name
                ).apply {
                    putExtra(
                        getString(R.string.deeplink),
                        deeplinkValue
                    )
                }
            )
            .build()
    }

    private fun isLaunchFromHistory(): Boolean =
        intent.flags and Intent.FLAG_ACTIVITY_LAUNCHED_FROM_HISTORY == Intent.FLAG_ACTIVITY_LAUNCHED_FROM_HISTORY

    private fun sendAppsFlyerInitEvent(deepLinkResult: DeepLinkResult) {
        val formattedData: HashMap<String, String> = HashMap()
        formattedData["status"] = deepLinkResult.status.name
        formattedData["event_name"] = "appsflyer_initialised"
        if (deepLinkResult.status == DeepLinkResult.Status.FOUND) {
            deepLinkResult.deepLink.campaign?.let {
                formattedData["campaign"] = it
            }
            deepLinkResult.deepLink.mediaSource?.let {
                formattedData["media_source"] = it
            }
            appsFlyerDeeplinkMediaSource = formattedData["media_source"]
            deepLinkResult.deepLink.isDeferred?.let {
                formattedData["is_deferred"] = it.toString()
            }
            deepLinkResult.deepLink.deepLinkValue?.let {
                formattedData["deep_link_value"] = it
            }
            deepLinkResult.deepLink.campaignId?.let {
                formattedData["campaign_id"] = it
            }
        } else if (deepLinkResult.error != null) {
            formattedData["error"] = deepLinkResult.error.name
        }
        if (isAppOpen) {
            runOnUiThread {
                localMethodChannelExt?.invokeMethod(Constants.AF_LINK, formattedData)
                methodChannel?.invokeMethod(Constants.AF_LINK, formattedData)
            }
        } else {
            conversionData = formattedData
        }
    }

    private fun safetyCheck() {
        if (!BuildConfig.DEBUG) {
            checkRootDevice()
            checkTampering()
            FridaDetection.checkForFrida();
        }
    }

    private fun checkRootDevice() {
        val rootBeer = RootBeer(AckoApplication.getContext())
        if (rootBeer.isRooted) {
            Toast.makeText(this, "Root device detected", Toast.LENGTH_LONG).show()
            AlertDialog.Builder(this).setTitle("Alert")
                .setMessage("Your device is rooted. Acko application will not work on rooted device")
                .setCancelable(false)
                .setPositiveButton(
                    "Ok"
                ) { dialog, id ->
                    exitProcess(0)
                }.show()
        }
    }

    private fun checkTampering() {
        if (!BuildConfig.DEBUG && packageName.compareTo(Constants.PACKAGE_NAME) != 0 && packageName.compareTo(Constants.BETA_PACKAGE_NAME) != 0) {
            AlertDialog.Builder(this).setTitle("Alert")
                .setMessage(
                    "Tampering detected.Application will not work on properly." +
                            "Please download the app from play store and try again"
                )
                .setCancelable(false)
                .setPositiveButton(
                    "Ok"
                ) { dialog, id ->
                    exitProcess(0)
                }.show()
        }
    }

    override fun attachBaseContext(base: Context?) {
        MultiDex.install(this)
        super.attachBaseContext(base)
    }

    private fun deleteWebViewCache() {
        CoroutineScope(Dispatchers.IO).launch {
            val cacheDir = cacheDir.parent
            if (cacheDir != null) {
                val file = File(cacheDir) //Cache path
                file.let { cacheFile ->
                    if (cacheFile.exists()) {
                        if (cacheFile.isDirectory) {
                            val files = cacheFile.listFiles()
                            files?.let {
                                for (i in it.indices) {
                                    if (it[i].name.equals(
                                            "app_webview",
                                            true
                                        ) || it[i].name.equals(
                                            "WebView",
                                            true
                                        )
                                    ) {
                                        val delete = it[i].deleteRecursively()
                                        Logger.d("Flutter", "delete $delete")
                                    }
                                }

                            }
                        }
                    } else {
                        println("delete file no exists ")
                    }

                }
            }
        }
    }

    fun onUserLogout() {
        isMyAccountLoaded = false
        storedDeepLinkUntilNextAppOpen = null
    }

    fun onMyAccountLoaded() {
        showInAppUpdate()
        setUpJuspay()
        isMyAccountLoaded = true
        storedDeepLinkUntilNextAppOpen?.value?.let {
            openPage(it)
        }
    }

    private fun showInAppUpdate() {
        if (viewModel.flexiUpdate) {
            popupSnackbarForUpdate(
                shouldUpdateApp = true,
                message = "An app update is available",
                actionString = "UPDATE",
                snackbarDisplayLength = 10000
            )
        }
    }

    private fun popupSnackbarForUpdate(
        shouldUpdateApp: Boolean = false,
        message: String,
        actionString: String? = null,
        snackbarDisplayLength: Int = 5
    ) {
        methodChannel?.invokeMethod(
            "show_app_update", hashMapOf(
                "update_action_type" to shouldUpdateApp,
                "message" to message,
                "action_string" to actionString,
                "duration" to snackbarDisplayLength
            )
        )
    }

    private fun setUpJuspay() {
        CoroutineScope(Dispatchers.Default).launch {
            val preFetchPayload = JSONObject()

            try {
                val innerPayload = JSONObject()
                val clientId = "acko_android"
                innerPayload.put("clientId", clientId)
                preFetchPayload.put("payload", innerPayload)
                preFetchPayload.put("service", "in.juspay.hyperpay")
            } catch (e: JSONException) {
                e.printStackTrace()
            }
            HyperServices.preFetch(this@AckoActivity, preFetchPayload)
        }

    }

//    override fun onCommunication(
//        dto: FlutterCommunicatorDto,
//        onResult: (MutableMap<String, String>?) -> Unit
//    ) {
//        when (dto.state) {
//            FlutterCommunicationState.MY_ACCOUNT_LOADED -> onMyAccountLoaded()
//            FlutterCommunicationState.APP_OPEN -> appOpenInit()
//            FlutterCommunicationState.PLAY_LOADED -> onPlaystoreLoaded()
//            FlutterCommunicationState.LOAD_APP_RATING -> loadAppRatingOnHomePage(dto.shouldDisplayRatings)
//            FlutterCommunicationState.DISPLAY_NUMBER_HINT -> onDisplayNumberHint()
//            FlutterCommunicationState.START_SMS_LISTENER -> startSmsListener()
//            FlutterCommunicationState.HEALTH_KIT_PERMISSION -> healthKitPermission()
//            FlutterCommunicationState.COMPLETE_IN_APP_UPDATE -> {}
//            FlutterCommunicationState.START_IN_APP_UPDATE -> {}
//            null
//            -> { /*Do nothing*/
//            }
//        }
//    }

    fun completeInAppUpdate() {
        appUpdateManager?.completeUpdate()
    }

    fun startInAppUpdateFromCom() {
        startInAppUpdate(
            viewModel.updateInfo,
            isImmediateUpdate = false
        )
    }

    fun appOpenInit() {
        checkIfAppInstalled()
        isAppOpen = true
        if (fbInit != null) {
            methodChannel?.invokeMethod(Constants.AF_LINK, fbInit)
            fbInit = null
        }
        if (conversionData != null) {
            methodChannel?.invokeMethod(Constants.AF_LINK, conversionData)
            conversionData = null
        }
    }

    fun checkIfAppInstalled() {
        if (!getPreferences(MODE_PRIVATE).getBoolean(
                AckoActivity.PREF_CHECK_INSTALL_REFERRER,
                false
            )
        ) {
            val params = "parameters" to hashMapOf(
                "platform" to "app_android",
                "redirection_url" to referrerUrl
            )

            methodChannel?.invokeMethod(
                "send_events",
                hashMapOf(
                    "event_name" to "acko_app_install",
                    params
                )
            )
            getPreferences(MODE_PRIVATE).edit()
                .putBoolean(AckoActivity.PREF_CHECK_INSTALL_REFERRER, true).apply()
        }
    }

    fun startSmsListener() {
        smsRetrievalReceiver = SmsRetrievalReceiver()
        smsRetrievalReceiver?.initOTPRetrievalListener(this)
        registerReceiver(smsRetrievalReceiver, IntentFilter().also {
            it.addAction(SmsRetriever.SMS_RETRIEVED_ACTION)
        })
        val client = SmsRetriever.getClient(this /* context */)
        val task = client.startSmsRetriever()

        task.addOnSuccessListener {

            Logger.d("sms_read", "connection success")
        }

        task.addOnFailureListener {

            Logger.d("sms_read", "connection fail")
        }
    }

    private fun startInAppUpdate(it: AppUpdateInfo?, isImmediateUpdate: Boolean) {
        it?.let { info ->
            try {
                appUpdateManager?.startUpdateFlowForResult(
                    info,
                    if (isImmediateUpdate) AppUpdateType.IMMEDIATE else AppUpdateType.FLEXIBLE,
                    this,
                    if (isImmediateUpdate) AckoActivity.REQUEST_CODE_IMMEDIATE_UPDATE else AckoActivity.REQUEST_CODE_FLEXI_UPDATE
                )
            } catch (e: Exception) {
                //intent exception
                FirebaseCrashlytics.getInstance().recordException(e)
                e.printStackTrace()
            }
        }
    }

    fun onDisplayNumberHint() {
        val hintRequest: HintRequest = HintRequest.Builder()
            .setPhoneNumberIdentifierSupported(true)
            .build()
        val intent: PendingIntent =
            Credentials.getClient(AckoApplication.getContext())
                .getHintPickerIntent(hintRequest)
        startIntentSenderForResult(
            intent.intentSender,
            AckoActivity.PHONE_NUMBER_PICKER_HINT,
            null,
            0,
            0,
            0
        )
    }

    fun loadAppRatingOnHomePage(shouldDisplayRatings: Boolean) {
        if (!this.isFinishing && !this.isDestroyed && shouldDisplayRatings && !AppRatingFragment().isVisible) {
            AppRatingFragment().apply {
                isCancelable = false
                show(
                    supportFragmentManager,
                    AppRatingFragment::class.java.name
                )
            }
        }
    }

    fun onPlaystoreLoaded() {
        viewModel.updateAppRattingPrefs.value = AppRatingsPrefs(
            isHomeRatingViewShown = false,
            isUserRated = true
        )
        viewModel.trackAppRatingEvents.value = AppRatingEvent(
            event = "in_app_rating_homepage_yes_clicked",
            pageTitle = "Home Rating Page"
        )
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (requestCode) {
            REQUEST_CODE_FLEXI_UPDATE -> {
                when (resultCode) {
                    Activity.RESULT_OK -> {
                        methodChannel?.invokeMethod(
                            Constants.IS_APP_UPDATE,
                            Constants.FLEXI_UPDATE_DIALOG_CTA
                        )
                        //Events.trackAppUpdateEvent(TrackerStates.FLEXI_UPDATE_DIALOG_CTA)
                        popupSnackbarForUpdate(
                            shouldUpdateApp = false,
                            message = "Your new version of app is downloading...",
                            snackbarDisplayLength = 10000
                        )
                    }

                    Activity.RESULT_CANCELED, ActivityResult.RESULT_IN_APP_UPDATE_FAILED -> {
                        methodChannel?.invokeMethod(
                            Constants.IS_APP_UPDATE,
                            Constants.FLEXI_DIALOG_CANCEL_CTA
                        )
                        //Events.trackAppUpdateEvent(TrackerStates.FLEXI_DIALOG_CANCEL_CTA)
                    }

                }
            }

            REQUEST_CODE_IMMEDIATE_UPDATE -> {
                when (resultCode) {
                    Activity.RESULT_CANCELED, ActivityResult.RESULT_IN_APP_UPDATE_FAILED -> checkForUpdate()
                }
            }

            PHONE_NUMBER_PICKER_HINT -> {
                if (resultCode == Activity.RESULT_OK) {
                    val credentials = data?.getParcelableExtra<Credential>(Credential.EXTRA_KEY)
                    credentials?.let {
                        localMethodChannel?.invokeMethod(
                            "phone_number_received",
                            it.id.substring(3)
                        )
                        Logger.d("phone_hint", "phone number " + it.id)
                    }

                }
            }

            HEALTH_DATA_REQUEST_CODE -> {
                //do handle with health activity resultant data
            }
        }
        filePicker?.onActivityResult(
            requestCode,
            resultCode,
            data
        )
        for (fragment in supportFragmentManager.fragments)
            fragment.onActivityResult(requestCode, resultCode, data)
    }

    override fun onDestroy() {
        handler.removeCallbacksAndMessages(null)
        val flutterEngine =
            FlutterEngineCache.getInstance().get(Constants.FLUTTER_ENGINE)
        flutterEngine?.platformViewsController?.detach()
        flutterEngine?.platformViewsController?.destroyOverlaySurfaces()
        appUpdateManager?.unregisterListener(this)
        viewModel.isBottomTabsCreated = false
//        flutterFragment?.onDestroy()
//        flutterFragment = null
        super.onDestroy()
        flutterEngine?.destroy()
        FlutterEngineCache.getInstance().remove(Constants.FLUTTER_ENGINE)
    }

    override fun onStateUpdate(state: InstallState) {
        if (state.installStatus() == InstallStatus.DOWNLOADED) {
            popupSnackbarForUpdate(
                shouldUpdateApp = false,
                message = "An update has been downloaded.",
                actionString = "RESTART",
                snackbarDisplayLength = 10000
            )
        } else if (state.installStatus() == InstallStatus.FAILED) {
            popupSnackbarForUpdate(
                shouldUpdateApp = false,
                message = "An update failed",
                snackbarDisplayLength = 2
            )
        } else if (state.installStatus() == InstallStatus.INSTALLED) {
            popupSnackbarForUpdate(
                shouldUpdateApp = false,
                message = "Your new version of app installed successfully.",
                snackbarDisplayLength = 5
            )
        } else if (state.installStatus() == InstallStatus.DOWNLOADING) {
            if (state.bytesDownloaded() < 10)
                popupSnackbarForUpdate(
                    shouldUpdateApp = false,
                    message = "Your new version of app is downloading...",
                    snackbarDisplayLength = 5
                )
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String>,
        grantResults: IntArray
    ) {
        filePicker?.onRequestPermissionResult(
            requestCode,
            grantResults
        )
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
    }

    override fun sendEvent(data: AppRatingEvent) {
        methodChannel?.invokeMethod(
            "app_rating_event",
            HashMap<String, Any>().apply {
                data.event?.let { put("event", it) }
                data.pageTitle?.let { put("page_title", it) }
                data.feedback?.let { put("feedback", it) }
            }
        )
    }

    override fun onOtpReceived(otp: String) {
        localMethodChannel?.invokeMethod("otp_received", otp)
        if (smsRetrievalReceiver != null) {
            unregisterReceiver(smsRetrievalReceiver)
            smsRetrievalReceiver = null
        }
    }

    override fun onOtpReadTimeOut() {
    }

    override fun onOtpReceivedError(error: String) {
    }
}

fun logMessage(message: String) {
    FirebaseCrashlytics.getInstance().log("Native android $message")
}
