package com.acko.android.feature.notification

import android.Manifest
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.util.Log
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationCompat.PRIORITY_HIGH
import androidx.core.app.NotificationManagerCompat
import androidx.core.content.ContextCompat
import com.acko.android.AckoActivity
import com.acko.android.AckoApplication
import com.acko.android.BuildConfig
import com.acko.android.R
import com.acko.android.util.Constants
import com.acko.android.util.ext.SharedPreference
import com.acko.android.util.ext.SharedPreference.set
import com.appsflyer.AppsFlyerLib
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import com.squareup.picasso.Picasso
import com.webengage.sdk.android.WebEngage
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.dart.DartExecutor
import io.flutter.plugin.common.MethodChannel
import io.flutter.view.FlutterCallbackInformation
import io.flutter.embedding.engine.loader.FlutterLoader
import io.flutter.FlutterInjector
import java.util.Locale

class AckoDroidMessagingService : FirebaseMessagingService() {


    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)
        //Ignore AppsFlyer uninstall tracking notifications
        if (remoteMessage.data.containsKey("af-uinstall-tracking")) {
            sendNotificationStatus()
            return
        }

        val data = remoteMessage.data
        if (data.containsKey("source") && "webengage" == data["source"]) {
            WebEngage.get().receive(data)
        } else if (data.containsKey("source") && data["source"] == "acko") {
            showDownloadCompleteNotification(this, remoteMessage)
            sendSegmentEventToDart(data)
            sendNotificationReceivedStatus(remoteMessage)
            val bundle = Bundle()
            bundle.putString("lob", data["lob"])
            bundle.putString("notification_name", data["name"])
            FirebaseAnalytics.getInstance(this).logEvent("notification_received", bundle)
        }
    }

    private fun sendEventToDart(eventName: String, eventProperties: HashMap<String, Any?>){
        eventProperties.put("android_config_data", hashMapOf(
            "base_url" to BuildConfig.BASE_URL,
            "app_version" to BuildConfig.VERSION_NAME,
            "build_number" to BuildConfig.VERSION_CODE,
            "health_base_url" to BuildConfig.HEALTH_BASE_URL,
            "webview" to "android_app",
            "build_flavor" to BuildConfig.FLAVOR,
        )
        )
        val mainHandler = Handler(Looper.getMainLooper())
        val runnable = Runnable {
            val backgroundFlutterEngine = FlutterEngine(applicationContext)
            val initNativeToDartCommunicationHandlerCallback =
                SharedPreference.appPrefs(applicationContext, false)
                    .getLong("CALLBACK_DISPATCHER_HANDLE_KEY", 0)

            if(initNativeToDartCommunicationHandlerCallback.toInt() != 0){
                val callbackInfo = FlutterCallbackInformation.lookupCallbackInformation(
                    initNativeToDartCommunicationHandlerCallback
                )
                val loader = FlutterInjector.instance().flutterLoader()
                loader.startInitialization(applicationContext)
                loader.ensureInitializationComplete(applicationContext, null)

                val args = DartExecutor.DartCallback(
                    applicationContext.assets,
                    loader.findAppBundlePath(),
                    callbackInfo
                )
                backgroundFlutterEngine.dartExecutor.executeDartCallback(args)

                val mBackgroundChannel = MethodChannel(
                    backgroundFlutterEngine.getDartExecutor().getBinaryMessenger(),
                    Constants.DART_COMMUNITCATION_HELPER_CHANNEL
                )
                Log.e("sharandebug", "notificationnn3")
                mBackgroundChannel.invokeMethod(eventName, eventProperties)
            }
        }
        mainHandler.post(runnable)
    }

    private fun sendNotificationStatus(){
        val notificationManager = applicationContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        val notificationsEnabled = notificationManager.areNotificationsEnabled()
        sendEventToDart("notification_enable_status", HashMap<String, Any?>().apply {
            put("notification_enabled", notificationsEnabled)
            put("device_id", Settings.Secure.getString(applicationContext.contentResolver, Settings.Secure.ANDROID_ID))
        })
    }

    private fun sendNotificationReceivedStatus(remoteMessage: RemoteMessage){
        sendEventToDart("communication_service_notification_status_update", getCommunicationServiceParams(remoteMessage,
            "DELIVERED"))
    }

    private fun getCommunicationServiceParams(remoteMessage: RemoteMessage, status: String): HashMap<String, Any?>{
        val requestId = remoteMessage.data["request_id"]
        return  HashMap<String, Any?>().apply {
                put("status", status)
                put("request_id", requestId)
                put("external_id", remoteMessage.messageId)
                put("data", remoteMessage.data)
            }
    }

    private fun sendSegmentEventToDart(data: Map<String, String>) {
        sendEventToDart("sendSegmentEvent", HashMap<String, Any?>().apply {
            put("event_name", "push_notification_receive_success")
            put("product", data["lob"])
            put("name", data["name"])
            put("type", data["type"])
            put("platform", "Android")
            put("deeplink_url", data["deeplink"])
        })
    }

    private fun showDownloadCompleteNotification(
        context: Context,
        remoteMessage: RemoteMessage,
    ) {

        val NOTIF_ID = (0..1000).random()

        val builder = NotificationCompat.Builder(context, "notification")
        val data = remoteMessage.data
        builder
            .setSmallIcon(R.drawable.ic_notification)
            .setColor(ContextCompat.getColor(context, R.color.purple))
            .setOngoing(false)
            .setPriority(PRIORITY_HIGH)
            .setAutoCancel(true)
            .setChannelId("111")
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)

        val title = data["title"]
        val style = NotificationCompat.BigTextStyle()
        if (!title.isNullOrEmpty()) {
            style.setBigContentTitle(title)
            builder.setContentTitle(title)
        }

        val content = data["content"]
        if (!content.isNullOrEmpty()) {
            style.bigText(content)
            builder.setContentText(content)
        }

        val bigPictureUrl = data["image_url"]
        if (!bigPictureUrl.isNullOrEmpty()) {
            val bitmap = Picasso.get().load(bigPictureUrl).get()
            builder.setStyle(
                NotificationCompat.BigPictureStyle()
                    .bigPicture(bitmap)
            )
        } else {
            builder.setStyle(style)
        }
        val intent = Intent(this, NotificationClickHandlerActivity::class.java).apply {
            putExtra("title", title)
            putExtra("content", content)
            putExtra("is_notification_clicked", true)
            if (data.containsKey("deeplink"))
                putExtra(getString(R.string.deeplink), data["deeplink"])
            if (data.containsKey("type"))
                putExtra("type", data["type"])
            if (data.containsKey("lob"))
                putExtra("lob", data["lob"])
            if (data.containsKey("name"))
                putExtra("name", data["name"])
            putExtra("communication_service_notification_status_update", getCommunicationServiceParams(remoteMessage, "CLICKED"))
            putExtra("is_notification_clicked", true)
            action = "notification_data"
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        // Create an Intent for the activity you want to start
// Create the TaskStackBuilder
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        builder.setContentIntent(
            pendingIntent
        )

        val notificationManager = NotificationManagerCompat.from(context)
        if (Build.VERSION.SDK_INT >= 26) {
            val notificationChannel = NotificationChannel(
                "111", "Acko communication",
                NotificationManager.IMPORTANCE_HIGH
            )
            notificationChannel.description = "Get your important insurance update!"
            notificationManager.createNotificationChannel(notificationChannel)
        }
        if (ActivityCompat.checkSelfPermission(
                this,
                Manifest.permission.POST_NOTIFICATIONS
            ) != PackageManager.PERMISSION_GRANTED
        ) { return }
        notificationManager.notify(NOTIF_ID, builder.build())

    }

    override fun onNewToken(newToken: String) {
        super.onNewToken(newToken)
        Log.d("Token", newToken)
        val prefs = SharedPreference.appPrefs(AckoApplication.getContext(), false)
        prefs["fcm_token"] = newToken
        WebEngage.get().setRegistrationID(newToken)
        AppsFlyerLib.getInstance().updateServerUninstallToken(
            applicationContext,
            newToken
        )
        val notificationManager = applicationContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        val notificationsEnabled = notificationManager.areNotificationsEnabled()
        sendEventToDart("update_fcm_token", HashMap<String, Any?>().apply {
            put("fcm_token", newToken)
            put("device_id", Settings.Secure.getString(applicationContext.contentResolver, Settings.Secure.ANDROID_ID))
            put("notification_enabled", notificationsEnabled)
        })
    }
}