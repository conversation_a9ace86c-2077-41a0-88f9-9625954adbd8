package com.acko.android.util

import android.annotation.SuppressLint
import android.content.Context
import android.provider.Settings
import android.util.Log
import com.acko.android.AckoActivity
import com.acko.android.AckoApplication
import com.acko.android.BuildConfig
import com.acko.android.R
import com.acko.android.base.common.CustomInAppCallbacks
import com.acko.android.base.common.CustomPushNotificationCallBack
import com.acko.android.util.ext.SharedPreference
import com.acko.android.util.ext.SharedPreference.get
import com.acko.android.util.ext.SharedPreference.set
import com.appsflyer.AppsFlyerLib
import com.facebook.FacebookSdk
import com.google.firebase.messaging.FirebaseMessaging
import com.webengage.sdk.android.PushChannelConfiguration
import com.webengage.sdk.android.WebEngage
import com.webengage.sdk.android.WebEngageActivityLifeCycleCallbacks
import com.webengage.sdk.android.WebEngageConfig
import java.util.*


object AnalyticsHelper{
    fun init(context: Context) {
        initWebEngage(context)
        initAppsflyer(context)
    }

    private fun initAppsflyer(context: Context) {
        val appsflyer = AppsFlyerLib.getInstance()
        appsflyer.setDebugLog(BuildConfig.DEBUG)
        appsflyer.init("nvTCkGCccZLQAC6zydZDMJ", null, context)
        val prefs = SharedPreference.appPrefs(context, false)
        FacebookSdk.setAutoInitEnabled(true)
        FacebookSdk.fullyInitialize()
        FacebookSdk.setIsDebugEnabled(BuildConfig.DEBUG)
        FacebookSdk.setAutoLogAppEventsEnabled(true)
        FacebookSdk.setAdvertiserIDCollectionEnabled(true)
        AppsFlyerLib.getInstance().setAndroidIdData(Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID));
        val uuid: String? = prefs[Constants.PREF_UUID]
        if (uuid != null) {
            AppsFlyerLib.getInstance().setCustomerUserId(uuid)
        } else {
            val newUuid = UUID.randomUUID().toString()
            prefs[Constants.PREF_UUID] = newUuid
            AppsFlyerLib.getInstance().setCustomerUserId(newUuid)
        }
    }

    private fun initWebEngage(context: Context) {
        val pushChannelConfiguration = PushChannelConfiguration.Builder()
            .setNotificationChannelName("Offers")
            .setNotificationChannelDescription("Product updates")
            .setNotificationChannelImportance(4)
            .build()

        val webEngageConfig = WebEngageConfig.Builder()
            .setWebEngageKey(context.getString(R.string.web_engage_license_key))
            .setDefaultPushChannelConfiguration(pushChannelConfiguration)
            .setPushAccentColor(R.color.colorPrimary)
            .setPushLargeIcon(R.drawable.ic_notification_icon)
            .setPushSmallIcon(R.drawable.ic_notification)
            .setDebugMode(com.google.firebase.crashlytics.BuildConfig.DEBUG) // only in development mode
            .build()

        (context as AckoApplication).registerActivityLifecycleCallbacks(
            WebEngageActivityLifeCycleCallbacks(
                context,
                webEngageConfig
            )
        )
        WebEngage.registerPushNotificationCallback(CustomPushNotificationCallBack())
        WebEngage.registerInAppNotificationCallback(CustomInAppCallbacks())
        FirebaseMessaging.getInstance().token.addOnSuccessListener {
            Log.e("firebase-token", it)
//            Log.i("device-details", getDeviceDetails())
            val prefs = SharedPreference.appPrefs(AckoApplication.getContext(), false)
            prefs["fcm_token"] = it
            WebEngage.get().setRegistrationID(it)
        }
    }

}