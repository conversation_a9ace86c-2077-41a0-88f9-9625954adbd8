# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/
android/.gradle/*/**
android/.gradle/
android/app/src/main/java/
android/gradle/wrapper/gradle-wrapper.jar
android/gradlew
android/gradlew.bat
android/local.properties
android/app/.cxx

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/
pubspec.lock
.ios
.android
.vscode

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.github/
.pub-cache/
.pub/
/build/
android/app/build
android/app/build/**/*
.fvm/flutter_sdk

# Web related
lib/generated_plugin_registrant.dart

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release
/android/app/prod
/android/app/prod_flutter/*/**
/android/app/prod/*/**
/android/app/build
/android/gradlew
/android/gradlew.bat
/android/local.properties
/android/.gradle/
/android/gradle/wrapper/gradle-wrapper.jar
/android/app/src/main/java/io/flutter/plugins/GeneratedPluginRegistrant.java

/assets/security/
