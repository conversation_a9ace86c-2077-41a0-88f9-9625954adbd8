library acko_web_view_module;

import 'dart:io';

import 'package:acko_logger/acko_logger.dart';
import 'package:acko_logger/events/info/webview_info_event.dart';
import 'package:acko_logger/model/info_model.dart';
import 'package:acko_web_view_module/common/AckoAppBarActionWidget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:lottie/lottie.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/utilities.dart';

import '../bloc/bloc.dart';
import '../bloc/states.dart';
import '../common/appbar_action.dart';
import '../common/custom_dartz.dart';
import '../lob_contract_classes/view_contract.dart';
import '../web_view_registers.dart';

class AckoWebView extends StatefulWidget {
  final String url;

  const AckoWebView({Key? key, required this.url}) : super(key: key);

  @override
  State<AckoWebView> createState() => AckoWebViewState();
}

class AckoWebViewState extends State<AckoWebView>
    with WidgetsBindingObserver
    implements RouteAware {
  InAppWebViewController? _controller;

  late AckoWebViewBloc _bloc;

  String _lastScrollableAllowedUrl = "";
  bool _showPageLoader = false;
  bool _canShowNativeAppBar = true;
  List<Widget> actionsList = List.empty(growable: true);

  bool _cookiesInitialized = false;

  @override
  void initState() {
    super.initState();
    _bloc = BlocProvider.of(context);
    _logInitEvent();
    setInitialConfig();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _updateLobsContextAndController();
    });
    if (DownloadableFileType.extensions
        .any((ext) => widget.url.toLowerCase().endsWith(ext))) {
      setupDownloadListener();
    }
  }

  void setupDownloadListener() {
    if (Platform.isAndroid) {
      Constants.LOCAL_TO_SCREEN_PLATFORM_CHANNEL
          .setMethodCallHandler((call) async {
        if (call.method == 'onDownloadComplete') {
          final filePath = call.arguments['filePath'];
          if (filePath != null) {
            Constants.PLATFORM_CHANNEL
                .invokeMethod("open_file", {"path": filePath.toString()});
          }
          Navigator.pop(context);
        }
      });
    }
  }

  _logInitEvent() {
    AckoLoggerManager.instance.logInfo(
      event: WebviewInfoEvent(
        webviewCurrentState: "webview_init",
        url: widget.url, page: 'webview'
      )
    );
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
  }

  addAppBarActions(List<AppBarAction> actionsWidget) {
    setState(() {
      actionsList.clear();
      if (actionsWidget.isEmpty) return;
      actionsList = actionsWidget
          .map((action) => AckoAppBarActionWidget(
              appBarAction: action,
              onAppBarActionPressed: onAppBarActionPressed))
          .toList();
    });
  }

  onAppBarActionPressed(String? actionValue) {
    if ((actionValue ?? "").trim().isEmpty) return;
    for (int index = 0; index < WebViewRegisters.lobs.length; index++) {
      Either<NotHandlingMethod, HandlingVoidMethod> result =
          WebViewRegisters.lobs[index].onAppBarActionPressed(actionValue!);
      if (result.isRight()) break;
    }
  }

  refreshPage() {
    setState(() {});
  }

  void blocStateListener(WebViewState state) {
    for (var lob in WebViewRegisters.lobs) {
      lob.blocStateListener(state);
    }
  }

  setInitialConfig() {
    _bloc.setCookies(widget.url).then((value) {
      ///If delay is not added, cookies might not be properly detected in the webflow for a small period
      Future.delayed(Duration(milliseconds: 300), () {
        if (!mounted) return;
        setState(() {
          _cookiesInitialized = true;
        });
      });
    });
    Uri uri = Uri.parse(widget.url);
    if (uri.queryParameters.containsKey('hide_app_bar') &&
        uri.queryParameters['hide_app_bar']!.trim().toLowerCase() == 'true') {
      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        if (!mounted) return;
        setState(() {
          _canShowNativeAppBar = false;
        });
      });
    }
  }

  AppBar _getDefaultAppBar() {
    return AppBar(
      automaticallyImplyLeading: false,
      elevation: 0.0,
      centerTitle: false,
      toolbarHeight: 52.0,
      titleSpacing: 0,
      backgroundColor: Colors.white,
      actions: actionsList,
      title: IconButton(
        icon: Icon(
          Icons.arrow_back_ios_rounded,
          color: Color(0xff36354C),
        ),
        onPressed: () async {
          goBackPressed(context);
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: Platform.isAndroid,
      body: AckoWillPopScope(
        onWillPop: () => goBackPressed(context),
        child: SafeArea(
          maintainBottomViewPadding: Platform.isIOS,
          child: BlocConsumer<AckoWebViewBloc, WebViewState>(
            builder: (BuildContext context, state) {
              return Column(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (_canShowNativeAppBar) _getDefaultAppBar(),
                  if (_cookiesInitialized) Expanded(child: _getWebView()),
                ],
              );
            },
            listener: (BuildContext context, WebViewState? state) {
              if (state != null) blocStateListener(state);
            },
          ),
        ),
      ),
    );
  }

  _getWebView() => Stack(
        children: [
          InAppWebView(
            initialUrlRequest: URLRequest(url: WebUri(widget.url)),
            initialSettings: _buildInAppWebViewOptions(),
            onDownloadStartRequest: _onDownloadStart,
            shouldOverrideUrlLoading: _shouldOverrideUrlLoading,
            onCreateWindow: _onCreateWindow,
            onGeolocationPermissionsShowPrompt:
                _onGeolocationPermissionsShowPrompt,
            onPermissionRequest: _onPermissionRequest,
            onPageCommitVisible: _onPageCommitVisible,
            onWebViewCreated: _onWebViewCreated,
            onLoadStart: _onLoadStart,
            onLoadStop: _onLoadStop,
            onReceivedError: _onLoadError,
            onConsoleMessage: _onConsoleMessage,
            onCloseWindow: _onCloseWindow,
            onProgressChanged: _onProgressChange,
          ),
          if (_showPageLoader) _getPageLoader()
        ],
      );

  InAppWebViewSettings _buildInAppWebViewOptions() => InAppWebViewSettings(
        useHybridComposition: true,
        useOnRenderProcessGone: false,
        supportMultipleWindows: false,
        domStorageEnabled: true,
        allowFileAccess: true,
        cacheEnabled: false,
        javaScriptEnabled: true,
        useOnDownloadStart: true,
        javaScriptCanOpenWindowsAutomatically: true,
        mediaPlaybackRequiresUserGesture: false,
        useShouldOverrideUrlLoading: true,
        allowsInlineMediaPlayback: true,
      );

  Future<GeolocationPermissionShowPromptResponse?>
      _onGeolocationPermissionsShowPrompt(
          InAppWebViewController controller, String origin) async {
    return GeolocationPermissionShowPromptResponse(
        origin: origin, allow: true, retain: true);
  }

  _onPageCommitVisible(controller, url) {
    for (int index = 0; index < WebViewRegisters.lobs.length; index++) {
      WebViewRegisters.lobs[index].onPageCommitVisible(controller, url);
    }
  }

  _getPageLoader() => Align(
        alignment: Alignment.center,
        child: Container(
            color: Colors.transparent,
            height: 100,
            width: 100,
            child: Center(
                child: Lottie.asset(
              'assets/anim/timeline.json',
            ))),
      );

  void _onLoadStart(InAppWebViewController controller, Uri? url) {
    for (int index = 0; index < WebViewRegisters.lobs.length; index++) {
      WebViewRegisters.lobs[index].onLoadStart(controller, url);
    }
  }

  void _onLoadStop(InAppWebViewController controller, Uri? url) {
    for (int index = 0; index < WebViewRegisters.lobs.length; index++) {
      WebViewRegisters.lobs[index].onLoadStop(controller, url);
    }
  }

  void _onCloseWindow(InAppWebViewController controller) {
    for (int index = 0; index < WebViewRegisters.lobs.length; index++) {
      WebViewRegisters.lobs[index].onCloseWindow(controller);
    }
  }

  void pageLoadCompleted(InAppWebViewController controller){
    for (int index = 0; index < WebViewRegisters.lobs.length; index++) {
      WebViewRegisters.lobs[index].onPageCompleteLoaded(controller);
    }
  }

  void _onProgressChange(InAppWebViewController controller, int progress) {
    if(progress == 100){
      pageLoadCompleted(controller);
    }

    if ((progress / 100) < 0.3) {
      if (!_showPageLoader) {
        setState(() {
          _showPageLoader = true;
        });
      }
    } else {
      if (_showPageLoader) {
        setState(() {
          _showPageLoader = false;
        });
      }
    }
  }

  void _onWindowFocus(InAppWebViewController controller) {}

  void _onConsoleMessage(
      InAppWebViewController controller, ConsoleMessage message) {
    for (int index = 0; index < WebViewRegisters.lobs.length; index++) {
      WebViewRegisters.lobs[index].onConsoleMessage(controller, message);
    }
  }

  void _onLoadError(
    InAppWebViewController controller,
    WebResourceRequest request,
    WebResourceError error,
  ) {
    for (int index = 0; index < WebViewRegisters.lobs.length; index++) {
      WebViewRegisters.lobs[index]
          .onLoadError(controller, request.url, 500, error.description);
    }
  }

  void _onWebViewCreated(InAppWebViewController controller) async {
    _controller = controller;
    _updateLobsContextAndController();
    _addJsHandlers();
    _addBaseJSHandler();
    for (int index = 0; index < WebViewRegisters.lobs.length; index++) {
      WebViewRegisters.lobs[index].onWebViewCreated(controller);
    }
  }

  _addBaseJSHandler() {
    _controller!.addJavaScriptHandler(
        handlerName: "WebViewConnect",
        callback: (args) async {
          if (args.length > 0) {
            for (int index = 0; index < WebViewRegisters.lobs.length; index++) {
              Either<NotHandlingMethod, dynamic> res = await WebViewRegisters
                  .lobs[index]
                  .handleWebViewActions(args[0], _controller!);
              if (res.isRight()) {
                return Future.value(res.getOrElse(() => null));
              }
            }
          }
        });
  }

  _addJsHandlers() {
    for (int index = 0; index < WebViewRegisters.lobs.length; index++) {
      WebViewRegisters.lobs[index].addJsHandler(_controller!);
    }
  }

  Future<NavigationActionPolicy> _shouldOverrideUrlLoading(
      InAppWebViewController controller, NavigationAction action) async {
    for (int index = 0; index < WebViewRegisters.lobs.length; index++) {
      Either<NotHandlingMethod, NavigationActionPolicy> res =
          await WebViewRegisters.lobs[index]
              .shouldOverrideUrlLoading(controller, action);
      if (res.isRight()) {
        return res.getOrElse(() => NavigationActionPolicy.CANCEL);
      }
    }

    /// default return
    return Future.value(NavigationActionPolicy.ALLOW);
  }

  Future<bool> goBackPressed(BuildContext context) async {
    for (int index = 0; index < WebViewRegisters.lobs.length; index++) {
      Either<NotHandlingMethod, bool> result =
          await WebViewRegisters.lobs[index].canGoBack(_controller!);
      if (result.isRight()) return Future.value(result.getOrElse(() => true));
    }
    StateProvider().notify(ObserverState.REFRESH_SUPPORT_TAB);

    if (await _controller!.canGoBack()) {
      WebHistory? history = await _controller!.getCopyBackForwardList();
      if (history != null) {
        if (history.currentIndex! < 1 && Navigator.of(context).canPop()) {
          Navigator.of(context).pop(true);
          return Future.value(true);
        } else {
          _controller!.goBack();
          return Future.value(false);
        }
      }
    }

    if (mounted) {
      Navigator.pop(context, true);
      return Future.value(true);
    } else {
      return Future.value(false);
    }
  }

  Future<PermissionResponse?> _onPermissionRequest(
      InAppWebViewController controller, PermissionRequest request) async {
    return PermissionResponse(
      resources: request.resources,
      action: PermissionResponseAction.GRANT,
    );
  }

  void _onLoadResource(controller, resource) {}

  Future<void> _onDownloadStart(InAppWebViewController controller,
      DownloadStartRequest downloadStartRequest) async {
    for (int index = 0; index < WebViewRegisters.lobs.length; index++) {
      Either<NotHandlingMethod, void> result = await WebViewRegisters
          .lobs[index]
          .onDownloadStart(controller, downloadStartRequest);
      if (result.isRight()) break;
    }
  }

  Future<bool> _onCreateWindow(
      InAppWebViewController controller, CreateWindowAction request) {
    for (int index = 0; index < WebViewRegisters.lobs.length; index++) {
      final res =
          WebViewRegisters.lobs[index].onCreateWindow(controller, request);
      if (res.isRight()) break;
    }
    return Future.value(true);
  }

  _updateLobsContextAndController() {
    WebViewRegisters.instance.setWebViewConfig(context, widget.url, this);
  }

  @override
  void didPop() {
    _updateLobsContextAndController();
  }

  @override
  void didPopNext() {}

  @override
  void didPush() {}

  @override
  void didPushNext() {}

  handlePageLoaderVisibility(bool showLoader) {
    if (_showPageLoader != showLoader) {
      setState(() {
        _showPageLoader = showLoader;
      });
    }
  }
}
