import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../sdui.dart';
import 'package:sdui/src/core/extensions/string_extensions.dart';

class IntentLauncherAction extends SDUIAction {
  String? url;
  Map<String, dynamic>? queryParams;
  final List<AnalyticsAction> analytics = [];

  IntentLauncherAction({this.url});

  IntentLauncherAction.fromJson(Map<String, dynamic>? json) {
    if (json != null) {
      if (json['actionName'] != null) {
        type = json['actionName'].toString().getSDUIActionEnum();
      }
      if (json['url'] != null) {
        url = json["url"];
      }
      if (json['queryParams'] != null) {
        queryParams = json['queryParams'];
      }

      var analyticResponse = json['analytics'];
      if (analyticResponse is Map<String, dynamic>) {
        analytics.addAll([AnalyticsAction.fromJson(analyticResponse)]);
      } else {
        if (analyticResponse is List<dynamic>) {
          analytics.addAll(analyticResponse
              .map((e) => AnalyticsAction.fromJson(e))
              .toList());
        }
      }
    }
  }

  String? encodeQueryParameters(Map<String, dynamic> params) {
    return params.entries
        .map((MapEntry<String, dynamic> e) =>
            '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');
  }

  @override
  Future<void> executeAction(
      BuildContext context, Map<String, dynamic>? data) async {
    if (context.mounted) {
      for (var element in analytics) {
        element.executeAction(context, data);
      }
    }

    Uri? uri = Uri.tryParse(url ?? "");
    if (queryParams != null && uri != null) {
      uri = uri.replace(query: encodeQueryParameters(queryParams ?? {}));
    }
    if (uri != null) {
      launchUrl(uri);
    }
  }
}
