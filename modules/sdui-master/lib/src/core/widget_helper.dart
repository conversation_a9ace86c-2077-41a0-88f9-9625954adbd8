import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:sdui/sdui.dart';
import 'package:shimmer/shimmer.dart';
import 'package:flutter/animation.dart';

import 'models/gradient_border.dart';

Widget? toIcon(String? code, {double? size, SDUIHexColor? color}) {
  if (code?.startsWith("http://") == true ||
      code?.startsWith("https://") == true) {
    return CachedNetworkImage(
        imageUrl: code!,
        width: size,
        height: size,
        errorWidget: (context, url, error) => const Icon(Icons.error));
  }
  return null;
}

EdgeInsets? getEdgeInsets(dynamic data) {
  if (data == null) return EdgeInsets.zero;
  if (data['all'] != null) return EdgeInsets.all(checkDouble(data['all']));
  if (data['vertical'] != null && data['horizontal'] != null) {
    return EdgeInsets.symmetric(
        vertical: checkDouble(data['vertical']),
        horizontal: checkDouble(data['horizontal']));
  }
  if (data['vertical'] != null) {
    return EdgeInsets.symmetric(vertical: checkDouble(data['vertical']));
  }
  if (data['horizontal'] != null) {
    return EdgeInsets.symmetric(horizontal: checkDouble(data['horizontal']));
  } else {
    return EdgeInsets.only(
      left: checkDouble(data?['left']),
      top: checkDouble(data?['top']),
      right: checkDouble(data?['right']),
      bottom: checkDouble(data?['bottom']),
    );
  }
}

Axis toAxis(String? direction) =>
    direction?.toLowerCase() == "horizontal" ? Axis.horizontal : Axis.vertical;

Color? toColor(SDUIHexColor? hexColor) {
  if (hexColor == null || hexColor.value == null) {
    return null;
  }
  final hexCode = hexColor.value?.replaceFirst('#', '');
  return Color(int.parse('FF$hexCode', radix: 16))
      .withOpacity(hexColor.opacity ?? 1);
}

LinearGradient? toGradient(SDUIGradientHexColor? sduiGradient) {
  if (sduiGradient == null || sduiGradient.colors == null) {
    return null;
  }

  final colors = <Color>[];

  for (var color in sduiGradient.colors!) {
    final hexCode = color.value?.replaceFirst('#', '');
    colors.add(Color(int.parse('FF$hexCode', radix: 16))
        .withOpacity(color.opacity ?? 1));
  }

  LinearGradient gradient = LinearGradient(
      begin: sduiGradient.start ?? Alignment.centerLeft,
      end: sduiGradient.end ?? Alignment.centerRight,
      colors: colors);

  return gradient;
}

RadialGradient? toRadialGradient(SDUIGradientHexColor? sduiGradient) {
  if (sduiGradient == null || sduiGradient.colors == null) {
    return null;
  }

  final colors = <Color>[];

  for (var color in sduiGradient.colors!) {
    final hexCode = color.value?.replaceFirst('#', '');
    colors.add(Color(int.parse('FF$hexCode', radix: 16))
        .withOpacity(color.opacity ?? 1));
  }

  RadialGradient gradient = RadialGradient(
    center: sduiGradient.center ?? Alignment.center,
    radius: sduiGradient.radius ?? 0.5,
    colors: colors,
  );

  return gradient;
}

TextAlign? toTextAlign(String? alignment) {
  switch (alignment?.toLowerCase()) {
    case 'left':
      return TextAlign.left;
    case 'right':
      return TextAlign.right;
    case 'center':
      return TextAlign.center;
    case 'justify':
      return TextAlign.justify;
    case 'end':
      return TextAlign.end;
    case 'start':
      return TextAlign.start;
  }
  return null;
}

// IconData? toIconData(String? code) {
//   if (code == null) {
//     return null;
//   }
//
//   final hexCode = code.replaceFirst('0x', '');
//   return IconData(int.parse(hexCode, radix: 16), fontFamily: 'MaterialIcons');
// }

MainAxisSize toMainAxisSize(String? value) {
  switch (value?.toLowerCase()) {
    case "min":
      return MainAxisSize.min;
  }
  return MainAxisSize.max;
}

CrossAxisAlignment toCrossAxisAlignment(String? value) {
  switch (value?.toLowerCase()) {
    case "end":
      return CrossAxisAlignment.end;
    case "center":
      return CrossAxisAlignment.center;
    case "start":
      return CrossAxisAlignment.start;
    case "baseline":
      return CrossAxisAlignment.baseline;
    case "stretch":
      return CrossAxisAlignment.stretch;
  }
  return CrossAxisAlignment.center;
}

Curve? toCurve(String? curve) {
  switch (curve) {
    case 'linear':
      return Curves.linear;

    case 'ease':
      return Curves.ease;

    case 'easeIn':
    case 'ease-in':
      return Curves.easeIn;

    case 'easeOut':
    case 'ease-out':
      return Curves.easeOut;

    case 'easeInOut':
    case 'ease-in-out':
      return Curves.easeInOut;

    case 'fastOutSlowIn':
    case 'fast-out-slow-in':
      return Curves.fastOutSlowIn;

    case 'bounceIn':
    case 'bounce-in':
      return Curves.bounceIn;

    case 'bounceOut':
    case 'bounce-out':
      return Curves.bounceOut;

    case 'bounceInOut':
    case 'bounce-in-out':
      return Curves.bounceInOut;

    case 'elasticIn':
    case 'elastic-in':
      return Curves.elasticIn;

    case 'elasticOut':
    case 'elastic-out':
      return Curves.elasticOut;

    case 'elasticInOut':
    case 'elastic-in-out':
      return Curves.elasticInOut;

    case 'decelerate':
      return Curves.decelerate;

    case 'fastLinearToSlowEaseIn':
      return Curves.fastLinearToSlowEaseIn;

    case 'slowMiddle':
      return Curves.slowMiddle;

    default:
      return Curves.easeInOut;
  }
}

MainAxisAlignment toMainAxisAlignment(String? value) {
  switch (value?.toLowerCase()) {
    case "right":
    case "end":
      return MainAxisAlignment.end;
    case "center":
      return MainAxisAlignment.center;
    case "left":
    case "start":
      return MainAxisAlignment.start;
    case "spacearound":
      return MainAxisAlignment.spaceAround;
    case "spaceevenly":
      return MainAxisAlignment.spaceEvenly;
    case "spacebetween":
      return MainAxisAlignment.spaceBetween;
  }
  return MainAxisAlignment.start;
}

BoxFit? toBoxFit(String? fit) {
  switch (fit?.toLowerCase()) {
    case "none":
      return BoxFit.none;
    case "contain":
      return BoxFit.contain;
    case "cover":
      return BoxFit.cover;
    case "fill":
      return BoxFit.fill;
    case "fitHeight":
      return BoxFit.fitHeight;
    case "fitWidth":
      return BoxFit.fitWidth;
    case "scaleDown":
      return BoxFit.scaleDown;
  }
  return null;
}

Alignment? toAlignment(String? alignment) {
  switch (alignment) {
    case 'bottom-center':
    case 'bottomCenter':
      return Alignment.bottomCenter;

    case 'bottom-left':
    case 'bottomLeft':
    case 'bottomleft':
      return Alignment.bottomLeft;

    case 'bottom-right':
    case 'bottomRight':
    case 'bottomright':
      return Alignment.bottomRight;

    case 'center':
      return Alignment.center;

    case 'centerLeft':
    case 'center-left':
    case 'centerleft':
      return Alignment.centerLeft;

    case 'center-right':
    case 'centerright':
    case 'centerRight':
      return Alignment.centerRight;

    case 'top-center':
    case 'topCenter':
    case 'topcenter':
      return Alignment.topCenter;

    case 'top-left':
    case 'topLeft':
    case 'topleft':
      return Alignment.topLeft;

    case 'top-right':
    case 'topRight':
    case 'topright':
      return Alignment.topRight;
  }
  return null;
}

StackFit? toStackFit(String? stackFit) {
  switch (stackFit) {
    case 'loose':
      return StackFit.loose;
    case 'expand':
      return StackFit.expand;
    case 'passthrough':
      return StackFit.passthrough;
    default:
      return null;
  }
}

Clip? toClip(String? clip) {
  switch (clip?.toLowerCase()) {
    case "none":
      return Clip.none;
    case "antialias":
      return Clip.antiAlias;
    case "antialiaswithsavelayer":
      return Clip.antiAliasWithSaveLayer;
    case "hardedge":
      return Clip.hardEdge;
  }
  return null;
}

Clip? toOverflow(String? overflow) {
  switch (overflow?.toLowerCase()) {
    case "none":
      return Clip.none;
    case "antialias":
      return Clip.antiAlias;
    case "antialiaswithsavelayer":
      return Clip.antiAliasWithSaveLayer;
    case "hardedge":
      return Clip.hardEdge;
  }
  return null;
}

Axis toScrollDirection(String? direction) {
  if (direction == null) return Axis.horizontal;
  switch (direction.toLowerCase()) {
    case 'vertical':
      return Axis.vertical;
    default:
      return Axis.horizontal;
  }
}

ScrollPhysics toScrollPhysics(bool? scrollOnUserInput) =>
    scrollOnUserInput ?? false
        ? const RangeMaintainingScrollPhysics()
        : const NeverScrollableScrollPhysics();

double checkDouble(dynamic value,
    {bool isHeight = false, BuildContext? context}) {
  if (value is String) {
    if (double.parse(value) < 1.1 && context != null) {
      double val = double.parse(value);
      if (val == 0.99) {
        val = 1;
      }
      Size size = MediaQuery.of(context).size;
      return (isHeight ? size.height * val : size.width * val);
    }
    return double.parse(value);
  } else if (value is int) {
    return 0.0 + value;
  } else if (value is double) {
    if (value < 1.1 && value > 0 && context != null) {
      double val = value + 0.0;
      if (val == 0.99) {
        val = 1;
      }
      Size size = MediaQuery.of(context).size;
      return (isHeight ? size.height * val : size.width * val);
    }
    return 0.0 + value;
  } else if (value == null) {
    return 0.0;
  } else {
    return value;
  }
}

int checkInt(dynamic value) {
  if (value is String) {
    return int.parse(value);
  } else if (value is double) {
    return value.toInt();
  } else if (value == null) {
    return 0;
  } else {
    return value;
  }
}

Widget getShimmerLoader() {
  return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Column(crossAxisAlignment: CrossAxisAlignment.start, children: <Widget>[
          Padding(
            padding: const EdgeInsets.only(left: 20, top: 20, bottom: 20),
            child: Container(
              width: 160,
              height: 15.0,
              color: Colors.white,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 20, bottom: 10),
            child: Container(
              width: 80,
              height: 15.0,
              color: Colors.white,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 20, top: 10, bottom: 10),
            child: Container(
              width: 40.0,
              height: 10.0,
              color: Colors.white,
            ),
          )
        ])
      ]));
}

BorderRadius? getBorderRadius(WidgetBorder? border) {
  return border?.cornerRadius != null
      ? BorderRadius.circular(border?.cornerRadius! ?? 0.0)
      : (border?.radiusTopLeft != null ||
              border?.radiusBottomRight != null ||
              border?.radiusTopRight != null ||
              border?.radiusBottomLeft != null)
          ? BorderRadius.only(
              topLeft: Radius.circular(border?.radiusTopLeft ?? 0),
              topRight: Radius.circular(border?.radiusTopRight ?? 0),
              bottomLeft: Radius.circular(border?.radiusBottomLeft ?? 0),
              bottomRight: Radius.circular(border?.radiusBottomRight ?? 0))
          : null;
}

BoxDecoration getBoxDecoration(
    WidgetBackground? background, WidgetBorder? border,
    {SDUIBoxShape? boxShape, List<BoxShadow>? boxShadowList}) {

  bool hasGradientBorder = border?.gradientBackground != null &&
      border!.gradientBackground!.gradient != null;
  return BoxDecoration(
      gradient: background?.radialGradient ?? background?.gradient,
      shape: boxShape?.boxShape ?? BoxShape.rectangle,
      border: hasGradientBorder
          ? GradientBoxBorder(
        gradient: border.gradientBackground!.gradient as LinearGradient,
        width: border.thickness ?? 1.0,
      )
          : Border.all(
        width: border?.thickness ?? 0.0,
        color: border?.color ?? Colors.transparent,
      ),
      borderRadius: getBorderRadius(border),
      color: background?.color,
      image: background?.backgroundImageUrl != null
          ? DecorationImage(
        image: NetworkImage(background!.backgroundImageUrl!),
        fit: BoxFit.cover,
      )
          : null,
      boxShadow: boxShadowList);
}

List<Widget> parseChildren(Map<String, dynamic> json) {
  List<Widget> childWidgets = [];
  var child = json["child"];
  if (child is Map<String, dynamic>) {
    childWidgets.addAll([SDUIParser.getInstance().fromJson(child)]);
  } else {
    var children = json["children"];
    if (children is List<dynamic>) {
      childWidgets.addAll(
          children.map((e) => SDUIParser.getInstance().fromJson(e)).toList());
    }
  }
  return childWidgets;
}

class StatefulWrapper extends StatefulWidget {
  final Function onInit;
  final Widget child;

  const StatefulWrapper({Key? key, required this.onInit, required this.child})
      : super(key: key);

  @override
  _StatefulWrapperState createState() => _StatefulWrapperState();
}

class _StatefulWrapperState extends State<StatefulWrapper> {
  @override
  void initState() {
    widget.onInit();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
