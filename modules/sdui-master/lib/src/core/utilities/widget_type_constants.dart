const image = "component.image.Image";
const listview = "component.list.ListView";
const listTile = "component.list.listTile";
const text = "component.text.AckoText";
const button = "component.button.Button";
const eventCard = "component.card.EventCard";
const inputField = "component.inputField.AckoInputField";
const circularLoadingIndicator =
    "component.circularLoadingIndicator.AckoCircularLoadingIndicator";
const searchbar = "component.searchbar.AckoSearchBar";
const dropdown = "component.dropdown.AckoDropDown";
const bannerCard = "component.card.BannerCard";
const expansionTile = "component.card.ExpansionTile";
const gridViewComponent = "component.grid.GridView";
const flexgridViewComponent = "component.grid.FlexGridView";
const gridTile = "component.grid.GridTile";
const bgImageLabelTextTile = "component.tile.BgImageLabelTextTile";
const gradientGridTile = "component.grid.GradientGridTile";
const tabBar = "component.tab.TabBar";
const divider = "component.configuration.Divider";
const row = "atom.flex.Row";
const column = "atom.flex.Column";
const container = "atom.container.Container";
const positioned = "atom.positioned.Positioned";
const align = "atom.align.Align";
const padding = "atom.padding.Padding";
const appBar = "component.appbar.AppBar";
const screen = "component.screen.Screen";
const tabBarView = "component.tab.TabBarView";
const tab = "component.tab.Tab";
const shimmer = "component.shimmer.ShimmerWidget";
const textfield = "component.textfield.TextField";
const extendedCTATextfield = "component.textfield.ExtendedCTATextField";
const collapsibleWidget = "component.card.CollapsibleWrapper";
const coverageCard = "component.card.CoverageCard";
const pointerWidget = "component.card.PointerWrapper";
const contentTextCard = "component.section.ContentTextCard";
const checkboxImageCard = "component.card.CheckBoxImageCard";
const contentSection = "component.section.ContentSection";
const commonDataStoreWidget = "CommonDataStoreAsyncWidget";
const asyncConditionalWidget = "AsyncConditionalWidget";
const stack = "atom.flex.Stack";
const dottedBorder = "atom.border.DottedBorder";
const assetBasicInfo = "AssetBasicInfo";
const textWeight = "component.configuration.TextWeight";
const paragraph = "component.configuration.Paragraph";
const label = "component.configuration.Label";
const heading = "component.configuration.Heading";
const display = "component.configuration.Display";
const surveyForm = "SurveyForm";
const alertsWidget = 'AlertWidget';
const asyncWidget = 'Async';
const primaryBuyCard = "component.primaryBuyCard";
const buyProgessCard = "component.healthBuyProgessCard";
const mediaBanner = "component.mediaBanner";
const animatedCollapsibleBanner = "component.banner.AnimatedCollapsibleBanner";
const animatedDialog = "component.banner.AnimatedDialog";
const imageGalleryView = 'component.image.ImageGalleryView';
const sduiParentCustomActionCard = 'component.action.SduiParentCustomActionCard';
const backdropFilter = 'atom.backdrop.BackdropFilter';
