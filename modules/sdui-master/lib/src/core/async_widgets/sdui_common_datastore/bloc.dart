import 'package:networking_module/networking_module.dart';
import 'package:sdui/sdui.dart';
import 'package:session_manager_module/SessionManager/session_manager_module.dart';
import 'package:utilities/utilities.dart';
import 'package:utilities/widgets/acko_safe_cubit.dart';

class CommonDataStoreBloc extends AckoSafeCubit<CommonDataStoreBlocStates>
    with LocationHeaderMixin
    implements SessionModuleInterface {
  final BaseRepository _repo = BaseRepository();

  CommonDataStoreBloc() : super(CommonDataStoreInitState()) {
    SessionManager.instance.registerModuleForLogout(this);
  }

  bool allLobsApisCalled = false;
  bool isFirstLoadInistialized = false;
  List<Map<dynamic, dynamic>> lobUrlsList = List.empty(growable: true);

  final Map<String, dynamic> _lobDataMap = {};

  final Map<String, dynamic> _apiCallInProgressMap = {};

  List observerStatesList = List.empty(growable: true);

  clearDataStore() {
    allLobsApisCalled = false;
    lobUrlsList.clear();
    _lobDataMap.clear();
    _apiCallInProgressMap.clear();
  }

  Future<ResponseWrapper> initBaseConfig({Map<String,dynamic>? queryParams}) async {
    final response = await _repo.getResponse("/app-config", queryParams: queryParams);
    if (response.data != null) {
      fetchLobData((response.data!["urls"] as List)
          .map((e) => e as Map<dynamic, dynamic>)
          .toList());
    }
    return response;
  }

  fetchLobData(List<Map<dynamic, dynamic>> urlsList) async {
    lobUrlsList.addAll(urlsList);
    allLobsApisCalled = false;
    Future.wait(lobUrlsList
            .map((element) => fetchData(element['url'], element['type']))
            .toList())
        .then((value) {
      allLobsApisCalled = true;
      isFirstLoadInistialized = true;
      emit(LobDataUpdated(lobApiStatus: allLobsApisCalled));
    });
  }

  Future refreshCommonDataStoreData() {
    _lobDataMap.clear();
    emit(LobDataUpdated(lobApiStatus: allLobsApisCalled));
    return fetchLobData([]);
  }

  refreshLobData(String lob, {bool refreshCache = false}) {
    _lobDataMap[lob] = null;
    emit(LobDataUpdated(lobApiStatus: allLobsApisCalled));
    if (lobUrlsList.isNotEmpty) {
      var element = lobUrlsList.firstWhere((element) => element['type'] == lob);
      fetchData(element['url'], element['type'], refreshCache: refreshCache);
    }
  }

  Future<void> fetchCacheRefresh(
      String url, String type, ResponseWrapper response) async {
    if (await ConnectionStatus.instance.isNetworkAvaialable() &&
        (url.contains('/app-config') || url.contains('/proposal')) &&
        response.statusCode == 304 &&
        response.fromCache!) {
      final etag = createEtag(response.data);
      fetchData(url, type, refreshCache: true, etag: etag);
    } else {
      return;
    }
  }

  Future<void> fetchData(String url, String type,
      {String? etag, bool refreshCache = false}) async {
    if (_apiCallInProgressMap[type] != null &&
        _apiCallInProgressMap[type] == true) {
      return;
    }
    _apiCallInProgressMap[type] = true;
    await ensureLocationHeadersInitialised();
    final response = await _repo.getResponse(
      url,
      etag: etag,
      requestHeaders: locationDetails?.toJson(),
      refreshCache: refreshCache,
    );

    _lobDataMap[type] = response.data ?? (_lobDataMap[type] ?? <String, dynamic>{});
    if (response.data != null &&
        response.data!.containsKey('observables') &&
        response.data!["observables"] != null) {
      executeObservables(response.data!['observables']);
    }
    _apiCallInProgressMap[type] = false;

    fetchCacheRefresh(url, type, response);

    emit(LobDataUpdated(lobApiStatus: allLobsApisCalled));
  }

  executeObservables(observables) {
    if (observables is List) {
      final stateProvider = StateProvider();
      for (var element in observables) {
        ObserverState? state = stateProvider.getStateFromString(element["id"]);
        if (state != null) {
          observerStatesList.add({"state": state, "data": element["data"]});
          stateProvider.notify(state, data: element["data"]);
        }
      }
    }
  }

  Map<String, dynamic> getAllCards(List<String> modes) {
    Map<String, dynamic> map = {};
    for (var element in modes) {
      final obj = getPositionedDataFromMap(element);
      if (obj != null) {
        map[element.split(".")[0]] = obj;
      }
    }
    return map;
  }

  Map<String, dynamic>? getLobData(String lob) {
    if (_lobDataMap.containsKey(lob)) return _lobDataMap[lob];
    return null;
  }

  Map<String, dynamic>? getPositionedDataFromMap(String position) {
    ///auto.assets.explore

    List<String> positions = position.split('.');
    Map<String, dynamic>? data;
    if (positions.isNotEmpty) {
      data = <String, dynamic>{};
      data.addAll(_lobDataMap);
      for (final String key in positions) {
        if (data?[key] == null) {
          data = null;
          break;
        }
        data = Map.from(data![key]);
      }
    }
    return data;
  }

  @override
  Future<void> handleLogout() async {
    await clearDataStore();
  }
}

abstract class CommonDataStoreBlocStates {}

class CommonDataStoreInitState extends CommonDataStoreBlocStates {}

class LobDataUpdated extends CommonDataStoreBlocStates {
  late bool allLobsApisCalled;
  LobDataUpdated({lobApiStatus = false}) {
    allLobsApisCalled = lobApiStatus;
  }
}
