import 'package:flutter_bloc/flutter_bloc.dart';

class SduiParentCubit<T extends SduiParentState> extends Cubit<T> {
  /// A specialized cubit class for handling SDUI (Server-Driven UI) state management.
  ///
  /// This class extends the base cubit functionality to safely handle state emissions
  /// and maintain SDUI-specific data.
  ///
  /// Properties:
  /// - [sduiJson]: Stores the raw JSON data received from the server for UI rendering
  /// - [stateVariables]: Maintains a map of dynamic variables used across the UI state
  ///
  /// The emit override ensures state updates only occur when the cubit is active.
  SduiParentCubit(super.initialState);

  @override
  void emit(T state) {
    if (!isClosed) {
      super.emit(state);
    }
  }

  Map<String, dynamic>? sduiJson;
  Map<String, dynamic> stateVariables = {};

  /// Updates a value in the nested JSON structure based on a dot-notation key.
  ///
  /// This method traverses through a nested JSON structure (Map and List combinations)
  /// to find and update a value at a specific location identified by [target<PERSON><PERSON>].
  ///
  /// Parameters:
  /// - [target<PERSON>ey]: A string representing the path to the target value using dot notation
  ///   (e.g., "parent.child.grandchild" or "items.123")
  /// - [newValue]: The new value to be set at the specified location
  ///
  /// The method handles:
  /// - Nested maps (Map<String, dynamic>)
  /// - Lists containing maps with 'id' fields
  /// - Dot notation path traversal
  ///
  /// When the target is found, it updates the value and emits a [UpdateFromParentJson] state.
  /// If the target key is not found, no changes are made to the structure.
  ///
  /// Example:
  /// ```dart
  /// updateJsonByKey('parent.child.id123', newValue);
  /// ```
  void updateJsonByKey(String targetKey, dynamic newValue) {
    sduiJson ??= {};
    var stack = [(sduiJson!, '')];

    while (stack.isNotEmpty) {
      var (current, prefix) = stack.removeLast();

      for (var entry in current.entries) {
        var currentKey = prefix.isEmpty ? entry.key : '$prefix.${entry.key}';

        if (currentKey == targetKey) {
          current[entry.key] = newValue;
          emit(UpdateFromParentJson(sduiJson) as T);
          return;
        }

        if (entry.value is Map<String, dynamic>) {
          stack.add((entry.value, currentKey));
        } else if (entry.value is List) {
          for (var i = 0; i < (entry.value as List).length; i++) {
            var item = (entry.value as List)[i];
            if (item is Map<String, dynamic> && item.containsKey('id')) {
              var listItemKey = '$currentKey.${item['id']}';
              if (listItemKey == targetKey) {
                (entry.value as List)[i] = newValue;
                emit(UpdateFromParentJson(sduiJson) as T);
                return;
              }
              stack.add((item, listItemKey));
            }
          }
        }
      }
    }
  }

  /// Updates the state variables with the provided key-value pairs.
  ///
  /// Takes a [Map] of updates where:
  /// - If value is null, removes the corresponding key from state
  /// - If value is not null, updates or adds the key-value pair
  ///
  /// After updates are applied, emits a new [UpdateFromParentState]
  ///
  /// Example:
  /// ```dart
  /// updateState({'key1': 'value1', 'key2': null});
  /// // Adds/updates 'key1' and removes 'key2' from state
  /// ```
  void updateState(Map<String, dynamic> updates) {
    for (var entry in updates.entries) {
      if (entry.value == null) {
        stateVariables.remove(entry.key);
      } else {
        stateVariables[entry.key] = entry.value;
      }
    }
    emit(UpdateFromParentState(stateVariables) as T);
  }

  /// Removes the specified keys from the state variables.
  ///
  /// Takes a [Map] of keys to remove and cleans them from the state,
  /// then emits a new [UpdateFromParentState] with updated variables.
  ///
  /// Example:
  /// ```dart
  /// removeFromState({'key1': null, 'key2': null});
  /// // Removes both 'key1' and 'key2' from state
  /// ```
  void removeFromState(Map<String, dynamic> keysToRemove) {
    for (var key in keysToRemove.keys) {
      stateVariables.remove(key);
    }
    emit(UpdateFromParentState(stateVariables) as T);
  }

  void executeCustomAction(Map<String, dynamic>? actionData) {}
}

abstract class SduiParentState {}

class UpdateFromParentState extends SduiParentState {
  UpdateFromParentState(this.state);
  final Map<String, dynamic> state;
}

class UpdateFromParentJson extends SduiParentState {
  UpdateFromParentJson(this.json);
  final Map<String, dynamic>? json;
}
