import 'dart:convert';

import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';
import 'package:sdui/src/atom_widgets/backdrop_filter.dart';
import 'package:sdui/src/atom_widgets/dotted_border.dart';
import 'package:sdui/src/atom_widgets/draggable_scrollable_sheet.dart';
import 'package:sdui/src/atom_widgets/listtile.dart';
import 'package:sdui/src/atom_widgets/padding.dart';
import 'package:sdui/src/atom_widgets/shimmer.dart';
import 'package:sdui/src/molecular_widgets/animated_collapsible_banner/animated_collpasible_banner.dart';
import 'package:sdui/src/molecular_widgets/banner_card/event_card.dart';
import 'package:sdui/src/molecular_widgets/cards/coverage_card/coverage_card.dart';
import 'package:sdui/src/molecular_widgets/collapsible_widget/collapsible_widget.dart';
import 'package:sdui/src/molecular_widgets/content_section/content_section.dart';
import 'package:sdui/src/molecular_widgets/content_section/content_text_card.dart';
import 'package:sdui/src/molecular_widgets/dropdown/dropdown.dart';
import 'package:sdui/src/molecular_widgets/gridview/custom/bg_image_label_text_tile.dart';
import 'package:sdui/src/molecular_widgets/gridview/flex_gridview.dart';
import 'package:sdui/src/molecular_widgets/input_field/input_field.dart';
import 'package:sdui/src/molecular_widgets/pointer_widget/pointerWidget.dart';
import 'package:sdui/src/molecular_widgets/primary_buy_cards/widgets/media_banner.dart';
import 'package:sdui/src/molecular_widgets/sdui_parent_custom_action_card/sdui_parent_custom_action_card.dart';
import 'package:sdui/src/molecular_widgets/search_tile/extended_cta_search_tile.dart';
import 'package:sdui/src/molecular_widgets/searchbar/searchbar_kit.dart';
import 'package:sdui/src/molecular_widgets/surveyForm/surveyFormBloc/bloc.dart';

import '../../atom_widgets/center.dart';
import '../../atom_widgets/expanded_list_view.dart';
import '../../atom_widgets/positioned.dart';
import '../../atom_widgets/sdui_screen.dart';
import '../../atom_widgets/spacer.dart';
import '../../molecular_widgets/asset_basic_info/asset_basic_info.dart';
import '../../molecular_widgets/circular_loading_indicator/circular_loading_indicator.dart';
import '../../molecular_widgets/cta_content_card/cta_content_card.dart';
import '../../molecular_widgets/dynamic_widget/dynamic_component.dart';
import '../../molecular_widgets/image_gallery_view/image_gallery_view.dart';
import '../../molecular_widgets/listview/pageview.dart';
import '../../molecular_widgets/surveyForm/surveyForm.dart';
import '../async_widgets/common_data_store_ui/ui/common_data_store_ui_wrapper.dart';

/// Parser that convert JSON to flutter [Widget]
/// todo :- integrate pdp in myaccount page
/// todo:- handle sdui url using deeplink
///
abstract class SDUIBaseParser {
  Widget parseJson(String json, BuildContext context) {
    var data = jsonDecode(json);
    return fromJson(data);
  }

  dynamic fromJson(Map<String, dynamic> json,
      {SDUIBaseParser? parser, int? widgetPosition, String? key}) {
    var type = json["\$type"];
    late Widget widget;
    switch (type) {
      case "aspectratio":
        widget = AspectRatioWidget();
        break;
      case "draggableScrollableBottomSheet":
        widget = DraggableScrollableSheetWidget();
        break;
      case "Async":
        widget = BlocProvider<AsyncWidgetCubit>(
          key: ValueKey(json['attributes']['url']),
          create: (context) => AsyncWidgetCubit(),
          child: AsyncWidget().fromJson(json),
        );
        break;
      case "center":
        widget = CenterWidget();
        break;
      case "sdui_icon_button":
        widget = SDUIIconButton();
        break;
      case "banner_card_v2":
        widget = EventCard();
        break;
      case "container":
      case container:
        widget = ContainerWidget();
        break;
      case "cliprrect":
        widget = ClipRRectWidget();
        break;
      case "chip":
        widget = ChipWidget();
        break;
      case column:
        widget = ColumnWidget();
        break;
      case 'CtaContentCard':
        widget = CtaContentCard();
        break;
      case row:
        widget = SDUIRow();
        break;
      case "circleavatar":
        widget = CircleAvatarWidget();
        break;
      case "CircularWidgetSelector":
        widget = CircularWidgetSelector().fromJson(json);
        break;
      case "dialog":
        widget = DialogWidget();
        break;
      case "divider":
      case divider:
        widget = DividerWidget();
        break;
      case "atom.flex.Expanded":
        widget = ExpandedWidget();
        break;
      case "fittedbox":
        widget = FittedBoxWidget();
        break;
      case "atom.flex.Flexible":
        widget = FlexibleWidget();
        break;
      case "form":
        widget = SDUIForm();
        break;
      case "atom_icon":
        widget = SDUIIcon();
        break;
      case "pageview":
        widget = SDUIPageView().fromJson(json);
        break;
      // case "horizontal_list":
      //   widget = HorizontalList(
      //     position: widgetPosition,
      //     key: key.isNotNullAndEmpty ? Key(key!) : null,
      //   );
      //   break;
      case 'positioned':
      case positioned:
        widget = SDUIPositioned();
        break;
      case 'align':
      case align:
        widget = SDUIAlign();
        break;
      case 'padding':
      case padding:
        widget = SDUIPadding();
        break;
      case "spacer":
        widget = SDUISpacer();
        break;
      case "atom.flex.SingleChildScrollView":
        widget = SDUISingleChildScrollView();
        break;
      case stack:
        widget = SDUIStack();
        break;
      case "text":
      case text:
        widget = SDUIText();
        break;
      case screen:
        widget = SDUIScreen();
        break;
      case shimmer:
        widget = ShimmerWidget();
        break;
      case "button":
      case button:
        widget = SDUIButton();
        break;
      case "inputField":
      case inputField:
        widget = SDUIInputField();
        break;
      case "circularLoadingIndicator":
      case circularLoadingIndicator:
        widget = SDUICircularLoadingIndicator();
        break;
      case "searchbar":
      case searchbar:
        widget = SDUISearchBar();
        break;
      case "dropdown":
      case dropdown:
        widget = SDUIDropDown();
        break;
      case "bannerCard":
      case bannerCard:
        widget = BannerCard();
        break;
      case "gridView":
      case gridViewComponent:
        widget = SDUIGridView();
        break;
      case flexgridViewComponent:
        widget = FlexGridView();
        break;

      case "expansionTile":
      case expansionTile:
        widget = SDUIExpansionTile();
        break;
      case "dynamic_component":
        widget = DynamicComponent();
        break;
      case "gridTile":
      case gridTile:
        widget = SDUIGridTile();
        break;
      case bgImageLabelTextTile:
        widget = BgImageLabelTextTile();
        break;
      case gradientGridTile:
        widget = GradientGridTile();
        break;
      case "listview":
      case listview:
        widget = SDUIListView();
        break;
      case "component.expandable.ExpandableWidget":
        widget = SDUIExpandableWidget();
        break;
      case "listTile":
      case listTile:
        widget = SDUIListTile();
        break;
      case eventCard:
        widget = EventCard();
        break;
      case coverageCard:
        widget = CoverageCard();
        break;
      case "tabBar":
      case tabBar:
        widget = SduiTabBar();
        break;
      case "wrap":
        widget = SDUIWrap();
        break;
      case "icon":
      case "image":
      case image:
        widget = SDUIImage();
        break;
      case "appbar":
      case appBar:
        widget = SDUIAppBar();
        break;
      case textfield:
        widget = SearchTile();
        break;
      case extendedCTATextfield:
        widget = ExtendedCTASearchTile();
        break;
      case collapsibleWidget:
        widget = CollapsibleWidget(
          key: GlobalKey(),
        );
        break;
      case pointerWidget:
        widget = PointerWidget();
        break;
      case contentTextCard:
        widget = ContentTextCard();
        break;

      case checkboxImageCard:
        widget = CheckboxImageCard();
        break;
      case contentSection:
        widget = ContentSection();
        break;
      case dottedBorder:
        widget = DottedBorder();
        break;
      case assetBasicInfo:
        widget = AssetBasicInfo();
        break;
      case animatedDialog:
        widget = AnimatedDialog();
        break;
      case primaryBuyCard:
        widget = PrimaryBuyCardTemplateParser();
        break;
      case buyProgessCard:
        widget = BuyProgressCard();
        break;
      case mediaBanner:
        widget = MediaBanner();
        break;
      case backdropFilter:
        widget = BackdropFilterWidget();
        break;
      case animatedCollapsibleBanner:
        widget = AnimatedCollapsibleBanner();
        break;
      case imageGalleryView:
        widget = SDUIImageGalleryView();
        break;
      case sduiParentCustomActionCard:
        widget = SduiParentCustomActionCard();
        break;
      case commonDataStoreWidget:
        widget = BlocProvider(
            create: (context) => CommonDataStoreViewBloc(),
            child: CommonDataStoreUiWrapperParser.fromJson(json));
        break;
      case asyncConditionalWidget:
        widget = BlocProvider(
            create: (context) => AsyncConditionalBloc(json),
            child: AsyncConditionalWrapperParser());
        break;
      case alertsWidget:
        BlocProvider(
          create: (context) => AlertsBloc(id: json['id']),
          child: AlertsWidget(),
        );

      case surveyForm:
        widget = BlocProvider(
          create: (context) => SurveyFormBloc(json),
          child: SurveyForm(),
        );

      /// If widget used is not atomic and feature specific, widget will be parsed from here
      default:
        widget = SDUINull();
    }
    if (widget is SDUIStatelessWidget) {
      (widget).fromJson(json);
    }
    return widget;
  }

  Widget parseWidgetAttributes(dynamic widget, Map<String, dynamic> json) {
    try {
      // TabBarView
      if (widget is SduiTabBar) {
        widget = TabBarParser().parseTabBarView(widget, json);
      }

      // Attributes
      var attributes = json["attributes"];
      if (attributes is Map<String, dynamic> && widget is! BlocProvider) {
        widget.fromJson(attributes);
      }

      // Action
      var action = json["actions"];
      if (action is Map<String, dynamic>) {
        widget.action.clear();
        widget.action.addAll([SDUIAction().fromJson(action)]);
      } else {
        var children = json["actions"];
        if (children is List<dynamic>) {
          widget.action.clear();
          widget.action
              .addAll(children.map((e) => SDUIAction().fromJson(e)).toList());
        }
      }

      // Analytics
      var analytics = json['analytics'];
      if (analytics is Map<String, dynamic>) {
        widget.analytics.clear();
        widget.analytics.addAll([AnalyticsAction.fromJson(analytics)]);
      } else {
        var children = json["analytics"];
        if (children is List<dynamic>) {
          widget.analytics.clear();
          widget.analytics.addAll(
              children.map((e) => AnalyticsAction.fromJson(e)).toList());
        }
      }

      // Children
      var child = json["child"];
      if (child is Map<String, dynamic>) {
        widget.children.add(fromJson(child));
      } else {
        var childrenWidgets = json["children"];
        if (childrenWidgets is List<dynamic>) {
          for (var ele in childrenWidgets) {
            Widget childWidget = fromJson(ele);
            widget.children.add(childWidget);
          }
        }
      }
    } catch (e, stk) {
      print(e);
      print(stk);

      FirebaseCrashlytics.instance
          .recordFlutterError(FlutterErrorDetails(exception: e, stack: stk));

      /// fallback when parsing throws any error
      widget = SDUINull();
    }
    return widget;
  }
}

class SDUIParser extends SDUIBaseParser {
  static final SDUIParser _singleton = SDUIParser._internal();

  SDUIParser._internal();

  factory SDUIParser() {
    return _singleton;
  }

  static SDUIParser getInstance() => _singleton;

  @override
  Widget fromJson(Map<String, dynamic> json,
      {SDUIBaseParser? parser, int? widgetPosition, String? key}) {
    Widget? widget;

    /// navigate to specific parser based on parser param
    if (parser != null) {
      widget = parser.fromJson(json,
          parser: parser, widgetPosition: widgetPosition, key: key);
    } else {
      /// iterate through all feature specific parsers
      for (var element in _parserList) {
        widget =
            element.fromJson(json, widgetPosition: widgetPosition, key: key);
      }

      /// if widget is not found in feature specific parsers search in atomic level switch case
      widget ??= super.fromJson(json, widgetPosition: widgetPosition, key: key);
    }

    /// parse attributes, children, actions, analytics
    widget = parseWidgetAttributes(widget, json);
    return widget;
  }

  /// declare all parsers specific parsers here
  List<SDUIBaseParser> get _parserList => [];
}
