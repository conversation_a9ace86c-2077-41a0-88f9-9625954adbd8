import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:sdui/sdui.dart';

class BackdropFilterWidget extends SDUIStatelessWidget {
  double? sigmaX;
  double? sigmaY;
  double? borderRadius;
  Widget? childWidget;

  BackdropFilterWidget({
    super.key,
    this.sigmaX,
    this.sigmaY,
    this.childWidget,
  });

  @override
  fromJson(Map<String, dynamic>? json) {
    sigmaX = (json?['filter']?['sigmaX'] ?? 0).toDouble();
    sigmaY = (json?['filter']?['sigmaY'] ?? 0).toDouble();
    borderRadius = (json?['borderRadius'] ?? 0).toDouble();
    return super.fromJson(json);
  }

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(borderRadius ?? 0),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: sigmaX ?? 0, sigmaY: sigmaY ?? 0),
        child: child() ?? const SizedBox.shrink(),
      ),
    );
  }
}
