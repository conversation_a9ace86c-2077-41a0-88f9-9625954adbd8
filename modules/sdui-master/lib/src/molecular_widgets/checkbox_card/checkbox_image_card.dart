import 'package:design_module/utilities/color_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';

class CheckboxImageCard extends SDUIStatefulWidget {
  double? height;
  WidgetBackground? background;
  WidgetBorder? border;
  EdgeInsets? margin, padding;
  SDUIText? title;
  SDUIText? subtitle;
  SDUIText? v1;
  SDUIText? v2;
  SDUIText? v3;
  Widget? image;
  Widget? overlay;
  bool initialCheckboxValue;
  bool disable;
  Map<String, dynamic>? stateVariables = {};
  final Function(bool)? onCheck;

  CheckboxImageCard({
    super.key,
    this.initialCheckboxValue = false,
    this.disable = false,
    this.overlay,
    this.onCheck,
  });

  @override
  fromJson(Map<String, dynamic>? json) {
    if (json != null) {
      if (json['title'] != null) {
        title = SDUIParser.getInstance().fromJson(json['title']) as SDUIText;
      }
      if (json['subtitle'] != null) {
        subtitle =
            SDUIParser.getInstance().fromJson(json['subtitle']) as SDUIText;
      }
      if (json['v1'] != null) {
        v1 = SDUIParser.getInstance().fromJson(json['v1']) as SDUIText;
      }
      if (json['v2'] != null) {
        v2 = SDUIParser.getInstance().fromJson(json['v2']) as SDUIText;
      }
      if (json['v3'] != null) {
        v3 = SDUIParser.getInstance().fromJson(json['v3']) as SDUIText;
      }
      if (json['image'] != null) {
        image = SDUIParser.getInstance().fromJson(json['image']);
      }
      if (json['overlay'] != null) {
        overlay = SDUIParser.getInstance().fromJson(json['overlay']);
      }
      if (json['background'] != null) {
        background = WidgetBackground.fromJson(json['background']);
      }
      if (json['border'] != null) {
        border = WidgetBorder.fromJson(json['border']);
      }
      height = checkDouble(json['height']);
      initialCheckboxValue = json['initialCheckboxValue'] ?? false;
      disable = json['disable'] ?? false;
      if (json['margin'] != null) {
        margin = getEdgeInsets(json['margin']);
      }
      if (json['padding'] != null) {
        padding = getEdgeInsets(json['padding']);
      }

      if (json['stateVariables'] != null) {
        stateVariables = json['stateVariables'];
      }
    }
    return super.fromJson(json);
  }

  @override
  State<CheckboxImageCard> createState() => _CheckboxImageCardState();
}

class _CheckboxImageCardState extends State<CheckboxImageCard> {
  late bool isChecked;
  SduiParentCubit? parentCubit;

  @override
  void initState() {
    super.initState();
    isChecked = widget.initialCheckboxValue;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      parentCubit = context.read<SduiParentCubit>();
      parentCubit?.updateState(widget.stateVariables ?? {});
    });
  }

  @override
  void dispose() {
    parentCubit?.removeFromState(widget.stateVariables!);
    super.dispose();
  }

  bool _isStateVariableMatch(Map<String, dynamic> currentState) {
    if (widget.stateVariables == null) return isChecked;

    // Check each key from stateVariables
    for (var entry in widget.stateVariables!.entries) {
      final String stateKey = entry.key;
      List<String> expectedIds = [];
      if (entry.value is List) {
        expectedIds = (entry.value as List)
            .whereType<Map<String, dynamic>>()
            .map((e) => e['id']?.toString() ?? '')
            .where((id) => id.isNotEmpty)
            .toList();
      }

      // Check if the key exists in current state
      if (currentState.containsKey(stateKey)) {
        final stateValue = currentState[stateKey];

        // Check if the value is a list
        if (stateValue is List) {
          // Look for a matching id in the list
          for (var item in stateValue) {
            if (item is Map<String, dynamic> &&
                item.containsKey('id') &&
                expectedIds.contains(item['id'])) {
              return true; // Found a match
            }
          }
          return false;
        }
      }
    }
    return isChecked; // No matches found
  }

  void _handleCheckboxChange(bool? value) {
    setState(() {
      isChecked = value ?? false;
    });

    if (widget.stateVariables != null) {
      if (isChecked) {
        parentCubit?.updateState(widget.stateVariables!);
      } else {
        parentCubit?.removeFromState(widget.stateVariables!);
      }
    }

    if (widget.onCheck != null) {
      widget.onCheck!(isChecked);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          margin: widget.margin ?? const EdgeInsets.symmetric(vertical: 8),
          padding: widget.padding ?? const EdgeInsets.all(16),
          decoration: getBoxDecoration(widget.background, widget.border),
          child: AnimatedSize(
            duration: const Duration(milliseconds: 500),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Flexible(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          if (widget.title != null) widget.title!,
                          const SizedBox(height: 2),
                          if (widget.subtitle != null) widget.subtitle!,
                        ],
                      ),
                    ),
                    BlocBuilder<SduiParentCubit, SduiParentState>(
                        builder: (context, state) {
                      bool checkboxValue = isChecked;
                      if (state is UpdateFromParentState) {
                        checkboxValue = widget.stateVariables != null
                            ? _isStateVariableMatch(state.state)
                            : isChecked;
                      }

                      return SizedBox(
                        width: 24,
                        height: 24,
                        child: Checkbox(
                          value: checkboxValue,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4),
                          ),
                          activeColor: color121212,
                          onChanged:
                              widget.disable ? null : _handleCheckboxChange,
                        ),
                      );
                    }),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    if (widget.image != null) widget.image!,
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          if (widget.v1 != null) widget.v1!,
                          const SizedBox(height: 8),
                          if (widget.v2 != null) widget.v2!,
                          const SizedBox(height: 8),
                          if (widget.v3 != null) widget.v3!,
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        if (widget.overlay != null)
          Positioned.fill(
            child: Container(
              margin: widget.margin ?? const EdgeInsets.symmetric(vertical: 8),
              child: widget.overlay,
            ),
          ),
      ],
    );
  }
}
