import 'package:design_module/uikit/widgets/button/acko_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sdui/sdui.dart';

class SduiParentCustomActionCard extends SDUIStatelessWidget {
  SduiParentCustomActionCard({super.key});

  Widget? title;
  String? ctaText;
  String? cardType;
  Map<String, dynamic>? actionData;

  @override
  fromJson(Map<String, dynamic>? json) {
    json = json?["attributes"];
    if (json != null) {
      if (json['title'] != null) {
        title = SDUIParser.getInstance().fromJson(json['title']);
      }
      ctaText = json['ctaText'] as String?;
      cardType = json['cardType'] as String?;
      actionData = json['actionData'] as Map<String, dynamic>?;
    }
    return super.fromJson(json);
  }

  @override
  Widget build(BuildContext context) {
    if (cardType == 'backgroundCard') {
      return Padding(
        padding: const EdgeInsets.only(top: 12.0),
        child: Container(
          padding: const EdgeInsets.all(14.0),
          decoration: const BoxDecoration(
            color: Color(0xFFf5f5f5),
            borderRadius: BorderRadius.all(Radius.circular(12.0)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (title != null) title!,
              if (ctaText != null)
                Padding(
                  padding: const EdgeInsets.only(top: 12.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      AckoSecondaryButtonS(
                          text: ctaText!,
                          onTap: () {
                            context
                                .read<SduiParentCubit>()
                                .executeCustomAction(actionData!);
                          }),
                    ],
                  ),
                ),
            ],
          ),
        ),
      );
    } else if (cardType == 'freeFlowCard') {
      return Center(
        child: Container(
          padding: const EdgeInsets.only(top: 12),
          width: MediaQuery.of(context).size.width * 0.86,
          decoration: const BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(12.0)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (title != null) title!,
              if (ctaText != null)
                Padding(
                  padding: const EdgeInsets.only(top: 12.0),
                  child: AckoSecondaryButtonS(
                      text: ctaText!,
                      onTap: () {
                        context
                            .read<SduiParentCubit>()
                            .executeCustomAction(actionData!);
                      }),
                ),
            ],
          ),
        ),
      );
    }
    return const SizedBox.shrink();
  }
}
