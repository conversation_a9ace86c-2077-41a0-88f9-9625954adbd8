import 'dart:async';

import 'package:flutter/material.dart';
import 'package:sdui/sdui.dart';
import 'package:sdui/src/core/extensions/string_extensions.dart';

import 'circular_widget.dart';

class CircularWidgetSelector extends SDUIStatefulWidget {
  CircularWidgetSelector({Key? key}) : super(key: key);
  List<CircularWidget>? circularWidgets;
  double? radius;
  double? selectedRadius;
  Color? activeColor;
  Color? inactiveColor;
  double? widgetSpacing;
  CircularWidget? addWidget;
  EdgeInsets? padding;
  EdgeInsets? margin;
  double? width;
  double? height;
  Map<String, Widget> contentWidget = {};
  WidgetBorder? selectedBorder;
  WidgetBackground? background;
  String? defaultId;
  StreamController<String>? updateIdListener;

  @override
  fromJson(Map<String, dynamic>? json) {
    circularWidgets = [];
    if (json?['circularWidgets'] != null) {
      json!['circularWidgets'].forEach((widgetJson) {
        circularWidgets!.add(CircularWidget()..fromJson(widgetJson));
      });
    }
    radius = checkDouble(json?["radius"]);
    width = checkDouble(json?["width"]);
    height = checkDouble(json?["height"]);
    if (json?['selectedBorder'] != null) {
      selectedBorder = WidgetBorder.fromJson(json?['selectedBorder']);
    }
    addWidget = (json?['addWidget'] == null)
        ? null
        : (CircularWidget()..fromJson(json?['addWidget']));
    if (addWidget != null) {
      circularWidgets!.add(addWidget!);
    }
    if (json?['background'] != null) {
      background = WidgetBackground.fromJson(json?['background']);
    }
    padding = getEdgeInsets(json?['padding']);
    margin = getEdgeInsets(json?['margin']);
    widgetSpacing = checkDouble(json?["widgetSpacing"]);
    if (json?["contentWidgets"] != null &&
        json!["contentWidgets"].runtimeType is List) {
      for (var element in (json["contentWidgets"] as List)) {
        contentWidget[element["id"]] =
            SDUIParser.getInstance().fromJson(element["widget"]);
      }
    }
    defaultId = json?['defaultId'];
    activeColor = toColor(SDUIHexColor.fromJson(json?['activeColor']));
    inactiveColor = toColor(SDUIHexColor.fromJson(json?['inactiveColor']));
    selectedRadius = checkDouble(json?["selectedRadius"]);
    return super.fromJson(json);
  }

  @override
  State<StatefulWidget> createState() => _CircularWidgetSelectorState();
}

class _CircularWidgetSelectorState extends State<CircularWidgetSelector>
    with AutomaticKeepAliveClientMixin {
  List<CircularWidget> circularWidgets = List.empty(growable: true);
  String selectedId = "";
  late PageController _pageController;
  StreamSubscription<String>? _subscription;

  @override
  void initState() {
    selectedId = widget.defaultId ?? widget.circularWidgets!.first.id;
    _pageController = PageController(initialPage: 0, viewportFraction: 0.2);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _initCircularWidgetCallback();
      _initListener();
      _centerListWithSelectedItem();
    });
    super.initState();
  }

  @override
  void dispose(){
    _subscription?.cancel();
    super.dispose();
  }

  _centerListWithSelectedItem() {
    int index = circularWidgets.indexWhere((e) => e.id == selectedId);
    _pageController.animateToPage(index,
        duration: const Duration(milliseconds: 300), curve: Curves.ease);
  }

  _initListener() {
    if (widget.updateIdListener != null) {
      _subscription?.cancel();
      _subscription = widget.updateIdListener!.stream.asBroadcastStream().listen((String id) {
        if(mounted){
          onTapped(id, context);
        }
      });
    }
  }

  _initCircularWidgetCallback() {
    circularWidgets.clear();
    if (widget.circularWidgets != null) {
      for (var element in widget.circularWidgets!) {
        element.onTapped = onTapped;
        circularWidgets.add(element);
      }
    }
    setState(() {});
  }

  void setSelectedId(String id) {
    setState(() {
      selectedId = id;
      _centerListWithSelectedItem();
    });
  }

  onTapped(String id, BuildContext context) {
    if (id == selectedId) return;
    if (!id.equalsIgnoreCase("add")) {
      setSelectedId(id);
    }
    widget.onWidgetTapped(context, properties: {"id": id});
  }

  @override
  Widget build(BuildContext context) {
    if ((widget.circularWidgets ?? []).isEmpty) {
      return const SizedBox.shrink();
    }
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          width: checkDouble(widget.width, context: context),
          child: Stack(
            children: [
              Container(
                padding: widget.padding,
                margin: widget.margin,
                height: checkDouble(widget.height,
                    context: context, isHeight: true),
                decoration: getBoxDecoration(widget.background, null),
                child: Center(
                  child: PageView.builder(
                      controller: _pageController,
                      scrollDirection: Axis.horizontal,
                      itemCount: circularWidgets.length,
                      itemBuilder: (context, index) => Center(
                            child: AnimatedContainer(
                              duration: const Duration(milliseconds: 300),
                              padding: const EdgeInsets.all(4),
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: (circularWidgets[index]
                                        .id
                                        .toString()
                                        .equalsIgnoreCase(
                                            selectedId.toString()))
                                    ? Border.all(
                                        width:
                                            widget.selectedBorder?.thickness ??
                                                0.0,
                                        color: widget.selectedBorder?.color ??
                                            Colors.transparent)
                                    : null,
                                color: (circularWidgets[index]
                                        .id
                                        .toString()
                                        .equalsIgnoreCase(
                                            selectedId.toString()))
                                    ? widget.activeColor
                                    : widget.inactiveColor,
                              ),
                              height: (circularWidgets[index]
                                      .id
                                      .toString()
                                      .equalsIgnoreCase(selectedId.toString()))
                                  ? widget.selectedRadius! * 2
                                  : (widget.radius! * 2),
                              width: (circularWidgets[index]
                                      .id
                                      .toString()
                                      .equalsIgnoreCase(selectedId.toString()))
                                  ? widget.selectedRadius! * 2
                                  : widget.radius! * 2,
                              child: Center(
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(100),
                                  child: circularWidgets[index],
                                ),
                              ),
                            ),
                          )),
                ),
              ),
              if (circularWidgets.length > 3)
                Positioned(
                    left: 0,
                    child: IgnorePointer(
                      child: Container(
                        width: 64,
                        height: 48,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                              colors: [
                                const Color(0xff101010),
                                const Color(0xff101010).withOpacity(0)
                              ],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight),
                        ),
                      ),
                    )),
              if (circularWidgets.length > 3)
                Positioned(
                    right: 0,
                    child: IgnorePointer(
                      child: Container(
                        width: 64,
                        height: 48,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                              colors: [
                                const Color(0xff101010),
                                const Color(0xff101010).withOpacity(0)
                              ],
                              begin: Alignment.centerRight,
                              end: Alignment.centerLeft),
                        ),
                      ),
                    ))
            ],
          ),
        ),
      ],
    );
  }

  @override
  bool get wantKeepAlive => true;
}
