import 'package:flutter/material.dart';
import 'package:sdui/sdui.dart';
import 'package:sdui/src/core/extensions/string_extensions.dart';

class EventCard extends SDUIStatelessWidget {
  WidgetBackground? background;
  WidgetBorder? border;

  EventCard(
      {super.key,
      this.background,
      this.border,
      this.padding,
      this.margin,
      this.width,
      this.height,
      this.image,
      this.vText1,
      this.vText2,
      this.vText3,
      this.vText4,
      this.button});

  EdgeInsets? padding;
  EdgeInsets? margin;
  double? width;
  double? height;
  SDUIImage? image;
  SDUIText? vText1, vText2, vText3, vText4;
  Widget? cta, tag;
  Widget? button;
  int? priority;
  @override
  fromJson(Map<String, dynamic>? json) {
    if (json != null) {
      // Action
      if (json['attributes'] != null) {
        json = json['attributes'];
      }

      padding = getEdgeInsets(json?["padding"]);
      margin = getEdgeInsets(json?["margin"]);
      if (json?['background'] != null) {
        background = WidgetBackground.fromJson(json?['background']);
      }
      if (json?['border'] != null) {
        border = WidgetBorder.fromJson(json?['border']);
      }

      if (json?['image'] != null) {
        image = SDUIParser.getInstance().fromJson(json?['image']) as SDUIImage;
      }
      if (json?['vText1'] != null) {
        vText1 = SDUIParser.getInstance().fromJson(json?['vText1']) as SDUIText;
      }
      if (json?['vText2'] != null) {
        vText2 = SDUIParser.getInstance().fromJson(json?['vText2']) as SDUIText;
      }
      if (json?['vText3'] != null) {
        vText3 = SDUIParser.getInstance().fromJson(json?['vText3']) as SDUIText;
      }
      if (json?['vText4'] != null) {
        vText4 = SDUIParser.getInstance().fromJson(json?['vText4']) as SDUIText;
      }
      if (json?['button'] != null) {
        button = SDUIParser.getInstance().fromJson(json?['button']);
      }
      if (json?['tag'] != null) {
        tag = SDUIParser.getInstance().fromJson(json?['tag']);
      }
      if (json?['cta'] != null) {
        cta = SDUIParser.getInstance().fromJson(json?['cta']);
      }
      width = checkDouble(json?["width"], isHeight: false);
      height = checkDouble(json?["height"], isHeight: true);
      priority = checkInt(json?['priority']);
    }
  }

  void analyticEvent(BuildContext context) {
    for (var element in analytics) {
      element.executeAction(context, null);
    }
  }

  @override
  Widget build(BuildContext context) => StatefulWrapper(
      onInit: () {
        analyticEvent(context);
      },
      child: InkWell(
        onTap: () {
          for (var element in action) {
            element.executeAction(context, null);
          }
        },
        child: ContainerWidget(
          width: EventCardTemplate.getWidgetWidth(width),
          height: EventCardTemplate.getWidgetHeight(height),
          margin: EventCardTemplate.getWidgetMargin(margin),
          padding: EventCardTemplate.getWidgetPadding(padding),
          decoration: getBoxDecoration(
              EventCardTemplate.getWidgetBackground(background),
              EventCardTemplate.getWidgetBorder(border)),
          childWidget: IntrinsicHeight(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                if (image != null) EventCardTemplate.getImage(image!),
                Expanded(
                  flex: 30,
                  child: IgnorePointer(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (tag != null) tag!,
                        if (vText1 != null && vText1!.value.isNotNullOrEmpty)
                          EventCardTemplate.getText1(vText1!),
                        if (vText2 != null && vText2!.value.isNotNullOrEmpty)
                          EventCardTemplate.getText2(vText2!),
                        if (vText3 != null && vText3!.value.isNotNullOrEmpty)
                          EventCardTemplate.getText3(vText3!),
                        if (vText4 != null && vText4!.value.isNotNullOrEmpty)
                          EventCardTemplate.getText4(vText4!),
                        if (cta != null) cta!
                      ],
                    ),
                  ),
                ),
                if (button != null) button!
              ],
            ),
          ),
        ),
      )
    );
}

class EventCardTemplate {
  static getImage(SDUIImage image) {
    return SDUIImage(
      width: image.width ?? 20,
      height: image.height ?? 40,
      margin: image.margin ?? const EdgeInsets.only(left: 10),
      alignment: image.alignment ?? Alignment.centerLeft,
      imageUrl: image.imageUrl ??
          "https://firebasestorage.googleapis.com/v0/b/acko-sandbox.appspot.com/o/icons%2Fnotification_line_blue.svg?alt=media&token=11b02a25-f6a4-4cca-804f-db2493ceb5ed",
    );
  }

  static getText1(SDUIText text) {
    return SDUIText(
      value: text.value ?? "",
      textStyle: text.textStyle ?? 'pXSmall',
      margin: text.margin ?? const EdgeInsets.only(left: 10, bottom: 2),
      alignment: text.alignment ?? TextAlign.center,
      maxLines: text.maxLines,
      icon: text.icon,
      iconConfiguration: text.iconConfiguration,
      textLayout: text.textLayout,
      textConfiguration: text.textConfiguration,
      background: text.background,
      border: text.border,
      childWidgets: text.childWidgets ?? text.childrenWidgets,
      textColor: text.textColor ?? const Color(0xFFFFFFFF),
    );
  }

  static getText2(SDUIText text) {
    return SDUIText(
      value: text.value ?? "",
      textStyle: text.textStyle ?? 'lSmall',
      margin: text.margin ?? const EdgeInsets.only(left: 10, bottom: 2),
      alignment: text.alignment ?? TextAlign.center,
      maxLines: text.maxLines,
      icon: text.icon,
      iconConfiguration: text.iconConfiguration,
      textLayout: text.textLayout,
      textConfiguration: text.textConfiguration,
      background: text.background,
      border: text.border,
      childWidgets: text.childWidgets ?? text.childrenWidgets,
      textColor: text.textColor ?? const Color(0xFFFFFFFF),
    );
  }

  static getText3(SDUIText text) {
    return SDUIText(
      value: text.value ?? "",
      textStyle: text.textStyle ?? 'pXSmall',
      margin: text.margin ?? const EdgeInsets.only(left: 10, bottom: 2),
      alignment: text.alignment ?? TextAlign.center,
      icon: text.icon,
      iconConfiguration: text.iconConfiguration,
      textLayout: text.textLayout,
      childWidgets: text.childWidgets ?? text.childrenWidgets,
      textConfiguration: text.textConfiguration,
      background: text.background,
      border: text.border,
      textColor: text.textColor ?? const Color(0xFFFFFFFF),
    );
  }

  static getText4(SDUIText text) {
    return Padding(
      padding: text.margin ?? const EdgeInsets.only(left: 10, bottom: 2),
      child: SDUIText(
        value: text.value ?? "",
        textStyle: text.textStyle ?? 'pXSmall',
        alignment: text.alignment ?? TextAlign.center,
        icon: text.icon,
        iconConfiguration: text.iconConfiguration,
        textLayout: text.textLayout,
        childWidgets: text.childWidgets ?? text.childrenWidgets,
        textConfiguration: text.textConfiguration,
        background: text.background,
        border: text.border,
        textColor: text.textColor ?? const Color(0xFFFFFFFF),
      ),
    );
  }

  static getWidgetBackground(WidgetBackground? background) {
    return background ??
        WidgetBackground(
          gradient: LinearGradient(colors: [
            toColor(SDUIHexColor(value: "#1B73E8", opacity: 0.4))!,
            toColor(SDUIHexColor(value: "#000000", opacity: 1))!
          ], begin: Alignment.bottomLeft, end: Alignment.topRight),
        );
  }

  static getWidgetHeight(double? height) {
    return height ?? 0.8;
  }

  static getWidgetWidth(double? width) {
    return width ?? 100;
  }

  static getWidgetMargin(EdgeInsets? margin) {
    return margin ?? const EdgeInsets.symmetric(vertical: 30, horizontal: 12);
  }

  static getWidgetPadding(EdgeInsets? padding) {
    return padding ?? const EdgeInsets.all(10);
  }

  static getWidgetBorder(WidgetBorder? border) {
    return WidgetBorder(
        thickness: border?.thickness ?? 0.0002,
        cornerRadius: border?.cornerRadius,
        radiusBottomLeft: border?.radiusBottomLeft,
        radiusBottomRight: border?.radiusBottomRight,
        radiusTopLeft: border?.radiusTopLeft,
        radiusTopRight: border?.radiusTopRight,
        color: border?.color ?? toColor(SDUIHexColor(value: "#ffffff")));
  }

  static getButton(SDUIText text) {
    return SDUIText(
      value: text.value ?? "",
      textStyle: 'lSmall',
      margin:
          text.margin ?? const EdgeInsets.only(right: 15, bottom: 15, top: 10),
      padding: text.padding ??
          const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      alignment: text.alignment ?? TextAlign.right,
      textColor: text.textColor ?? const Color(0xFF000000),
      textConfiguration: text.textConfiguration ?? TextConfiguration.colored,
      background: text.background ??
          WidgetBackground(color: toColor(SDUIHexColor(value: "#FFFFFF"))),
      border: text.border ?? WidgetBorder(cornerRadius: 10),
    );
  }
}
