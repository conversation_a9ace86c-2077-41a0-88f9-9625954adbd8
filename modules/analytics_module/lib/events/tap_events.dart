import 'package:analytics/platforms/firebase_platform.dart';
import 'package:analytics/platforms/r2d2_platform.dart';
import 'package:analytics/platforms/segment_platform.dart';

import 'events_base.dart';

class TapConstants extends BaseEventsClass {
  TapConstants._(eventName, eventPlatforms) : super(eventName, eventPlatforms);
  static TapConstants TAP_BTN_GPS_SEARCH_RESULT =
      TapConstants._('tap_btn_gps_search_result', [SegmentPlatform()]);

  static TapConstants TAP_BTN_SERVICES_RECENT_SEARCH =
      TapConstants._('tap_btn_services_recent_search', [SegmentPlatform()]);

  static TapConstants TAP_BTN_SCHEDULE_CALLBACK =
      TapConstants._('tap_btn_schedule_callback', [SegmentPlatform()]);

  static TapConstants TAP_BTN_SERVICES_ENTRY_ICON =
      TapConstants._('tap_btn_services_entry_icon', [SegmentPlatform()]);

  static TapConstants TAP_BTN_OTHER_SERVICES_ENTRY_CARD =
      TapConstants._('tap_btn_other_services_entry_card', [SegmentPlatform()]);

  static TapConstants TAP_BTN_SEARCH_VEHICLE_IN_SERVICE =
      TapConstants._('tap_btn_search_vehicle_in_service', [SegmentPlatform()]);

  static TapConstants TAP_BTN_SEE_ALL_SERVICES =
      TapConstants._('tap_btn_see_all_services', [SegmentPlatform()]);

  static TapConstants TAP_BTN_QUICK_ACTIONS =
      TapConstants._('tap_btn_quick_actions', [SegmentPlatform()]);

  static TapConstants TAP_BTN_SERVICES_VIEW_TNC =
      TapConstants._('tap_btn_services_view_tnc', [SegmentPlatform()]);

  static TapConstants TAP_BTN_CHECK_ANOTHER_REG_NUMBER =
      TapConstants._('tap_btn_check_another_reg_number', [SegmentPlatform()]);

  static TapConstants TAP_BTN_MMV_CONTINUE =
      TapConstants._('tap_btn_mmv_continue', [SegmentPlatform()]);

  static TapConstants TAP_BTN_USE_CURRENT_LOCATION =
      TapConstants._('tap_btn_use_current_location', [SegmentPlatform()]);

  static TapConstants TAP_BTN_GRANT_LOCATION_PERMISSION =
      TapConstants._('tap_btn_grant_location_permission', [SegmentPlatform()]);

  static TapConstants NEARBY_FUEL_STATION_SUCCESS_API_LOADED = TapConstants._(
      'nearby_fuel_station_success_api_loaded', [SegmentPlatform()]);

  static TapConstants TAB_BTN_FUEL_STATION_LOCATION =
      TapConstants._('tap_btn_fuel_station_locator', [SegmentPlatform()]);

  static TapConstants TAB_BTN_SERVICES_NAVIGATE =
      TapConstants._('tap_btn_services_navigate', [SegmentPlatform()]);

  static TapConstants CHALLAN_FAILURE_API_LOADED = TapConstants._(
      'challan_failure_api_loaded', [SegmentPlatform(), FirebasePlatform()]);

  static TapConstants CHALLAN_SUCCESS_API_LOADED = TapConstants._(
      'challan_success_api_loaded', [SegmentPlatform(), FirebasePlatform()]);

  static TapConstants TAP_BTN_DYNAMIC_FOOTER_CTA =
      TapConstants._('tap_btn_dynamic_footer_cta', [SegmentPlatform()]);

  static TapConstants TAP_BTN_DYNAMIC_FOOTER_BANNER =
      TapConstants._('tap_btn_dynamic_footer_banner', [SegmentPlatform()]);

  static TapConstants TAP_BTN_ADD_NEW_ASSET =
      TapConstants._('tap_btn_add_new_asset', [SegmentPlatform()]);

  static TapConstants TAP_BTN_CONTINUE_ADD_ASSET =
      TapConstants._('tap_btn_continue_add_asset', [SegmentPlatform()]);

  static TapConstants TAP_BTN_CONFIRM_ADD_ASSET =
      TapConstants._('tap_btn_confirm_add_asset', [SegmentPlatform()]);

  static TapConstants TAP_BTN_ASSET_ADDED_GOT_IT =
      TapConstants._('tap_btn_asset_added_got_it', [SegmentPlatform()]);

  static TapConstants TAP_BTN_ASSET_ALREADY_EXISTS =
      TapConstants._('tap_btn_asset_already_exists', [SegmentPlatform()]);

  static TapConstants TAP_BTN_ADD_ANOTHER_ASSET =
      TapConstants._('tap_btn_add_another_asset', [SegmentPlatform()]);

  static TapConstants TAP_BTN_VIEW_ASSET_DETAILS =
      TapConstants._('tap_btn_view_asset_details', [SegmentPlatform()]);

  static TapConstants TAP_BTN_VIEW_ASSET_OVERVIEW =
      TapConstants._('tap_btn_view_asset_overview', [SegmentPlatform()]);

  static TapConstants TAP_BTN_UPLOAD_REG_DOC =
      TapConstants._('tap_btn_upload_reg_doc', [SegmentPlatform()]);

  static TapConstants TAP_BTN_UPLOAD_PUC_DOC =
      TapConstants._('tap_btn_upload_puc_doc', [SegmentPlatform()]);

  static TapConstants TAP_BTN_UPLOAD_INSURANCE_DOC =
      TapConstants._('tap_btn_upload_insurance_doc', [SegmentPlatform()]);

  static TapConstants TAP_BTN_DOC_UPLOAD_SOURCE =
      TapConstants._('tap_btn_doc_upload_source', [SegmentPlatform()]);

  static TapConstants TAP_BTN_SHARE_ASSET_DOC =
      TapConstants._('tap_btn_share_asset_doc', [SegmentPlatform()]);

  static TapConstants TAP_BTN_MANAGE_ASSETS =
      TapConstants._('tap_btn_manage_assets', [SegmentPlatform()]);

  static TapConstants TAP_BTN_ASSET_CONTEXT_MENU =
      TapConstants._('tap_btn_asset_context_menu', [SegmentPlatform()]);

  static TapConstants TAP_BTN_REMOVE_ASSET =
      TapConstants._('tap_btn_remove_asset', [SegmentPlatform()]);

  static TapConstants TAP_BTN_REMOVE_ASSET_CONFIRM =
      TapConstants._('tap_btn_remove_asset_confirm', [SegmentPlatform()]);

  static TapConstants TAP_BTN_REMOVE_ASSET_CANCEL =
      TapConstants._('tap_btn_remove_asset_cancel', [SegmentPlatform()]);

  static TapConstants TAP_BTN_MOBILE_REPAIR_SELECTED_BRAND = TapConstants._(
      'tap_btn_mobile_repair_selected_brand', [SegmentPlatform()]);

  static TapConstants TAP_BTN_MOBILE_REPAIR_VIEW_GENERIC =
      TapConstants._('tap_btn_mobile_repair_view_generic', [SegmentPlatform()]);

  static TapConstants TAP_BTN_MOBILE_REPAIR_VIEW_ACKO =
      TapConstants._('tap_btn_mobile_repair_view_acko', [SegmentPlatform()]);

  static TapConstants TAP_BTN_CANCEL_REMOVE_ASSET_DOC =
      TapConstants._('tap_btn_cancel_remove_asset_doc', [SegmentPlatform()]);

  static TapConstants TAP_BTN_CONFIRM_REMOVE_ASSET_DOC =
      TapConstants._('tap_btn_confirm_remove_asset_doc', [SegmentPlatform()]);

  static TapConstants TAP_BTN_TRY_AGAIN_TO_FETCH_RECORDS =
      TapConstants._('tap_btn_try_again_to_fetch_records', [SegmentPlatform()]);

  static TapConstants TAP_BTN_ASSET_NOT_YOUR_VEHICLE =
      TapConstants._('tap_btn_add_asset_not_your_vehicle', [SegmentPlatform()]);

  static TapConstants ADD_ASSET_VEHICLE_NUMBER_ENTERED =
      TapConstants._('add_asset_vehicle_number_entered', [SegmentPlatform()]);

  static TapConstants TAP_START_KYC =
      TapConstants._('tap_start_kyc', [SegmentPlatform()]);

  static TapConstants TAP_KYC_SKIP =
      TapConstants._('tap_kyc_skip', [SegmentPlatform()]);

  static TapConstants TAP_DO_IT_LATER_MODAL =
      TapConstants._('tap_do_it_later_modal', [SegmentPlatform()]);

  static TapConstants TAP_CONTINUE_WITH_KYC_MODAL =
      TapConstants._('tap_continue_with_kyc_modal', [SegmentPlatform()]);

  static TapConstants TAP_NAME_CONTINUE =
      TapConstants._('tap_name_continue', [SegmentPlatform()]);

  static TapConstants TAP_NAME_FIELD =
      TapConstants._('tap_name_field', [SegmentPlatform()]);

  static TapConstants TAP_CONTINUE_KYC_VERIFIED =
      TapConstants._('tap_continue_kyc_verified', [SegmentPlatform()]);

  static TapConstants TAP_RETRY_KYC_REJECTED =
      TapConstants._('tap_retry_kyc_rejected', [SegmentPlatform()]);

  static TapConstants TAP_SKIP_KYC_REJECTED =
      TapConstants._('tap_skip_kyc_rejected', [SegmentPlatform()]);

  static TapConstants TAP_RETRY_KYC_FAILED =
      TapConstants._('tap_retry_kyc_failed', [SegmentPlatform()]);

  static TapConstants TAP_CONTINUE_MANUAL_REVIEW =
      TapConstants._('tap_continue_manual_review', [SegmentPlatform()]);

  static TapConstants TAP_BTN_PUSH_NOTIFICATION =
      TapConstants._('tap_btn_push_notification', [SegmentPlatform()]);

  static TapConstants PUSH_NOTIFICATION_RECEIVE_SUCCESS =
      TapConstants._('push_notification_receive_success', [SegmentPlatform()]);

  static TapConstants TAP_BTN_APP_ONBOARDING_SKIP =
      TapConstants._('tap_btn_app_onboarding_skip', [SegmentPlatform()]);

  static TapConstants TAP_BTN_APP_ONBOARDING_NEXT_STEP =
      TapConstants._('tap_btn_app_onboarding_next_step', [SegmentPlatform()]);

  static TapConstants TAP_BTN_APP_ONBOARDING_STEP_COMPLETE = TapConstants._(
      'tap_btn_app_onboarding_step_complete', [SegmentPlatform()]);

  static TapConstants TAP_BTN_LOGIN_WITH_PHONE_CONTINUE =
      TapConstants._('tap_btn_login_with_phone_continue', [SegmentPlatform()]);

  static TapConstants TAP_BTN_RECOVER_MY_ACCOUNT =
      TapConstants._('tap_btn_recover_my_account', [SegmentPlatform()]);

  static TapConstants TAP_BTN_LOGIN_OTP_ENTERED =
      TapConstants._('tap_btn_login_otp_entered', [SegmentPlatform()]);

  static TapConstants TAP_BTN_RECOVER_ACCOUNT_CONTINUE =
      TapConstants._('tap_btn_recover_account_continue', [SegmentPlatform()]);

  static TapConstants TAP_BTN_SEND_RECOVERY_CODE =
      TapConstants._('tap_btn_send_recovery_code', [SegmentPlatform()]);

  static TapConstants TAP_BTN_ACCOUNT_RECOVERY_NO_ACCESS =
      TapConstants._('tap_btn_account_recovery_no_access', [SegmentPlatform()]);

  static TapConstants TAP_BTN_RECOVER_ACCOUNT_BACK_TO_LOGIN = TapConstants._(
      'tap_btn_recover_account_back_to_login', [SegmentPlatform()]);

  static TapConstants TAP_BTN_MOBILE_REPAIR_RAISE_CLAIM =
      TapConstants._('tap_btn_mobile_repair_raise_claim', [SegmentPlatform()]);

  static TapConstants TAP_BTN_PROFILE_AVATAR =
      TapConstants._('tap_btn_profile_avatar', [SegmentPlatform()]);

  static TapConstants TAP_BTN_HEALTH_CARD_GET_QUOTE =
      TapConstants._('tap_btn_health_card_get_quote', [SegmentPlatform()]);

  static TapConstants TAP_BTN_HEALTH_GMC_LINK_CARD =
      TapConstants._('tap_btn_health_gmc_link_card', [SegmentPlatform()]);

  static TapConstants TAP_BTN_QUICK_ACTION_SELECT_POLICY_CARD = TapConstants._(
      'tap_btn_quick_action_select_policy_card', [SegmentPlatform()]);

  static TapConstants TAP_BTN_CLOSE_QUICK_ACTIONS_POLICIES_LIST =
      TapConstants._(
          'tap_btn_close_quick_actions_policies_list', [SegmentPlatform()]);

  static TapConstants TAP_BTN_GET_INSURANCE_CARD =
      TapConstants._('tap_btn_get_insurance_card', [SegmentPlatform()]);

  static TapConstants TAP_BTN_NEW_AUTO_INSURANCE =
      TapConstants._('tap_btn_new_auto_insurance', [SegmentPlatform()]);

  static TapConstants TAP_BTN_GET_INSURANCE_ENTER_JOURNEY = TapConstants._(
      'tap_btn_get_insurance_enter_journey', [SegmentPlatform()]);

  static TapConstants TAP_CARD_POLICY =
      TapConstants._('tap_card_policy', [SegmentPlatform()]);

  static TapConstants TAP_CARD_CROSS_SELL = TapConstants._(
      'tap_card_cross_sell', [SegmentPlatform(), R2D2Platform()]);

  static TapConstants TAP_CARD_POLICY_SUB_CARD =
      TapConstants._('tap_card_policy_sub_card', [SegmentPlatform()]);

  static TapConstants TAP_BACK_NAVIGATION =
      TapConstants._('tap_back_navigation', [SegmentPlatform()]);

  static TapConstants TAP_BTN_BOTTOM_NAVIGATION_TAB =
      TapConstants._('tap_btn_bottom_navigation_tab', [SegmentPlatform()]);

  static TapConstants TAP_BTN_SUPPORT_WRITE_TO_US =
      TapConstants._('tap_btn_support_write_to_us', [SegmentPlatform()]);

  static TapConstants TAP_BTN_SUPPORT_CALL_US =
      TapConstants._('tap_btn_support_call_us', [SegmentPlatform()]);

  static TapConstants TAP_BTN_SUPPORT_FAQS =
      TapConstants._('tap_btn_support_faqs', [SegmentPlatform()]);

  static TapConstants TAP_BTN_SUPPORT_GUIDES =
      TapConstants._('tap_btn_support_guides', [SegmentPlatform()]);

  static TapConstants TAP_BTN_SUPPORT_ARTICLES =
      TapConstants._('tap_btn_support_articles', [SegmentPlatform()]);

  static TapConstants TAP_BTN_SUPPORT_EBOOKS =
      TapConstants._('tap_btn_support_ebooks', [SegmentPlatform()]);

  static TapConstants TAP_BTN_LOGOUT =
      TapConstants._('tap_btn_logout', [SegmentPlatform()]);

  static TapConstants TAP_PROFILE_PERSONAL_DETAILS =
      TapConstants._('tap_profile_personal_details', [SegmentPlatform()]);

  static TapConstants TAP_PROFILE_SAVED_CARDS =
      TapConstants._('tap_profile_saved_cards', [SegmentPlatform()]);

  static TapConstants TAP_PROFILE_PAYOUT_PREFERENCE =
      TapConstants._('tap_profile_payout_preference', [SegmentPlatform()]);

  static TapConstants TAP_BTN_PROFILE_NOTIFICATIONS_SECTION = TapConstants._(
      'tap_btn_profile_notifications_section', [SegmentPlatform()]);

  static TapConstants TAP_BTN_PROFILE_ABOUT_US_SECTION =
      TapConstants._('tap_btn_profile_about_us_section', [SegmentPlatform()]);

  static TapConstants TAP_BTN_PROFILE_PRIVACY_POLICY_SECTION = TapConstants._(
      'tap_btn_profile_privacy_policy_section', [SegmentPlatform()]);

  static TapConstants TAP_PROFILE_NAME_EDIT_INTENT =
      TapConstants._('tap_profile_name_edit_intent', [SegmentPlatform()]);

  static TapConstants TAP_PROFILE_NAME_UPDATED =
      TapConstants._('tap_profile_name_updated', [SegmentPlatform()]);

  static TapConstants TAP_BTN_PROFILE_DEACTIVATE_ACCOUNT =
      TapConstants._('tap_btn_profile_deactivate_account', [SegmentPlatform()]);

  static TapConstants TAP_BTN_PROFILE_DEACTIVATE_ACCOUNT_CONFIRM =
      TapConstants._(
          'tap_btn_profile_deactivate_account_confirm', [SegmentPlatform()]);

  static TapConstants TAP_BTN_PROFILE_DEACTIVATE_ACCOUNT_CANCEL =
      TapConstants._(
          'tap_btn_profile_deactivate_account_cancel', [SegmentPlatform()]);

  static TapConstants TAP_PROFILE_WHATSAPP_OPT_IN =
      TapConstants._('tap_profile_whatsapp_opt-in', [SegmentPlatform()]);

  static TapConstants TAP_PROFILE_WHATSAPP_OPT_OUT =
      TapConstants._('tap_profile_whatsapp_opt-out', [SegmentPlatform()]);

  static TapConstants TAP_BTN_ASSET_CARD_SELECTED_FOR_SERVICE = TapConstants._(
      'tap_btn_asset_card_selected_for_service', [SegmentPlatform()]);

  static TapConstants TAP_BTN_COMPLETE_PROFILE_ADD_DATA_INITIATE =
      TapConstants._(
          'tap_btn_complete_profile_add_data_initiate', [SegmentPlatform()]);

  static TapConstants TAP_BTN_COMPLETE_PROFILE_ADD_INFO_INITIATE =
      TapConstants._(
          'tap_btn_complete_profile_add_info_initiate', [SegmentPlatform()]);

  static TapConstants TAP_BTN_COMPLETE_PROFILE_ADD_INFO_CONFIRM =
      TapConstants._(
          'tap_btn_complete_profile_add_info_confirm', [SegmentPlatform()]);

  static TapConstants TAP_BTN_COMPLETE_PROFILE_ADD_INFO =
      TapConstants._('tap_btn_complete_profile_add_info', [SegmentPlatform()]);

  static TapConstants TAP_BTN_COMPLETE_PROFILE_UPDATE_INFO = TapConstants._(
      'tap_btn_complete_profile_update_info', [SegmentPlatform()]);

  static TapConstants TAP_BTN_COMPLETE_PROFILE_SAVE_AND_ADD_ANOTHER =
      TapConstants._(
          'tap_btn_complete_profile_save_and_add_another', [SegmentPlatform()]);

  static TapConstants TAP_BTN_PROFILE_EDIT_INFO =
      TapConstants._('tap_btn_profile_edit_info', [SegmentPlatform()]);

  static TapConstants TAP_BTN_PROFILE_REMOVE_INFO =
      TapConstants._('tap_btn_profile_remove_info', [SegmentPlatform()]);

  static TapConstants TAP_BTN_PROFILE_REMOVE_INFO_CONFIRM = TapConstants._(
      'tap_btn_profile_remove_info_confirm', [SegmentPlatform()]);

  static TapConstants TAP_BTN_PROFILE_REMOVE_INFO_CANCEL =
      TapConstants._('tap_btn_profile_remove_info_cancel', [SegmentPlatform()]);

  static TapConstants TAP_BTN_ENTER_ACKO_DRIVE =
      TapConstants._('tap_btn_enter_acko_drive', [SegmentPlatform()]);

  static TapConstants TAP_BTN_ACKO_DRIVE_CITY_SELECTED =
      TapConstants._('tap_btn_acko_drive_city_selected', [SegmentPlatform()]);

  static TapConstants TAP_BTN_ACKO_DRIVE_CONFIRM_CONSENT =
      TapConstants._('tap_btn_acko_drive_confirm_consent', [SegmentPlatform()]);

  static TapConstants TAP_BTN_TRAVEL_COMING_SOON_REGISTER_INTEREST =
      TapConstants._(
          'tap_btn_travel_coming_soon_register_interest', [SegmentPlatform()]);

  static TapConstants TAP_BTN_PRODUCT_CATEGORY_NAV_SELECTION = TapConstants._(
      'tap_btn_product_category_nav_selection', [SegmentPlatform()]);

  static TapConstants TAP_BTN_CHALLANS_VIEW_ALL_VIOLATIONS = TapConstants._(
      'tap_btn_challans_view_all_violations', [SegmentPlatform()]);

  static TapConstants CHALLANS_VIOLATIONS_LIST_LOADED =
      TapConstants._('challans_violations_list_loaded', [SegmentPlatform()]);

  static TapConstants TAP_BTN_CHALLANS_VIEW_ALL_VIOLATIONS_DONE =
      TapConstants._(
          'tap_btn_challans_view_all_violations_done', [SegmentPlatform()]);

  static TapConstants TAP_BTN_CHALLANS_PAY_NOW =
      TapConstants._('tap_btn_challans_pay_now', [SegmentPlatform()]);

  static TapConstants CHALLANS_PAYMENT_BREAKUP_LOADED =
      TapConstants._('challans_payment_breakup_loaded', [SegmentPlatform()]);

  static TapConstants TAP_BTN_CHALLANS_PROCEED_TO_PAY =
      TapConstants._('tap_btn_challans_proceed_to_pay', [SegmentPlatform()]);

  static TapConstants TAP_BTN_CHALLANS_CHECK_CHALLAN_STATUS = TapConstants._(
      'tap_btn_challans_check_challan_status', [SegmentPlatform()]);

  static TapConstants CHALLANS_PAYMENT_INPROGRESS_CARD_LOADED = TapConstants._(
      'challans_payment_inprogress_card_loaded', [SegmentPlatform()]);

  static TapConstants TAP_BTN_CHALLANS_DOWNLOAD_RECEIPT =
      TapConstants._('tap_btn_challans_download_receipt', [SegmentPlatform()]);

  static TapConstants TAP_BTN_CHALLANS_CONTACT_SUPPORT =
      TapConstants._('tap_btn_challans_contact_support', [SegmentPlatform()]);

  static TapConstants TAP_BTN_CHALLANS_TRANSACTION_HISTORY = TapConstants._(
      'tap_btn_challans_transaction_history', [SegmentPlatform()]);

  static TapConstants TAP_BTN_CHALLANS_SEE_HOW_IT_WORKS =
      TapConstants._('tap_btn_challans_see_how_it_works', [SegmentPlatform()]);

  static TapConstants TAP_BTN_CHALLANS_TRANSACTION_DETAILS = TapConstants._(
      'tap_btn_challans_transaction_details', [SegmentPlatform()]);

  static TapConstants TAP_BTN_ACTIVE_POLICIES_LIST_CARD =
      TapConstants._('tap_btn_active_policies_list_card', [SegmentPlatform()]);

  static TapConstants TAP_BTN_HEALTH_AROGYA_SANJEEVANI_CARD = TapConstants._(
      'tap_btn_health_arogya_sanjeevani_card', [SegmentPlatform()]);

  static TapConstants TAP_BTN_BIKE_DETECTED_MODAL_BUY_BIKE_INSURANCE =
      TapConstants._('tap_btn_bike_detected_modal_buy_bike_insurance',
          [SegmentPlatform()]);
  static TapConstants TAP_BTN_BIKE_DETECTED_MODAL_EDIT_REG_NO = TapConstants._(
      'tap_btn_bike_detected_modal_edit_reg_no', [SegmentPlatform()]);

  static TapConstants TAP_BTN_BIKE_DETECTED_MODAL_CROSS =
      TapConstants._('tap_btn_bike_detected_modal_cross', [SegmentPlatform()]);

  static TapConstants TAP_BTN_POLICY_ALREADY_EXISTS_MODAL_VIEW_POLICY =
      TapConstants._('tap_btn_policy_already_exists_modal_view_policy',
          [SegmentPlatform()]);

  static TapConstants TAP_BTN_POLICY_ALREADY_EXISTS_MODAL_EDIT_REG_NO =
      TapConstants._('tap_btn_policy_already_exists_modal_edit_reg_no',
          [SegmentPlatform()]);

  static TapConstants TAP_BTN_POLICY_ALREADY_EXISTS_MODAL_LOGIN =
      TapConstants._(
          'tap_btn_policy_already_exists_modal_login', [SegmentPlatform()]);

  static TapConstants TAP_BTN_REMINDERS_REMINDER_SETUP =
      TapConstants._('tap_btn_reminders_reminder_setup', [SegmentPlatform()]);

  static TapConstants TAP_BTN_REMINDERS_TOGGLE =
      TapConstants._('tap_btn_reminders_toggle', [SegmentPlatform()]);

  static TapConstants TAP_BTN_REMINDERS_SET_REMINDER_CONFIRM = TapConstants._(
      'tap_btn_reminders_set_reminder_confirm', [SegmentPlatform()]);

  static TapConstants REMINDER_SET_SUCCESSFULLY_TOAST_LOADED = TapConstants._(
      'reminder_set_successfully_toast_loaded', [SegmentPlatform()]);

  static TapConstants TAP_BTN_REMINDERS_REFRESH_DATA =
      TapConstants._('tap_btn_reminders_refresh_data', [SegmentPlatform()]);

  static TapConstants REMINDER_EXPIRY_DATE_FETCH_FAILED =
      TapConstants._('reminder_expiry_date_fetch_failed', [SegmentPlatform()]);

  static TapConstants TAP_BTN_CMS_VIDEO_PLAY_PAUSE =
      TapConstants._('tap_btn_cms_video_play_pause', [SegmentPlatform()]);

  static TapConstants TAP_BTN_CMS_VIDEO_THUMBNAIL =
      TapConstants._('tap_btn_cms_video_thumbnail', [SegmentPlatform()]);

  static TapConstants TAP_BTN_CMS_VIDEO_LIKE =
      TapConstants._('tap_btn_cms_video_like', [SegmentPlatform()]);

  static TapConstants TAP_BTN_CMS_VIDEO_SHARE =
      TapConstants._('tap_btn_cms_video_share', [SegmentPlatform()]);
  static TapConstants TAP_VIEW_PAYMENT_SCHEDULE_CTA =
      TapConstants._('tap_view_payment_schedule_cta', [SegmentPlatform()]);

  static TapConstants TAP_MAKE_POLICY_CHANGES =
      TapConstants._('tap_make_policy_changes', [SegmentPlatform()]);

  static TapConstants TAP_POLICY_STATUS_CTA =
      TapConstants._('tap_policy_status_cta', [SegmentPlatform()]);

  static TapConstants TAP_POLICY_ENDORSEMENT_CARD_CTA =
      TapConstants._('tap_policy_endorsement_card_cta', [SegmentPlatform()]);

  static TapConstants TAP_SELECT_POLICY =
      TapConstants._('tap_select_policy', [SegmentPlatform()]);
  static TapConstants TAP_BTN_POLICY_ALREADY_EXISTS_MODAL_CROSS =
      TapConstants._(
          'tap_btn_policy_already_exists_modal_cross', [SegmentPlatform()]);

  static TapConstants TAP_BTN_CMS_VIDEO_FULL_SCREEN =
      TapConstants._('tap_btn_cms_video_full_screen', [SegmentPlatform()]);

  static TapConstants TAP_BTN_CMS_VIDEO_REPLAY =
      TapConstants._('tap_btn_cms_video_replay', [SegmentPlatform()]);
  static TapConstants TAP_BTN_CAR_DETECTED_MODAL_BUY_CAR_INSURANCE =
      TapConstants._(
          'tap_btn_car_detected_modal_buy_car_insurance', [SegmentPlatform()]);

  static TapConstants TAP_BTN_CAR_DETECTED_MODAL_EDIT_REG_NO = TapConstants._(
      'tap_btn_car_detected_modal_edit_reg_no', [SegmentPlatform()]);

  static TapConstants TAP_BTN_CAR_DETECTED_MODAL_CROSS =
      TapConstants._('tap_btn_car_detected_modal_cross', [SegmentPlatform()]);

  static TapConstants TAP_EMI_OPTION =
      TapConstants._('tap_emi_option', [SegmentPlatform()]);

  static TapConstants TAP_CARD_OPTION =
      TapConstants._('tap_card_option', [SegmentPlatform()]);

  static TapConstants TAP_UPI_OPTION =
      TapConstants._('tap_upi_option', [SegmentPlatform()]);

  static TapConstants TAP_NETBANKING_EVENT =
      TapConstants._('tap_netbanking_event', [SegmentPlatform()]);

  static TapConstants TAP_WALLET_OPTION =
      TapConstants._('tap_wallet_option', [SegmentPlatform()]);

  static TapConstants TAP_IFRAME_PAYNOW_CTA =
      TapConstants._('tap_iframe_paynow_cta', [SegmentPlatform()]);

  static TapConstants TAP_BTN_SEE_WHY_LINK =
      TapConstants._('tap_btn_see_why_link', [SegmentPlatform()]);

  static TapConstants TAP_BTN_USER_ABORTED =
      TapConstants._('tap_btn_user_aborted', [SegmentPlatform()]);

  static TapConstants TAP_BTN_INTERACTION =
      TapConstants._('tap_btn_interaction', [SegmentPlatform()]);

  static TapConstants TAP_BTN_ABHA_ENTRY =
      TapConstants._('tap_btn_abha_entry', [SegmentPlatform()]);

  static TapConstants TAB_CLICKED =
      TapConstants._('tab_clicked', [R2D2Platform()]);

  static TapConstants TAP_CANCEL_POLICY_PDP =
      TapConstants._('Tap_cancel_policy_pdp', [SegmentPlatform()]);
  static TapConstants TAP_NO_CANCELLATION =
      TapConstants._('Tap_No_Cancellation', [SegmentPlatform()]);
  static TapConstants TAP_YES_CANCELLATION =
      TapConstants._('Tap_Yes_Cancellation', [SegmentPlatform()]);
  static TapConstants TAP_BTN_HEALTH_CARD_2 =
      TapConstants._('tap_btn_health_card_2', [SegmentPlatform()]);

  static TapConstants REGISTRATION_NUMBER_BIKE =
      TapConstants._('registration_number_bike', [FirebasePlatform()]);

  static TapConstants REGISTRATION_NUMBER_CAR =
      TapConstants._('registration_number_car', [FirebasePlatform()]);

  static TapConstants GENERATE_LEAD =
      TapConstants._('generate_lead', [FirebasePlatform()]);
  static TapConstants SELECT_ITEM_BIKE =
      TapConstants._('select_item_bike', [FirebasePlatform()]);

  static TapConstants PURCHASE =
      TapConstants._('purchase', [FirebasePlatform()]);
}
