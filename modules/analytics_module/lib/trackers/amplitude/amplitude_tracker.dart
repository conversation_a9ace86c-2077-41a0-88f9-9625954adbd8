import 'package:amplitude_flutter/amplitude.dart';
import 'package:amplitude_flutter/configuration.dart';
import 'package:amplitude_flutter/events/base_event.dart';
import 'package:amplitude_flutter/events/identify.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/events_base.dart';
import 'package:analytics/trackers/tracker_contract.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';

import '../event_callback.dart';

class AmplitudeTracker extends TrackerContract {
  static bool isAnalyticsPlatformsInitialized = false;
  late Amplitude? _amplitude;

  @override
  Future<void> initTracker({Map<String, dynamic>? config}) async {
    if (config == null || config["amplitudeApiKey"] == null) {
      return;
    }

    try {
      _amplitude = Amplitude(Configuration(apiKey: config["amplitudeApiKey"]));
      final initialized = await _amplitude?.isBuilt;
      isAnalyticsPlatformsInitialized = initialized ?? false;
    } catch (ex, stack) {
      FirebaseCrashlytics.instance.recordError(ex, stack);
    }
  }

  @override
  Future<void> resetTracker() async {
    if (isAnalyticsPlatformsInitialized) {
      await _amplitude?.reset();
    }
  }

  @override
  bool canSend(BaseEventsClass event) {
    return isAnalyticsPlatformsInitialized;
  }

  @override
  void sendEvent(BaseEventsClass event, Map<String, dynamic> properties,
      {EventCallBack? callback}) {

    final amplitudeEvent =
        BaseEvent(event.eventName, eventProperties: properties);
    _amplitude?.track(amplitudeEvent);
  }

  @override
  Future<void> identifyUser(
      String identifier, IdentifierType identifierType) async {
    if (isAnalyticsPlatformsInitialized && identifierType == IdentifierType.userId) {
      await _amplitude?.setUserId(identifier);
    }
  }

  @override
  bool canRegisterIdentifier(IdentifierType type) {
    return (type == IdentifierType.userId);
  }

  @override
  Future<void> attachUserProperties(Map<String, dynamic> userProps) async {

    final identify = Identify();
    userProps.forEach((key, value) {
      identify.set(key, value);
    });

    await _amplitude?.identify(identify);
  }

  @override
  Future<void> sendScreenViewEvent(String? route, String platform) async {}
}
