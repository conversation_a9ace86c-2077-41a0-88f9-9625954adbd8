import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/asset_view/asset_view_shimmer/gmc_upsell_shimmer.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/buy_banner.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/health_life/tap_events/health_life_tap_events.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:policy_details/policy_details.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/domain/models/hl_cards_data.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/view/hl_pwilo_banners.dart';

class HealthPolicyGmcUpsellCardView extends StatelessWidget {
  const HealthPolicyGmcUpsellCardView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HealthPolicyDetailsCubit, HealthPolicyDetailsState>(
      buildWhen: (prev, curr) =>
      curr is HealthPolicyDetailsLoading ||
          curr is HealthPolicyDetailsLoaded,
      builder: (context, state) {
        if (state is HealthPolicyDetailsLoaded) {
          final upsellModel = state.upsellModels;
          final cubit = context.read<HealthPolicyDetailsCubit>();

          final upsellCards = upsellModel?.healthGmcUpsellCardModel?.gmcUpsellCards;

          if (upsellModel != null && upsellCards.isNotNullOrEmpty) {
            return SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: List.generate(upsellCards!.length, (index) {
                  final cardData = upsellCards[index];
                  if(cardData == null) return const SizedBox.shrink();
                  return Container(
                    width: MediaQuery.of(context).size.width - (upsellCards.length > 1 ? 48 : 0),
                    margin: const EdgeInsets.only(bottom: 16),
                    child: BuyBanner(
                      title: cardData.title,
                      subtitle: cardData.subtitle,
                      imageUrl: cardData.imageUrl,
                      redirectionUrl: cardData.redirectUrl,
                      analyticalEvent: () {
                        cubit.handlePDPViewTapEvent(upsell_card_event);
                      },
                      fromPage: 'l3_pdp_page'
                    ),
                  );
                }),
              ),
            );
          } else if (upsellModel?.pwiLoCardsData != null) {
            final healthPWILOCards = upsellModel?.pwiLoCardsData?.data?.cards;
            final lifePWILOCards = upsellModel?.lifePwiLoCardsData?.data?.cards;
            final List<Cards> pwiLoCardsData = [];
            if (healthPWILOCards != null && healthPWILOCards.isNotEmpty) {
              pwiLoCardsData.addAll(healthPWILOCards);
            }
            if (lifePWILOCards != null && lifePWILOCards.isNotEmpty) {
              pwiLoCardsData.addAll(lifePWILOCards);
            }
            return SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.only(bottom: 4, left: 12, right: 12, top: 0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(pwiLoCardsData.length, (index) {
                  final cardData = pwiLoCardsData[index];
                  if(cardData == null) return const SizedBox.shrink();
                  return HLPWILOBanners(
                    pwiLoCardsData: cardData,
                    isLast: index == pwiLoCardsData.length - 1,
                    totalCards: pwiLoCardsData.length,
                    analyticalEvent: () {
                      AnalyticsTrackerManager.instance.sendEvent(
                          event: HLTrackEvents.TAP_L3_PDP_PAGE,
                          properties: {
                            "journey": "${cardData.lob}_retail_pwilo",
                            "product": cardData.lob,
                            "platform": Util.getPlatform(),
                            "visit_entry_tag": "entry",
                            "from_page": "policy_details"
                          }
                      );
                    },
                  );
                }),
              ),
            );
          } else {
            return const SizedBox.shrink();
          }
        } else if (state is HealthPolicyDetailsLoading) {
          return const GMCUpsellBannerShimmer();
        } else {
          return const SizedBox.shrink();
        }
      },
    );
  }
}