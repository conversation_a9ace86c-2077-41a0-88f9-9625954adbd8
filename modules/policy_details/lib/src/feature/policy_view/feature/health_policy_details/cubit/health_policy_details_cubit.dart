// ignore_for_file: lines_longer_than_80_chars

import 'dart:convert';

import 'package:acko_flutter/feature/health_home/models/health_header_data_model.dart';
import 'package:acko_flutter/feature/health_policy/repository/health_policy_repository.dart';
import 'package:acko_flutter/feature/vas/gmc_upsell/model/gmc_upsell_dto.dart';
import 'package:acko_flutter/framework/pdp/health/common/constants.dart';
import 'package:acko_flutter/framework/pdp/health/common/pdp_utils.dart';
import 'package:acko_flutter/framework/pdp/health/models/health_page_crosssell_model.dart';
import 'package:acko_flutter/framework/pdp/health/models/health_policies_basic_details.dart';
import 'package:acko_flutter/framework/pdp/health/models/person_data_model.dart';
import 'package:acko_flutter/framework/pdp/health/models/policy_holder_data_model.dart';
import 'package:acko_flutter/framework/pdp/health/models/tabular_text_model.dart';
import 'package:acko_flutter/framework/pdp/health/repo/health_repo.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/health_coverages_tab.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/health_life/page_events/health_life_page_events.dart';
import 'package:analytics/events/health_life/tap_events/health_life_tap_events.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:policy_details/policy_details.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/domain/models/enums.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/domain/models/gmc_renewal_banner.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/domain/models/health_gmc_upsell_card_model.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/domain/models/hl_cards_data.dart';
import 'package:policy_details/src/feature/feature.dart';
import 'package:utilities/remote_config/remote_config.dart';
import 'package:utilities/widgets/acko_safe_cubit.dart';

part 'health_policy_details_state.dart';

class HealthPolicyDetailsCubit extends AckoSafeCubit<HealthPolicyDetailsState> {
  HealthPolicyDetailsCubit(this.filter, this.defaultValue, this.person)
      : super(HealthPolicyDetailsLoading()) {
    isMandateProposal = filter.containsKey('proposalId');
  }

  final Map<String, dynamic> filter;
  final int defaultValue;
  final HealthPolicyHolder? person;
  bool isMandateProposal = false;
  HealthPolicyDetailsModel? healthPolicyDetailsModel;
  GMCUpsellSheetDto? gmcUpsellData;
  HealthHeaderDataModel? healthPolicies;
  HealthAssetDetailsResponse? _assetData;
  HealthAssetDetailsRepo healthAssetDetailsRepo = HealthAssetDetailsRepo();

  HealthPolicyDetailsRepo healthPolicyDetailsRepo = HealthPolicyDetailsRepo();
  final HealthRepo _healthRepo = HealthRepo(AggregatedPageMode.policy);

  Future<void> refreshBloc() async {
    emit(HealthPolicyDetailsLoading());
    await getPolicyData();
  }

  Future<void> getPolicyData() async {
    healthPolicyDetailsModel = await healthPolicyDetailsRepo.getPolicyDetails(
      policyNumber: filter['policy_number'].toString(),
      policyId: filter['policy_id'].toString(),
    );
    final gmcRenewalData = healthPolicyDetailsRepo.getGMCRenewalData();
    if (healthPolicyDetailsModel != null) {
      if (filter['policy_number'] == null &&
          healthPolicyDetailsModel!.policyNumber.isNotNullOrEmpty) {
        filter['policy_number'] = healthPolicyDetailsModel!.policyNumber;
      }
      filter['plan_name'] = healthPolicyDetailsModel!.planName;
      final upsellModel = await getUpsellData();
      emit(
        HealthPolicyDetailsLoaded(
          healthPolicyDetailsModel: healthPolicyDetailsModel!,
          upsellModels: upsellModel,
        ),
      );
    } else {
      emit(
        HealthPolicyDetailsError(),
      );
    }
    await getPolicyTabBarData(healthPolicyDetailsModel);
    await getPolicyNotifications(gmcRenewalData);
    handlePDPViewEvent();
  }

  Future<UpsellCardModels?> getUpsellData() async {
    final results = await Future.wait([
      getPWILOData(),
      getLifePWILOData(),
    ]);

    final gmcUpsellData = getGMCUpsellData() as HealthGmcUpsellCardModel?;
    final pwiloCardsData = results[0] as PWILOCardsData?;
    final lifePwiLoCardsData = results[1] as PWILOCardsData?;

    if (gmcUpsellData?.showUpsellCard ?? false) {
      return UpsellCardModels(
        healthGmcUpsellCardModel: gmcUpsellData,
      );
    }

    return UpsellCardModels(
      pwiLoCardsData: pwiloCardsData,
      lifePwiLoCardsData: lifePwiLoCardsData,
    );
  }

  Future<PWILOCardsData?> getPWILOData() async {
    var pwiloRemoteData =
        RemoteConfigInstance.instance.getData(RemoteConfigKeysSet.PWILO_CARDS);
    String? healthCardId;
    if (pwiloRemoteData == null) {
      await FirebaseRemoteConfig.instance.fetchAndActivate();
      pwiloRemoteData = RemoteConfigInstance.instance
          .getData(RemoteConfigKeysSet.PWILO_CARDS);
    } else {
      final pwiloConfig = jsonDecode(pwiloRemoteData.toString());
      healthCardId = pwiloConfig['health_cards'] as String?;
    }

    final pwiLoData = await _healthRepo.getPWILOCardData();
    _filterAndSetLob(pwiLoData, healthCardId, 'health');

    return pwiLoData;
  }

  Future<PWILOCardsData?> getLifePWILOData() async {
    var pwiloRemoteData =
        RemoteConfigInstance.instance.getData(RemoteConfigKeysSet.PWILO_CARDS);
    String? lifeCardId;
    if (pwiloRemoteData == null) {
      await FirebaseRemoteConfig.instance.fetchAndActivate();
      pwiloRemoteData = RemoteConfigInstance.instance
          .getData(RemoteConfigKeysSet.PWILO_CARDS);
    } else {
      final pwiloConfig = jsonDecode(pwiloRemoteData.toString());
      lifeCardId = pwiloConfig['life_cards'] as String?;
    }
    final lifePwiLoData = await _healthRepo.getLifePWILOCardData();
    _filterAndSetLob(lifePwiLoData, lifeCardId, 'life');

    return lifePwiLoData;
  }

  void _filterAndSetLob(PWILOCardsData? pwiLoData, String? cardId, String lob) {
    if (cardId.isNotNullOrEmpty) {
      pwiLoData?.data?.cards?.removeWhere((card) => card.id != cardId);
    }
    pwiLoData?.data?.cards?.forEach((card) {
      card.lob = lob;
    });
  }

  Future<void> getPolicyNotifications(RenewalBanner? renewalBanner) async {
    emit(HealthPolicyNotificationsLoading());
    final policyCards = healthPolicyDetailsModel?.policyCards
            ?.where(
              (element) =>
                  element.cardType.notEqualsIgnoreCase('policy_enrolment'),
            )
            .toList() ??
        [];
    policyCards
        .removeWhere((element) => element.data?.ctaText.isNullOrEmpty ?? false);

    if ((renewalBanner?.showGmcRenewalBanner ?? false) &&
        (healthPolicyDetailsModel?.isGMCPolicy ?? false) &&
        (healthPolicyDetailsModel?.isExpired ?? false) &&
        (healthPolicyDetailsModel?.masterPolicyNumber
                .equalsIgnoreCase(renewalBanner?.masterPolicyNumber) ??
            false)) {
      policyCards.add(
        PolicyCard(
          cardType: 'gmc_renewal',
          data: PolicyNotificationData(
            title: renewalBanner?.title ?? '',
            subTitle: renewalBanner?.subtitle ?? '',
            policyName: healthPolicyDetailsModel?.planName,
            cardColor: renewalBanner?.cardColor ?? '',
          ),
        ),
      );
    }

    if (policyCards.isNotEmpty) {
      emit(HealthPolicyNotificationsLoaded(policyCards: policyCards));
    } else {
      emit(HealthPolicyNotificationsEmpty());
    }
  }

  Future<void> getPolicyTabBarData(
    HealthPolicyDetailsModel? healthPolicyDetailsModel,
  ) async {
    final crossSellList = await fetchCrossSellData();
    final isFitnessTrackingEnabled = await fetchFitnessTrackingData();

    final topupValue = healthPolicyDetailsModel?.topup?.displayValue;
    var deductibleValue = healthPolicyDetailsModel?.deductibles?.displayValue;

    if (healthPolicyDetailsModel?.deductibles?.value == 0) {
      deductibleValue = '';
    }

    final policyDetailsMap = <String, String>{
      'Sum insured': (healthPolicyDetailsModel
                  ?.sumInsured?.displayValue.isNotNullOrEmpty ??
              false)
          ? '${healthPolicyDetailsModel?.sumInsured?.displayValue}'
          : '',
      if (topupValue.isNotNullOrEmpty) 'Top up': topupValue!,
      if (deductibleValue.isNotNullOrEmpty) 'Deductible': deductibleValue ?? '',
      'Policy number': healthPolicyDetailsModel?.displayPolicyNumber ?? '-',
    };

    final policyDetailsData = policyDetailsMap.entries
        .map((entry) => TabularText(lText: entry.key, rText: entry.value))
        .toList();

    final policyDetailsModel = PolicyDetailsModel(
      title: 'Policy details',
      policyDetailsData: policyDetailsData,
    );

    /// PolicyHolderUserDetails ->
    final user = healthPolicyDetailsModel?.users
        ?.firstWhere((element) => element.role.equalsIgnoreCase('proposer'));
    final policyHolderUserDetails = PolicyHolderDataModel(
      fullName: user?.name ?? '',
      email: user?.email ?? '',
      mobileNumber: user?.phone ?? '',
      pinCode: user?.pincode ?? '',
    );

    final coverPersonData = <PersonData>[];
    if (healthPolicyDetailsModel?.insureds?.isNotNullOrEmpty ?? false) {
      for (final coveredPerson in healthPolicyDetailsModel!.insureds!) {
        coverPersonData.add(
          PersonData(
            name: coveredPerson.name ?? '',
            relationship: coveredPerson.relationship,
            dateOfBirth: coveredPerson.dob,
            sumInsured:
                coveredPerson.additionalInfo?.baseSumInsured?.displayValue,
            topup: coveredPerson.additionalInfo?.topUpSumInsured?.displayValue,
            deductible: coveredPerson.additionalInfo?.deductible?.displayValue,
            addon: coveredPerson.additionalInfo?.addOnSumInsured?.displayValue,
          ),
        );
      }
    }

    /// overview -> policyDetailsModel, coverPersonData, policyHolderUserDetails

    emit(
      HealthPolicyDetailsTabBarLoaded(
        policyDetailsModel: policyDetailsModel,
        coverPersonData: coverPersonData,
        policyHolderUserDetails: policyHolderUserDetails,
        coveragesTabUIModel: getCoveragesTabData(healthPolicyDetailsModel),
        crossSellList: crossSellList,
        isFitnessTrackingEnabled: isFitnessTrackingEnabled,
        tabBarDefaultValue: defaultValue,
        person: person,
      ),
    );
  }

  String calculateFullDeductibles(String? deductible) {
    final deductibleIntValue = int.tryParse(deductible ?? '');
    final result = formatIndianCurrency(deductibleIntValue);
    return result;
  }

  String formatIndianCurrency(int? val) {
    if (val == null || val == 0) {
      return '';
    } else if (val >= 990000000) {
      return 'Unlimited';
    } else if (val < 1000) {
      return '₹$val';
    } else if (val < 100000) {
      final result = val / 1000;
      return '₹${result.toStringAsFixed(result.truncateToDouble() == result ? 0 : 2)} k';
    } else if (val < 10000000) {
      final result = val / 100000;
      return '₹${result.toStringAsFixed(result.truncateToDouble() == result ? 0 : 2)} L';
    } else {
      final result = val / 10000000;
      return '₹${result.toStringAsFixed(result.truncateToDouble() == result ? 0 : 2)} Cr';
    }
  }

  List<CoveragesTabUIModel> getCoveragesTabData(
    HealthPolicyDetailsModel? healthPolicyDetailsModel,
  ) {
    final coveragesTabData = <CoveragesTabUIModel>[];
    healthPolicyDetailsModel?.insureds?.forEach((insured) {
      var expansionTileData = <ExpansionTileData>[];
      final coveragesData = <CoveragesData>[];
      insured.categorisedCovers?.forEach((categorisedCover) {
        expansionTileData = [];
        categorisedCover.covers?.forEach((cover) {
          expansionTileData.add(
            ExpansionTileData(
              expansionTitle: cover.coverName ?? '',
              expansionDescription: cover.description ?? '',
              detailsList: cover.detailsList,
              ctaText: cover.ctaText,
              ctaUrl: cover.ctaUrl,
            ),
          );
        });
        coveragesData.add(
          CoveragesData(
            title: categorisedCover.title ?? '',
            expansionData: expansionTileData,
          ),
        );
      });
      coveragesTabData.add(
        CoveragesTabUIModel(
          userDataModel: UserData(
            userId: insured.uhid ?? '',
            initials: Util.getInitials(insured.name ?? '').toString(),
            userName: insured.name ?? '',
            gender: insured.gender,
            relationship: insured.relationship,
            id: (healthPolicyDetailsModel.policyNumber.isNotNullOrEmpty)
                ? healthPolicyDetailsModel.policyNumber
                : (healthPolicyDetailsModel.proposalId?.isNotNullOrEmpty ??
                        false)
                    ? healthPolicyDetailsModel.proposalId
                    : null,
          ),
          coveragesData: coveragesData,
        ),
      );
    });
    return coveragesTabData;
  }

  Future<List<HealthPageCrossSellV3Dao>?> fetchCrossSellData() async {
    final crossSellList = await _healthRepo.getCrossSellData();
    return crossSellList;
  }

  Future<bool> fetchFitnessTrackingData() async {
    final isFitnessTrackingEnabled =
        await _healthRepo.isFitnessTrackingEnabled(filter);
    return isFitnessTrackingEnabled ?? false;
  }

  void handlePDPViewEvent() {
    final planName = healthPolicyDetailsModel?.planName ?? '';
    AnalyticsTrackerManager.instance.sendEvent(
      event: HLPageEvents.VIEW_L3_PDP_PAGE,
      properties: {
        'product': getProductInfo(
          planName,
          healthPolicyDetailsModel?.productType ?? '',
        ),
        'plan_name': planName,
        'policy_status': healthPolicyDetailsModel?.policyStatus,
        'page_name': 'l3_pdp_page',
      },
    );
  }

  String getProductInfo(String planName, String productType) {
    var product = '';
    if (productType == 'enterprise') {
      product = 'gmc';
    } else if (planName.containsIgnoreCase('health')) {
      product = 'retail-health';
    } else {
      product = 'retail-life';
    }
    return product;
  }

  void handlePDPViewTapEvent(String cta) {
    final policyInfo = healthPolicyDetailsModel;
    final planName = policyInfo?.planName ?? '';
    final tapCTA = cta;
    AnalyticsTrackerManager.instance.sendEvent(
      event: HLTrackEvents.TAP_L3_PDP_PAGE,
      properties: {
        'product': getProductInfo(planName, policyInfo?.productType ?? ''),
        'plan_name': planName,
        'tap_cta': tapCTA,
        'policy_status': policyInfo?.policyStatus,
        'page_name': 'l3_pdp_page',
      },
    );
  }

  void handlePDPViewCoveragesTapEvent(
    String cta,
    String relationship,
    List<ExpansionTileData> expansionData,
  ) {
    final policyInfo = healthPolicyDetailsModel;
    final planName = policyInfo?.planName ?? '';
    final tapCTA = cta;
    AnalyticsTrackerManager.instance.sendEvent(
      event: HLTrackEvents.TAP_L3_PDP_COVERAGES_TAB,
      properties: {
        'product': getProductInfo(planName, policyInfo?.productType ?? ''),
        'plan_name': planName,
        'tap_cta': tapCTA,
        'members_selected': relationship,
        'covers': getCoverageData(expansionData),
        'policy_status': policyInfo?.policyStatus,
        'page_name': 'l3_pdp_page',
      },
    );
  }

  String getCoverageData(List<ExpansionTileData> expansionData) {
    final expansionDataList = <String>[];
    var coveragesString = '';
    for (final element in expansionData) {
      expansionDataList.add(element.expansionTitle);
    }
    if (expansionDataList.length > 1) {
      coveragesString = expansionDataList.join(',');
    } else {
      expansionDataList.isEmpty
          ? coveragesString
          : coveragesString = expansionDataList.first;
    }
    return coveragesString;
  }

  HealthGmcUpsellCardModel getGMCUpsellData() {
    final gmcUpsellCards = healthPolicyDetailsModel?.upsellBannerData;
    return HealthGmcUpsellCardModel(
      gmcUpsellCards: gmcUpsellCards,
      showUpsellCard: gmcUpsellCards.isNotNullOrEmpty,
    );
  }

  bool shouldShowGMCUpsellBanner(HealthHeaderDataModel? headerDataModel) {
    var shouldShowGMCUpsellBanner = true;
    headerDataModel?.activePolicies?.forEach((element) {
      if (element.isAarogyaSanjeevaniPolicy ||
          element.productType.equalsIgnoreCase('retail')) {
        shouldShowGMCUpsellBanner = false;
      }
    });
    return shouldShowGMCUpsellBanner;
  }

  Future<GMCUpsellSheetDto?> fetchGMCUpsellData() async {
    return gmcUpsellData ??= await _healthRepo.getGMCUpsellData();
  }

  Future<HealthAssetDetailsResponse?> fetchAssetData() async {
    _assetData ??= await healthAssetDetailsRepo.getAssetDetails(
      apiType: ProjectionApiType.assetDetails,
      allPolicies: true,
      filterRenewed: true,
    );
    return _assetData;
  }

  Map<String, dynamic>? findGMCUpsellInfo(
    int sumInsuredCap,
    bool shouldShowGmcUpsellBanner,
  ) {
    if (gmcUpsellData?.disableBanner == true) {
      return {
        'showUpsellCard': false,
      };
    }
    if (_assetData?.insureds != null) {
      for (final asset in _assetData!.insureds!) {
        if (!asset.relationship.equalsIgnoreCase('self')) continue;
        final gmcPoliciesOnly =
            asset.showGmcUpsellBanner && asset.proposals.isNullOrEmpty;
        if (gmcPoliciesOnly && asset.policies != null) {
          for (final element in asset.policies!) {
            if (element.isGMCPolicy &&
                element.policyType.equalsIgnoreCase('base') &&
                !element.isExpired) {
              /// Check if the corporate name matches any excluded corporates
              if (element.isCorporateExcluded(
                  gmcUpsellData?.excludedCorporates ?? [])) return null;
              final totalSumInsured = (element.sumInsured?.value ?? 0) +
                  (element.topup?.value ?? 0);
              return {
                'sumInsured':
                    HealthPDPUtils.formatIndianCurrency(totalSumInsured)
                        .replaceAll(RegExp(r'\s+'), ''),
                'showUpsellCard': totalSumInsured <= sumInsuredCap,
              };
            }
          }
        }
      }
    }
    return null;
  }
}
