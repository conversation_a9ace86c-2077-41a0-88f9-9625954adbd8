import 'package:acko_flutter/framework/pdp/health/common/tap_handler.dart';
import 'package:acko_flutter/framework/pdp/health/models/health_policies_basic_details.dart';
import 'package:acko_flutter/framework/pdp/health/models/ui_models/health_pdp_base_ui_model.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/pdp_policy_view_services.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/policy_view/policy_view_shimmer/pdp_policy_grid_view_shimmer.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_flutter/util/health/health_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:policy_details/policy_details.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/constants/constants.dart';

class HealthPolicyServicesView extends StatelessWidget {
  const HealthPolicyServicesView({
    required this.healthPDPPolicyPageCubit,
    super.key,
  });

  final HealthPolicyDetailsCubit healthPDPPolicyPageCubit;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HealthPolicyDetailsCubit, HealthPolicyDetailsState>(
      buildWhen: (prev, curr) =>
          curr is HealthPolicyDetailsLoaded ||
          curr is HealthPolicyDetailsLoading,
      builder: (context, state) {
        if (state is HealthPolicyDetailsLoaded) {
          return getPolicyHeaderActionItemsGrid(
            context: context,
            policyFeature: state.healthPolicyDetailsModel.features,
            editMemberUrl: state.healthPolicyDetailsModel.editMemberUrl,
            endorsementUrl: state.healthPolicyDetailsModel.endorsementUrl,
            enrolmentJourneyUrl: state.healthPolicyDetailsModel.enrolmentUrl,
            stage: state.healthPolicyDetailsModel.stage,
            documents: state.healthPolicyDetailsModel.policyDocuments,
            premiumDetail: state.healthPolicyDetailsModel.premiumDetail,
            isUpcoming: state.healthPolicyDetailsModel.isUpcoming,
            users: state.healthPolicyDetailsModel.users?.first,
            isExpired: state.healthPolicyDetailsModel.isPolicyEnded,
            productType: state.healthPolicyDetailsModel.productType,
            isAsp: state.healthPolicyDetailsModel.isAarogyaSanjeevaniPolicy,
            isGmcPolicy: state.healthPolicyDetailsModel.isGMCPolicy,
          );
        } else {
          return const PDPPolicyGridViewShimmer();
        }
      },
    );
  }

  Widget getPolicyHeaderActionItemsGrid({
    required BuildContext context,
    List<HealthPolicyService>? policyFeature,
    String? endorsementUrl,
    String? editMemberUrl,
    String? enrolmentJourneyUrl,
    String? stage,
    PremiumDetail? premiumDetail,
    List<HealthPolicyDocuments>? documents,
    bool? isUpcoming,
    bool? isExpired,
    Users? users,
    String? productType,
    bool? isAsp,
    bool? isGmcPolicy,
  }) {
    if (policyFeature.isNotNullOrEmpty) {
      final gridTiles = <PolicyActionGridTileModel>[];
      for (final element in policyFeature!) {
        String? actionUrl;
        if (element.action.equalsIgnoreCase('do_endorsements')) {
          actionUrl =
              (productType.equalsIgnoreCase(HealthConstants.RETAIL_PRODUCT) ||
                      productType.equalsIgnoreCase(HealthConstants.ASP_PRODUCT))
                  ? endorsementUrl
                  : (stage.equalsIgnoreCase(LinkPolicyState.ENROLLMENT))
                      ? editMemberUrl ?? enrolmentJourneyUrl
                      : null;
        } else if (element.action.equalsIgnoreCase('see_payment_schedule')) {
          actionUrl = premiumDetail?.paymentSchedulePageUrl;
        } else if (element.action.equalsIgnoreCase('get_help') ||
            element.action.equalsIgnoreCase('download_documents')) {
          actionUrl =
              (productType.equalsIgnoreCase(HealthConstants.RETAIL_PRODUCT) ||
                      productType.equalsIgnoreCase(HealthConstants.ASP_PRODUCT))
                  ? Constants.RETAIL_HEALTH_HELP_PAGE
                  : Constants.ENTERPRISE_HELP_PAGE;
        }

        healthPDPPolicyPageCubit.filter['allPolicies'] = [
          healthPDPPolicyPageCubit.healthPolicyDetailsModel!,
        ];
        final gridTile = PolicyActionGridTileModel(
          policyActionGridTileHeader: element.title.equalsIgnoreCase('claims')
              ? 'Claim'
              : element.title!,
          policyActionGridTileImagePath: element.icon ?? '',
          policyActionGridOnClickAction: () {
            healthPDPPolicyPageCubit.handlePDPViewTapEvent(element.title ?? '');
            if (element.title.containsIgnoreCase('checkup')) {
              Navigator.of(context).pushNamed(
                Routes.AHC_LANDING,
                arguments: {
                  'policy_number': healthPDPPolicyPageCubit
                      .healthPolicyDetailsModel?.policyNumber,
                },
              );
              return;
            }
            HealthTapHandler.gridOnTapHandler(
              element.action,
              actionUrl,
              context,
              documents: documents,
              pdpFilterMap: healthPDPPolicyPageCubit.filter,
              isAsp: isAsp ?? false,
              isGmcPolicy: isGmcPolicy ?? false,
              productType: productType,
              policyDetails: healthPDPPolicyPageCubit.healthPolicyDetailsModel,
              fromPage: 'health_policy_view'
            );
          },
        );

        if (element.action.equalsIgnoreCase('see_payment_schedule')) {
          if (premiumDetail?.paymentSchedulePageUrl.isNotNullOrEmpty ?? false) {
            if (premiumDetail?.paymentFrequency.equalsIgnoreCase('monthly') ??
                false) {
              gridTiles.add(gridTile);
            } else if ((premiumDetail?.remainingPremium?.grossAmount ?? 0) >
                0) {
              gridTiles.add(gridTile);
            }
          }
        } else if (element.action.equalsIgnoreCase('download_documents')) {
          if (documents.isNotNullOrEmpty) {
            gridTiles.add(gridTile);
          }
        } else if (element.action.equalsIgnoreCase('do_endorsements')) {
          if (healthPDPPolicyPageCubit.healthPolicyDetailsModel?.policyStatus
                  .notEqualsIgnoreCase('lapsed') ??
              false) {
            gridTiles.add(gridTile);
          }
        } else {
          gridTiles.add(gridTile);
        }
      }
      return buildPolicyActionGridView(policyActionGridTiles: gridTiles);
    }

    return const SizedBox.shrink();
  }

  Widget buildPolicyActionGridView({
    required List<PolicyActionGridTileModel> policyActionGridTiles,
  }) {
    final gridTiles =
        buildGridActionTiles(gridActionTiles: policyActionGridTiles);

    if (policyActionGridTiles.isEmpty || gridTiles.isEmpty) {
      return const SizedBox.shrink();
    }

    final gridType =
        gridTiles.length < 4 ? ListType.normal : ListType.expandable;

    return SDUIGridView(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      crossAxisCount: 4,
      mainAxisSpacing: 10,
      crossAxisSpacing: 10,
      maxItems: 4,
      gridType: gridType,
      childWidgets: gridTiles,
    );
  }
}
