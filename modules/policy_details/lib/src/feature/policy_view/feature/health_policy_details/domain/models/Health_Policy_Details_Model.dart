import 'package:acko_flutter/feature/claim/advance_cash/onboarding/data/AdvanceCashState.dart';
import 'package:acko_flutter/feature/vas/gmc_upsell/model/gmc_upsell_dto.dart';
import 'package:acko_flutter/framework/pdp/health/models/health_assets_details.dart';
import 'package:acko_flutter/framework/pdp/health/models/health_policies_basic_details.dart';
import 'package:acko_flutter/framework/pdp/health/models/ui_models/policy_card_model.dart';
import 'package:intl/intl.dart';
import 'package:policy_details/policy_details.dart';
import 'package:utilities/core/string_extensions.dart';

class HealthPolicyDetailsModel extends PolicyDetailsBaseModel {
  HealthPolicyDetailsModel({
    this.proposalId,
    this.proposalStatus,
    this.proposalSubStatus,
    this.policyId,
    this.policyNumber,
    this.displayPolicyNumber,
    this.masterPolicyNumber,
    this.planId,
    this.productPlanType,
    this.planName,
    this.policyStartDate,
    this.policyEndDate,
    this.stage,
    this.policyStatus,
    this.sumInsured,
    this.copay,
    this.deductibles,
    this.topup,
    this.remainingSumInsured,
    this.kycDetails,
    this.tpa,
    this.tpaLogoUrl,
    this.packageId,
    this.corporate,
    this.editMemberUrl,
    this.enrolmentUrl,
    this.endorsementUrl,
    this.renewalUrl,
    this.premiumDetail,
    this.users,
    this.insureds,
    this.policyCards,
    this.policyDocuments,
    this.keyCoversObj,
    this.features,
    this.productType,
    this.policyType,
    this.policySubstatus,
    this.issuanceType,
    this.enrolmentJourneyURL,
    this.basbaPolicy,
    this.policyConstruct,
    this.recommendationSection,
    this.upsellBannerData,
  });

  HealthPolicyDetailsModel.error({
    super.error,
    this.proposalId,
    this.proposalSubStatus,
    this.proposalStatus,
    this.policyId,
    this.policyNumber,
    this.displayPolicyNumber,
    this.masterPolicyNumber,
    this.planId,
    this.productPlanType,
    this.planName,
    this.policyStartDate,
    this.policyEndDate,
    this.stage,
    this.policyStatus,
    this.sumInsured,
    this.copay,
    this.deductibles,
    this.topup,
    this.remainingSumInsured,
    this.kycDetails,
    this.tpa,
    this.tpaLogoUrl,
    this.packageId,
    this.corporate,
    this.editMemberUrl,
    this.enrolmentUrl,
    this.endorsementUrl,
    this.renewalUrl,
    this.premiumDetail,
    this.users,
    this.insureds,
    this.policyCards,
    this.policyDocuments,
    this.keyCoversObj,
    this.features,
    this.productType,
    this.policyType,
    this.policySubstatus,
    this.issuanceType,
    this.enrolmentJourneyURL,
    this.prePolicyEditCard,
    this.basbaPolicy,
    this.policyConstruct,
    this.recommendationSection,
    this.upsellBannerData,
  });

  HealthPolicyDetailsModel.fromJson(Map<String, dynamic> json) {
    proposalId = json['proposal_id'] as String?;
    proposalStatus = json['proposal_status'] as String?;
    proposalSubStatus = json['proposal_sub_status'] as String?;
    policyId = json['policy_id'] as String?;
    policyNumber = json['policy_number'] as String?;
    displayPolicyNumber = json['display_policy_number'] as String?;
    masterPolicyNumber = json['master_policy_number'] as String?;
    planId = json['plan_id'] as String?;
    productPlanType = json['product_plan_type'] as String?;
    planName = json['plan_name'] as String?;
    policyStartDate = json['policy_start_date'] as String?;
    policyEndDate = json['policy_end_date'] as String?;
    stage = json['stage'] as String?;
    enrolmentStage = json['enrolment_stage'] as String?;
    policyStatus = json['policy_status'] as String?;
    sumInsured = json['sum_insured'] != null
        ? SumInsured.fromJson(json['sum_insured'] as Map<String, dynamic>)
        : null;
    copay = json['copay'] != null
        ? SumInsured.fromJson(json['copay'] as Map<String, dynamic>)
        : null;
    deductibles = json['deductible'] != null
        ? SumInsured.fromJson(json['deductible'] as Map<String, dynamic>)
        : null;
    topup = json['top_up_sum_insured'] != null
        ? SumInsured.fromJson(
            json['top_up_sum_insured'] as Map<String, dynamic>,
          )
        : null;
    remainingSumInsured = json['remaining_sum_insured'] != null
        ? RemainingSumInsured.fromJson(
            json['remaining_sum_insured'] as Map<String, dynamic>,
          )
        : null;
    kycDetails = json['kyc_details'] != null
        ? KycDetails.fromJson(json['kyc_details'] as Map<String, dynamic>)
        : null;
    tpa = json['tpa'] as String?;
    tpaLogoUrl = json['tpa_logo_url'] as String?;
    packageId = json['package_id'] as String?;
    corporate = json['corporate'] as String?;
    corporateIconUrl = json['partner_icon_url'] as String?;
    enrolmentUrl = json['enrolment_url'] as String?;
    editMemberUrl = json['edit_member_url'] as String?;
    endorsementUrl = json['endorsement_url'] as String?;
    renewalUrl = json['renewal_url'] as String?;
    premiumDetail = json['premium_detail'] != null
        ? PremiumDetail.fromJson(json['premium_detail'] as Map<String, dynamic>)
        : null;
    users = json['users'] == null
        ? []
        : List<Users>.from(
            (json['users'] as List).map(
              (x) => Users.fromJson(x as Map<String, dynamic>),
            ),
          );
    insureds = json['insureds'] == null
        ? []
        : List<Insureds>.from(
            (json['insureds'] as List).map(
              (x) => Insureds.fromJson(x as Map<String, dynamic>),
            ),
          );
    policyDocuments = json['policy_documents'] == null
        ? []
        : List<HealthPolicyDocuments>.from(
            (json['policy_documents'] as List).map(
              (x) => HealthPolicyDocuments.fromJson(x as Map<String, dynamic>),
            ),
          );
    policyCards = json['policy_cards'] == null
        ? []
        : List<PolicyCard>.from(
            (json['policy_cards'] as List).map(
              (x) => PolicyCard.fromJson(x as Map<String, dynamic>),
            ),
          );

    keyCoversObj = (json['key_covers'] != null &&
            json['key_covers'] is Map<String, dynamic>)
        ? KeyCoversObj.fromJson(json['key_covers'] as Map<String, dynamic>?)
        : null;

    features = json['features'] == null
        ? []
        : List<HealthPolicyService>.from(
            (json['features'] as List).map(
              (x) => HealthPolicyService.fromJson(x as Map<String, dynamic>),
            ),
          );

    productType = json['product_type'] as String?;
    policyType = json['policy_type'] as String?;
    policySubstatus = json['policy_sub_status'] as String?;
    assetPolicyCoverageText = json['asset_policy_coverage_text'] as String?;
    issuanceType = json['issuance_type'] as String?;
    enrolmentJourneyURL = json['enrolment_journey_url'] as String?;
    enrolmentRemainingDays = json['enrolment_remaining_days'] as num?;
    prePolicyEditCard = json['pre_policy_edit_card'] != null
        ? PrePolicyEditCard.fromJson(
            json['pre_policy_edit_card'] as Map<String, dynamic>,
          )
        : null;
    basbaPolicy = json['basba_policy'] as bool?;
    policyConstruct = json['policy_construct'] as String?;
    recommendationSection = json['recommendation_section'] != null && json['recommendation_section'] is Map<String, dynamic>
        ? RecommendationSection.fromJson(
            json['recommendation_section'] as Map<String, dynamic>,
          )
        : null;
    upsellBannerData = json['upsell_cards'] != null && json['upsell_cards'] is List
        ? (json['upsell_cards'] as List)
        .map((item) => CardData.fromJson(item as Map<String, dynamic>))
        .toList()
        : null;
  }

  Map<String, dynamic> toJson() {
    return {
      'proposal_id': proposalId,
      'proposal_status': proposalStatus,
      'proposal_sub_status': proposalSubStatus,
      'policy_id': policyId,
      'policy_number': policyNumber,
      'display_policy_number': displayPolicyNumber,
      'master_policy_number': masterPolicyNumber,
      'plan_id': planId,
      'product_plan_type': productPlanType,
      'plan_name': planName,
      'policy_start_date': policyStartDate,
      'policy_end_date': policyEndDate,
      'stage': stage,
      'enrolment_stage': enrolmentStage,
      'enrolment_remaining_days': enrolmentRemainingDays,
      'policy_status': policyStatus,
      'sum_insured': sumInsured?.toJson(),
      'copay': copay?.toJson(),
      'deductibles': deductibles?.toJson(),
      'top_up_sum_insured': topup?.toJson(),
      'remaining_sum_insured': remainingSumInsured?.toJson(),
      'kyc_details': kycDetails?.toJson(),
      'tpa': tpa,
      'tpa_logo_url': tpaLogoUrl,
      'package_id': packageId,
      'corporate': corporate,
      'partner_icon_url': corporateIconUrl,
      'enrolment_url': enrolmentUrl,
      'edit_member_url': editMemberUrl,
      'endorsement_url': endorsementUrl,
      'renewal_url': renewalUrl,
      'premium_detail': premiumDetail?.toJson(),
      'users': users?.map((user) => user.toJson()).toList(),
      'insureds': insureds?.map((insured) => insured.toJson()).toList(),
      'policy_documents': policyDocuments?.map((doc) => doc.toJson()).toList(),
      'policy_cards': policyCards?.map((card) => card.toJson()).toList(),
      'key_covers': keyCoversObj?.toJson(),
      'features': features?.map((feature) => feature.toJson()).toList(),
      'product_type': productType,
      'policy_type': policyType,
      'policy_sub_status': policySubstatus,
      'issuance_type': issuanceType,
      'enrolment_journey_url': enrolmentJourneyURL,
      'pre_policy_edit_card': prePolicyEditCard,
      'basba_policy': basbaPolicy,
      'policy_construct': policyConstruct,
    };
  }

  DateTime get startDateTime => policyStartDate.isNotNullAndEmpty
      ? DateTime.parse(policyStartDate!).toLocal()
      : DateTime.now().toLocal();

  DateTime get endDateTime => policyEndDate.isNotNullAndEmpty
      ? DateTime.parse(policyEndDate!).toLocal()
      : DateTime.now().toLocal();

  bool get isUpcoming =>
      policyStartDate.isNotNullAndEmpty &&
      DateTime.now().toLocal().isBefore(startDateTime);

  bool get isPolicyEnded =>
      policyEndDate.isNotNullAndEmpty &&
      DateTime.now().toLocal().isAfter(endDateTime);

  bool get isGMCPolicy => productType.containsIgnoreCase('enterprise');

  bool get isAarogyaSanjeevaniPolicy =>
      planId.containsIgnoreCase('Arogya Sanjeevani') ||
      productPlanType.equalsIgnoreCase('asp');

  bool get isRetailTopup =>
      productType.containsIgnoreCase('retail') &&
      policyType.containsIgnoreCase('topup');

  bool get isDeactivated =>
      policyStatus.equalsIgnoreCase('lapsed') &&
      policySubstatus.equalsIgnoreCase('expired');

  bool get isOnHold =>
      policySubstatus.equalsIgnoreCase('lapsed') && policySubstatus == null;

  bool get isPremiumPending =>
      policyStatus.equalsIgnoreCase('lapsed') &&
      (policySubstatus.equalsIgnoreCase('on_hold') ||
          policySubstatus.equalsIgnoreCase('onhold'));

  bool get isUnderReview =>
      policyStatus.equalsIgnoreCase('lapsed') &&
      policySubstatus.equalsIgnoreCase('uw_review_pending');

  bool get isExpired =>
      isDeactivated ||
      (policyEndDate.isNotNullAndEmpty && DateTime.now().isAfter(endDateTime));

  bool get isExpiredPolicy =>
      policyEndDate.isNotNullAndEmpty && DateTime.now().isAfter(endDateTime);

  String endDateInLocal() {
    final tFormat = DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");
    final endDate =
        tFormat.format(tFormat.parse(policyEndDate!, true).toLocal());
    return endDate;
  }

  String startDateInLocal() {
    final tFormat = DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS");
    final startDate =
        tFormat.format(tFormat.parse(policyStartDate!, true).toLocal());
    return startDate;
  }

  HealthPolicyStates get getHealthPolicyState {
    if (isPremiumPending) {
      /// on hold | policy status lapsed & substatus is null
      return HealthPolicyStates.PREMIUM_PENDING;
    } else if (isOnHold) {
      /// on hold - premium pending | status: lapsed & substatus: on hold
      return HealthPolicyStates.ON_HOLD;
    } else if (isUnderReview) {
      /// under review | substatus: uw_review_pending
      return HealthPolicyStates.UNDER_REVIEW;
    } else if (isDeactivated) {
      /// locked | status: lapsed & substatus: expired
      return HealthPolicyStates.DEACTIVATED;
    } else if (isExpired) {
      /// date passed more checks related to deactivation
      return HealthPolicyStates.EXPIRED;
    } else if (isPolicyEnded) {
      /// date passed | end date is expired
      return HealthPolicyStates.EXPIRED;
    } else if (isUpcoming) {
      return HealthPolicyStates.UPCOMING;
    } else {
      /// if none of the above | active
      return HealthPolicyStates.ACTIVE;
    }
  }

  bool get showGmcUpsellBanner {
    var gmcUpsellBanner = true;
    if (isAarogyaSanjeevaniPolicy || productType.equalsIgnoreCase('retail')) {
      final policyState = getHealthPolicyState;
      gmcUpsellBanner = policyState == HealthPolicyStates.EXPIRED;
    }
    return gmcUpsellBanner;
  }

  String get getHealthPolicyStatus {
    // todo: get status colors too
    var healthPolicyStatus = '';
    if (isPremiumPending) {
      /// on hold | policy status lapsed & substatus is null : PREMIUM_PENDING
      healthPolicyStatus = 'Policy on hold';
    } else if (isOnHold) {
      /// on hold - premium pending | status: lapsed & substatus: on hold : ON_HOLD
      healthPolicyStatus = 'Policy on hold';
    } else if (isUnderReview) {
      /// under review | substatus: uw_review_pending : UW_REVIEW
      healthPolicyStatus = 'Policy under review';
    } else if (isDeactivated) {
      /// locked | status: lapsed & substatus: expired : DEACTIVATED
      healthPolicyStatus = 'Policy on hold';
    } else if (isExpired) {
      /// date passed more checks related to deactivation : EXPIRED
      healthPolicyStatus =
          getExpirationMessage(); // 'Expired on ${formattedExpiryDateDDMMMYY}';
    } else if (isPolicyEnded) {
      /// date passed | end date is expired : EXPIRED
      healthPolicyStatus =
          getExpirationMessage(); // 'Expired on ${formattedExpiryDateDDMMMYY}';
    } else {
      /// if none of the above | active : ACTIVE
      healthPolicyStatus =
          getExpirationMessage(); // 'Till ${formattedExpiryDateDDMMMYY}';
    }
    return healthPolicyStatus;
  }

  // todo: color coding

  String getExpirationMessage() {
    if (policyEndDate?.isEmpty ?? true) {
      return '';
    }

    // Parse the end date
    final endDateTime = DateTime.parse(policyEndDate!);

    // Get the current date in UTC
    final currentDate = DateTime.now().toUtc();

    // Calculate the difference in days
    final difference = endDateTime.toUtc().difference(currentDate);
    final remainingDays = difference.inDays;

    // Format the expiration date
    final formattedExpiryDateDDMMMYY =
        DateFormat('dd MMM yy').format(endDateTime);

    // Provide the expiration message based on the remaining days
    if (remainingDays > 60) {
      return 'Till $formattedExpiryDateDDMMMYY';
    } else if (remainingDays > 1) {
      return 'Expires in $remainingDays days';
    } else if (remainingDays == 1) {
      return 'Expires tomorrow';
    } else if (remainingDays == 0) {
      return 'Expires today';
    } else {
      return 'Expired on $formattedExpiryDateDDMMMYY';
    }
  }

  String get formattedStartDateDDMMMYY {
    if (policyStartDate.isNotNullOrEmpty) {
      return DateFormat('dd MMM ‘yy').format(
        DateFormat('yyyy-MM-dd').parse(policyStartDate!, true).toLocal(),
      );
    } else {
      return '';
    }
  }

  String get getRemainingDays {
    if (policyStartDate.isNullOrEmpty) return '';
    var startDateTime = DateTime.parse(policyStartDate ?? '');
    startDateTime = DateTime(
      startDateTime.year,
      startDateTime.month,
      startDateTime.day,
      23,
      59,
      59,
    );
    final currentDate = DateTime.now().toUtc();
    final difference = startDateTime.difference(currentDate);
    final remainingDays = difference.inDays;
    final remainingDaysString = remainingDays.toString();
    return remainingDaysString;
  }

  String get getProtectionStartsMessage {
    final remainingDays = getRemainingDays;
    if (remainingDays.isEmpty) return '';

    final remainingDaysCount = int.tryParse(remainingDays) ?? 0;
    if (remainingDaysCount <= 0) {
      return issuanceType.equalsIgnoreCase('Portability')
          ? 'Your protection has already started'
          : 'Your renewed policy has already started';
    }
    final pluralSuffix = remainingDaysCount == 1 ? 'day' : 'days';
    return issuanceType.equalsIgnoreCase('Portability')
        ? 'Your protection starts in $remainingDaysCount $pluralSuffix'
        : 'Your renewed policy starts in $remainingDaysCount $pluralSuffix';
  }

  String? proposalId;
  String? proposalStatus;
  String? proposalSubStatus;
  String? policyId;
  String? policyNumber;
  String? displayPolicyNumber;
  String? masterPolicyNumber;
  String? planId;
  String? productPlanType;
  String? planName;
  String? policyStartDate;
  String? policyEndDate;
  String? stage;
  String? enrolmentStage;
  num? enrolmentRemainingDays;
  String? policyStatus;
  String? policySubstatus;
  String? assetPolicyCoverageText;
  SumInsured? sumInsured;
  SumInsured? copay;
  SumInsured? topup;
  SumInsured? deductibles;
  RemainingSumInsured? remainingSumInsured;
  KycDetails? kycDetails;
  String? tpa;
  String? tpaLogoUrl;
  String? packageId;
  String? corporate;
  String? corporateIconUrl;
  String? editMemberUrl;
  String? enrolmentUrl;
  String? endorsementUrl;
  String? renewalUrl;
  PremiumDetail? premiumDetail;
  List<Users>? users;
  List<Insureds>? insureds;
  List<PolicyCard>? policyCards;
  List<HealthPolicyDocuments>? policyDocuments;
  KeyCoversObj? keyCoversObj;
  List<HealthPolicyService>? features;
  String? productType;
  String? policyType;
  String? issuanceType;
  String? enrolmentJourneyURL;
  PrePolicyEditCard? prePolicyEditCard;
  bool? basbaPolicy;
  String? policyConstruct;
  RecommendationSection? recommendationSection;
  List<CardData>? upsellBannerData;
}

class SumInsured {
  SumInsured({this.value, this.displayValue});

  SumInsured.fromJson(Map<String, dynamic> json) {
    value = json['value'] as int?;
    displayValue = json['display_value'] as String?;
  }

  int? value;
  String? displayValue;

  Map<String, dynamic> toJson() {
    return {
      'value': value,
      'display_value': displayValue,
    };
  }
}

class AdditionalInfo {
  AdditionalInfo.fromJson(Map<String, dynamic> json) {
    baseSumInsured = json['base_sum_insured'] != null
        ? SumInsured.fromJson(json['base_sum_insured'] as Map<String, dynamic>)
        : null;
    topUpSumInsured = json['top_up_sum_insured'] != null
        ? SumInsured.fromJson(
            json['top_up_sum_insured'] as Map<String, dynamic>,
          )
        : null;
    addOnSumInsured = json['addon_sum_insured'] != null
        ? SumInsured.fromJson(json['addon_sum_insured'] as Map<String, dynamic>)
        : null;
    deductible = json['deductible'] != null
        ? SumInsured.fromJson(json['deductible'] as Map<String, dynamic>)
        : null;
  }
  SumInsured? baseSumInsured;
  SumInsured? topUpSumInsured;
  SumInsured? addOnSumInsured;
  SumInsured? deductible;
}

class RemainingSumInsured {
  RemainingSumInsured({this.value});

  RemainingSumInsured.fromJson(Map<String, dynamic> json) {
    value = json['value'] as int?;
  }

  Map<String, dynamic> toJson() {
    return {
      'value': value,
    };
  }

  int? value;
}

class KycDetails {
  KycDetails({this.status, this.redirectUrl});

  KycDetails.fromJson(Map<String, dynamic> json) {
    status = json['status'] as String?;
    redirectUrl = json['redirect_url'] as String?;
  }

  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'redirect_url': redirectUrl,
    };
  }

  String? status;
  String? redirectUrl;
}

class PremiumDetail {
  PremiumDetail({
    this.grossPremium,
    this.remainingPremium,
    this.paymentFrequency,
    this.instalmentAmount,
    this.paymentPlanId,
    this.paymentScheduleId,
    this.paymentStatus,
    this.paymentSchedulePageUrl,
  });

  PremiumDetail.fromJson(Map<String, dynamic> json) {
    remainingPremium = RemainingPremium.fromJson(json);
    paymentFrequency = json['payment_frequency'] as String?;
    instalmentAmount = json['instalment_amount'] as double?;
    paymentPlanId = json['payment_plan_id'] as String?;
    paymentScheduleId = json['payment_schedule_id'] as String?;
    paymentStatus = json['payment_status'] as String?;
    paymentSchedulePageUrl = json['payment_schedule_page_url'] as String?;
  }

  Map<String, dynamic> toJson() {
    return {
      'gross_premium': grossPremium,
      'remaining_premium':
          remainingPremium != null ? {'value': remainingPremium} : null,
      'payment_frequency': paymentFrequency,
      'instalment_amount': instalmentAmount,
      'payment_plan_id': paymentPlanId,
      'payment_schedule_id': paymentScheduleId,
      'payment_status': paymentStatus,
      'payment_schedule_page_url': paymentSchedulePageUrl,
    };
  }

  double? grossPremium;
  RemainingPremium? remainingPremium;
  String? paymentFrequency;
  double? instalmentAmount;
  String? paymentPlanId;
  String? paymentScheduleId;
  String? paymentStatus;
  String? paymentSchedulePageUrl;
}

class RemainingPremium {
  RemainingPremium.fromJson(Map<String, dynamic> json) {
    final grossAmountValue = json['gross_amount'] as num?;
    grossAmount = grossAmountValue?.toDouble();
    final netAmountValue = json['net_amount'] as num?;
    netAmount = netAmountValue?.toDouble();
    final taxValue = json['tax'] as num?;
    tax = taxValue?.toDouble();
  }

  Map<String, dynamic> toJson() {
    return {
      'gross_amount': grossAmount,
      'net_amount': netAmount,
      'tax': tax,
    };
  }

  double? grossAmount;
  double? netAmount;
  double? tax;
}

class Users {
  Users({
    this.userId,
    this.name,
    this.phone,
    this.gender,
    this.role,
    this.age,
    this.email,
    this.employeeID,
    this.pincode,
  });

  Users.fromJson(Map<String, dynamic> json) {
    userId = json['user_id'] as String?;
    name = json['name'] as String?;
    phone = json['phone'] as String?;
    gender = json['gender'] as String?;
    role = json['role'] as String?;
    email = json['email'] as String?;
    pincode = json['pin_code'] as String?;
    employeeID = json['employee_id'] as String?;
    age = json['age'] as int?;
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'name': name,
      'phone': phone,
      'gender': gender,
      'role': role,
      'email': email,
      'pincode': pincode,
      'employee_id': employeeID,
      'age': age,
    };
  }

  String? userId;
  String? name;
  String? email;
  String? employeeID;
  String? pincode;
  String? phone;
  String? gender;
  String? role;
  int? age;
}

class Insureds {
  Insureds({
    this.insuredId,
    this.insuredRef,
    this.gender,
    this.dob,
    this.name,
    this.relationship,
    this.ecardLink,
    this.relation,
    this.age,
    this.uhid,
    this.covers,
    this.categorisedCovers,
    this.riskStartDate,
    this.riskEndDate,
  });

  Insureds.fromJson(Map<String, dynamic> json) {
    insuredId = json['insured_id'] as String?;
    insuredRef = json['insured_ref'] as String?;
    gender = json['gender'] as String?;
    dob = json['dob'] as String?;
    name = json['name'] as String?;
    relationship = json['relationship'] as String?;
    ecardLink = json['ecard_link'] as String?;
    relation = json['relation'] as String?;
    age = json['age'] as int?;
    uhid = json['uhid'] as String?;
    covers = json['covers'] == null
        ? []
        : List<Covers>.from(
            (json['covers'] as List).map(
              (x) => Covers.fromJson(x as Map<String, dynamic>),
            ),
          );
    categorisedCovers = json['categorised_covers'] == null
        ? []
        : List<CategorisedCover>.from(
            (json['categorised_covers'] as List).map(
              (x) => CategorisedCover.fromJson(x as Map<String, dynamic>),
            ),
          );
    categorisedCovers
        ?.removeWhere((element) => element.covers?.isEmpty ?? false);
    riskStartDate = json['risk_start_date'] as String?;
    riskEndDate = json['risk_end_date'] as String?;
    additionalInfo = json['additional_info'] != null
        ? AdditionalInfo.fromJson(
            json['additional_info'] as Map<String, dynamic>,
          )
        : null;
  }

  Map<String, dynamic> toJson() {
    return {
      'insured_id': insuredId,
      'insured_ref': insuredRef,
      'gender': gender,
      'dob': dob,
      'name': name,
      'relationship': relationship,
      'ecard_link': ecardLink,
      'relation': relation,
      'age': age,
      'uhid': uhid,
      'covers': covers?.map((x) => x.toJson()).toList(),
      'categorised_covers': categorisedCovers?.map((x) => x.toJson()).toList(),
      'risk_start_date': riskStartDate,
      'risk_end_date': riskEndDate,
      'no_waiting_info': noWaitingInfo?.toJson(),
      'waiting_info': waitingInfo?.toJson(),
    };
  }

  String? insuredId;
  String? insuredRef;
  String? gender;
  String? dob;
  String? name;
  String? relationship;
  String? ecardLink;
  String? relation;
  int? age;
  String? uhid;
  List<Covers>? covers;
  List<CategorisedCover>? categorisedCovers;
  String? riskStartDate;
  String? riskEndDate;
  NoWaitingInfo? noWaitingInfo;
  NoWaitingInfo? waitingInfo;
  AdditionalInfo? additionalInfo;
}

class CategorisedCover {
  CategorisedCover({
    this.title,
    this.iconUrl,
    this.covers,
  });

  CategorisedCover.fromJson(Map<String, dynamic> json) {
    title = json['title'] as String?;
    iconUrl = json['icon_url'] as String?;
    covers = json['covers'] == null
        ? []
        : List<Covers>.from(
            (json['covers'] as List).map(
              (x) => Covers.fromJson(x as Map<String, dynamic>),
            ),
          );
    covers?.removeWhere((element) => element.coverId == 'topup');
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'icon_url': iconUrl,
      'covers': covers?.map((x) => x.toJson()).toList(),
    };
  }

  String? title;
  String? iconUrl;
  List<Covers>? covers;
}

class Covers {
  Covers({
    this.coverId,
    this.coverName,
    this.sellablePlanUnitId,
    this.planId,
    this.description,
    this.applicablePolicyDetails,
    this.detailsList,
    this.ctaText,
    this.ctaUrl,
  });

  Covers.fromJson(Map<String, dynamic> json) {
    coverId = json['cover_id'] as String?;
    coverName = json['cover_name'] as String?;
    sellablePlanUnitId = json['sellable_plan_unit_id'] as String?;
    planId = json['plan_id'] as String?;
    description = json['description'] as String?;
    applicablePolicyDetails = json['applicable_policy_details'] == null
        ? []
        : List<ApplicablePolicyDetails>.from(
            (json['applicable_policy_details'] as List).map(
              (x) =>
                  ApplicablePolicyDetails.fromJson(x as Map<String, dynamic>),
            ),
          );
    detailsList = json['detail_list'] != null
        ? List<String>.from(json['detail_list'] as Iterable)
        : null;
    ctaText = json['cta_text'] as String?;
    ctaUrl = json['cta_url'] as String?;
  }

  Map<String, dynamic> toJson() {
    return {
      'cover_id': coverId,
      'cover_name': coverName,
      'sellable_plan_unit_id': sellablePlanUnitId,
      'plan_id': planId,
      'description': description,
      'applicable_policy_details':
          applicablePolicyDetails?.map((x) => x.toJson()).toList(),
      'details_list': detailsList,
      'cta_text': ctaText,
      'cta_url': ctaUrl,
    };
  }

  String? coverId;
  String? coverName;
  String? sellablePlanUnitId;
  String? planId;
  String? description;
  List<ApplicablePolicyDetails>? applicablePolicyDetails;
  List<String>? detailsList;
  String? ctaText;
  String? ctaUrl;
}

class ApplicablePolicyDetails {
  ApplicablePolicyDetails({this.policyNumber, this.uhid, this.planId});

  ApplicablePolicyDetails.fromJson(Map<String, dynamic> json) {
    policyNumber = json['policy_number'] as String?;
    uhid = json['uhid'] as String?;
    planId = json['plan_id'] as String?;
  }

  Map<String, dynamic> toJson() {
    return {
      'policy_number': policyNumber,
      'uhid': uhid,
      'plan_id': planId,
    };
  }

  String? policyNumber;
  String? uhid;
  String? planId;
}

class Metadata {
  Metadata({this.userId});

  Metadata.fromJson(Map<String, dynamic> json) {
    userId = json['user_id'] as String?;
  }

  String? userId;
}

class KeyCoversObj {
  KeyCoversObj({
    this.waitingInfo,
    this.nonWaitingInfo,
    this.keyCovers,
  });

  KeyCoversObj.fromJson(Map<String, dynamic>? json) {
    if (json == null) return;
    waitingInfo = json['waiting_info'] != null
        ? WaitingInfoCover.fromJson(
            json['waiting_info'] as Map<String, dynamic>,
          )
        : null;
    nonWaitingInfo = json['no_waiting_info'] != null
        ? WaitingInfoCover.fromJson(
            json['no_waiting_info'] as Map<String, dynamic>,
          )
        : null;
    keyCovers = json['key_covers'] == null
        ? []
        : List<KeyCoversModel>.from(
            (json['key_covers'] as List).map(
              (x) => KeyCoversModel.fromJson(x as Map<String, dynamic>),
            ),
          );
  }

  Map<String, dynamic> toJson() {
    return {
      'waiting_info': waitingInfo?.toJson(),
      'no_waiting_info': nonWaitingInfo?.toJson(),
      'key_covers': keyCovers?.map((x) => x.toJson()).toList(),
    };
  }

  WaitingInfoCover? waitingInfo;
  WaitingInfoCover? nonWaitingInfo;
  List<KeyCoversModel>? keyCovers;
}

class WaitingInfoCover {
  WaitingInfoCover({
    this.backgroundColor,
    this.description,
    this.iconUrl,
  });

  WaitingInfoCover.fromJson(Map<String, dynamic> json) {
    backgroundColor = json['background_color'] as String?;
    description = json['description'] as String?;
    iconUrl = json['icon'] as String?;
  }

  Map<String, dynamic> toJson() {
    return {
      'background_color': backgroundColor,
      'description': description,
      'icon': iconUrl,
    };
  }

  String? backgroundColor;
  String? description;
  String? iconUrl;
}

class KeyCoversModel {
  KeyCoversModel({
    this.subTitle,
    this.title,
    this.iconUrl,
  });

  KeyCoversModel.fromJson(Map<String, dynamic> json) {
    subTitle = json['description'] as String?;
    title = json['title'] as String?;
    iconUrl = json['icon'] as String?;
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{
      'description': subTitle,
      'title': title,
      'icon': iconUrl,
    };
    return data;
  }

  String? subTitle;
  String? title;
  String? iconUrl;
}

class PolicyCard {
  PolicyCard({
    this.cardType,
    this.data,
  });

  factory PolicyCard.fromJson(Map<String, dynamic> json) => PolicyCard(
        cardType: json['card_type'] as String?,
        data: json['data'] == null
            ? null
            : PolicyNotificationData.fromJson(
                json['data'] as Map<String, dynamic>,
              ),
      );

  Map<String, dynamic> toJson() {
    return {
      'card_type': cardType,
      'data': data?.toJson(),
    };
  }

  final String? cardType;
  final PolicyNotificationData? data;
}

class PolicyNotificationData {
  PolicyNotificationData({
    this.ctaText,
    this.instalmentDate,
    this.subTitle,
    this.title,
    this.instalmentAmount,
    this.policyName,
    this.redirectURL,
    this.advanceCashStatus,
    this.cardColor,
  });

  factory PolicyNotificationData.fromJson(Map<String, dynamic> json) =>
      PolicyNotificationData(
        ctaText: json['cta_text'] as String?,
        instalmentDate: json['instalment_date'] as String?,
        subTitle: json['sub_title'] as String?,
        title: json['title'] as String?,
        policyName: json['plan_name'] as String?,
        instalmentAmount: json['instalment_amount'] as String?,
        redirectURL: json['redirect_url'] as String?,
        cardColor: json['card_color'] as String?,
      );

  Map<String, dynamic> toJson() {
    return {
      'cta_text': ctaText,
      'instalment_date': instalmentDate,
      'sub_title': subTitle,
      'title': title,
      'instalment_amount': instalmentAmount,
      'plan_name': policyName,
      'redirect_url': redirectURL,
      'advance_cash_status': advanceCashStatus?.toJson(),
    };
  }

  final String? policyName;
  final String? redirectURL;
  final String? ctaText;
  final String? instalmentDate;
  final String? subTitle;
  final String? title;
  final String? instalmentAmount;
  final AdvanceCashDiscoveryState? advanceCashStatus;
  final String? cardColor;
}

class PrePolicyEditCard {
  PrePolicyEditCard({this.cardType, this.data});

  factory PrePolicyEditCard.fromJson(Map<String, dynamic> json) =>
      PrePolicyEditCard(
        cardType: json['card_type'] as String?,
        data: json['data'] != null
            ? PPEData.fromJson(json['data'] as Map<String, dynamic>)
            : null,
      );

  final String? cardType;
  final PPEData? data;

  Map<String, dynamic> toJson() => {
        'card_type': cardType,
        'data': data?.toJson(),
      };
}

class PPEData {
  PPEData({this.subTitle, this.title, this.planName, this.redirectUrl});

  factory PPEData.fromJson(Map<String, dynamic> json) => PPEData(
        subTitle: json['sub_title'] as String?,
        title: json['title'] as String?,
        planName: json['plan_name'] as String?,
        redirectUrl: json['redirect_url'] as String?,
      );
  final String? subTitle;
  final String? title;
  final String? planName;
  final String? redirectUrl;

  Map<String, dynamic> toJson() => {
        'sub_title': subTitle,
        'title': title,
        'plan_name': planName,
        'redirect_url': redirectUrl,
      };
}

class RecommendationSection {
  final String? title;
  final List<RecommendationBanners>? banners;

  RecommendationSection({this.title, this.banners});

  factory RecommendationSection.fromJson(Map<String, dynamic> json) {
    return RecommendationSection(
      title: json['title'] as String?,
      banners: json['banners'] != null
          ? (json['banners'] as List)
          .map((v) => RecommendationBanners.fromJson(v as Map<String, dynamic>))
          .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() => {
    'title': title,
    'banners': banners?.map((v) => v.toJson()).toList(),
  };
}

class RecommendationBanners {
  final String? title;
  final String? subtitle;
  final String? imageUrl;
  final BannerCta? cta;

  RecommendationBanners({this.title, this.subtitle, this.imageUrl, this.cta});

  factory RecommendationBanners.fromJson(Map<String, dynamic> json) {
    return RecommendationBanners(
      title: json['title'] as String?,
      subtitle: json['subtitle'] as String?,
      imageUrl: json['image_url'] as String?,
      cta: json['cta'] != null ? BannerCta.fromJson(json['cta'] as Map<String, dynamic>) : null,
    );
  }

  Map<String, dynamic> toJson() => {
    'title': title,
    'subtitle': subtitle,
    'image_url': imageUrl,
    'cta': cta?.toJson(),
  };
}

class BannerCta {
  final String? text;
  final String? redirectUrl;

  BannerCta({this.text, this.redirectUrl});

  factory BannerCta.fromJson(Map<String, dynamic> json) {
    return BannerCta(
      text: json['text'] as String?,
      redirectUrl: json['redirect_url'] as String?,
    );
  }

  Map<String, dynamic> toJson() => {
    'text': text,
    'redirect_url': redirectUrl,
  };
}

class UpsellBannerData {
  final String? title;
  final String? subtitle;
  final String? imageUrl;
  final String? upsellCardType;
  final String? action;
  final String? redirectUrl;

  UpsellBannerData({
    this.title,
    this.subtitle,
    this.imageUrl,
    this.upsellCardType,
    this.action,
    this.redirectUrl,
  });

  factory UpsellBannerData.fromJson(Map<String, dynamic> json) {
    return UpsellBannerData(
      title: json['title'] as String?,
      subtitle: json['subtitle'] as String?,
      imageUrl: json['image_url'] as String?,
      upsellCardType: json['upsell_card_type'] as String?,
      action: json['action'] as String?,
      redirectUrl: json['redirect_url'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'subtitle': subtitle,
      'image_url': imageUrl,
      'upsell_card_type': upsellCardType,
      'action': action,
      'redirect_url': redirectUrl,
    };
  }
}

extension HealthPolicyDetailsModelX on HealthPolicyDetailsModel {
  bool isCorporateExcluded(List<String> excludedCorporates) {
    return excludedCorporates.any(
      (excludedCorporate) => excludedCorporate.equalsIgnoreCase(corporate),
    );
  }
}