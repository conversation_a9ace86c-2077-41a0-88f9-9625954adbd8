import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/common/view/AckoText.dart';
import 'package:acko_flutter/common/view/toast.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/health_life/tap_events/health_life_tap_events.dart';
import 'package:flutter/material.dart';
import 'package:policy_details/policy_details.dart';
import 'package:sdui/sdui.dart';

import 'package:utilities/constants/constants.dart';

class RecommendedForYouSection extends StatelessWidget {
  final RecommendationSection? recommendationSection;

  const RecommendedForYouSection({
    Key? key,
    this.recommendationSection
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (recommendationSection?.banners.isNotNullOrEmpty ?? false) {
      return SDUIListView(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        childWidgets: [
          SDUIText(
            value: recommendationSection?.title,
            textStyle: "hSmall",
            padding: EdgeInsets.only(bottom: 24),
          ),
          ...recommendationSection!.banners!
              .map((element) => _buildCrossSellCard(element, context))
              .toList(),
        ],
      );
    } else {
      return SizedBox.shrink();
    }
  }

  Widget _buildCrossSellCard(
      RecommendationBanners banner, BuildContext context) {

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(boxShadow: [
        BoxShadow(
          color: color323247.withOpacity(0.05), // #3232470D
          offset: Offset(0, 3),
          blurRadius: 8,
          spreadRadius: -1,
        ),
        BoxShadow(
          color: color0C1A4B.withOpacity(0.24), // #0C1A4B3D
          offset: Offset(0, 0),
          blurRadius: 1,
          spreadRadius: 0,
        ),
      ], color: colorFFFFFF, borderRadius: BorderRadius.circular(16)),
      clipBehavior: Clip.hardEdge,
      child: Padding(
        padding: EdgeInsets.all(12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if(banner.title.isNotNullOrEmpty)
                  TextEuclidSemiBold(
                    banner.title!,
                    textColor: color451999,
                    textAlign: TextAlign.start,
                    maxLines: 10,
                  ),
                  if(banner.subtitle.isNotNullOrEmpty)
                  ...[
                    const SizedBox(height: 8,),
                    TextEuclidMediumL2(
                      banner.subtitle!,
                      textColor: color4B4B4B,
                      textAlign: TextAlign.start,
                      maxLines: 10,
                    ),
                  ],
                  if(banner.cta?.text.isNotNullOrEmpty ?? false)
                  InkWell(
                    onTap: (){
                      /// existing recommendation banner click event
                      AnalyticsTrackerManager.instance.sendEvent(
                        event: HLTrackEvents.TAP_UPSELL_HEALTH_INSURANCE,
                        properties: {
                          "page": "Health PDP",
                          "platform": Util.getPlatform(),
                          "journey": "reimbursement",
                          "product": "health",
                          "funnel": "Entry",
                          "banner": {
                            "name": "Platinum health plan",
                            "type": "Upsell",
                          },
                          "from_page": "Health PDP",
                        },
                      );
                      if(banner.cta!.redirectUrl.isNotNullOrEmpty) {
                        Navigator.pushNamed(context, Routes.WEB_PAGE, arguments: {
                          "url": banner.cta!.redirectUrl,
                        });
                      } else {
                        AckoToast.show(redir_url_not_found);
                      }
                    },
                    child: SDUIText(
                        margin: const EdgeInsets.only(top: 12),
                        value: banner.cta!.text,
                        textConfiguration: TextConfiguration.colored,
                        background: WidgetBackground(color: Colors.transparent),
                        textColor: color040222,
                        textStyle: "lSmall",
                        border: WidgetBorder(
                            cornerRadius: 8, color: color040222, thickness: 1),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 8)),
                  )
                ],
              ),
            ),
            const SizedBox(
              width: 28,
            ),
            if(banner.imageUrl.isNotNullOrEmpty)
            SDUIImage(
              imageUrl: banner.imageUrl,
              width: 80,
              height: 80,
            ),
          ],
        ),
      ),
    );
  }
}
