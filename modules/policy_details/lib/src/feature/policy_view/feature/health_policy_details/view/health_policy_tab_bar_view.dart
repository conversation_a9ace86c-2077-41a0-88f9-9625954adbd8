import 'package:acko_flutter/framework/pdp/health/view/widgets/health_coverages_tab.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/pdp_covered_members_details_section.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/pdp_policy_details_section.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/pdp_policy_holder_details_section.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/pdp_tabview.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/policy_view/policy_view_shimmer/pdp_content_view_shimmer.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:policy_details/src/feature/policy_view/feature/health_policy_details/cubit/health_policy_details_cubit.dart';
import 'package:policy_details/src/feature/policy_view/feature/health_policy_details/view/recommendation_banner_section.dart';

class HealthPolicyTabBarView extends StatefulWidget {
  const HealthPolicyTabBarView({
    required this.healthPDPPolicyPageCubit,
    super.key,
  });

  final HealthPolicyDetailsCubit healthPDPPolicyPageCubit;

  @override
  State<HealthPolicyTabBarView> createState() => _HealthPolicyTabBarViewState();
}

class _HealthPolicyTabBarViewState extends State<HealthPolicyTabBarView> {
  int tabIndex = 0;

  @override
  void initState() {
    tabIndex = widget.healthPDPPolicyPageCubit.defaultValue;
    super.initState();
  }

  void _onTabSelected(int index) {
    setState(() {
      tabIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HealthPolicyDetailsCubit, HealthPolicyDetailsState>(
      buildWhen: (prev, curr) =>
          curr is HealthPolicyDetailsTabBarLoaded ||
          curr is HealthPolicyDetailsLoading ||
          curr is HealthPolicyDetailsError,
      builder: (context, state) {
        if (state is HealthPolicyDetailsTabBarLoaded) {
          final tabBarIndex = state.tabBarDefaultValue ?? 0; //1
          final canShowBothTabs = state.coveragesTabUIModel.isNotNullOrEmpty &&
              (state.policyDetailsModel != null);
          return Column(
            children: canShowBothTabs
                ? policyDetailsWithTabs(tabBarIndex, state)
                : [
                    if (state.policyDetailsModel != null)
                      ..._policyOverViewWidgets(state)
                    else if (state.coveragesTabUIModel.isNotNullOrEmpty)
                      _coveragesTabContent(state),
                  ],
          );
        } else if (state is HealthPolicyDetailsLoading) {
          return const PDPContentViewShimmer();
        } else {
          return const SizedBox.shrink();
        }
      },
    );
  }

  List<Widget> policyDetailsWithTabs(
    int tabBarIndex,
    HealthPolicyDetailsTabBarLoaded state,
  ) {
    return [
      PDPTabView(
        tabOneName: tabBarIndex == 0 ? 'Coverages' : 'Overview',
        tabTwoName: tabBarIndex == 0 ? 'Overview' : 'Coverages',
        onTabSelected: _onTabSelected,
        tabValue: tabBarIndex,
      ),
      if (tabIndex == 0)
        _coveragesTabContent(state)
      else
        ..._policyOverViewWidgets(state),
    ];
  }

  CoveragesTabContent _coveragesTabContent(
    HealthPolicyDetailsTabBarLoaded state,
  ) {
    return CoveragesTabContent(
      coveragesTabUIModel: state.coveragesTabUIModel ?? [],
      person: state.person,
      tapEvent: (tapCta, coveragesData, relationship) =>
          widget.healthPDPPolicyPageCubit.handlePDPViewCoveragesTapEvent(
        tapCta,
        relationship,
        coveragesData,
      ),
    );
  }

  List<Widget> _policyOverViewWidgets(HealthPolicyDetailsTabBarLoaded state) {
    return [
      PolicyDetails(policyDetails: state.policyDetailsModel),
      const SizedBox(height: 32),
      CoveredMembersDetails(
        coveredMembers: state.coverPersonData,
      ),
      PolicyHolderDetails(policyHolderData: state.policyHolderUserDetails),
      RecommendedForYouSection(recommendationSection: widget.healthPDPPolicyPageCubit.healthPolicyDetailsModel?.recommendationSection)
    ];
  }
}
