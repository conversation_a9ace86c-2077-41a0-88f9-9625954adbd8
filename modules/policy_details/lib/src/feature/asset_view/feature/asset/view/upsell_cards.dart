import 'package:acko_flutter/feature/vas/gmc_upsell/model/gmc_upsell_dto.dart';
import 'package:acko_flutter/framework/pdp/health/models/enums.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/asset_view/asset_view_shimmer/gmc_upsell_shimmer.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/buy_banner.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/health_life/tap_events/health_life_tap_events.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:policy_details/policy_details.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/domain/models/hl_cards_data.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/view/hl_pwilo_banners.dart';

class UpsellCards extends StatelessWidget {
  const UpsellCards({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HealthAssetDetailsCubit, HealthAssetDetailsState>(
      buildWhen: (prev, curr) =>
          curr is HealthAssetDetailsLoaded || curr is HealthAssetDetailsLoading,
      builder: (context, state) {
        if (state is HealthAssetDetailsLoaded) {
          final bloc = context.read<HealthAssetDetailsCubit>();
          final upsellModel = state.upsellCardsModel?.healthGmcUpsellCardModel;
          final showLifeUpsellBanner = upsellModel != null &&
              bloc.policyTypes.length == 1 &&
              bloc.policyTypes.first == PolicyTypeEnum.life;
          final showGMCUpsellBanner = upsellModel != null &&
              upsellModel.showUpsellCard &&
              upsellModel.sumInsured != null;
          final showPWILOCard =
              (state.upsellCardsModel?.pwiLoCardsData != null ||
                      state.upsellCardsModel?.lifePwiLoCardsData != null) &&
                  bloc.filter['relationship'] == 'Self';

          final healthPWILOCards =
              state.upsellCardsModel?.pwiLoCardsData?.data?.cards;
          final lifePWILOCards =
              state.upsellCardsModel?.lifePwiLoCardsData?.data?.cards;

          final pwiloCards = <Cards>[];

          if (healthPWILOCards.isNotNullOrEmpty) {
            pwiloCards.addAll(healthPWILOCards!);
          }

          if (lifePWILOCards.isNotNullOrEmpty) {
            pwiloCards.addAll(lifePWILOCards!);
          }

          if (showLifeUpsellBanner) {
            final lifeUpsellBannerData = state.upsellCardsModel
                ?.healthGmcUpsellCardModel?.lifeUpsellBannerData;
            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: BuyBanner(
                title: lifeUpsellBannerData?.title,
                subtitle: lifeUpsellBannerData?.subtitle,
                imageUrl: lifeUpsellBannerData?.imageUrl,
                buyType: BuyType.life_cross_sell,
                analyticalEvent: () {
                  bloc.handleAssetPageTapEvent(
                    'fresh_retail_health_upsell_card',
                  );
                },
                fromPage: 'l1_asset_view_page',
              ),
            );
          }
          if (showGMCUpsellBanner) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: BuyBanner(
                title: upsellModel.gmcUpsellBannerData?.title,
                subtitle: upsellModel.gmcUpsellBannerData?.subtitle,
                imageUrl: upsellModel.gmcUpsellBannerData?.imageUrl,
                sumInsured: upsellModel.sumInsured,
                analyticalEvent: () {
                  bloc.handleAssetPageTapEvent('gmc_upsell_card');
                },
                fromPage: 'l1_asset_view_page',
              ),
            );
          }
          if (showPWILOCard && pwiloCards.isNotNullOrEmpty) {
            return SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.only(bottom: 16, left: 12, right: 12, top: 0),
                child: Row(
                  children: List.generate(pwiloCards.length, (index) {
                    if (pwiloCards[index] == null) {
                      return const SizedBox.shrink();
                    }
                    return HLPWILOBanners(
                      pwiLoCardsData: pwiloCards[index],
                      isLast: index == pwiloCards.length - 1,
                      totalCards: pwiloCards.length,
                      analyticalEvent: () {
                        AnalyticsTrackerManager.instance.sendEvent(
                            event: HLTrackEvents.TAP_L1_ASSET_VIEW_PAGE,
                            properties: {
                              "journey": "${pwiloCards[index].lob}_retail_pwilo",
                              "product": pwiloCards[index].lob,
                              "platform": Util.getPlatform(),
                              "visit_entry_tag": "entry",
                              "from_page": "asset_view"
                            });
                      },
                    );
                  }),
                ));
          } else {
            return const SizedBox.shrink();
          }
        } else if (state is HealthAssetDetailsLoading) {
          return const GMCUpsellBannerShimmer();
        } else {
          return const SizedBox.shrink();
        }
      },
    );
  }
}
