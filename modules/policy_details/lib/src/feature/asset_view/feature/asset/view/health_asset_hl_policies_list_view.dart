// ignore_for_file: cascade_invocations, lines_longer_than_80_chars

import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/view/router.dart';
import 'package:acko_flutter/framework/pdp/health/common/tap_handler.dart';
import 'package:acko_flutter/framework/pdp/health/models/health_policies_basic_details.dart';
import 'package:acko_flutter/framework/pdp/health/models/ui_models/policy_card_model.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/pdp_covered_policy_item.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/pdp_full_width_cta.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/pdp_raise_claim_sheet.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_flutter/util/health/health_constants.dart';
import 'package:design_module/design_module.dart';
import 'package:design_module/uikit/widgets/button/acko_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:policy_details/policy_details.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/constants/constants.dart';

class HealthAssetPoliciesListView extends StatelessWidget {
  const HealthAssetPoliciesListView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HealthAssetDetailsCubit, HealthAssetDetailsState>(
      buildWhen: (prev, curr) =>
          curr is HealthAssetDetailsLoading || curr is HealthAssetDetailsLoaded,
      builder: (context, state) {
        // Access the Cubit using context.read<HealthAssetDetailsCubit>()
        final bloc = context.read<HealthAssetDetailsCubit>();
        final policyMap = bloc.policyMap;
        if (state is HealthAssetDetailsLoaded) {
          final selectedAsset = state.selectedHealthAsset;
          final person = HealthPolicyHolder(
            name: selectedAsset['name'] as String?,
            gender: selectedAsset['gender'] as String?,
            relationship: selectedAsset['relationship'] as String?,
          );
          final coveragesPolicyCards =
              state.insured?.additionalInfo?.policyCards;
          return Column(
            children: [
              if (coveragesPolicyCards.isNotNullOrEmpty && policyMap != null)
                Container(
                  margin: const EdgeInsets.fromLTRB(16, 0, 16, 12),
                  // padding: EdgeInsets.all(12),
                  decoration: const BoxDecoration(
                    color: colorFFFFFF,
                    borderRadius: BorderRadius.all(Radius.circular(16)),
                  ),
                  child: ClipPath(
                    clipper: BottomClippingBorderRadiusClipper(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SDUIText(
                          value: 'YOUR HEALTH COVERAGES',
                          padding: const EdgeInsets.only(top: 12, left: 12),
                          maxLines: 3,
                          alignment: TextAlign.start,
                          textColor: const Color(0xFF4B4B4B),
                          textStyle: 'pXSmall',
                        ),
                        ListView.separated(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 16,
                          ),
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemBuilder: (context, index) {
                            AssetPolicyCard? coverageCard;
                            coverageCard = coveragesPolicyCards?[index];
                            final healthPolicyDetailsModel = policyMap[
                                coverageCard?.policyId ??
                                    coverageCard?.policyNumber];
                            final displayValue = coverageCard?.sumInsured ?? '';
                            return CoveredPolicyItem(
                              title: coverageCard?.title ?? '',
                              subtitle:
                                  'with ${healthPolicyDetailsModel?.planName ?? ''}',
                              status: healthPolicyDetailsModel
                                      ?.getHealthPolicyStatus ??
                                  '',
                              statusColor: (healthPolicyDetailsModel
                                          ?.getHealthPolicyState !=
                                      HealthPolicyStates.ACTIVE)
                                  ? const Color(0xFFD83D37)
                                  : const Color(0xFF0B753E),
                              statusColorBg: (healthPolicyDetailsModel
                                          ?.getHealthPolicyState !=
                                      HealthPolicyStates.ACTIVE)
                                  ? const Color(0xFFFBEAEA)
                                  : const Color(0xFFEBFBEE),
                              onTap: () {
                                bloc.handleAssetPageTapEvent('plan_card');

                                if (bloc.navigateToKeyCovers(
                                  coverageCard,
                                  healthPolicyDetailsModel,
                                )) {
                                  navigateToCoveragesScreen(
                                    healthPolicyDetailsModel,
                                    person,
                                    healthPolicyDetailsModel?.productType ?? '',
                                    '',
                                    state.insured?.roomRentValue,
                                    context,
                                    displayValue,
                                    hasNoWaitingPeriodCover: state
                                            .insured?.hasNoWaitingPeriodCover ??
                                        false,
                                  );
                                } else {
                                  navigateToPolicyDetailsPage(
                                    coverageCard?.policyId,
                                    coverageCard?.policyNumber,
                                    person,
                                    context,
                                  );
                                }
                              },
                            );
                          },
                          separatorBuilder: (context, index) {
                            return const Padding(
                              padding: EdgeInsets.symmetric(vertical: 10),
                              child: Divider(color: colorE8E8E8),
                            );
                          },
                          itemCount: coveragesPolicyCards?.length ?? 0,
                        ),
                        Container(
                          color: colorF5F5F5,
                          padding: const EdgeInsets.all(12),
                          child: HealthFullWidthCTA(
                            text: 'Raise a claim',
                            onTap: () async {

                              final isAcrEnabled = bloc.advanceCashDetailsData?.isEligibleForAdvanceCash;
                              final hasAcrData = bloc.advanceCashDetailsData?.advanceCashData != null;

                              bloc..handleAssetPageTapEvent('raise_claim');

                              var (uhids, name) = bloc.selectedInsured!.getUhidsAndName();
                              final (raiseClaimUrl, viewClaimUrl, isEducationEnabled) = await bloc.getClaimUrlsAndFlag();

                              if(isEducationEnabled) {
                                Navigator.pushNamed(context, Routes.WEB_PAGE_V2,
                                    arguments: {'url': "$raiseClaimUrl&isAcrEnabled=$isAcrEnabled&hasAcrData=$hasAcrData&uhids=$uhids&name=$name",});
                                return;
                              } else {
                                HealthTapHandler.gridOnTapHandler(
                                  'register_reimbursement_claim',
                                  null,
                                  context,
                                );
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

              // if (healthPolicyList.isEmpty)
              //   // (state.assetData?.selectedAsset?.allProposals.isNullOrEmpty ??
              //   //     false)
              //   _getNoHealthCoversWidget(),
            ],
          );
        } else {
          return const SizedBox.shrink();
        }
      },
    );
  }

  void navigateToCoveragesScreen(
    HealthPolicyDetailsModel? element,
    HealthPolicyHolder? person,
    String productInfo,
    String? upcomingPolicyStartDate,
    String? roomRentValue,
    BuildContext context,
    String? sumInsuredDisplayValue, {
    required bool hasNoWaitingPeriodCover,
  }) {
    if (element == null) return;
    String? policyId;
    String? policyNumber;
    String? proposalId;
    String? policyName;
    String? policyStatus;
    String? policyStartDate;
    String? sumInsured;
    String? deductibles;
    String? copay;
    String? roomRent;
    String? paymentUrl;

    /// fetch the policy id of member or base policy
    policyId = element.policyId;
    policyNumber = element.policyNumber;
    proposalId = element.proposalId;
    policyName = element.planName;
    policyStartDate = upcomingPolicyStartDate;
    policyStatus = element.policyEndDate ??
        ''; // Util.fullFormatDate(element.endDate ?? '');

    sumInsured = sumInsuredDisplayValue;
    deductibles = element.deductibles?.displayValue;
    roomRent = roomRentValue;
    copay = element.copay?.displayValue;

    final isGMC = element.productType
        .containsIgnoreCase(HealthConstants.ENTERPRISE_PRODUCT);

    final resultMap = <String, String>{
      'Sum insured': sumInsured ?? '',
      if (deductibles.isNotNullOrEmpty && deductibles != '0')
        'Deductible': deductibles ?? '0L',
      if (roomRent.isNotNullOrEmpty && roomRent != '0')
        'Room Rent': roomRent ?? '',
      if (copay.isNotNullOrEmpty && copay != '0') 'Copay': '$copay%',
    };

    resultMap.removeWhere((key, value) => value.isNullOrEmpty);

    paymentUrl = element.premiumDetail?.paymentSchedulePageUrl;

    final keyCoversList = <CardCoverModel>[];

    WaitingInfoModel? waitingInfo;
    WaitingInfoModel? noWaitingInfo;

    final keyCoversObj = element.keyCoversObj;
    if (keyCoversObj != null &&
        keyCoversObj.keyCovers != null &&
        keyCoversObj.keyCovers!.isNotNullOrEmpty) {
      for (final cover in keyCoversObj.keyCovers!) {
        var subtitle = cover.subTitle;
        subtitle = subtitle!.replaceAll(r'\n', '\n');
        keyCoversList.add(
          CardCoverModel(
            coverTitle: cover.title,
            coverDesc: subtitle,
            coverImg: cover.iconUrl,
          ),
        );
      }

      if (!element.isExpiredPolicy) {
        if (keyCoversObj.waitingInfo != null) {
          waitingInfo = WaitingInfoModel(
            imageUrl: keyCoversObj.waitingInfo?.iconUrl,
            description: keyCoversObj.waitingInfo?.description,
            bgColor: keyCoversObj.waitingInfo?.backgroundColor,
          );
        } else if (keyCoversObj.nonWaitingInfo != null) {
          noWaitingInfo = WaitingInfoModel(
            imageUrl: keyCoversObj.nonWaitingInfo?.iconUrl,
            description: keyCoversObj.nonWaitingInfo?.description,
            bgColor: keyCoversObj.nonWaitingInfo?.backgroundColor,
          );
        }
      }
    }

    var upcomingStatus = '';

    if (element.isUpcoming) {
      upcomingStatus =
          'Upcoming new policy will be starting from ${element.formattedStartDateDDMMMYY}.';
    }

    Navigator.pushNamed(
      context,
      Routes.HEALTH_PDP_COVERAGES_SCREEN,
      arguments: {
        'route_animation_type': RouteAnimationType.fade_anim,
        'policyId': policyId,
        'proposalId': proposalId,
        'policyStartDate': policyStartDate,
        'policyName': policyName,
        'policyStatus': policyStatus,
        'policyKeyCovers': keyCoversList,
        'isExpired': element.isExpiredPolicy,
        'waitingInfo': waitingInfo,
        'noWaitingInfo': noWaitingInfo,
        'policyInfoMap': resultMap,
        'upcomingStatus': upcomingStatus,
        'selectedPolicyHolder': person,
        'productInfo': productInfo,
        'policy_number': policyNumber,
      },
    );
  }

  void navigateToPolicyDetailsPage(
    String? policyId,
    String? policyNumber,
    HealthPolicyHolder? person,
    BuildContext context,
  ) {
    final arguments = <String, dynamic>{
      'defaultTab': '0',
      'selectedPolicyHolder': person
    };
    if (policyId != null) {
      arguments['policy_id'] = policyId;
    } else if (policyNumber != null) {
      arguments['policy_number'] = policyNumber;
    }
    Navigator.pushNamed(context, Routes.HEALTH_HOME_PAGE, arguments: arguments);
  }
}

class BottomClippingBorderRadiusClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    const radius = 16.0; // Radius of the container's border radius
    path.moveTo(0, 0);
    path.lineTo(0, size.height - radius);
    path.arcToPoint(
      Offset(radius, size.height),
      radius: const Radius.circular(radius),
      clockwise: false,
    );
    path.lineTo(size.width - radius, size.height);
    path.arcToPoint(
      Offset(size.width, size.height - radius),
      radius: const Radius.circular(radius),
      clockwise: false,
    );
    path.lineTo(size.width, 0);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(covariant CustomClipper<Path> oldClipper) => false;
}
