import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/view/router.dart';
import 'package:acko_flutter/feature/p2I/common/Utils/p2i_constants.dart'
    as HealthPolicyStates;
import 'package:acko_flutter/framework/pdp/health/common/tap_handler.dart';
import 'package:acko_flutter/framework/pdp/health/models/health_policies_basic_details.dart';
import 'package:acko_flutter/framework/pdp/health/models/ui_models/policy_card_model.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/pdp_covered_policy_item.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_flutter/util/health/health_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:policy_details/policy_details.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/domain/models/hl_asset_constants.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/view/health_asset_hl_policies_list_view.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/utilities.dart';

class HealthPoliciesListView extends StatelessWidget {
  const HealthPoliciesListView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HealthAssetDetailsCubit, HealthAssetDetailsState>(
      buildWhen: (prev, curr) =>
          curr is HealthAssetDetailsLoading || curr is HealthAssetDetailsLoaded,
      builder: (context, state) {
        // Access the Cubit using context.read<HealthAssetDetailsCubit>()
        final bloc = context.read<HealthAssetDetailsCubit>();
        final policyMap = bloc.policyMap;
        if (state is HealthAssetDetailsLoaded) {
          final selectedAsset = state.selectedHealthAsset;
          final person = HealthPolicyHolder(
            name: selectedAsset[HealthAssetConstants.name] as String?,
            gender: selectedAsset[HealthAssetConstants.gender] as String?,
            relationship:
                selectedAsset[HealthAssetConstants.relationship] as String?,
          );
          final coveragesPolicyCards =
              state.insured?.additionalInfo?.policyCards;
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (coveragesPolicyCards.isNotNullOrEmpty && policyMap != null)
                Padding(
                  padding: const EdgeInsets.only(left: 16),
                  child: SDUIText(
                    value: getCoverageHeading(
                      coveragesPolicyCards: coveragesPolicyCards,
                      policyMap: policyMap,
                      lifePolicyHeaders: bloc.lifePolicyHeaders,
                    ),
                    textColor: color121212,
                    textStyle: 'hSmall',
                  ),
                ),
              getHealthCoversWidget(
                coveragesPolicyCards,
                policyMap,
                bloc,
                context,
                person,
                state,
              ),
            ],
          );
        } else {
          return const SizedBox.shrink();
        }
      },
    );
  }

  Widget getHealthCoversWidget(
    List<AssetPolicyCard>? coveragesPolicyCards,
    Map<String, HealthPolicyDetailsModel>? policyMap,
    HealthAssetDetailsCubit bloc,
    BuildContext context,
    HealthPolicyHolder? person,
    HealthAssetDetailsLoaded state,
  ) {
    if (coveragesPolicyCards.isNotNullOrEmpty && policyMap != null) {
      return Padding(
        padding: const EdgeInsets.all(20),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              colors: [
                color5920C5,
                color5920C5.withOpacity(0.6),
                colorDFDFDF.withOpacity(0.55),
                colorDFDFDF.withOpacity(0.55),
                colorDFDFDF.withOpacity(0.55),
                colorDFDFDF.withOpacity(0.55),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomCenter,
            ),
          ),
          padding: const EdgeInsets.all(2), // thickness of border
          child: Container(
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [
                  colorFCFBFF,
                  colorEFE9FB,
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              borderRadius:
                  BorderRadius.circular(18), // Slightly smaller radius
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ListView.separated(
                  padding: const EdgeInsets.symmetric(
                      horizontal: 12, vertical: 16),
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    final coverageCard = coveragesPolicyCards?[index];
                    final healthPolicyDetailsModel = policyMap![
                        coverageCard?.policyId ?? coverageCard?.policyNumber];
                    final displayValue = coverageCard?.sumInsured ?? '';

                    return CoveredPolicyItem(
                      title: coverageCard?.title ?? '',
                      subtitle:
                          'with ${healthPolicyDetailsModel?.planName ?? ''}',
                      status:
                          healthPolicyDetailsModel?.getHealthPolicyStatus ??
                              '',
                      statusColor:
                          healthPolicyDetailsModel?.getHealthPolicyState !=
                                  HealthPolicyStates.ACTIVE
                              ? colorD83D37
                              : color0B753E,
                      statusColorBg:
                          healthPolicyDetailsModel?.getHealthPolicyState !=
                                  HealthPolicyStates.ACTIVE
                              ? colorFBEAEA
                              : colorEBFBEE,
                      onTap: () {
                        bloc.handleAssetPageTapEvent(
                            HealthAssetConstants.planCard);

                        if (bloc.navigateToKeyCovers(
                            coverageCard, healthPolicyDetailsModel)) {
                          navigateToCoveragesScreen(
                            healthPolicyDetailsModel,
                            person,
                            healthPolicyDetailsModel?.productType ?? '',
                            '',
                            state.insured?.roomRentValue,
                            context,
                            displayValue,
                            hasNoWaitingPeriodCover:
                                state.insured?.hasNoWaitingPeriodCover ??
                                    false,
                          );
                        } else {
                          navigateToPolicyDetailsPage(
                            coverageCard?.policyId,
                            coverageCard?.policyNumber,
                            person,
                            context,
                          );
                        }
                      },
                    );
                  },
                  separatorBuilder: (context, index) => const Padding(
                    padding: EdgeInsets.symmetric(vertical: 10),
                    child: Divider(color: colorE8E8E8),
                  ),
                  itemCount: coveragesPolicyCards?.length ?? 0,
                ),
                Padding(
                  padding: const EdgeInsets.all(12),
                  child: SizedBox(
                    width: double.infinity,
                    child: OutlinedButton(
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: color121212),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                            12,
                          ),
                        ),
                        padding: const EdgeInsets.symmetric(
                          vertical: 12,
                        ),
                      ),
                      onPressed: () async {
                        final isAcrEnabled = bloc
                            .advanceCashDetailsData?.isEligibleForAdvanceCash;
                        final hasAcrData =
                            bloc.advanceCashDetailsData?.advanceCashData !=
                                null;

                        bloc.handleAssetPageTapEvent(
                            HealthAssetConstants.raiseClaim);

                        var (uhids, name) =
                            bloc.selectedInsured!.getUhidsAndName();
                        final (
                          raiseClaimUrl,
                          viewClaimUrl,
                          isEducationEnabled
                        ) = await bloc.getClaimUrlsAndFlag();

                        if (isEducationEnabled) {
                          Navigator.pushNamed(context, Routes.WEB_PAGE_V2,
                              arguments: {
                                'url':
                                    "$raiseClaimUrl&isAcrEnabled=$isAcrEnabled&hasAcrData=$hasAcrData&uhids=$uhids&name=$name",
                              });
                          return;
                        } else {
                          HealthTapHandler.gridOnTapHandler(
                            HealthAssetConstants.registerReimbursementClaim,
                            null,
                            context,
                          );
                        }
                      },
                      child: SDUIText(
                        value: HealthAssetConstants.raiseAClaim,
                        textStyle: 'lSmall',
                        textColor: color040222,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }
    return const SizedBox.shrink();
  }

  void navigateToCoveragesScreen(
    HealthPolicyDetailsModel? element,
    HealthPolicyHolder? person,
    String productInfo,
    String? upcomingPolicyStartDate,
    String? roomRentValue,
    BuildContext context,
    String? sumInsuredDisplayValue, {
    required bool hasNoWaitingPeriodCover,
  }) {
    if (element == null) return;
    String? policyId;
    String? policyNumber;
    String? proposalId;
    String? policyName;
    String? policyStatus;
    String? policyStartDate;
    String? sumInsured;
    String? deductibles;
    String? copay;
    String? roomRent;
    String? paymentUrl;

    /// fetch the policy id of member or base policy
    policyId = element.policyId;
    policyNumber = element.policyNumber;
    proposalId = element.proposalId;
    policyName = element.planName;
    policyStartDate = upcomingPolicyStartDate;
    policyStatus = element.policyEndDate ??
        ''; // Util.fullFormatDate(element.endDate ?? '');

    sumInsured = sumInsuredDisplayValue;
    deductibles = element.deductibles?.displayValue;
    roomRent = roomRentValue;
    copay = element.copay?.displayValue;

    final isGMC = element.productType
        .containsIgnoreCase(HealthConstants.ENTERPRISE_PRODUCT);

    final resultMap = <String, String>{
      HealthAssetConstants.sumInsured: sumInsured ?? '',
      if (deductibles.isNotNullOrEmpty && deductibles != '0')
        HealthAssetConstants.deductible: deductibles ?? '0L',
      if (roomRent.isNotNullOrEmpty && roomRent != '0')
        HealthAssetConstants.roomRent: roomRent ?? '',
      if (copay.isNotNullOrEmpty && copay != '0')
        HealthAssetConstants.copay: '$copay%',
    };

    resultMap.removeWhere((key, value) => value.isNullOrEmpty);

    paymentUrl = element.premiumDetail?.paymentSchedulePageUrl;

    final keyCoversList = <CardCoverModel>[];

    WaitingInfoModel? waitingInfo;
    WaitingInfoModel? noWaitingInfo;

    final keyCoversObj = element.keyCoversObj;
    if (keyCoversObj != null &&
        keyCoversObj.keyCovers != null &&
        keyCoversObj.keyCovers!.isNotNullOrEmpty) {
      for (final cover in keyCoversObj.keyCovers!) {
        var subtitle = cover.subTitle;
        subtitle = subtitle!.replaceAll(r'\n', '\n');
        keyCoversList.add(
          CardCoverModel(
            coverTitle: cover.title,
            coverDesc: subtitle,
            coverImg: cover.iconUrl,
          ),
        );
      }

      if (!element.isExpiredPolicy) {
        if (keyCoversObj.waitingInfo != null) {
          waitingInfo = WaitingInfoModel(
            imageUrl: keyCoversObj.waitingInfo?.iconUrl,
            description: keyCoversObj.waitingInfo?.description,
            bgColor: keyCoversObj.waitingInfo?.backgroundColor,
          );
        } else if (keyCoversObj.nonWaitingInfo != null) {
          noWaitingInfo = WaitingInfoModel(
            imageUrl: keyCoversObj.nonWaitingInfo?.iconUrl,
            description: keyCoversObj.nonWaitingInfo?.description,
            bgColor: keyCoversObj.nonWaitingInfo?.backgroundColor,
          );
        }
      }
    }

    var upcomingStatus = '';

    if (element.isUpcoming) {
      upcomingStatus =
          '${HealthAssetConstants.upcomingPolicyText}${element.formattedStartDateDDMMMYY}.';
    }

    Navigator.pushNamed(
      context,
      Routes.HEALTH_PDP_COVERAGES_SCREEN,
      arguments: {
        HealthAssetConstants.routeAnimationType: RouteAnimationType.fade_anim,
        HealthAssetConstants.policyId: policyId,
        HealthAssetConstants.proposalId: proposalId,
        HealthAssetConstants.policyStartDate: policyStartDate,
        HealthAssetConstants.policyName: policyName,
        HealthAssetConstants.policyStatus: policyStatus,
        HealthAssetConstants.policyKeyCovers: keyCoversList,
        HealthAssetConstants.isExpired: element.isExpiredPolicy,
        HealthAssetConstants.waitingInfo: waitingInfo,
        HealthAssetConstants.noWaitingInfo: noWaitingInfo,
        HealthAssetConstants.policyInfoMap: resultMap,
        HealthAssetConstants.upcomingStatus: upcomingStatus,
        HealthAssetConstants.selectedPolicyHolder: person,
        HealthAssetConstants.productInfo: productInfo,
        HealthAssetConstants.policyNumber: policyNumber,
      },
    );
  }

  void navigateToPolicyDetailsPage(
    String? policyId,
    String? policyNumber,
    HealthPolicyHolder? person,
    BuildContext context,
  ) {
    final arguments = <String, dynamic>{
      HealthAssetConstants.defaultTab: '0',
      HealthAssetConstants.selectedPolicyHolder: person
    };
    if (policyId != null) {
      arguments[HealthAssetConstants.policyId] = policyId;
    } else if (policyNumber != null) {
      arguments[HealthAssetConstants.policyNumber] = policyNumber;
    }
    Navigator.pushNamed(context, Routes.HEALTH_HOME_PAGE, arguments: arguments);
  }

  String getCoverageHeading({
    required List<AssetPolicyCard>? coveragesPolicyCards,
    required Map<String, dynamic>? policyMap,
    required PolicyHeadersModel? lifePolicyHeaders,
  }) {
    final hasHealthCoverage = coveragesPolicyCards != null &&
        coveragesPolicyCards.isNotEmpty &&
        policyMap != null;

    final hasLifeCoverage =
        lifePolicyHeaders != null && lifePolicyHeaders.policy != null;

    if (hasHealthCoverage && hasLifeCoverage) {
      return HealthAssetConstants.yourCoverages;
    } else if (hasHealthCoverage) {
      return HealthAssetConstants.yourHealthCoverages;
    } else if (hasLifeCoverage) {
      return HealthAssetConstants.yourLifeCoverages;
    } else {
      return '';
    }
  }
}
