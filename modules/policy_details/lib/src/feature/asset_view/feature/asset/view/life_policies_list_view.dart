import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/pdp_covered_policy_item.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:policy_details/policy_details.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/cubit/health_asset_details_cubit.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/domain/models/hl_asset_constants.dart';

class LifePoliciesListView extends StatelessWidget {
  const LifePoliciesListView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HealthAssetDetailsCubit, HealthAssetDetailsState>(
      buildWhen: (prev, curr) =>
          curr is HealthAssetDetailsLoading || curr is HealthAssetDetailsLoaded,
      builder: (context, state) {
        if (state is HealthAssetDetailsLoaded) {
          final lifePolicyHeaders =
              context.read<HealthAssetDetailsCubit>().lifePolicyHeaders;
          final title =
              '${LifeAssetConstants.payoutTitlePrefix}${lifePolicyHeaders?.policy?.policyDetails?.policyStateDetails?.sumAssured?.displayValue}${LifeAssetConstants.payoutTitleSuffix}';

          final subtitle =
              '${LifeAssetConstants.subtitlePrefix}${lifePolicyHeaders?.policy?.policyDetails?.planDisplayName}';

          final status = lifePolicyHeaders
                  ?.policy?.policyDetails?.policyStatus ??
              '${LifeAssetConstants.defaultStatusPrefix}${(lifePolicyHeaders?.policy?.policyDetails?.coversTill ?? '').toLowerCase().extractNumber()}';

          final endDate = lifePolicyHeaders?.policy?.policyDetails?.endDate;
          final expiryCase =
              status.containsIgnoreCase(LifeAssetConstants.expire) ||
                  (endDate ?? DateTime.now()).isBefore(
                    DateTime.now(),
                  );
          final deactivateCase =
              status.containsIgnoreCase(LifeAssetConstants.deactivate);

          if (lifePolicyHeaders != null && lifePolicyHeaders.policy != null) {
            return Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: LinearGradient(
                  colors: [
                    color5920C5,
                    color5920C5.withOpacity(0.6),
                    colorDFDFDF,
                    colorDFDFDF,
                    colorDFDFDF,
                    colorDFDFDF,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomCenter,
                ),
              ),
              margin: const EdgeInsets.only(left: 16, right: 16),
              padding: const EdgeInsets.all(2),
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: colorF8F7FC,
                  borderRadius: BorderRadius.circular(18),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CoveredPolicyItem(
                      title: title,
                      subtitle: subtitle,
                      status: status ?? '',
                      statusColor: expiryCase || deactivateCase
                          ? colorD83D37
                          : color0B753E,
                      statusColorBg: expiryCase || deactivateCase
                          ? colorFBEAEA
                          : colorEBFBEE,
                      onTap: () {
                        final cubit = context.read<HealthAssetDetailsCubit>();
                        final service = cubit.concatenatedSubServices
                            .firstWhereOrNullIterable(
                          (element) => element.serviceId.containsIgnoreCase(
                              LifeAssetConstants.editPolicy),
                        );
                        Navigator.push(
                          context,
                          MaterialPageRoute<void>(
                            builder: (context) => LifeCoverageSummaryPage(
                              lifePolicyHeadersModel: lifePolicyHeaders,
                              endorsementUrl: service?.redirectUrl ??
                                  LifeAssetConstants.emptyString,
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            );
          }
        }
        return const SizedBox.shrink();
      },
    );
  }
}
