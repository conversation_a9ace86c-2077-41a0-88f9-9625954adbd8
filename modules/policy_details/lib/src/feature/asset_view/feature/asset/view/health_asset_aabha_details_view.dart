import 'dart:async';

import 'package:acko_flutter/common/view/FullPageLoader.dart';
import 'package:acko_flutter/feature/abha-journey/cubit/abha_journey_cubit.dart';
import 'package:acko_flutter/feature/abha-journey/domain/models/abha_member_list_detail_model.dart';
import 'package:acko_flutter/feature/webview/web_view_actions.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/asset_view/asset_view_shimmer/abha_card_shimmer.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/pdp_abha_asset_card.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:policy_details/policy_details.dart';
import 'package:policy_details/src/core/util/extensions.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/constants/constants.dart';

class HealthAssetAbhaDetailsView extends StatefulWidget {
  const HealthAssetAbhaDetailsView({super.key});

  @override
  State<HealthAssetAbhaDetailsView> createState() =>
      _HealthAssetAbhaDetailsViewState();
}

class _HealthAssetAbhaDetailsViewState
    extends State<HealthAssetAbhaDetailsView> {
  late HealthAssetDetailsCubit _bloc;
  bool isLoadingIndicatorDisplayed = false;
  AbhaJourneyCubit? abhaCubit;
  bool abhaJourneyInitiated = false;

  @override
  void initState() {
    _bloc = BlocProvider.of<HealthAssetDetailsCubit>(context);
    abhaCubit = BlocProvider.of<AbhaJourneyCubit>(context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HealthAssetDetailsCubit, HealthAssetDetailsState>(
      buildWhen: (prev, curr) =>
          curr is HealthAssetDetailsLoading || curr is HealthAssetDetailsLoaded,
      builder: (context, state) {
        if (state is HealthAssetDetailsLoaded) {
          final abhaId = state.abhaData?.abhaId ?? '';
          return Column(
            children: [
              if (state.statusModel != null &&
                  state.statusModel!.notProtectedData != null) ...[
                SDUIText(
                  padding: const EdgeInsets.fromLTRB(12, 0, 12, 16),
                  value: getNotProtectedValue(
                    _bloc.filter['relationship'] as String?,
                    _bloc.filter['name'] as String?,
                  ),
                  textColor: const Color(0xFFF07975),
                  textStyle: 'lMedium',
                  maxLines: 2,
                ),
              ],
              IgnorePointer(
                ignoring: abhaJourneyInitiated,
                child: AssetAbhaCard(
                  hasAbha: abhaId.isNotEmpty,
                  abhaNumber: abhaId,
                  onTap: () async {
                    if (state.abhaData != null) {
                      unawaited(
                        Navigator.pushNamed(
                          context,
                          Routes.ABHA_DETAIL_PAGE,
                          arguments: {
                            'abhaNumber': state.abhaData?.abhaId,
                          },
                        ),
                      );
                    } else {
                      _bloc.handleAssetPageTapEvent('abha_link');
                      final relationship =
                          (_bloc.filter['relationship'] as String?) ?? '';
                      final abhaMemberListDetails = _bloc.healthAssetDetailsData
                              .abhaDetailsInfo?.abhaMemberListDetails
                              ?.where(
                                (element) =>
                                    element.relationship == relationship,
                              )
                              .firstOrNull ??
                          AbhaMemberListDetailModel(relationship: relationship);

                      setState(() {
                        FullPageLoader.instance.showFullPageLoader(context);
                        abhaJourneyInitiated = true;
                      });
                      await abhaCubit?.createTransaction(
                        abhaDetails: abhaMemberListDetails,
                      );
                      setState(() {
                        abhaJourneyInitiated = false;
                        FullPageLoader.instance.dismissFullPageLoader(context);
                      });
                    }
                  },
                ),
              ),
              buildAbhaNavigationWidget(),
            ],
          );
        } else if (state is HealthAssetDetailsLoading) {
          return const AbhaCardShimmer();
        } else {
          return const SizedBox.shrink();
        }
      },
    );
  }

  Widget buildAbhaNavigationWidget() {
    return BlocListener<AbhaJourneyCubit, AbhaJourneyState>(
      listenWhen: (prev, curr) =>
          prev.abhaTransactionModel != curr.abhaTransactionModel,
      listener: (context, state) async {
        if (state.abhaTransactionModel?.redirectionUrlWithRelationship
                .isNullOrEmpty ??
            false) return;
        final redirectionUrl = Util.getFormattedRedirectionUrl(
          state.abhaTransactionModel?.redirectionUrlWithRelationship,
          true,
          false,
        );
        final value = await Navigator.pushNamed(
          context,
          Routes.WEB_PAGE_V2,
          arguments: {
            'url': redirectionUrl,
          },
        );
        if (value is WebViewResult && value.result == Result.RESULT_BACK) {
          unawaited(_bloc.getAbhaData(isFromAbhaView: true));
        }
      },
      child: const SizedBox.shrink(),
    );
  }

  String getNotProtectedValue(String? relationship, String? name) {
    var notProtectedValue = '';
    if (relationship.equalsIgnoreCase('self')) {
      notProtectedValue = 'You are not protected with ACKO';
    } else if (relationship == 'Other') {
      notProtectedValue = '$name is not protected';
    } else {
      notProtectedValue =
          "Your ${relationship?.toLowerCase() ?? ''} is not protected";
    }
    return notProtectedValue;
  }
}
