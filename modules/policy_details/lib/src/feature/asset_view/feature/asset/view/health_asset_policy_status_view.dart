import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/feature/health_home/models/health_policy_header_response.dart';
import 'package:acko_flutter/framework/pdp/health/common/tap_handler.dart';
import 'package:acko_flutter/framework/pdp/health/models/enums.dart';
import 'package:acko_flutter/framework/pdp/health/models/health_policies_basic_details.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/asset_view/asset_view_shimmer/asset_view_shimmers.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/life_asset/life_will_service_grid_item.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/optional_alert_overlay_widget.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/pdp_asset_document_download_sheet.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/pdp_not_protected.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:design_module/design_module.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:policy_details/policy_details.dart';
import 'package:policy_details/src/core/util/extensions.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/domain/models/Health_asset_services_model.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/utilities.dart';

class HealthAssetPolicyStatusView extends StatelessWidget {
  HealthAssetPolicyStatusView({required this.bloc, super.key});

  HealthAssetDetailsCubit bloc;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HealthAssetDetailsCubit, HealthAssetDetailsState>(
      buildWhen: (prev, curr) =>
          curr is HealthAssetDetailsLoading || curr is HealthAssetDetailsLoaded,
      builder: (context, state) {
        if (state is HealthAssetDetailsLoaded) {
          if (state.statusModel != null) {
            if (state.statusModel!.alertData != null) {
              return Column(
                children: [
                  _buildCareCenter(
                    state.statusModel!.healthAssetServicesModel,
                    context,
                  ),
                  OptionalAlertOverlayWidget(
                    childrenContentWidget: Column(
                      children: [
                        _buildAssetServices(
                          state.statusModel!.healthAssetServicesModel,
                          context,
                        ),
                        if (state.statusModel!.healthAssetServicesModel
                                ?.lockerServices !=
                            null)
                          buildLockerServices(
                            state.statusModel!.healthAssetServicesModel,
                            context,
                          ),
                      ],
                    ),
                    alertUIModel: state.statusModel!.alertData,
                    shouldDisplayAlert: true,
                    tapEvent: () =>
                        bloc.handleAssetPageTapEvent('overall_lock_card_cta'),
                  ),
                ],
              );
            } else if (state.statusModel!.error != null) {
              return Container(
                height: MediaQuery.of(context).size.height * 0.5,
                color: color121212,
              );
            } else if (state.statusModel!.notProtectedData != null) {
              return NotProtectedWidget(
                crossSellModel: state.statusModel!.notProtectedData,
                tapEvent: (tapType) {
                  bloc.handleAssetPageTapEvent(tapType);
                },
              );
            } else if (state.statusModel!.healthAssetServicesModel != null) {
              return Column(
                children: [
                  _buildCareCenter(
                    state.statusModel!.healthAssetServicesModel,
                    context,
                  ),
                  _buildAssetServices(
                    state.statusModel!.healthAssetServicesModel,
                    context,
                  ),
                  buildLockerServices(
                    state.statusModel!.healthAssetServicesModel,
                    context,
                  ),
                ],
              );
            } else {
              return const SizedBox.shrink();
            }
          } else {
            return const SizedBox.shrink();
          }
        } else if (state is HealthAssetDetailsLoading) {
          return const PolicyStatusShimmer();
        } else {
          return const SizedBox.shrink();
        }
      },
    );
  }

  Widget _buildCareCenter(
    HealthAssetServicesModel? assetServicesModel,
    BuildContext context,
  ) {
    if (assetServicesModel == null) {
      return const SizedBox.shrink();
    }
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 32, 16, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (assetServicesModel.isLocked &&
              (assetServicesModel.lockMessage?.isNotNullOrEmpty ?? false))
            Container(
              margin: const EdgeInsets.only(bottom: 20),
              decoration: const BoxDecoration(
                color: Color(0xFFFBEAEA),
                borderRadius: BorderRadius.all(Radius.circular(12)),
              ),
              padding: const EdgeInsets.all(8),
              child: Row(
                children: [
                  const Icon(Icons.info_outline_rounded),
                  Expanded(
                    child: SDUIText(
                      padding: const EdgeInsets.only(left: 12),
                      value: assetServicesModel.lockMessage,
                      maxLines: 4,
                      textStyle: 'pSmall',
                      textColor: const Color(0xFF121212),
                    ),
                  ),
                ],
              ),
            ),
          SDUIText(
            padding: const EdgeInsets.only(bottom: 20),
            value: 'Your care centre',
            maxLines: 3,
            textStyle: 'hSmall',
            textColor: const Color(0xFF121212),
          ),
          _getNetworkHospitalCard(
            assetServicesModel.lockNetworkHospitals,
            context,
          ),
        ],
      ),
    );
  }

  Widget _buildAssetServices(
    HealthAssetServicesModel? assetServicesModel,
    BuildContext context,
  ) {
    if (assetServicesModel == null) {
      return const SizedBox.shrink();
    }
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const SizedBox(height: 8),
          getServicesSection(
            assetServicesModel.assetServices,
            assetServicesModel.isLocked,
            assetServicesModel.redirUrl,
            bloc,
          ),
        ],
      ),
    );
  }

  Widget _getNetworkHospitalCard(bool isLocked, BuildContext context) {
    return InkWell(
      onTap: () {
        bloc.handleAssetPageTapEvent('cashless_hospital');
        if (isLocked) {
          HealthTapHandler.loadHealthBuyJourney(context);
        } else {
          HealthTapHandler.loadNetworkHospitalsModule(
            context,
            'health_asset_view',
            null,
          );
        }
      },
      child: Container(
        decoration: BoxDecoration(
          color: colorFFFFFF,
          borderRadius: const BorderRadius.all(Radius.circular(20)),
          border: Border.all(color: const Color(0xFFE8E8E8)),
        ),
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(12),
              child: Row(
                children: [
                  SDUIImage(
                    imageUrl:
                        'https://marketing.ackoassets.com/images/health/asset/Hospital.png',
                    width: 64,
                    height: 64,
                    radius: 12,
                  ),
                  const SizedBox(
                    width: 12,
                  ),
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SDUIText(
                          value: '10,500+ cashless hospitals',
                          maxLines: 3,
                          alignment: TextAlign.start,
                          textColor: color121212,
                          textStyle: 'hXXSmall',
                          padding: const EdgeInsets.only(bottom: 4),
                        ),
                        Row(
                          children: [
                            const Icon(
                              Icons.location_on_outlined,
                              color: Color(0xFF4B4B4B),
                              size: 16,
                            ),
                            SDUIText(
                              value: 'across India',
                              maxLines: 3,
                              alignment: TextAlign.start,
                              textColor: color4B4B4B,
                              textStyle: 'c1',
                              padding: const EdgeInsets.only(left: 4),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  const Icon(Icons.arrow_forward_rounded),
                ],
              ),
            ),
            if (isLocked)
              Align(
                alignment: Alignment.centerLeft,
                child: Container(
                  margin: const EdgeInsets.only(top: 12, left: 12),
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    color: colorE8E8E8.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: const EdgeInsets.all(8),
                  child: getLockedWidget(48, 30),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget getServicesSection(
    HealthPolicyServiceCategory? assetServices,
    bool isLocked,
    String? redirUrl,
    HealthAssetDetailsCubit cubit,
  ) {
    // check if polictType list has health and life
    final lifePolicy = cubit.policyTypes.length > 1 &&
        cubit.policyTypes.contains(PolicyTypeEnum.life);

    return GridView.builder(
      itemCount: (assetServices?.actions?.length ?? 0) + (lifePolicy ? 1 : 0),
      padding: EdgeInsets.zero,
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemBuilder: (context, index) {
        if (index <= (assetServices?.actions?.length ?? 0) - 1) {
          return _getCard(
            assetServices?.actions?[index],
            isLocked,
            redirUrl,
            context,
          );
        } else {
          return const LifeWillServiceGridItem();
        }
      },
    );
  }

  Widget getLockedWidget(double containerSize, double lockIconSize) {
    return Container(
      width: containerSize,
      height: containerSize,
      padding: const EdgeInsets.all(8),
      decoration:
          const BoxDecoration(shape: BoxShape.circle, color: Colors.white),
      child: SvgPicture.asset(
        width: lockIconSize,
        height: lockIconSize,
        Util.getAssetImage(assetName: 'ic_service_lock.svg'),
      ),
    );
  }

  Widget _getCard(
    PolicyAction? service,
    bool isLocked,
    String? redirUrl,
    BuildContext context,
  ) {
    if (service == null) return const SizedBox.shrink();
    return InkWell(
      onTap: () {
        bloc.handleTapEventForHealthServices(
          service,
        ); //HealthTapHandler.loadHealthBuyJourney(context);
        if (!isLocked) {
          HealthTapHandler.gridOnTapHandler(
            service.actionKey,
            null,
            context,
            insured: bloc.selectedInsured,
            hasLifePolicy: bloc.hasLifePolicy,
            service: service,
          );
        }
      },
      child: Container(
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(20)),
          color: colorF5F5F5,
        ),
        // padding: EdgeInsets.all(12),
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.only(
                top: 12,
                left: 12,
                right: 30,
              ),
              child: Align(
                alignment: Alignment.topLeft,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SDUIText(
                      value: service.title ?? '',
                      maxLines: 3,
                      alignment: TextAlign.start,
                      textColor: color121212,
                      textStyle: 'lSmall',
                    ),
                    if (service.badgeLabel.isNotNullOrEmpty)
                      Container(
                        margin: const EdgeInsets.only(top: 8),
                        decoration: const BoxDecoration(
                          gradient: LinearGradient(
                            colors: [Color(0xFFDE6D2E), Color(0xFFE157D4)],
                            stops: [0.0, 1.0],
                          ),
                          borderRadius: BorderRadius.all(Radius.circular(20)),
                        ),
                        padding: const EdgeInsets.symmetric(
                          vertical: 4,
                          horizontal: 8,
                        ),
                        child: SDUIText(
                          value: service.badgeLabel,
                          textStyle: 'lXSmall',
                          textColor: const Color(0xFFFFFFFF),
                        ),
                      ),
                  ],
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.zero,
              child: Align(
                alignment: Alignment.bottomCenter,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Padding(
                      padding: EdgeInsets.only(left: 12, bottom: 12),
                      child: Icon(
                        Icons.arrow_forward,
                        color: color121212,
                      ),
                    ),
                    SDUIImage(
                      padding: const EdgeInsets.only(right: 4, bottom: 4),
                      imageUrl: service.icon ?? '',
                      height: 72,
                      width: 72,
                    ),
                  ],
                ),
              ),
            ),
            if (isLocked)
              Container(
                decoration: BoxDecoration(
                  color: colorE8E8E8.withOpacity(0.7),
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
            if (isLocked)
              Align(
                child: getLockedWidget(48, 30),
              ),
          ],
        ),
      ),
    );
  }

  Widget buildLockerServices(
    HealthAssetServicesModel? assetServicesModel,
    BuildContext context,
  ) {
    final serviceList = <Widget>[];
    final hasOnlyUpcomingPolicies =
        bloc.selectedInsured?.hasOnlyUpcomingPolicies ?? false;
    final hasOnlyExpiredPolicies =
        bloc.selectedInsured?.hasOnlyExpiredPolicies ?? false;
    final hasOnlyDeactivatedPolicies =
        bloc.selectedInsured?.hasOnlyDeactivatedPolicies ?? false;
    final hasOnlyGMCPolicies =
        bloc.selectedInsured?.hasOnlyExpiredPolicies ?? false;

    assetServicesModel?.lockerServices?.actions?.forEach((element) {
      if (((element.actionKey?.equalsIgnoreCase('download_documents') ??
                  false) &&
              !hasOnlyGMCPolicies) ||
          bloc.hasLifePolicy) {
        serviceList
          ..add(
            getHealthLockerCard(
              element,
              assetServicesModel.pdpFilterMap,
              assetServicesModel.policyDocModel,
              assetServicesModel.lockDocuments ?? false,
              assetServicesModel.redirUrl ?? '',
              context,
            ),
          ) // state.isLocked
          ..add(const SizedBox(width: 12));
      } else if ((element.actionKey?.equalsIgnoreCase('download_ecard') ??
              false) &&
          !hasOnlyExpiredPolicies &&
          !hasOnlyUpcomingPolicies &&
          !hasOnlyDeactivatedPolicies) {
        serviceList
          ..add(
            getHealthLockerCard(
              element,
              assetServicesModel.pdpFilterMap,
              assetServicesModel.policyDocModel,
              assetServicesModel.lockEcards ?? false,
              assetServicesModel.redirUrl ?? '',
              context,
            ),
          ) // _bloc.hasOnlyProposals
          ..add(const SizedBox(width: 12));
      }
    });
    return serviceList.isNullOrEmpty
        ? const SizedBox.shrink()
        : Container(
            width: MediaQuery.of(context).size.width,
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 40),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SDUIText(
                  padding: const EdgeInsets.only(bottom: 20),
                  value: bloc.hasLifePolicy || bloc.hasLifeProposals
                      ? 'Your health and life locker'
                      : assetServicesModel?.assetServices?.title ?? '',
                  maxLines: 3,
                  textStyle: 'hSmall',
                  textColor: const Color(0xFF121212),
                ),
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: serviceList,
                  ),
                ),
                // _getServicesSection(state.assetServices, 1)
              ],
            ),
          );
  }

  Widget getHealthLockerCard(
    PolicyAction? service,
    Map<dynamic, dynamic>? pdpFilterMap,
    List<AssetPolicyDocumentModel>? assetPolicyDocumentModel,
    bool isLocked,
    String lockRedirection,
    BuildContext context,
  ) {
    if (service == null && bloc.hasLifePolicy == false) {
      return const SizedBox.shrink();
    }
    return Column(
      children: [
        InkWell(
          onTap: () {
            bloc.handleTapEventForHealthServices(service);
            if (!isLocked) {
              if ((service?.actionKey ?? '')
                  .equalsIgnoreCase('download_documents')) {
                bloc.handleBottomSheetViewEvent('doc_download_select_policy');
                context.showAckoModalBottomSheet(
                  backgroundColor: colorFFFFFF,
                  child: BlocProvider.value(
                    value: bloc,
                    child: DownloadDocumentSheet(
                      assetPolicyDocumentModel: assetPolicyDocumentModel,
                      tapEvent: () {
                        bloc.handleBottomSheetTapEvent(
                          'doc_download_select_policy',
                        );
                      },
                    ),
                  ),
                );
              } else {
                HealthTapHandler.gridOnTapHandler(
                  service?.actionKey,
                  null,
                  context,
                  insured: bloc.selectedInsured,
                  pdpFilterMap: pdpFilterMap,
                  ctaType: 'asset_view',
                  assetPolicyDocumentModel: assetPolicyDocumentModel,
                ); //  isACEnabled: isACEnabled, advanceCashData: advanceCashData, assetResponse: policies, pdpFilterMap: pdpFilterMap
              }
            }
          },
          child: Stack(
            children: [
              Container(
                width: 96,
                height: 96,
                decoration: const BoxDecoration(
                  color: colorF5F5F5,
                  borderRadius: BorderRadius.all(Radius.circular(16)),
                ),
                padding: const EdgeInsets.all(24),
                child: Center(
                  child: SDUIImage(
                    imageUrl: service?.icon ?? '',
                    height: 48,
                    width: 48,
                  ),
                ),
              ),
              if (isLocked)
                Container(
                  width: 96,
                  height: 96,
                  decoration: BoxDecoration(
                    color: colorE8E8E8.withOpacity(0.7),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Center(child: getLockedWidget(40, 24)),
                ),
            ],
          ),
        ),
        SDUIText(
          padding: const EdgeInsets.only(top: 8),
          value: service?.title ?? '',
          alignment: TextAlign.start,
          textColor: color040222,
          textStyle: 'c1',
        ),
      ],
    );
  }
}
