import 'package:acko_flutter/framework/pdp/health/view/widgets/asset_view/asset_view_shimmer/header_asset_view_shimmer.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/pdp_asset_header.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:policy_details/policy_details.dart';
import 'package:utilities/utilities.dart';

class HealthAssetHeaderView extends StatelessWidget {
  const HealthAssetHeaderView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HealthAssetDetailsCubit, HealthAssetDetailsState>(
      buildWhen: (prev, curr) =>
          curr is HealthAssetDetailsLoading ||
          curr is HealthAssetDetailsLoaded ||
          curr is HealthAssetDetailsError,
      builder: (context, state) {
        if (state is HealthAssetDetailsLoaded) {
          final cubit = context.read<HealthAssetDetailsCubit>();
          return HealthAssetHeaderWidget(
            selectedHealthAsset: state.selectedHealthAsset,
          );
        } else if (state is HealthAssetDetailsLoading) {
          return const HeaderAssetViewShimmer();
        } else {
          return const SizedBox.shrink();
        }
      },
    );
  }
}
