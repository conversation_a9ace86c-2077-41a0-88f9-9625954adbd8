import 'dart:async';
import 'dart:convert';

import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/feature/abha-journey/domain/models/abha_details_enum.dart';
import 'package:acko_flutter/feature/abha-journey/domain/models/abha_details_model.dart';
import 'package:acko_flutter/feature/abha-journey/domain/models/abha_member_list_detail_model.dart';
import 'package:acko_flutter/feature/abha-journey/domain/repository/abha_journey_repository.dart';
import 'package:acko_flutter/feature/app_policy_page/model/proposals.dart';
import 'package:acko_flutter/feature/claim/advance_cash/onboarding/data/AdvanceCashState.dart';
import 'package:acko_flutter/feature/health_home/models/health_policy_header_response.dart';
import 'package:acko_flutter/feature/profile_completion/bloc/bloc_singleton_instance.dart';
import 'package:acko_flutter/feature/vas/gmc_upsell/model/gmc_upsell_dto.dart';
import 'package:acko_flutter/framework/pdp/health/common/constants.dart';
import 'package:acko_flutter/framework/pdp/health/common/pdp_utils.dart';
import 'package:acko_flutter/framework/pdp/health/models/enums.dart';
import 'package:acko_flutter/framework/pdp/health/models/health_policies_basic_details.dart';
import 'package:acko_flutter/framework/pdp/health/models/life_my_account_model.dart';
import 'package:acko_flutter/framework/pdp/health/models/members_list_model.dart';
import 'package:acko_flutter/framework/pdp/health/models/pdp_alert_model.dart';
import 'package:acko_flutter/framework/pdp/health/models/ui_models/more_actions_model.dart';
import 'package:acko_flutter/framework/pdp/health/models/ui_models/pdp_crosssell_v9_model.dart';
import 'package:acko_flutter/framework/pdp/health/models/ui_models/pdp_education_card_model.dart';
import 'package:acko_flutter/framework/pdp/health/models/ui_models/policy_card_model.dart';
import 'package:acko_flutter/framework/pdp/health/models/ui_models/policy_notification_card_model.dart';
import 'package:acko_flutter/framework/pdp/health/repo/health_repo.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/pdp_asset_document_download_sheet.dart';
import 'package:acko_flutter/network/ApiResponse.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_flutter/util/health/health_constants.dart';
import 'package:analytics/analytics_tracker_manager.dart';
import 'package:analytics/events/health_life/page_events/health_life_page_events.dart';
import 'package:analytics/events/health_life/tap_events/health_life_tap_events.dart';
import 'package:analytics/events/page_loaded_events.dart';
import 'package:analytics/events/tap_events.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:policy_details/policy_details.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/domain/models/Health_asset_services_model.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/domain/models/enums.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/domain/models/health_asset_data.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/domain/models/health_gmc_upsell_card_model.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/domain/models/health_policy_status_model.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/domain/models/hl_cards_data.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/remote_config/remote_config.dart';
import 'package:utilities/state_provider/StateProvider.dart';
import 'package:utilities/widgets/acko_safe_cubit.dart';

part 'asset_state.dart';

class HealthAssetDetailsCubit extends AckoSafeCubit<HealthAssetDetailsState> {
  HealthAssetDetailsCubit({required this.filter})
      : super(HealthAssetDetailsLoading());

  Map<dynamic, dynamic> filter;
  late BuildContext context;
  bool forceRefresh = false;
  FamilyMembersData? healthAssetsList;
  MemberData? memberData;
  List<Map<dynamic, dynamic>> allHealthAssets = [];
  HealthAssetDetailsRepo healthAssetDetailsRepo = HealthAssetDetailsRepo();
  HealthAssetDetailsResponse? _assetData;
  HealthAssetDetailsResponseInsured? selectedInsured;
  final _healthRepo = HealthRepo(AggregatedPageMode.asset);
  final _abhaJourneyRepository = AbhaJourneyRepository();
  HealthAssetDetailsData healthAssetDetailsData = HealthAssetDetailsData();
  bool isAbhaRefreshCalled = false;
  bool hasLifeProposals = false;
  PDPCrossSellV9? notProtectedData;
  List<PolicyNotification> policyNotifications = [];
  bool isNotProtected = false;
  List<PolicyTypeEnum> policyTypes = <PolicyTypeEnum>[];
  List<HealthPolicyDetailsModel> policyList = [];
  Map<String, HealthPolicyDetailsModel>? policyMap;
  List<HealthPolicyDetailsModel> proposalsList = [];
  AdvanceCashDiscoveryState? advanceCashDetailsData;
  bool isHealthCareServicesLocked = false;
  String alertWidgetMessage = '';
  bool isAdvanceCashEnabled = false;
  GMCUpsellSheetDto? gmcUpsellData;

  //Life Properties
  LifeMyAccountModel? lifeMyAccountModelData;
  LifeMyAccountModel? lifeMyAccountModel;
  PolicyHeadersModel? lifePolicyHeaders;
  PolicyHeadersModel? lifePolicyHeadersForSelf;
  final LifePolicyViewRepository _lifePolicyViewRepository =
      LifePolicyViewRepository();
  List<SubService> concatenatedSubServices = <SubService>[];
  List<HealthEducationCardData> lifeEducationalSectionList = [];
  bool hasLifePolicy = false;
  List<AllPolicy>? lifePolicyList;
  List<AllProposal>? lifeProposalList;
  Map<String, dynamic>? lifeProposalsDataList;
  bool isFirstTimeLoad = true;
  bool showV10UI = false;
  PWILOCardsData? healthPwiLoData;
  PWILOCardsData? lifePwiLoData;

  bool navigateToKeyCovers(
    AssetPolicyCard? coverageCard,
    HealthPolicyDetailsModel? healthPolicyDetailsModel,
  ) {
    return (coverageCard?.productType?.equalsIgnoreCase('retail') ?? false) ||
        ((coverageCard?.productType?.equalsIgnoreCase(
                  'enterprise',
                ) ??
                false) &&
            ((coverageCard?.policyType?.equalsIgnoreCase(
                      'base',
                    ) ??
                    false) ||
                (coverageCard?.policyType?.equalsIgnoreCase(
                      'member',
                    ) ??
                    false)) &&
            // Check policy construct is NOT individual
            !(healthPolicyDetailsModel?.policyConstruct?.equalsIgnoreCase(
                  'individual',
                ) ??
                false));
  }

  Future<void> refreshBloc(
    FamilyMembersData? membersData, {
    bool shouldShowLoadingIndicator = true,
  }) async {
    if (shouldShowLoadingIndicator) {
      showLoadingIndicator();
    }
    _clearData();
    loadAssetDetails(membersData);
  }

  void _clearData() {
    _assetData = null;
    notProtectedData = null;
    policyNotifications = [];
    policyList = [];
    proposalsList = [];
  }

  void loadAssetDetails(
    FamilyMembersData? membersDataList, {
    Map<dynamic, dynamic>? assetFilter,
  }) async {
    await getV10UIFlag();
    emit(HealthAssetDetailsLoading());
    if (membersDataList != null &&
        membersDataList.membersList.isNotNullOrEmpty) {
      healthAssetsList = membersDataList;
    }
    final assetName = assetFilter?['name'] as String?;
    // final assetDOB = assetFilter?['dob'] as String?;
    final filterName = filter['name'] as String?;
    // final filterDOB = filter['dob'] as String?;
    if (assetFilter != null && assetName.isNotNullOrEmpty) {
      memberData = membersDataList?.getSelectedMember(
        assetName!,
      );
      filter = memberData?.toMap() ?? {};
    } else if (filter.isNotNullOrEmpty && filterName.isNotNullOrEmpty) {
      memberData = membersDataList?.getSelectedMember(
        filterName!,
      );
      memberData ??= membersDataList?.membersList?.first;
      filter = memberData?.toMap() ?? {};
    } else {
      memberData = membersDataList?.membersList?.first;
      filter = memberData?.toMap() ?? {};
    }
    getAllHealthAssets(filter);
    getAssetDetails(assetFilter: filter);
  }

  Future<AbhaMemberListDetailModel?> getAbhaData({
    bool isFromAbhaView = false,
  }) async {
    emit(HealthAbhaDetailsLoading());
    var abhaDetailsInfo = healthAssetDetailsData.abhaDetailsInfo;
    if (isFromAbhaView == true || abhaDetailsInfo == null) {
      abhaDetailsInfo = await fetchAbhaDetails();
      healthAssetDetailsData.abhaDetailsInfo = abhaDetailsInfo;
    }
    final familyMemberName = (filter['name'] as String?) ?? '';
    final abhaData = abhaDetailsInfo.abhaMemberListDetails?.where((element) {
      var assetName = familyMemberName;
      assetName = assetName.replaceAll(' ', '');
      var abhaMemberName = element.name?.fullName ?? '';
      abhaMemberName = abhaMemberName.replaceAll(' ', '');
      return element.abhaId.isNotNullOrEmpty &&
          assetName.equalsIgnoreCase(abhaMemberName);
    }).firstOrNull;
    if (abhaData != null) {
      final stateProvider = StateProvider();
      final profileCompletionBloc =
          ProfileCompeltionBlocSingletonInstance.instance.blocInstance;
      final familyMember =
          profileCompletionBloc.profileCompletionModel?.familyMembers
              ?.where(
                (element) => element.name.equalsIgnoreCase(familyMemberName),
              )
              .firstOrNull;
      if (familyMember != null &&
          familyMember.abhaDetails?.abhaNumber == null &&
          !isAbhaRefreshCalled) {
        isAbhaRefreshCalled = true;
        stateProvider.notify(ObserverState.MyAccount_Refresh);
      }
      return abhaData;
    }
    return null;
  }

  Future<void> getAssetDetails({
    required Map<dynamic, dynamic> assetFilter,
    bool force = false,
  }) async {
    getAllHealthAssets(assetFilter);
    final result = await Future.wait([
      if (lifeMyAccountModelData == null) getLifeMyAccount(),
      if (_assetData == null) fetchAssetData(),
      if (notProtectedData == null) getPDPCrossSellData(),
      fetchAdvanceCashData(),
    ]);

    if (_assetData?.error != null) {
      emit(HealthAssetDetailsError());
      return;
    }

    final assetName = filter['name'] as String? ?? '';

    policyList = getPolicies(assetName);
    policyMap = createPolicyMap(policyList);
    proposalsList = getProposals(assetName);
    setPolicyTypes();

    final insured = _assetData?.insureds
        ?.where((element) => element.name.equalsIgnoreCase(assetName))
        .firstOrNull;

    isNotProtected = (_assetData == null || selectedInsured == null) &&
        (proposalsList.isNullOrEmpty) &&
        (policyList.isNullOrEmpty) &&
        (lifePolicyList.isNullOrEmpty) &&
        (!hasLifeProposals);
      emit(
        HealthAssetDetailsLoaded(
          selectedHealthAsset: assetFilter,
          abhaData: null,
          policyNotificationList: const [],
          healthPolicyList: policyList,
          insured: insured,
          statusModel: null,
          upsellCardsModel: null,
          prePolicyEditCardList: null,
        ),
      );
      emit(HealthAssetNotificationsLoading());
    final result2 = await Future.wait([
      getAbhaData(),
      getUpsellData(), // todo: this can be moved to result 3 to reduce latency...
      getPolicyNotifications(assetName),
    ]);

    policyNotifications = (result2.last as List<PolicyNotification>?) ?? [];

    final statusModel =
        getPolicyStatus(result.last as AdvanceCashDiscoveryState?);

    final prePolicyEditCardList = selectedInsured?.proposals
            ?.where((element) => element.prePolicyEditCard != null)
            .map((e) => e.prePolicyEditCard!)
            .toList() ??
        [];

    emit(
      HealthAssetDetailsLoaded(
        selectedHealthAsset: assetFilter,
        abhaData: result2.firstOrNull as AbhaMemberListDetailModel?,
        policyNotificationList: policyNotifications,
        healthPolicyList: policyList,
        insured: insured,
        statusModel: statusModel,
        upsellCardsModel: result2[1] as UpsellCardModels?,
        prePolicyEditCardList: prePolicyEditCardList,
      ),
    );
    unawaited(getEducationalData());
    getHelpData();
    unawaited(handleAssetPageViewEvent());
    isFirstTimeLoad = false;
  }

  void showLoadingIndicator() {
    emit(HealthAssetDetailsLoading());
  }

  Future<HealthAssetDetailsResponse?> fetchAssetData() async {
    if (forceRefresh || _assetData == null) {
      _assetData = await healthAssetDetailsRepo.getAssetDetails(
        apiType: ProjectionApiType.assetDetails,
        allPolicies: true,
        filterRenewed: true,
      );
    }
    return _assetData;
  }

  Future<AbhaDetailsModel> fetchAbhaDetails() async {
    return _abhaJourneyRepository.getAbhaDetails(
      abhaDetailsEnum: AbhaDetailsEnum.basic,
    );
  }

  void getAllHealthAssets(Map<dynamic, dynamic> selectedAsset) {
    allHealthAssets = [];
    if (healthAssetsList != null &&
        (healthAssetsList?.membersList.isNotNullOrEmpty ?? false)) {
      healthAssetsList?.membersList?.forEach((element) {
        if (element.isSelectedFilter(selectedAsset)) {
          allHealthAssets.add(element.toMap(selectedAsset: true));
        } else {
          allHealthAssets.add(element.toMap());
        }
      });
    }
  }

  Future<List<PolicyNotification>> getPolicyNotifications(
    String? assetName,
  ) async {
    final policyNotifications = <PolicyNotification>[];

    if (((filter['relationship'] ?? '') as String).equalsIgnoreCase('self')) {
      // final proposalNotifications =
      lifeMyAccountModelData?.allProposals?.forEach((proposal) {
        final cards = proposal.cardsV2;
        if (cards == null) return;
        for (final element in cards) {
          policyNotifications.add(
            PolicyNotification(
              cardTitle: element.title,
              cardSubtitle: element.subtitle,
              ctaText: element.ctaText,
              policyName: proposal.subtitle,
              onTap: element.redirectUrl,
            ),
          );
        }
      });

      lifeMyAccountModelData?.allPolicies?.forEach((element) {
        if (element.cards == null) return;

        /// policyExpiring subcards
        final expiringSubcards = element.cards?.policyExpiring?.subcards;
        if (expiringSubcards != null) {
          expiringSubcards.forEach((card) {
            policyNotifications.add(
              PolicyNotification(
                cardTitle: card.title,
                cardSubtitle: card.subtitle,
                ctaText: card.ctaText,
                policyName: card.type,
                onTap: card.redirectUrl,
              ),
            );
          });
        }

        /// policyOnHold subcards
        final onHoldSubcards = element.cards?.policyOnHold?.subcards;
        if (onHoldSubcards != null) {
          onHoldSubcards.forEach((card) {
            policyNotifications.add(
              PolicyNotification(
                cardTitle: card.title,
                cardSubtitle: card.subtitle,
                ctaText: card.ctaText,
                policyName: card.type,
                onTap: card.redirectUrl,
              ),
            );
          });
        }
      });
    }

    final advanceCashData = advanceCashDetailsData?.advanceCashData;
    final gmcRenewalData = healthAssetDetailsRepo.getGMCRenewalData();
    selectedInsured = _assetData?.insureds
        ?.where(
          (element) =>
              element.name
                  ?.replaceAll(' ', '')
                  .equalsIgnoreCase(assetName?.replaceAll(' ', '')) ??
              false,
        )
        .firstOrNull;

    if (selectedInsured == null) return policyNotifications;

    // Check if _assetData is not null
    selectedInsured!.policies?.forEach((policy) {
      policy.policyCards?.forEach((policyCard) {
        policyNotifications.add(
          PolicyNotification(
            cardTitle: policyCard.data?.title,
            cardSubtitle: policyCard.data?.subTitle,
            ctaText: policyCard.data?.ctaText,
            policyName: policyCard.data?.policyName,
            onTap: policyCard.data?.redirectURL,
            cardColor: policyCard.data?.cardColor,
          ),
        );
      });
      if ((gmcRenewalData?.showGmcRenewalBanner ?? false) &&
          policy.isGMCPolicy &&
          policy.isExpired &&
          policy.masterPolicyNumber
              .equalsIgnoreCase(gmcRenewalData?.masterPolicyNumber)) {
        policyNotifications.add(
          PolicyNotification(
            cardTitle: gmcRenewalData?.title ?? '',
            cardSubtitle: gmcRenewalData?.subtitle ?? '',
            policyName: policy.planName,
            cardColor: gmcRenewalData?.cardColor ?? '',
          ),
        );
      }
    });

    if (advanceCashData != null) {
      policyNotifications.add(
        PolicyNotification(
          cardTitle: advanceCashData.cta?.formattedStatusLabel(
            advanceCashData.patientName,
            advanceCashData.admissionDate,
            advanceCashData.expiryDate,
          ),
          // cardSubtitle: _advanceCashStatus?.advanceCashData?.formattedStatusDate,
          policyName:
              'Advance Cash ${rupee_symbol + advanceCashData.formattedAmountOnDiscoveryCard.toString()}',
          ctaText: advanceCashData.cta?.buttonText,
          onTap: advanceCashData.cta?.url,
          advanceCashStatus: advanceCashDetailsData,
        ),
      );
    }

    selectedInsured!.proposals?.forEach((proposal) {
      bool isBasbaProposal = proposal.basbaPolicy ?? false;
      proposal.policyCards?.forEach((policyCard) {
        policyNotifications.add(
          PolicyNotification(
            cardTitle: policyCard.data?.title,
            cardSubtitle: policyCard.data?.subTitle,
            ctaText: policyCard.data?.ctaText,
            policyName: policyCard.data?.policyName,
            onTap: policyCard.data?.redirectURL,
            basbaPolicy: isBasbaProposal
          ),
        );
      });
    });
    policyNotifications.removeWhere(
      (element) =>
          element.ctaText.isNullOrEmpty &&
          (element.cardTitle.isNullOrEmpty ||
              element.cardTitle.notEqualsIgnoreCase(gmcRenewalData?.title)),
    );
    return policyNotifications;
  }

  Proposals? _parseProposalsResponse(Map<String, dynamic>? data) {
    if (data == null) return null;
    final proposalsModel = Proposals.fromJson(data);
    return proposalsModel;
  }

  List<HealthPolicyDetailsModel> getPolicies(String? assetName) {
    final policiesList = <HealthPolicyDetailsModel>[];
    final insured = _assetData?.insureds
        ?.where((element) => element.name.equalsIgnoreCase(assetName))
        .firstOrNull;
    insured?.policies?.forEach(policiesList.add);
    return policiesList;
  }

  List<HealthPolicyDetailsModel> getProposals(String? assetName) {
    final policiesList = <HealthPolicyDetailsModel>[];
    final insured = _assetData?.insureds
        ?.where((element) => element.name.equalsIgnoreCase(assetName))
        .firstOrNull;
    insured?.proposals?.forEach(policiesList.add);
    return policiesList;
  }

  Future<AdvanceCashDiscoveryState?> fetchAdvanceCashData() async {
    if (forceRefresh || advanceCashDetailsData == null) {
      advanceCashDetailsData = await _healthRepo.getAdvanceCashDetailsData();
    }
    return advanceCashDetailsData;
  }

  //Life Related Code
  Future<void> getLifeMyAccount() async {
    lifeMyAccountModelData = await _healthRepo.getLifeMyAccountData();
    final lifePolicy =
        lifeMyAccountModelData?.allPolicies?.firstWhereOrNullIterable(
      (element) => element.planId.containsIgnoreCase('life'),
    );
    if (lifePolicy?.policyNumber.isNotNullOrEmpty ?? false) {
      await getLifePolicyHeaders(policyNumber: lifePolicy?.policyNumber);
    }
  }

  Future<void> getLifePolicyHeaders({String? policyNumber}) async {
    lifePolicyHeadersForSelf = await _lifePolicyViewRepository.getPolicyHeaders(
      policyNumber: policyNumber ?? '',
    );
    if (filter['relationship'] == 'Self') {
      lifePolicyHeaders = lifePolicyHeadersForSelf;
    }
    arrangeSubServices();
  }

  void arrangeSubServices() {
    final services = lifePolicyHeaders?.services;
    if (services == null) return;
    final subServices = services.map((e) => e.subServices).toList();
    concatenatedSubServices = subServices
        .expand<SubService>(
          (element) => element ?? [],
        )
        .toList();
  }

  void setPolicyTypes() {
    if (filter['relationship'] != 'Self') {
      lifeMyAccountModel = null;
      lifePolicyHeaders = null;
      lifeEducationalSectionList = <HealthEducationCardData>[];
      hasLifePolicy = false;
    } else {
      lifePolicyHeaders = lifePolicyHeadersForSelf;
      lifeMyAccountModel = lifeMyAccountModelData;
      final lifePolicy =
          lifeMyAccountModelData?.allPolicies?.firstWhereOrNullIterable(
        (element) => element.planId.containsIgnoreCase('life'),
      );
      hasLifePolicy = lifePolicy != null;
    }

    lifePolicyList = filter['relationship'] == 'Self'
        ? lifeMyAccountModel?.allPolicies
        : <AllPolicy>[];
    lifeProposalList = filter['relationship'] == 'Self'
        ? lifeMyAccountModel?.allProposals
        : <AllProposal>[];

    hasLifeProposals = lifeProposalList?.isNotEmpty ?? false;
    policyTypes.clear();
    if ((lifePolicyList.isNotNullOrEmpty) || hasLifeProposals) {
      policyTypes.add(PolicyTypeEnum.life);
    }
    if ((policyList.isNotNullOrEmpty) || (proposalsList.isNotNullOrEmpty)) {
      policyTypes.add(PolicyTypeEnum.health);
    }
  }

  bool get isLifeReinstateOrGracePeriod {
    final subcards = lifeMyAccountModel
        ?.allPolicies?.firstOrNull?.cards?.policyOnHold?.subcards;
    bool isReinstate = false;
    bool isGracePeriod = false;
    if (subcards != null) {
      for (var subcard in subcards) {
        if (subcard.id.equalsIgnoreCase(life_reinstate)) {
          isReinstate = true;
          break;
        }
        if (subcard.id.equalsIgnoreCase(life_reinstate_in_grace_period)) {
          isGracePeriod = true;
          break;
        }
      }
    }
    return isReinstate || isGracePeriod;
  }

  Future<void> fetchLifeEducationalSectionData() async {
    if (isLifeReinstateOrGracePeriod) return;

    final claimService = concatenatedSubServices.firstWhereOrNullIterable(
      (element) => element.serviceId.equalsIgnoreCase('Raise Claim'),
    );
    final claimTypeIds =
        claimService?.context?.claimsQuery?.map((e) => e.id).toList();
    if (claimTypeIds.isNullOrEmpty) return;
    final data = await RemoteConfigInstance.instance
        .getData(RemoteConfigKeysSet.lifeAssetEducationalSection) as String;
    final parsedList = jsonDecode(data);

    parsedList.forEach(
      (json) => lifeEducationalSectionList
          .add(HealthEducationCardData.fromJson(json as Map<String, dynamic>)),
    );

    //remove elements which are not in claimTypeIds
    //make all lowercase to match with the claimTypeIds
    final ids = claimTypeIds?.map((e) => e?.toLowerCase());
    lifeEducationalSectionList.removeWhere(
      (element) => ids != null && !ids.contains(element.id?.toLowerCase()),
    );
  }

  Future<void> getPDPCrossSellData() async {
    final notProtectedJsonStr = await RemoteConfigInstance.instance
        .getData(RemoteConfigKeysSet.V9_HEALTH_LIFE_CROSSSELL) as String;
    if (notProtectedJsonStr.isNotEmpty) {
      final jsonMap = json.decode(notProtectedJsonStr) as Map<String, dynamic>;
      notProtectedData = PDPCrossSellV9.fromJson(jsonMap);
    } else {
      notProtectedData = null;
    }
  }

  HealthPolicyStatusModel? getPolicyStatus(
    AdvanceCashDiscoveryState? advanceCashDiscoveryState,
  ) {
    //if (policyList.isNullOrEmpty) return null;
    final policy = policyList.firstOrNull;
    String? renewPolicyUrl;
    final assetServices = getAssetServices(advanceCashDiscoveryState);

    if ((policy?.policyStatus.equalsIgnoreCase('lapsed') ?? false) ||
        (policy?.premiumDetail?.paymentFrequency?.equalsIgnoreCase('yearly') ??
            false)) {
      renewPolicyUrl = policyNotifications.firstOrNull?.onTap;
    } else {
      renewPolicyUrl = policy?.premiumDetail?.paymentSchedulePageUrl;
    }

    if (_assetData == null) {
      return HealthPolicyStatusModel(error: ErrorHandler(something_went_wrong));
    }
    if ((_assetData?.insureds?.isNullOrEmpty ?? true) &&
        (lifeMyAccountModel?.allPolicies.isNullOrEmpty ?? true) &&
        hasLifeProposals) {
      return HealthPolicyStatusModel(error: ErrorHandler(something_went_wrong));
    }

    if (policyList.isNullOrEmpty &&
        proposalsList.isNullOrEmpty &&
        (lifeMyAccountModel?.allPolicies.isNullOrEmpty ?? true)) {
      return HealthPolicyStatusModel(notProtectedData: notProtectedData);
    }

    if (policyList.isNotNullOrEmpty &&
        proposalsList.isNullOrEmpty &&
        policyList.length == 1 &&
        !hasLifeProposals) {
      final policyState = policyList.firstOrNull?.getHealthPolicyState;

      final isGmc = policyList.firstOrNull?.isGMCPolicy ?? false;
      AlertUIModel alertUIModel;

      final policyEndDate = policyList.first.policyEndDate;
      final differenceInDays =
          HealthPDPUtils.calculateDifferenceInDays(policyEndDate) ?? 0;
      if (differenceInDays <= 30) {
        alertWidgetMessage =
            'You can renew this policy without any health tests within ${30 - differenceInDays} days';
      } else if (differenceInDays <= 90) {
        alertWidgetMessage =
            'Renew this policy now to get health coverage for you and your family.';
      }
      if (policyState == HealthPolicyStates.ON_HOLD ||
          policyState == HealthPolicyStates.PREMIUM_PENDING) {
        alertWidgetMessage =
            'Complete your monthly payment to unlock your policy';
        alertUIModel = AlertUIModel(
          actionCTAText: 'Pay now',
          alertImagePath: 'ic_new_lock.svg',
          alertWidgetTitle: 'You are not protected right now',
          alertWidgetMessage: alertWidgetMessage,
          onPressed: renewPolicyUrl,
          cardColor: const Color(0xFFD83D37),
        );
        assetServices?.isLocked = false;
        assetServices?.lockEcards = false;
        assetServices?.lockDocuments = false;
        return HealthPolicyStatusModel(
          alertData: alertUIModel,
          healthAssetServicesModel: assetServices,
        );
      } else if (policyState == HealthPolicyStates.DEACTIVATED && !isGmc) {
        alertWidgetMessage =
            'Reactivate this policy now to get health coverage for you and your family.';
        alertUIModel = AlertUIModel(
          actionCTAText: 'Reactivate your policy',
          alertImagePath: 'ic_new_lock.svg',
          alertWidgetTitle: 'You are not protected right now',
          alertWidgetMessage: alertWidgetMessage,
          onPressed: renewPolicyUrl,
          cardColor: const Color(0xFFD83D37),
        );
        assetServices?.isLocked = false;
        assetServices?.lockEcards = false;
        assetServices?.lockDocuments = false;
        return HealthPolicyStatusModel(
          alertData: alertUIModel,
          healthAssetServicesModel: assetServices,
        );
      } else if (policyState == HealthPolicyStates.EXPIRED &&
          !isGmc &&
          differenceInDays <= 90) {
        alertUIModel = AlertUIModel(
          actionCTAText: 'Renew your policy',
          alertImagePath: 'ic_new_lock.svg',
          alertWidgetTitle: 'You are not protected right now',
          alertWidgetMessage: alertWidgetMessage,
          onPressed: renewPolicyUrl,
          cardColor: const Color(0xFFD83D37),
        );
        assetServices?.isLocked = false;
        assetServices?.lockEcards = false;
        assetServices?.lockDocuments = false;
        return HealthPolicyStatusModel(
          alertData: alertUIModel,
          healthAssetServicesModel: assetServices,
        );
      } else if (policyState == HealthPolicyStates.UNDER_REVIEW) {
        alertWidgetMessage =
            'Your health care centre and locker will be unlocked after the policy is issued';
        alertUIModel = AlertUIModel(
          actionCTAText: '',
          alertImagePath: 'ic_new_lock.svg',
          alertWidgetTitle: 'You are not protected yet',
          alertWidgetMessage: alertWidgetMessage,
          cardColor: const Color(0xFFD83D37),
          onPressed: renewPolicyUrl,
        );
        assetServices?.isLocked = false;
        assetServices?.lockEcards = false;
        assetServices?.lockDocuments = false;
        return HealthPolicyStatusModel(
          alertData: alertUIModel,
          healthAssetServicesModel: assetServices,
        );
      } else if (policyState == HealthPolicyStates.UPCOMING) {
        alertWidgetMessage =
            'You have done your part! All benefits can be accessed after your policy is issued.';
        final remainingDays =
            int.parse(policyList.firstOrNull?.getRemainingDays ?? '0');
        var remainingDaysString = '';
        remainingDaysString =
            (remainingDays > 1) ? '$remainingDays days' : '$remainingDays day';
        alertUIModel = AlertUIModel(
          actionCTAText: '',
          // View
          alertImagePath: 'ic_purple_lock.svg',
          alertWidgetTitle:
              'You will be protected in just\n$remainingDaysString!',
          alertWidgetMessage: alertWidgetMessage,
          cardColor: const Color(0xFF5920C5),
          onPressed: '',
        );
        assetServices?.isLocked = false;
        assetServices?.lockEcards = false;
        assetServices?.lockDocuments = false;
        return HealthPolicyStatusModel(
          alertData: alertUIModel,
          healthAssetServicesModel: assetServices,
        );
      }
    } else if ((proposalsList.isNotNullOrEmpty) &&
        (policyList.isNullOrEmpty ?? false) &&
        !hasLifeProposals) {
      final redirectionURL = proposalsList.first.policyCards
          ?.where(
            (element) => !element.cardType.containsIgnoreCase('kyc'),
          )
          .firstOrNull
          ?.data
          ?.redirectURL;
      final alertUIModel = AlertUIModel(
        actionCTAText: proposalsList.first.policySubstatus
                    .containsIgnoreCase('UNDER_WRITER_REVIEW') ??
                false
            ? 'Check status'
            : 'Complete pending tasks',
        alertImagePath: 'ic_new_lock.svg',
        alertWidgetTitle: 'You are not protected',
        alertWidgetMessage: proposalsList.first.policySubstatus
                .containsIgnoreCase('UNDER_WRITER_REVIEW')
            ? 'Your health care centre and locker will be unlocked after the policy is issued'
            : 'Complete pending actions to unlock your health care centre and health locker',
        onPressed: redirectionURL ??
            proposalsList
                .firstOrNull?.policyCards?.firstOrNull?.data?.redirectURL ??
            '',
        cardColor: const Color(0xFFD83D37),
      );
      assetServices?.isLocked = false;
      assetServices?.lockDocuments = false;
      assetServices?.lockEcards = false;
      return HealthPolicyStatusModel(
        alertData: alertUIModel,
        healthAssetServicesModel: assetServices,
      );
    }
    return HealthPolicyStatusModel(
      healthAssetServicesModel: assetServices,
    );
  }

  HealthAssetServicesModel? getAssetServices(
    AdvanceCashDiscoveryState? advanceCashDiscoveryState,
  ) {
    HealthAssetServicesModel? servicesModel;
    final assetServicesData = <HealthPolicyServiceCategory>[];
    List<PolicyAction>? healthVASActions = [];
    healthVASActions = selectedInsured?.memberServices?.map((e) {
      return PolicyAction(
        title: e.title,
        icon: e.iconUrl,
        action: e.action,
        badgeLabel: e.subTitle,
        applicablePolicies: e.applicablePolicies,
      );
    }).toList();

    assetServicesData.add(
      HealthPolicyServiceCategory(
        title: 'Your health locker',
        actions: healthVASActions,
      ),
    );
    var lockDocuments = true;
    var lockEcards = true;

    final assetLockerServicesData = <HealthPolicyServiceCategory>[];
    List<PolicyAction>? healthLockerActions = [];
    healthLockerActions = selectedInsured?.memberLocker?.map((e) {
      if (e.lockerType == 'e_cards') {
        lockEcards = !(e.isEnabled ?? false);
      } else {
        lockDocuments = !(e.isEnabled ?? false);
      }
      return PolicyAction(
        title: e.title,
        icon: e.iconUrl,
        action: e.action,
        badgeLabel: e.subTitle,
      );
    }).toList();

    if (selectedInsured?.hasOnlyGMCPolicies ?? false) {
      healthLockerActions
          ?.removeWhere((element) => element.action == 'download_documents');
    }

    final containsDocumentLocker = healthLockerActions
        ?.where((element) => element.title.equalsIgnoreCase('documents'))
        .firstOrNull;

    if (hasLifePolicy && containsDocumentLocker == null) {
      healthLockerActions?.add(
        PolicyAction(
          title: 'Documents',
          icon:
              'https://marketing.ackoassets.com/images/health/asset/v9_ecard.png',
          action: 'download_documents',
          badgeLabel: '',
        ),
      );
    }

    assetLockerServicesData.add(
      HealthPolicyServiceCategory(
        title: 'Your health locker',
        actions: healthLockerActions,
      ),
    );

    isAdvanceCashEnabled =
        advanceCashDiscoveryState?.isEligibleForAdvanceCash ?? false;
    final advanceCashInfo = advanceCashDiscoveryState?.advanceCashData;

    final pdpFilterMap = filter;
    final assetPolicyDocumentModel = <AssetPolicyDocumentModel>[];

    final lockNetworkHospitals = (selectedInsured == null ||
            (policyList.isNullOrEmpty) &&
                (proposalsList.isNullOrEmpty) &&
                !(selectedInsured?.hasOnlyUpcomingPolicies ?? false)) &&
        (lifeMyAccountModel?.allPolicies.isNotNullOrEmpty ?? false);
    final memberService = selectedInsured?.memberServices?.firstOrNull;
    isHealthCareServicesLocked = !(memberService?.enabled ?? false);
    final healthCareRedirectionUrl = memberService?.redirectUrl ?? '';
    final policyStatusMessage =
        serviceLockedMessage(isLocked: isHealthCareServicesLocked);

    if (selectedInsured?.hasOnlyASPPolicies ?? false) {
      isHealthCareServicesLocked = true;
    }

    for (final element in policyList) {
      final docs = element.policyDocuments;
      final actionUrl = (element.productType
                  .equalsIgnoreCase(HealthConstants.RETAIL_PRODUCT) ||
              element.productType.equalsIgnoreCase(HealthConstants.ASP_PRODUCT))
          ? Constants.RETAIL_HEALTH_HELP_PAGE
          : Constants.ENTERPRISE_HELP_PAGE;
      if (docs.isNotNullOrEmpty) {
        assetPolicyDocumentModel.add(
          AssetPolicyDocumentModel(
            title: element.planName,
            documents: docs,
            actionUrl: actionUrl,
          ),
        );
      }
    }

    if (selectedInsured?.hasOnlyUpcomingPolicies ?? false) {
      lockEcards = true;
      lockDocuments = true;
    }

    if (selectedInsured?.policies?.length == 1 &&
        (selectedInsured?.policies?.first.policyStatus == 'lapsed' &&
            selectedInsured?.policies?.first.policySubstatus == 'onhold')) {
      lockEcards = false;
    }

    if (hasLifePolicy) lockDocuments = false;

    if (assetServicesData.isNotNullOrEmpty) {
      servicesModel = HealthAssetServicesModel(
        assetServices: assetServicesData.firstOrNull,
        advanceCashData: advanceCashInfo,
        isAdvancedCashEnabled: isAdvanceCashEnabled,
        pdpFilterMap: pdpFilterMap,
        lockNetworkHospitals: lockNetworkHospitals,
        isLocked: isHealthCareServicesLocked,
        redirUrl: healthCareRedirectionUrl,
        lockerServices: assetLockerServicesData.firstOrNull,
        lockDocuments: lockDocuments,
        lockEcards: lockEcards,
        policyDocModel: assetPolicyDocumentModel,
        lockMessage: selectedInsured?.hasOnlyASPPolicies == false
            ? policyStatusMessage
            : 'Your Aarogya Sanjeevani policy restricts access to health care benefits. Consider purchasing a personal policy for full access.',
      );
    }
    return servicesModel;
  }

  String serviceLockedMessage({bool isLocked = false}) {
    var policyStatus = '';

    if (!isLocked) {
      return policyStatus;
    }

    if (selectedInsured == null) {
      return 'Your policy is not eligible to access health care benefits. Please purchase personal health policy to access these exciting benefits';
    }

    if ((selectedInsured!.hasOnlyASPPolicies) ||
        (proposalsList.isNotNullOrEmpty)) {
      policyStatus = selectedInsured!.hasOnlyASPPolicies
          ? 'Your policy is not eligible to access health care benefits. Please purchase personal health policy to access these exciting benefits'
          : '';
    } else {
      if (policyList.isNotNullOrEmpty) {
        for (final policy in policyList) {
          if (policy.getHealthPolicyState == HealthPolicyStates.ACTIVE) {
            // policyStatus = "";
          } else if (policy.getHealthPolicyState ==
                  HealthPolicyStates.EXPIRED ||
              policy.getHealthPolicyState == HealthPolicyStates.DEACTIVATED) {
            policyStatus = (policyList.length ?? 0) > 1
                ? 'Your health care benefits are locked since your policy is expired.'
                : '';
            if (!policy.isGMCPolicy) {
              policyStatus += 'Renew your policy';
            }
          } else if (policy.getHealthPolicyState ==
                  HealthPolicyStates.ON_HOLD ||
              policy.getHealthPolicyState ==
                  HealthPolicyStates.PREMIUM_PENDING) {
            policyStatus =
                'Your health care benefits are locked since your policy is on hold. Pay the pending premium';
          }
        }
      }
    }
    return policyStatus;
  }

  Future<List<HealthEducationCardData>> fetchEducationalSectionData() async {

    final (raiseClaimUrl, viewClaimUrl, isEducationEnabled) = await getClaimUrlsAndFlag();

    var educationConfigKey = isEducationEnabled ? RemoteConfigKeysSet.ASSET_VIEW_EDUCATIONAL_SECTION_V2 : RemoteConfigKeysSet.ASSET_VIEW_EDUCATIONAL_SECTION;

    var jsonData = await RemoteConfigInstance.instance.getData(educationConfigKey);

    if (jsonData == null) return [];

    List<dynamic> parsedList;
    if (jsonData is String) {
      if (jsonData.isEmpty) return [];
      parsedList = jsonDecode(jsonData) as List<dynamic>;
    } else if (jsonData is List) {
      parsedList = jsonData;
    } else {
      return [];
    }

    final educationalSectionList = <HealthEducationCardData>[];
    parsedList.forEach(
      (json) => educationalSectionList
          .add(HealthEducationCardData.fromJson(json as Map<String, dynamic>)),
    );
    return educationalSectionList;
  }

  Future<void> getEducationalData() async {
    emit(HealthEducationalSectionLoading());
    var educationalSectionData = healthAssetDetailsData.educationalSectionData;

    if (educationalSectionData == null) {
      educationalSectionData = await fetchEducationalSectionData();
      healthAssetDetailsData.educationalSectionData = educationalSectionData;
    }

    if (lifeEducationalSectionList.isEmpty && //self or spouse
        (filter['relationship'] == 'Self')) {
      await fetchLifeEducationalSectionData();
    }

    final hasNoPolicy = policyList.isNullOrEmpty;
    final hasAllAspPolicies = selectedInsured?.hasOnlyASPPolicies ?? false;
    final hasAllUpcomingPolicies =
        selectedInsured?.hasOnlyUpcomingPolicies ?? false;

    Future.delayed(const Duration(milliseconds: 1), () {
      if (hasAllAspPolicies || hasNoPolicy || hasAllUpcomingPolicies) {
        emit(HealthNoEducationalSection());
      } else {
        if (isAdvanceCashEnabled == false) {
          educationalSectionData?.removeWhere(
            (element) => element.title.equalsIgnoreCase('ADVANCE CASH'),
          );
        }

        emit(
          HealthEducationalSectionLoaded(
            educationData: HealthEducationData(
              cards: educationalSectionData ?? [],
            ),
          ),
        );
      }
    });
  }

  Future<(String, String, bool)> getClaimUrlsAndFlag() async {
    var jsonData = await RemoteConfigInstance.instance.getData(RemoteConfigKeysSet.CLAIMS_ENTRY_CONFIG);

    final assetRaiseClaim = jsonData['asset_raise_claim'] as String? ?? '${Constants.BASE_URL}gi/p/health/claim/raise-claim?hide_app_bar=true';
    final assetViewClaim = jsonData['asset_view_claim'] as String? ?? '${Constants.BASE_URL}gi/p/health/claim/view-claims?hide_app_bar=true';
    final claimsEducationEnabled = jsonData['claims_education_enabled'] as bool? ?? false;

    return (assetRaiseClaim, assetViewClaim, claimsEducationEnabled);
  }

  Future<void> getV10UIFlag() async {
    showV10UI = (await RemoteConfigInstance.instance.getGbAsyncData(RemoteConfigKeysSet.APP_IA_VERSION)).toString().equalsIgnoreCase("app_v10");
  }

  void getHelpData() {
    emit(const HealthHelpDataLoading());
    Future.delayed(const Duration(milliseconds: 1), () async {
      final prospectMoreActionsList = [
        MoreActions(
          title: 'View member info',
          icon: 'member_info.svg',
          onTapAction: 'view_member_info'
        ),
        MoreActions(
          title: 'Get help',
          icon: 'ic_get_help.svg',
          onTapAction: 'support',
        ),
      ];

      if (policyTypes.isEmpty) {
        emit(HealthBuyJourneyCta(moreActionsList: prospectMoreActionsList));
      }

      if (selectedInsured == null &&
          (lifeMyAccountModel?.allPolicies.isNullOrEmpty ?? false) &&
          (!hasLifeProposals)) {
        emit(HealthMoreActionsError(ErrorHandler(something_went_wrong)));
        return;
      }

      if (selectedInsured == null &&
          (lifeMyAccountModel?.allPolicies.isNullOrEmpty ?? false) &&
          (!hasLifeProposals)) {
        emit(HealthBuyJourneyCta(moreActionsList: prospectMoreActionsList));
        return;
      }
      if (_assetData != null) {
        final allPolicies = policyList;
        final hasOnlyASP = selectedInsured?.hasOnlyASPPolicies ?? false;
        final hasOnlyUpcoming =
            selectedInsured?.hasOnlyUpcomingPolicies ?? false;
        var isClaimAllowed = false;

        isClaimAllowed = (!hasOnlyUpcoming && !hasOnlyASP) &&
            allPolicies.isNotNullOrEmpty &&
            ((allPolicies.length ?? 0) > 1 ||
                allPolicies.length == 1 &&
                    (allPolicies.first.getHealthPolicyStatus !=
                            HealthPolicyStates.ON_HOLD ||
                        allPolicies.first.getHealthPolicyStatus !=
                            HealthPolicyStates.PREMIUM_PENDING));

        final (raiseClaimUrl, viewClaimUrl, isEducationEnabled) = await getClaimUrlsAndFlag();

        final moreActionsList = [
          if (isClaimAllowed)
            MoreActions(
              title: 'View your claims',
              icon: 'ic_view_all_claims.svg',
              onTapAction: isEducationEnabled ? 'view_all_claims_v2' : 'load_claims_list',
            ),
          MoreActions(
            title: 'Get help',
            icon: 'ic_get_help.svg',
            onTapAction: 'get_new_health_help',
          ), // 'get_help_health'
        ];
        if (isClaimAllowed) {
          emit(
            HealthMoreActionsLoaded(
              moreActionsList: moreActionsList,
              hasLifePolicy: hasLifePolicy,
            ),
          );
        } else {
          if (isNotProtected) {
            emit(
              HealthMoreActionsLoaded(
                moreActionsList: prospectMoreActionsList,
                hasLifePolicy: hasLifePolicy,
              ),
            );
          } else {
            emit(
              HealthHelpDataLoaded(
                hasLifePolicy: hasLifePolicy,
                healthProposals: proposalsList,
              ),
            );
          }
        }
      } else {
        emit(HealthMoreActionsError(ErrorHandler(something_went_wrong)));
      }
    });
  }

  bool get isLifeServicesLocked {
    var isLocked = false;
    if (isLifeReinstateOrGracePeriod || (hasLifeProposals && !hasLifePolicy)) {
      isLocked = true;
    }
    lifeMyAccountModel?.allPolicies?.forEach((policy) {
      final expired = policy.endDate?.isAfter(DateTime.now());
      if (!(expired ?? false)) {
        isLocked = false;
      }
    });

    return isLocked;
  }

  //Events

  //Asset-Page View Event
  Future<void> handleAssetPageViewEvent() async {
    final product = getProductInfo();
    final memberType = filter['relationship'] as String?;
    final memberProtected = isNotProtected ? 'No' : 'Yes';
    final planName = getPlanName();
    final overallLockMessage = getOverallLockStatusMessage();
    final lockType = getLockType();
    final careCenterStatus = getCareCenterStatus();
    final planStatus = getPlanStatus();
    final needsAttentionCardType = getNeedsAttentionCardType();
    final lifeNotificationData = getLifeNotificationData();

    gmcUpsellData ??= await fetchGMCUpsellData();

    final gmcUpsellInfo = findGMCUpsellInfo(gmcUpsellData?.sumInsuredCap ?? 0);
    var upsellCardType =
        gmcUpsellInfo?['showUpsellCard'] as bool? ?? false ? 'gmc' : 'none';

    if (policyTypes.length == 1 && policyTypes.first == PolicyTypeEnum.life) {
      upsellCardType = 'fresh_retail_health';
    }

    AnalyticsTrackerManager.instance.sendEvent(
      event: HLPageEvents.VIEW_ASSET_L1_PAGE,
      properties: {
        'product': product,
        'member_type': memberType,
        'member_protected': memberProtected,
        if (planName.isNotEmpty) 'plan_name': planName,
        'page_name': 'l1_asset_view_page',
        'care_center_status': careCenterStatus,
        if (lockType.isNotEmpty) 'lock_type': lockType,
        if (needsAttentionCardType.isNotEmpty)
          'needs_attention_card_type': needsAttentionCardType,
        if (overallLockMessage.isNotEmpty)
          'overall_lock_status': overallLockMessage,
        'plan_status': planStatus,
        'locker_status': planStatus,
        'upsell_card_type': upsellCardType,
        "status_card": lifeNotificationData['notification_banner'],
        "plan_card": lifeNotificationData['notification_banner'],
        "days_from_due_date": lifeNotificationData['days_from_due_date'],
        if (lifeNotificationData['life_card_data'] != null)
          "overall_lock_card_cta": lifeNotificationData['life_card_data'],
      }..addAll(
          lifeNotificationData,
        ),
    );
  }

  Map<String, dynamic> getLifeNotificationData({bool appendBanner = false}) {
    final ids = <String>[];
    final daysFromDueDates = <num>[];
    String? lifeCardData;

    lifeMyAccountModel?.allPolicies?.forEach((element) {
      if (element.cards == null) return;

      /// policyExpiring subcards
      final expiringSubcards = element.cards?.policyExpiring?.subcards;
      if (expiringSubcards != null) {
        expiringSubcards.forEach((card) {
          if (card.id != null) {
            ids.add(card.id! + (appendBanner ? '_banner' : ''));
          }
          if (card.id.containsIgnoreCase('life_renewal') ||
              card.id.containsIgnoreCase('life_reinstate')) {
            lifeCardData = card.id;
          }
          if (card.daysFromDueDate != null) {
            daysFromDueDates.add(card.daysFromDueDate!);
          }
        });
      }

      /// policyOnHold subcards
      final onHoldSubcards = element.cards?.policyOnHold?.subcards;
      if (onHoldSubcards != null) {
        onHoldSubcards.forEach((card) {
          if (card.id != null) {
            ids.add(card.id! + (appendBanner ? '_banner' : ''));
          }
          if (card.id.containsIgnoreCase('life_renewal') ||
              card.id.containsIgnoreCase('life_reinstate')) {
            lifeCardData = card.id;
          }
          if (card.daysFromDueDate != null) {
            daysFromDueDates.add(card.daysFromDueDate!);
          }
        });
      }
    });

    return {
      'notification_banner': ids.join(','),
      'days_from_due_date': daysFromDueDates.join(','),
      if (lifeCardData.isNotNullOrEmpty) 'life_card_data': lifeCardData
    };
  }

  String getProductInfo() {
    final products = <String>[
      if ((selectedInsured?.retailPoliciesCount ?? 0) > 0) 'retail_health',
      if ((selectedInsured?.gmcPoliciesCount ?? 0) > 0) 'gmc',
      if (lifeMyAccountModel?.allPolicies?.isNotNullOrEmpty ?? false)
        'retail_life',
    ];

    if (products.length > 1) {
      return products.join(',');
    } else {
      return products.isEmpty ? 'none' : products.first;
    }
  }

  String getPlanName() {
    final planNames = <String>[];
    for (final element in policyList) {
      if (element.planName != null) {
        planNames.add(element.planName!);
      }
    }
    lifeMyAccountModel?.allPolicies?.forEach((element) {
      if (element.planName != null) {
        planNames.add(element.planName!);
      }
    });
    if (planNames.length > 1) {
      return planNames.join(',');
    } else {
      return planNames.isEmpty ? 'none' : planNames.first;
    }
  }

  String getOverallLockStatusMessage() {
    var overallLockMessage = '';
    final hasOnlyLifePolicy = policyList.isEmpty &&
        (lifeMyAccountModel?.allPolicies?.length ?? 0) > 0;
    final hasOverLay = (hasOnlyLifePolicy && isLifeServicesLocked) ||
        (policyList.isNotNullOrEmpty) &&
            (proposalsList.isNullOrEmpty) &&
            ((policyList.length) == 1) &&
            isHealthCareServicesLocked == true;
    if (hasOnlyLifePolicy && isLifeServicesLocked) {
      overallLockMessage = getLifeProposal();
    } else if (hasOverLay) {
      overallLockMessage =
          proposalsList.firstOrNull?.policySubstatus ?? alertWidgetMessage;
    }
    return overallLockMessage;
  }

  String getLifeProposal() {
    var proposalMessage = '';
    if (lifeMyAccountModel != null &&
        (lifeMyAccountModel?.allProposals?.isNotNullOrEmpty ?? false) &&
        (lifeMyAccountModel?.allProposals?.first.cardsV2?.isNotNullOrEmpty ??
            false)) {
      proposalMessage =
          lifeMyAccountModel?.allProposals?.first.cardsV2?.first.title ?? '';
    } else if ((lifeMyAccountModel?.allPolicies?.isNotNullOrEmpty ?? false)) {
      var onHoldTitle = lifeMyAccountModel?.allPolicies?.firstOrNull?.cards
              ?.policyOnHold?.subcards?.firstOrNull?.subtitle ??
          '';
      var expiringTitle = lifeMyAccountModel?.allPolicies?.firstOrNull?.cards
              ?.policyExpiring?.subcards?.firstOrNull?.subtitle ??
          '';

      if (onHoldTitle.isNotNullOrEmpty) {
        proposalMessage = onHoldTitle;
      } else if (expiringTitle.isNotNullOrEmpty) {
        proposalMessage = expiringTitle;
      }
    }
    return proposalMessage;
  }

  String getLockType() {
    var lockType = '';
    final hasOnlyLifePolicy = policyList.isEmpty &&
        (lifeMyAccountModel?.allPolicies?.length ?? 0) > 0;
    final hasOverLay = (hasOnlyLifePolicy && isLifeServicesLocked) ||
        (policyList.isNotNullOrEmpty) &&
            (proposalsList.isNullOrEmpty) &&
            (policyList.length == 1) &&
            isHealthCareServicesLocked == true;
    if (hasOverLay) {
      lockType = 'overall';
    } else if (isHealthCareServicesLocked == true || isLifeServicesLocked) {
      lockType = 'individual_card';
    } else {
      lockType = '';
    }
    return lockType;
  }

  String getCareCenterStatus() {
    final careCenterStatuses = <String>[];
    if (proposalsList.isNotEmpty || (policyList.isNotEmpty)) {
      careCenterStatuses.add(
        isHealthCareServicesLocked == true ? 'health-locked' : 'health-active',
      );
    } else {
      careCenterStatuses.add('health-na');
    }
    if ((lifeMyAccountModel?.allPolicies.isNotNullOrEmpty ?? false) ||
        hasLifeProposals) {
      careCenterStatuses.add(
        isLifeServicesLocked ? 'life-locked' : 'life-active',
      );
    } else {
      careCenterStatuses.add('life-na');
    }
    return careCenterStatuses.join(',');
  }

  String getPlanStatus() {
    final planStatus = <String>[];
    for (final policy in policyList) {
      final policyStatus = policy.getHealthPolicyState;
      switch (policyStatus) {
        case HealthPolicyStates.ACTIVE:
          planStatus.add('${policy.planName}-active');
        case HealthPolicyStates.DEACTIVATED:
          planStatus.add('${policy.planName}-deactivated');
        case HealthPolicyStates.ON_HOLD:
          planStatus.add('${policy.planName}-on hold');
        case HealthPolicyStates.UNDER_REVIEW:
          planStatus.add('${policy.planName}-under review');
        case HealthPolicyStates.EXPIRED:
          planStatus.add('${policy.planName}-expired');
        case HealthPolicyStates.PREMIUM_PENDING:
          planStatus.add('${policy.planName}-premium pending');
        case HealthPolicyStates.UPCOMING:
          planStatus.add('${policy.planName}-upcoming');
        default:
          planStatus.add('${policy.planName} - Not Found');
      }
    }

    lifeMyAccountModel?.allPolicies?.forEach((policy) {
      if (policy.endDate == null &&
          ((policy.endDate ?? DateTime.now()).isAfter(DateTime.now()))) {
        planStatus.add('${policy.planName} - expired');
      } else {
        if (policy.cards?.policyExpiring != null ||
            policy.cards?.policyOnHold != null) {
          planStatus.add('${policy.planName} - deactivated');
        } else
          planStatus.add('${policy.planName} - active');
      }
    });

    if (planStatus.length > 1) {
      return planStatus.join(',');
    } else {
      return planStatus.isEmpty ? 'none' : planStatus.first;
    }
  }

  String getNeedsAttentionCardType() {
    final needsAttentionCards = <String>[];
    final name = filter['name'] as String?;
    for (final element in policyNotifications) {
      needsAttentionCards.add('${element.policyName} - ${element.cardTitle}');
    }
    if (needsAttentionCards.length > 1) {
      return needsAttentionCards.join(',');
    } else {
      return needsAttentionCards.isEmpty ? 'none' : needsAttentionCards.first;
    }
  }

  void handleTapEventForAddFamily() {
    AnalyticsTrackerManager.instance.sendEvent(
      event: TapConstants.TAP_BTN_INTERACTION,
      properties: {
        'cta_text': 'add new family',
        'from_page': 'family_overview_page',
        'journey': 'Family',
      },
    );
  }

  // void handleWidgetVisibilityEvent(String pageName) {
  //   AnalyticsTrackerManager.instance.sendEvent(
  //     event: PageLoadedConstants.APP_VIEW_PAGE,
  //     properties: {
  //       'page_name': pageName,
  //       'from_page': 'health_asset',
  //     },
  //   );
  // }

  void handleAssetPageTapEvent(String type, {bool? isBasbaProposal}) {
    final product = getProductInfo();
    final memberType = filter['relationship'] as String?;
    final memberProtected = isNotProtected ? 'No' : 'Yes';
    final planName = getPlanName();
    final lifeNotificationData = getLifeNotificationData();

    AnalyticsTrackerManager.instance.sendEvent(
      event: HLTrackEvents.TAP_L1_ASSET_VIEW_PAGE,
      properties: {
        'product': product,
        'member_type': memberType,
        'member_protected': memberProtected,
        'tap_cta': type,
        'plan_name': planName,
        'page_name': 'l1_asset_view_page',
        "status_card": lifeNotificationData['notification_banner'],
        "plan_card": lifeNotificationData['notification_banner'],
        "days_from_due_date": lifeNotificationData['days_from_due_date'],
        if (lifeNotificationData['life_card_data'] != null)
          "overall_lock_card_cta": lifeNotificationData['life_card_data'],
        "is_basba_proposal": isBasbaProposal ?? false,
      }..addAll(
          lifeNotificationData,
        ),
    );
  }

  void handleTapEventForHealthServices(PolicyAction? service) {
    String action;
    switch (service?.action) {
      case 'load_doctor_on_call':
        action = 'teleconsults';
      case 'load_book_lab_test':
        action = 'lab_tests';
      case 'load_order_medicine':
        action = 'order_medicine';
      case 'download_ecard':
        action = 'ecards';
      default:
        action = 'documents';
    }
    handleAssetPageTapEvent(action);
  }

  void handleMoreActionsTapEvent(String actionType) {
    String action;
    if (actionType == 'View all claims') {
      action = 'view_all_claims';
    } else {
      action = 'get_help';
    }
    handleAssetPageTapEvent(action);
  }

  void handleBottomSheetViewEvent(String bottomSheetType) {
    final product = getProductInfo();
    final memberType = filter['relationship'];
    AnalyticsTrackerManager.instance.sendEvent(
      event: HLPageEvents.VIEW_BOTTOM_SHEET,
      properties: {
        'product': product,
        'member_type': memberType,
        'bottom_sheet_type': bottomSheetType,
        'page_name': 'l1_asset_view_page',
      },
    );
  }

  void handleBottomSheetTapEvent(String action) {
    final product = getProductInfo();
    final memberType = filter['relationship'];
    AnalyticsTrackerManager.instance.sendEvent(
      event: HLTrackEvents.TAP_ASSET_BOTTOM_SHEET,
      properties: {
        'product': product,
        'member_type': memberType,
        'tap_cta': getTapCTA(action),
        'bottom_sheet_type': getBottomSheetType(action),
        'page_name': 'l1_asset_view_page',
      },
    );
  }

  String getBottomSheetType(String action) {
    var bottomSheetType = '';
    switch (action) {
      case 'Cashless treatment':
      case 'Advance cash for treatment':
      case 'Get reimbursed':
        bottomSheetType = 'raise_claim_options';
      case 'health_buy_card':
      case 'life_buy_card':
        bottomSheetType = 'prospect_get_started';
      case 'doc_download_select_policy':
        bottomSheetType = 'doc_download_select_policy';
    }
    return bottomSheetType;
  }

  String getTapCTA(String action) {
    var tapCTA = '';
    switch (action) {
      case 'Cashless treatment':
        tapCTA = 'cashless';
      case 'Advance cash for treatment':
        tapCTA = 'advance_cash';
      case 'Get reimbursed':
        tapCTA = 'reimbursement';
      case 'health_buy_card':
        tapCTA = 'health_buy_card';
      case 'life_buy_card':
        tapCTA = 'life_buy_card';
      case 'doc_download_select_policy':
        tapCTA = 'policy_card';
    }
    return tapCTA;
  }

  Future<UpsellCardModels?> getUpsellData() async {
    final results = await Future.wait([
      fetchGMCUpsellData(),
      getPWILOData(),
      getLifePWILOData(),
    ]);

    final gmcUpsellData = results[0] as GMCUpsellSheetDto?;
    final PWILOCardsData? pwiloCardsData = results[1] as PWILOCardsData?;
    final PWILOCardsData? lifePwiloCardsData = results[2] as PWILOCardsData?;

    final sumInsuredCap = gmcUpsellData?.sumInsuredCap ?? 0;
    final upsellInfo = findGMCUpsellInfo(sumInsuredCap);
    final showGMCUpsellCard = upsellInfo?['showUpsellCard'] as bool? ?? false;

    if (showGMCUpsellCard) {
      return UpsellCardModels(
        healthGmcUpsellCardModel: HealthGmcUpsellCardModel(
          gmcUpsellBannerData: gmcUpsellData?.cardData,
          sumInsured: upsellInfo?['sumInsured'] as String?,
          showUpsellCard: showGMCUpsellCard,
          lifeUpsellBannerData: gmcUpsellData?.lifeUpsellCardData,
        ),
        pwiLoCardsData: null,
      );
    }

    notProtectedData?.plans?.plansList?.removeWhere((plan) {
      return pwiloCardsData?.data?.cards.isNotNullOrEmpty ?? false
          ? plan.id == "health"
          : false;
    });

    notProtectedData?.plans?.plansList?.removeWhere((plan) {
      return lifePwiloCardsData?.data?.cards.isNotNullOrEmpty ?? false
          ? plan.id == "life"
          : false;
    });

    return UpsellCardModels(
        healthGmcUpsellCardModel: null,
        pwiLoCardsData: pwiloCardsData,
        lifePwiLoCardsData: lifePwiloCardsData);
  }

  Future<HealthGmcUpsellCardModel?> getGMCUpsellData() async {
    gmcUpsellData = await fetchGMCUpsellData();
    final sumInsuredCap = gmcUpsellData?.sumInsuredCap ?? 0;
    final upsellInfo = findGMCUpsellInfo(sumInsuredCap);
    final sumInsured = upsellInfo?['sumInsured'] as String?;
    final showUpsellCard = upsellInfo?['showUpsellCard'] as bool? ?? false;
    return HealthGmcUpsellCardModel(
      gmcUpsellBannerData: gmcUpsellData?.cardData,
      sumInsured: sumInsured,
      showUpsellCard: showUpsellCard,
      lifeUpsellBannerData: gmcUpsellData?.lifeUpsellCardData,
    );
  }

  Future<PWILOCardsData?> getPWILOData() async {
    var pwiloRemoteData =
        RemoteConfigInstance.instance.getData(RemoteConfigKeysSet.PWILO_CARDS);
    String? healthCardId;
    if (pwiloRemoteData == null) {
      await FirebaseRemoteConfig.instance.fetchAndActivate();
      pwiloRemoteData = RemoteConfigInstance.instance
          .getData(RemoteConfigKeysSet.PWILO_CARDS);
    } else {
      final pwiloConfig = jsonDecode(pwiloRemoteData.toString());
      healthCardId = pwiloConfig['health_cards'] as String?;
    }
    healthPwiLoData ??= await _healthRepo.getPWILOCardData();
    _filterAndSetLob(healthPwiLoData, healthCardId, 'health');
    return healthPwiLoData;
  }

  Future<PWILOCardsData?> getLifePWILOData() async {
    var pwiloRemoteData =
        RemoteConfigInstance.instance.getData(RemoteConfigKeysSet.PWILO_CARDS);
    String? lifeCardId;
    if (pwiloRemoteData == null) {
      await FirebaseRemoteConfig.instance.fetchAndActivate();
      pwiloRemoteData = RemoteConfigInstance.instance
          .getData(RemoteConfigKeysSet.PWILO_CARDS);
    } else {
      final pwiloConfig = jsonDecode(pwiloRemoteData.toString());
      lifeCardId = pwiloConfig['life_cards'] as String?;
    }
    lifePwiLoData ??= await _healthRepo.getLifePWILOCardData();
    _filterAndSetLob(lifePwiLoData, lifeCardId, 'life');
    return lifePwiLoData;
  }

  void _filterAndSetLob(PWILOCardsData? pwiLoData, String? cardId, String lob) {
    if (cardId.isNotNullOrEmpty) {
      pwiLoData?.data?.cards?.removeWhere((card) => card.id != cardId);
    }
    pwiLoData?.data?.cards?.forEach((card) {
      card.lob = lob;
    });
  }

  Future<GMCUpsellSheetDto?> fetchGMCUpsellData() async {
    return gmcUpsellData ??= await _healthRepo.getGMCUpsellData();
  }

  Map<String, dynamic>? findGMCUpsellInfo(int sumInsuredCap) {
    if(gmcUpsellData?.disableBanner == true) {
      return {
        'showUpsellCard': false,
      };
    }
    if (_assetData?.insureds != null) {
      for (final asset in _assetData!.insureds!) {
        if (asset.relationship.notEqualsIgnoreCase('self')) continue;
        final gmcPoliciesOnly =
            asset.showGmcUpsellBanner && asset.proposals.isNullOrEmpty;
        if (gmcPoliciesOnly && asset.policies != null) {
          for (final element in asset.policies!) {
            if (element.isGMCPolicy &&
                element.policyType.equalsIgnoreCase('base') &&
                !element.isExpired) {
              /// Check if the corporate name matches any excluded corporates
              if(element.isCorporateExcluded(gmcUpsellData?.excludedCorporates ?? [])) return null;
              final totalSumInsured = (element.sumInsured?.value ?? 0) +
                  (element.topup?.value ?? 0);
              return {
                'sumInsured':
                    HealthPDPUtils.formatIndianCurrency(totalSumInsured)
                        .replaceAll(RegExp(r'\s+'), ''),
                'showUpsellCard': totalSumInsured <= sumInsuredCap,
              };
            }
          }
        }
      }
    }
    return null;
  }

  Map<String, HealthPolicyDetailsModel> createPolicyMap(List<HealthPolicyDetailsModel> policyList) {
    return {
      for (final policy in policyList)
        policy.policyId.isNotNullOrEmpty
            ? policy.policyId!
            : policy.policyNumber!: policy
    };
  }
}
