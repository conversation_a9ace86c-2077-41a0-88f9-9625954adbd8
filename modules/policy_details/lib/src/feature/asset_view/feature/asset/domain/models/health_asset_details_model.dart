import 'package:acko_flutter/feature/health_home/models/health_policy_header_response.dart';
import 'package:acko_flutter/framework/pdp/health/models/ui_models/policy_card_model.dart';
import 'package:acko_flutter/util/extensions.dart';
import 'package:acko_flutter/util/health/health_constants.dart';
import 'package:policy_details/policy_details.dart';

class HealthAssetDetailsResponse extends PolicyDetailsBaseModel {
  HealthAssetDetailsResponse({
    this.insureds,
  });

  HealthAssetDetailsResponse.error({
    super.error,
    this.insureds,
  });

  HealthAssetDetailsResponse.fromJson(Map<String, dynamic> json) {
    insureds = json['insureds'] == null
        ? []
        : List<HealthAssetDetailsResponseInsured>.from(
            (json['insureds'] as List).map(
              (x) => HealthAssetDetailsResponseInsured.fromJson(
                x as Map<String, dynamic>,
              ),
            ),
          );
  }

  List<HealthAssetDetailsResponseInsured>? insureds;
}

class HealthAssetDetailsResponseInsured {
  HealthAssetDetailsResponseInsured({
    this.insuredId,
    this.insuredRef,
    this.gender,
    this.dob,
    this.name,
    this.relationship,
    this.relation,
    this.ecardLink,
    this.age,
    this.uhid,
    this.covers,
    this.categorisedCovers,
    this.riskStartDate,
    this.riskEndDate,
    this.policies,
    this.proposals,
    this.memberServices,
    this.memberLocker,
    this.roomRentValue,
    this.additionalInfo,
  });

  HealthAssetDetailsResponseInsured.fromJson(Map<String, dynamic> json) {
    insuredId = json['insured_id'] as String?;
    insuredRef = json['insured_ref'] as String?;
    gender = json['gender'] as String?;
    dob = json['dob'] as String?;
    name = json['name'] as String?;
    relationship = json['relationship'] as String?;
    relation = json['relation'] as String?;
    ecardLink = json['ecard_link'] as String?;
    age = json['age'] as int?;
    uhid = json['uhid'] as String?;
    covers = json['covers'] == null
        ? []
        : List<Covers>.from(
            (json['covers'] as List).map(
              (x) => Covers.fromJson(x as Map<String, dynamic>),
            ),
          );
    categorisedCovers = json['categorised_covers'] == null
        ? []
        : List<CategorisedCover>.from(
            (json['categorised_covers'] as List).map(
              (x) => CategorisedCover.fromJson(x as Map<String, dynamic>),
            ),
          );
    riskStartDate = json['risk_start_date'] as String?;
    riskEndDate = json['risk_end_date'] as String?;
    policies = json['policies'] == null
        ? []
        : List<HealthPolicyDetailsModel>.from(
            (json['policies'] as List).map(
              (x) =>
                  HealthPolicyDetailsModel.fromJson(x as Map<String, dynamic>),
            ),
          );
    proposals = json['proposals'] == null
        ? []
        : List<HealthPolicyDetailsModel>.from(
            (json['proposals'] as List).map(
              (x) =>
                  HealthPolicyDetailsModel.fromJson(x as Map<String, dynamic>),
            ),
          );
    memberServices = json['member_services'] == null
        ? []
        : List<MemberService>.from(
            (json['member_services'] as List).map(
              (x) => MemberService.fromJson(x as Map<String, dynamic>),
            ),
          );
    memberLocker = json['member_locker'] == null
        ? []
        : List<MemberLocker>.from(
            (json['member_locker'] as List).map(
              (x) => MemberLocker.fromJson(x as Map<String, dynamic>),
            ),
          );
    roomRentValue = calculateRoomRentValue();
    additionalInfo = json['additional_info'] is Map<String, dynamic> ? AdditionalInfoData.fromJson(
      json['additional_info'] as Map<String, dynamic>,
    ) : null;
  }

  Map<String, dynamic> toJson() {
    return {
      'insured_id': insuredId,
      'insured_ref': insuredRef,
      'gender': gender,
      'dob': dob,
      'name': name,
      'relationship': relationship,
      'relation': relation,
      'ecard_link': ecardLink,
      'age': age,
      'uhid': uhid,
      'covers': covers?.map((x) => x.toJson()).toList(),
      'categorised_covers': categorisedCovers?.map((x) => x.toJson()).toList(),
      'risk_start_date': riskStartDate,
      'risk_end_date': riskEndDate,
      'policies': policies?.map((x) => x.toJson()).toList(),
      'proposals': proposals?.map((x) => x.toJson()).toList(),
      'member_services': memberServices?.map((x) => x.toJson()).toList(),
      'member_locker': memberLocker?.map((x) => x.toJson()).toList(),
      'room_rent_value': roomRentValue,
      'additional_info': additionalInfo?.toJson(),
    };
  }

  String calculateRoomRentValue() {
    var showRoomRent = true;

    policies?.forEach((element) {
      if (element.isAarogyaSanjeevaniPolicy || element.isGMCPolicy) {
        showRoomRent = false;
      }
    });

    final roomRentCover = covers?.firstWhere(
      (cover) => cover.coverId == 'room_rent',
      orElse: () => Covers(coverId: ''),
    );
    return ((roomRentCover?.coverId == 'room_rent') && showRoomRent)
        ? 'No limits'
        : '';
  }

  bool get hasOnlyUpcomingPolicies {
    var hasOnlyUpcomingPolicies = false;
    if (policies?.isNotEmpty ?? false) {
      hasOnlyUpcomingPolicies = true;
    }
    policies?.forEach((policy) {
      if (policy.getHealthPolicyState != HealthPolicyStates.UPCOMING) {
        hasOnlyUpcomingPolicies = false;
      }
    });
    return hasOnlyUpcomingPolicies;
  }

  bool get hasOnlyExpiredPolicies {
    var hasOnlyExpiredPolicies = false;
    if (policies?.isNotEmpty ?? false) {
      hasOnlyExpiredPolicies = true;
    }
    policies?.forEach((policy) {
      if (policy.getHealthPolicyState != HealthPolicyStates.EXPIRED) {
        hasOnlyExpiredPolicies = false;
      }
    });
    return hasOnlyExpiredPolicies;
  }

  bool get hasOnlyDeactivatedPolicies {
    var hasOnlyDeactivatedPolicies = false;
    if (policies?.isNotEmpty ?? false) {
      hasOnlyDeactivatedPolicies = true;
    }
    policies?.forEach((policy) {
      if (policy.getHealthPolicyState != HealthPolicyStates.DEACTIVATED) {
        hasOnlyDeactivatedPolicies = false;
      }
    });
    return hasOnlyDeactivatedPolicies;
  }

  bool get hasOnlyASPPolicies {
    return policies.isNotNullOrEmpty ? policies!.every((element) => element.isAarogyaSanjeevaniPolicy) : false;
  }

  bool get showGmcUpsellBanner {
    var gmcUpsellBanner = true;
    if (policies.isNullOrEmpty) return false;
    policies?.forEach((policy) {
      if (policy.isAarogyaSanjeevaniPolicy ||
          policy.productType.equalsIgnoreCase('retail')) {
        final policyState = policy.getHealthPolicyState;
        if (policyState == HealthPolicyStates.EXPIRED) {
          gmcUpsellBanner = true;
        } else {
          gmcUpsellBanner = false;
        }
      }
    });
    return gmcUpsellBanner;
  }

  bool get hasOnlyGMCPolicies {
    var hasOnlyGMCPolicies = true;
    if (policies.isNullOrEmpty) {
      hasOnlyGMCPolicies = false;
    }
    policies?.forEach((policy) {
      if (policy.productType != HealthConstants.ENTERPRISE_PRODUCT) {
        hasOnlyGMCPolicies = false;
      }
    });
    return hasOnlyGMCPolicies;
  }

  (String?, String?) getUhidsAndName() {
    final List<String> uhids = [];

    policies?.forEach((policy) {
      policy.insureds?.forEach((element) {
        if (name == element.name &&
            relationship == element.relationship &&
            dob == element.dob) {
          String? uhid = element.uhid;
          if (uhid != null && !uhids.contains(uhid)) {
            uhids.add(uhid);
          }
        }
      });
    });

    String? uhidString = uhids.isNotEmpty ? uhids.join(',') : null;
    return (uhidString, name);
  }

  void setRequiredProperties() {
    retailPoliciesCount = 0;
    gmcPoliciesCount = 0;
    aspPoliciesCount = 0;
    policies?.forEach((policy) {
      if (policy.productType == HealthConstants.ENTERPRISE_PRODUCT) {
        gmcPoliciesCount++;
        allRetailPolicies = false;
      } else if (policy.productType == HealthConstants.RETAIL_PRODUCT) {
        retailPoliciesCount++;
      } else {
        allRetailPolicies = false;
        aspPoliciesCount++;
      }
    });
  }

  String? insuredId;
  String? insuredRef;
  String? gender;
  String? dob;
  String? name;
  String? relationship;
  String? relation;
  String? ecardLink;
  int? age;
  String? uhid;
  List<Covers>? covers;
  List<CategorisedCover>? categorisedCovers;
  String? riskStartDate;
  String? riskEndDate;
  List<HealthPolicyDetailsModel>? policies;
  List<HealthPolicyDetailsModel>? proposals;
  List<MemberService>? memberServices;
  List<MemberLocker>? memberLocker;
  String? roomRentValue;
  AdditionalInfoData? additionalInfo;

  int retailPoliciesCount = 0;
  int gmcPoliciesCount = 0;
  int aspPoliciesCount = 0;
  bool hasNoWaitingPeriodCover = false;
  bool allRetailPolicies = true;
}

class MemberLocker {
  MemberLocker({
    this.isEnabled,
    this.lockerType,
    this.redirectUrl,
    this.title,
    this.iconUrl,
    this.serviceType,
    this.subTitle,
    this.action,
  });

  factory MemberLocker.fromJson(Map<String, dynamic> json) => MemberLocker(
        isEnabled: json['is_enabled'] as bool?,
        lockerType: json['locker_type'] as String?,
        redirectUrl: json['redirect_url'] as String?,
        title: json['title'] as String?,
        iconUrl: json['icon_url'] as String?,
        serviceType: json['service_type'] as String?,
        subTitle: json['sub_title'] as String?,
        action: json['action'] as String?,
      );

  Map<String, dynamic> toJson() {
    return {
      'redirect_url': redirectUrl,
      'title': title,
      'icon_url': iconUrl,
      'service_type': serviceType,
      'sub_title': subTitle,
      'action': action,
      'enabled': isEnabled,
    };
  }

  final bool? isEnabled;
  final String? lockerType;
  final String? redirectUrl;
  final String? title;
  final String? iconUrl;
  final String? serviceType;
  final String? subTitle;
  final String? action;
}

class MemberService {
  MemberService({
    this.redirectUrl,
    this.title,
    this.iconUrl,
    this.serviceType,
    this.subTitle,
    this.action,
    this.enabled = true,
    this.applicablePolicies,
  });

  factory MemberService.fromJson(Map<String, dynamic> json) => MemberService(
        redirectUrl: json['redirect_url'] as String?,
        title: json['title'] as String?,
        iconUrl: json['icon_url'] as String?,
        serviceType: json['service_type'] as String?,
        subTitle: json['sub_title'] as String?,
        action: json['action'] as String?,
        enabled: json['enabled'] as bool? ?? true,
        applicablePolicies: json['applicable_policies'] == null
            ? []
            : List<ApplicablePolicies>.from(
                (json['applicable_policies'] as List).map(
                  (x) => ApplicablePolicies.fromJson(x as Map<String, dynamic>),
                ),
              ),
      );

  Map<String, dynamic> toJson() {
    return {
      'redirect_url': redirectUrl,
      'title': title,
      'icon_url': iconUrl,
      'service_type': serviceType,
      'sub_title': subTitle,
      'action': action,
      'enabled': enabled,
    };
  }

  final String? redirectUrl;
  final String? title;
  final String? iconUrl;
  final String? serviceType;
  final String? subTitle;
  final String? action;
  final bool enabled;
  final List<ApplicablePolicies>? applicablePolicies;
}

class AdditionalInfoData {
  AdditionalInfoData({
    this.policyCards,
  });

  factory AdditionalInfoData.fromJson(Map<String, dynamic> json) =>
      AdditionalInfoData(
        policyCards: (json['asset_policy_cards'] != null &&
                json['asset_policy_cards'] is List)
            ? List<AssetPolicyCard>.from(
                (json['asset_policy_cards'] as List).map(
                  (x) {
                    return (x is Map<String, dynamic>)
                        ? AssetPolicyCard.fromJson(
                            x,
                          )
                        : null;
                  },
                ),
              )
            : [],
      );

  Map<String, dynamic> toJson() {
    return {
      'asset_policy_cards': policyCards,
    };
  }

  final List<AssetPolicyCard>? policyCards;
}

class AssetPolicyCard {
  AssetPolicyCard({
    this.policyId,
    this.policyNumber,
    this.title,
    this.sumInsured,
    this.policyType,
    this.productType,
  });

  factory AssetPolicyCard.fromJson(Map<String, dynamic> json) =>
      AssetPolicyCard(
        policyId: json['policy_id'] as String?,
        policyNumber: json['policy_number'] as String?,
        title: json['title'] as String?,
        sumInsured: json['si_string'] as String?,
        policyType: json['policy_type'] as String?,
        productType: json['product_type'] as String?,
      );

  Map<String, dynamic> toJson() {
    return {
      'policy_id': policyId,
      'policy_number': policyNumber,
      'title': title,
      'si_string': sumInsured,
      'policy_type': policyType,
      'product_type': productType,
    };
  }

  final String? policyId;
  final String? policyNumber;
  final String? sumInsured;
  final String? title;
  final String? policyType;
  final String? productType;
}
