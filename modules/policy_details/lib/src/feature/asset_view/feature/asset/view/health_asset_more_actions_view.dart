import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/asset_view/asset_view_shimmer/asset_view_shimmers.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/pdp_buy_journey_bottomsheet.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/pdp_full_width_cta.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/pdp_help_widget.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/pdp_more_actions_widget.dart';
import 'package:design_module/design_module.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:policy_details/policy_details.dart';
import 'package:utilities/visibility_detector/src/visibility_detector.dart';

class HealthAssetMoreActionsView extends StatelessWidget {
  const HealthAssetMoreActionsView({required this.bloc, super.key});

  final HealthAssetDetailsCubit bloc;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HealthAssetDetailsCubit, HealthAssetDetailsState>(
      buildWhen: (prev, curr) =>
      curr is HealthHelpDataLoaded ||
          curr is HealthHelpDataLoading ||
          curr is HealthMoreActionsLoaded ||
          curr is HealthBuyJourneyCta ||
          curr is HealthAssetDetailsLoading,
      builder: (context, state) {
        Widget child;

        if (state is HealthHelpDataLoaded) {
          child = HelpWidget(
            hasLifePolicy: state.hasLifePolicy,
            healthProposals: state.healthProposals,
            tapEvent: () => bloc.handleAssetPageTapEvent('get_help'),
          );
        } else if (state is HealthMoreActionsLoaded) {
          child = MoreActionsWidget(
            moreActionsList: state.moreActionsList,
            hasLifePolicy: state.hasLifePolicy,
            tapEvent: (value) {
              bloc.handleMoreActionsTapEvent(value);
            },
            fromPage: 'Asset_View',
            insured: bloc.selectedInsured,
            memberData: bloc.memberData,
          );
        } else if (state is HealthHelpDataLoading ||
            state is HealthAssetDetailsLoading) {
          child = const MoreActionsShimmer();
        } else if (state is HealthBuyJourneyCta) {
          child = Column(
            children: [
              MoreActionsWidget(
                moreActionsList: state.moreActionsList,
                hasLifePolicy: bloc.hasLifePolicy,
                tapEvent: (value) {
                  bloc.handleMoreActionsTapEvent(value);
                },
                fromPage: 'Asset_View',
                insured: bloc.selectedInsured,
                memberData: bloc.memberData,
              ),
              const SizedBox(height: 10),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 32),
                width: MediaQuery.of(context).size.width,
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0x0036354c).withOpacity(0.06),
                      offset: const Offset(0, -4),
                      blurRadius: 8,
                      spreadRadius: -2,
                    ),
                  ],
                ),
                child: HealthFullWidthCTA(
                  text: 'Get started',
                  onTap: () {
                    bloc
                      ..handleAssetPageTapEvent('prospect_get_started')
                      ..handleBottomSheetViewEvent('prospect_get_started');
                    context.showAckoModalBottomSheet(
                      backgroundColor: colorFFFFFF,
                      child: InsureSelectionWidget(
                        tapEvent: bloc.handleBottomSheetTapEvent,
                      ),
                    );
                  },
                ),
              ),
            ],
          );
        } else {
          child = const SizedBox.shrink();
        }
        return child;
      },
    );

  }
}
