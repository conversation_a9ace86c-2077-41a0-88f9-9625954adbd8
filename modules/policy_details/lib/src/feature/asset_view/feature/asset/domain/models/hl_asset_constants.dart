class HealthAssetConstants {
  static const String name = 'name';
  static const String gender = 'gender';
  static const String relationship = 'relationship';
  static const String dob = 'dob';
  static const String healthCoverage = 'Health Coverage';
  static const String yourCoverages = 'Your coverages';
  static const String yourHealthCoverages = 'Your health coverages';
  static const String yourLifeCoverages = 'Your life coverages';
  static const String raiseAClaim = 'Raise a claim';
  static const String sumInsured = 'Sum insured';
  static const String deductible = 'Deductible';
  static const String roomRent = 'Room Rent';
  static const String copay = 'Copay';
  static const String routeAnimationType = 'route_animation_type';
  static const String policyId = 'policyId';
  static const String proposalId = 'proposalId';
  static const String policyStartDate = 'policyStartDate';
  static const String policyName = 'policyName';
  static const String policyStatus = 'policyStatus';
  static const String policyKeyCovers = 'policyKeyCovers';
  static const String isExpired = 'isExpired';
  static const String waitingInfo = 'waitingInfo';
  static const String noWaitingInfo = 'noWaitingInfo';
  static const String policyInfoMap = 'policyInfoMap';
  static const String upcomingStatus = 'upcomingStatus';
  static const String selectedPolicyHolder = 'selectedPolicyHolder';
  static const String productInfo = 'productInfo';
  static const String policyNumber = 'policy_number';
  static const String defaultTab = 'defaultTab';
  static const String raiseClaim = 'raise_claim';
  static const String registerReimbursementClaim =
      'register_reimbursement_claim';
  static const String planCard = 'plan_card';
  static const String upcomingPolicyText = 'Upcoming new policy will be starting from ';
}

class LifeAssetConstants {
  static const payoutTitlePrefix = 'Payout of ₹';
  static const payoutTitleSuffix = ' in case of your death';
  static const subtitlePrefix = 'with ';
  static const defaultStatusPrefix = "Until you're ";
  static const editPolicyServiceId = 'Edit Policy';
  static const emptyString = '';
  static const expire = 'expire';
  static const deactivate = 'deactiv';
  static const editPolicy = 'Edit Policy';
}
