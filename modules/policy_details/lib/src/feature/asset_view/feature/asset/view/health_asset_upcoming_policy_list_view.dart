import 'package:acko_flutter/common/view/router.dart';
import 'package:acko_flutter/framework/pdp/health/common/pdp_utils.dart';
import 'package:acko_flutter/framework/pdp/health/models/health_policies_basic_details.dart';
import 'package:acko_flutter/framework/pdp/health/models/ui_models/policy_card_model.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/pdp_new_upcoming_policy_card.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/pdp_upcoming_policy_cards.dart';
import 'package:acko_flutter/util/health/health_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:policy_details/policy_details.dart';
import 'package:policy_details/src/core/util/extensions.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/domain/models/hl_asset_constants.dart';
import 'package:utilities/constants/constants.dart';

class HealthAssetUpcomingPolicyListView extends StatelessWidget {
  HealthAssetUpcomingPolicyListView({super.key, this.showV10UI = false});

  bool showV10UI = false;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<HealthAssetDetailsCubit, HealthAssetDetailsState>(
      buildWhen: (prev, curr) =>
          curr is HealthAssetDetailsLoaded || curr is HealthAssetDetailsLoading,
      builder: (context, state) {
        if (state is HealthAssetDetailsLoaded) {
          final selectedAsset = state.selectedHealthAsset;
          final person = HealthPolicyHolder(
            name: selectedAsset['name'] as String?,
            gender: selectedAsset['gender'] as String?,
            relationship: selectedAsset['relationship'] as String?,
          );
          final upcomingPolicyCards = getUpcomingPolicyCards(
            state.healthPolicyList,
            person,
            state.insured?.roomRentValue,
            context,
            hasNoWaitingInfoCover:
                state.insured?.hasNoWaitingPeriodCover ?? false,
          );
          if (upcomingPolicyCards.isNotNullOrEmpty) {
            return UpcomingPolicyCardListView(
              childrenWidget: upcomingPolicyCards,
            );
          } else {
            return const SizedBox.shrink();
          }
        } else {
          return const SizedBox.shrink();
        }
      },
    );
  }

  List<Widget> getUpcomingPolicyCards(
    List<HealthPolicyDetailsModel>? allAssetPolicies,
    HealthPolicyHolder? person,
    String? roomRentValue,
    BuildContext context, {
    required bool hasNoWaitingInfoCover,
  }) {
    final upcomingPolicyCards = <Widget>[];

    if (allAssetPolicies.isNullOrEmpty) return upcomingPolicyCards;

    allAssetPolicies?.forEach((element) {
      if (element.isUpcoming) {
        upcomingPolicyCards.add(
          UpcomingPolicyCardUI(
            policyData: element,
            onTap: (policyData) {
              context
                  .read<HealthAssetDetailsCubit>()
                  .handleAssetPageTapEvent('upcoming_plan_card');
              navigateToCoveragesScreen(
                policyData,
                person,
                policyData?.productType ?? '',
                policyData?.policyStartDate,
                roomRentValue,
                context,
                hasNoWaitingPeriodCover: hasNoWaitingInfoCover,
              );
            },
            showAppv10UI: showV10UI,
          ),
        );
      }
    });

    return upcomingPolicyCards;
  }

  void navigateToCoveragesScreen(
    HealthPolicyDetailsModel? element,
    HealthPolicyHolder? person,
    String productInfo,
    String? upcomingPolicyStartDate,
    String? roomRentValue,
    BuildContext context, {
    required bool hasNoWaitingPeriodCover,
  }) {
    if (element == null) return;
    String? policyId;
    String? policyNumber;
    String? proposalId;
    String? policyName;
    String? policyStatus;
    String? policyStartDate;
    String? sumInsured;
    String? deductibles;
    String? copay;
    String? roomRent;
    String? paymentUrl;

    /// fetch the policy id of member or base policy
    policyId = element.policyId;
    policyNumber = element.policyNumber;
    proposalId = element.proposalId;
    policyName = element.planName;
    policyStartDate = upcomingPolicyStartDate;
    policyStatus = element.policyEndDate ?? '';

    sumInsured = (element.sumInsured?.displayValue ?? '') +
        (element.topup?.displayValue ?? '');
    deductibles = element.deductibles?.displayValue;
    roomRent = roomRentValue;
    copay = element.copay?.displayValue;

    final resultMap = <String, String>{
      HealthAssetConstants.sumInsured: element.sumInsured?.displayValue != null
          ? '${element.sumInsured?.displayValue}'
          : '',
      if (deductibles.isNotNullOrEmpty && deductibles != '0')
        HealthAssetConstants.deductible: deductibles!,
      if (roomRent.isNotNullOrEmpty && roomRent != '0')
        HealthAssetConstants.roomRent: roomRent ?? '',
      if (copay.isNotNullOrEmpty && copay != '0')
        HealthAssetConstants.copay: '$copay%',
    };

    resultMap.removeWhere((key, value) => value.isNullOrEmpty);

    paymentUrl = element.premiumDetail?.paymentSchedulePageUrl;

    final keyCoversList = <CardCoverModel>[];

    WaitingInfoModel? waitingInfo;
    WaitingInfoModel? noWaitingInfo;

    final keyCoversObj = element.keyCoversObj;
    if (keyCoversObj != null &&
        keyCoversObj.keyCovers != null &&
        keyCoversObj.keyCovers!.isNotNullOrEmpty) {
      for (final cover in keyCoversObj.keyCovers!) {
        var subtitle = cover.subTitle;
        subtitle = subtitle!.replaceAll(r'\n', '\n');
        keyCoversList.add(
          CardCoverModel(
            coverTitle: cover.title,
            coverDesc: subtitle,
            coverImg: cover.iconUrl,
          ),
        );
      }

      final isGMC = element.productType
          .containsIgnoreCase(HealthConstants.ENTERPRISE_PRODUCT);

      if (!element.isExpiredPolicy) {
        if (keyCoversObj.waitingInfo != null) {
          waitingInfo = WaitingInfoModel(
            imageUrl: keyCoversObj.waitingInfo?.iconUrl,
            description: keyCoversObj.waitingInfo?.description,
            bgColor: keyCoversObj.waitingInfo?.backgroundColor,
          );
        } else if (keyCoversObj.nonWaitingInfo != null) {
          noWaitingInfo = WaitingInfoModel(
            imageUrl: keyCoversObj.nonWaitingInfo?.iconUrl,
            description: keyCoversObj.nonWaitingInfo?.description,
            bgColor: keyCoversObj.nonWaitingInfo?.backgroundColor,
          );
        }
      }
    }

    var upcomingStatus = '';

    if (element.isUpcoming) {
      upcomingStatus =
          '${HealthAssetConstants.upcomingPolicyText}${element.formattedStartDateDDMMMYY}.';
    }

    Navigator.pushNamed(
      context,
      Routes.HEALTH_PDP_COVERAGES_SCREEN,
      arguments: {
        HealthAssetConstants.routeAnimationType: RouteAnimationType.fade_anim,
        HealthAssetConstants.policyId: policyId,
        HealthAssetConstants.proposalId: proposalId,
        HealthAssetConstants.policyStartDate: policyStartDate,
        HealthAssetConstants.policyName: policyName,
        HealthAssetConstants.policyStatus: policyStatus,
        HealthAssetConstants.policyKeyCovers: keyCoversList,
        HealthAssetConstants.isExpired: element.isExpiredPolicy,
        HealthAssetConstants.waitingInfo: waitingInfo,
        HealthAssetConstants.noWaitingInfo: noWaitingInfo,
        HealthAssetConstants.policyInfoMap: resultMap,
        HealthAssetConstants.upcomingStatus: upcomingStatus,
        HealthAssetConstants.selectedPolicyHolder: person,
        HealthAssetConstants.productInfo: productInfo,
        HealthAssetConstants.policyNumber: policyNumber,
      },
    );
  }

  String calculateFullDeductibles(String? deductible) {
    final deductibleIntValue = int.tryParse(deductible ?? '');
    final result = HealthPDPUtils.formatIndianCurrency(deductibleIntValue ?? 0);
    return result;
  }
}
