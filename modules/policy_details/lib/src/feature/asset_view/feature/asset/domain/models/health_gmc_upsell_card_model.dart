import 'package:acko_flutter/feature/vas/gmc_upsell/model/gmc_upsell_dto.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/domain/models/hl_cards_data.dart';

class UpsellCardModels {
  HealthGmcUpsellCardModel? healthGmcUpsellCardModel;
  PWILOCardsData? pwiLoCardsData;
  PWILOCardsData? lifePwiLoCardsData;


  UpsellCardModels({this.healthGmcUpsellCardModel, this.pwiLoCardsData, this.lifePwiLoCardsData});
}

class HealthGmcUpsellCardModel {
  HealthGmcUpsellCardModel({
    required this.showUpsellCard,
    this.gmcUpsellBannerData,
    this.sumInsured,
    this.lifeUpsellBannerData,
    this.gmcUpsellCards
  });

  CardData? gmcUpsellBannerData;
  List<CardData>? gmcUpsellCards;
  String? sumInsured;
  bool showUpsellCard;
  LifeUpsellCardData? lifeUpsellBannerData;
}
