import 'package:acko_flutter/common/util/color_constants.dart';
import 'package:acko_flutter/common/util/scroll_fold_mixin.dart';
import 'package:acko_flutter/common/util/strings.dart';
import 'package:acko_flutter/feature/acko_services/ui/acko_service_loading_screen.dart';
import 'package:acko_flutter/feature/acko_services/ui/acko_services_initial_screen.dart';
import 'package:acko_flutter/framework/pdp/health/models/enums.dart';
import 'package:acko_flutter/framework/pdp/health/models/members_list_model.dart';
import 'package:acko_flutter/framework/pdp/health/view/screens/asset_member_selection_list_view.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/asset_view/AssetCircularWidget.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/asset_view/asset_view_shimmer/health_coverages_shimmer.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/life_asset/life_only_services.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/life_asset/life_share_nominee_card.dart';
import 'package:acko_flutter/framework/pdp/health/view/widgets/pdp_appbar.dart';
import 'package:acko_flutter/util/Utility.dart';
import 'package:acko_flutter/util/health/health_constants.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:policy_details/src/core/util/extensions.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/cubit/health_asset_details_cubit.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/view/asset_selection_carousel.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/view/health_asset_aabha_details_view.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/view/health_asset_education_view.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/view/health_asset_header_view.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/view/health_asset_hl_policies_list_view.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/view/health_asset_more_actions_view.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/view/health_asset_notifications_view.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/view/health_asset_policy_status_view.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/view/health_asset_pre_policy_edits_card_view.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/view/health_asset_upcoming_policy_list_view.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/view/health_policies_list_view.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/view/life_asset_policy_list_view.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/view/life_policies_list_view.dart';
import 'package:policy_details/src/feature/asset_view/feature/asset/view/upsell_cards.dart';
import 'package:sdui/sdui.dart';
import 'package:utilities/constants/constants.dart';
import 'package:utilities/state_provider/StateProvider.dart';

class HealthAssetDetailsView extends StatefulWidget {
  const HealthAssetDetailsView({super.key});

  @override
  State<HealthAssetDetailsView> createState() => _HealthAssetDetailsViewState();
}

class _HealthAssetDetailsViewState extends State<HealthAssetDetailsView> with ScrollFoldMixin
    implements StateListener {
  late HealthAssetDetailsCubit _bloc;
  final StateProvider _stateProvider = StateProvider();
  late CommonDataStoreBloc _commonDataStoreBloc;
  FamilyMembersData? familyMembersData;
  bool isLoadingIndicatorDisplayed = false;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _stateProvider.subscribe(this);
    _commonDataStoreBloc = BlocProvider.of(context);
    _bloc = BlocProvider.of<HealthAssetDetailsCubit>(context);
    familyMembersData = FamilyMembersData.fromJson(
      _commonDataStoreBloc
          .getPositionedDataFromMap('health.family.family_members_list'),
    );
    _bloc.loadAssetDetails(familyMembersData);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      addScrollFoldListener(_scrollController, 'family');
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _stateProvider.dispose(this);
    super.dispose();
  }

  Future<void> _refreshPage() async {
    await _bloc.refreshBloc(familyMembersData);
  }

  @override
  void onStateChanged(ObserverState state, {data}) {
    if (state == ObserverState.REFRESH_HEALTH_ASSET_VIEW) {
      _refreshPage();
    }
  }

  void _onSwipe(DragEndDetails details) {
    if (details.primaryVelocity == null) {
      return;
    }
    final familyMembersCount = familyMembersData?.membersList?.length ?? 0;
    Map<String, dynamic>? assetFilter;
    var indexOfCurrentSelectedMember = familyMembersData?.membersList
            ?.indexWhere(
          (member) =>
              member.title.equalsIgnoreCase(_bloc.filter['name'] as String?) &&
              member.subtitle.equalsIgnoreCase(
                _bloc.filter['relationship'] as String?,
              ) &&
              member.dob == _bloc.filter['dob'],
        ) ??
        0;
    if (details.primaryVelocity! < 0) {
      // Swiping left to show next member
      if (indexOfCurrentSelectedMember < familyMembersCount - 1) {
        indexOfCurrentSelectedMember++;
      }
    } else if (details.primaryVelocity! > 0) {
      // Swiping right to show previous member
      if (indexOfCurrentSelectedMember > 0) {
        indexOfCurrentSelectedMember--;
      }
    }
    final selectedFamilyMember =
        familyMembersData?.membersList?[indexOfCurrentSelectedMember] ??
            familyMembersData?.membersList?[0];
    assetFilter = selectedFamilyMember?.toMap();
    _bloc.loadAssetDetails(familyMembersData, assetFilter: assetFilter);
    _stateProvider.notify(
      ObserverState.REFRESH_HEALTH_MEMBER_CAROUSEL,
      data: assetFilter,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: BlocConsumer<CommonDataStoreBloc, CommonDataStoreBlocStates>(
        listener: (context, state) {
          final data = _commonDataStoreBloc
              .getPositionedDataFromMap('health.family.family_members_list');
          final newFamilyMemberData = FamilyMembersData.fromJson(data);

          if (!listEquals(
              newFamilyMemberData.membersList, familyMembersData?.membersList)) {
            familyMembersData = newFamilyMemberData;
            _bloc.refreshBloc(
              familyMembersData,
              shouldShowLoadingIndicator: false,
            );
            return;
          }

          if (familyMembersData?.membersList?.isNullOrEmpty ?? true) {
            if (!isLoadingIndicatorDisplayed) {
              _bloc.showLoadingIndicator();
            }
            familyMembersData = FamilyMembersData.fromJson(data);
            if (data != null) {
              _bloc.refreshBloc(
                familyMembersData,
                shouldShowLoadingIndicator: false,
              );
            }
          }
        },
        listenWhen: (prev, state) => state is LobDataUpdated,
        builder: (context, state) {
          if (familyMembersData == null) {
            if (_commonDataStoreBloc.getPositionedDataFromMap('health') !=
                null) {
              /// Health api loaded or failed or members api failed handle error case
              return AckoServicesIntiailScreen(
                isOutlinedButton: true,
                title: 'Something went wrong',
                imgUrl: Util.getAssetImage(assetName: 'ic_bucket_drop.svg'),
                btnTitle: try_again,
                onTap: () {
                  _bloc.refreshBloc(familyMembersData);
                },
              );
            } else {
              return _loadingState();
            }
          } else {
            return RefreshIndicator(
              onRefresh: _refreshPage,
              child: _setContentView(),
            );
          }
        },
      ),
    );
  }

  Widget _setContentView() {
    return BlocBuilder<HealthAssetDetailsCubit, HealthAssetDetailsState>(
      buildWhen: (prev, curr) =>
          curr is HealthAssetDetailsLoaded ||
          curr is HealthAssetDetailsLoading ||
          curr is HealthAssetDetailsError,
      builder: (context, state) {
        if (state is HealthAssetDetailsError) {
          return AckoServicesIntiailScreen(
            isOutlinedButton: true,
            title: 'Something went wrong',
            imgUrl: Util.getAssetImage(assetName: 'ic_bucket_drop.svg'),
            btnTitle: try_again,
            onTap: () {
              _bloc.refreshBloc(familyMembersData);
            },
          );
        } else {
          return GestureDetector(
            onHorizontalDragEnd: _onSwipe,
            child: SingleChildScrollView(
              controller: _scrollController,
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom + 32,
              ),
              child: Column(
                children: [
                  if (_bloc.showV10UI)
                    ClipRRect(
                        borderRadius: const BorderRadius.only(
                          bottomLeft: Radius.circular(16),
                          bottomRight: Radius.circular(16),
                        ),
                        child: getDarkWidgetContainer(state))
                  else
                    getDarkWidgetContainer(state),
                  if (state is HealthAssetDetailsLoaded &&
                      _bloc.policyTypes.length == 1 &&
                      _bloc.policyTypes.first == PolicyTypeEnum.life) ...[
                    const LifeOnlyServices(),
                  ] else ...[
                    if (_bloc.showV10UI) ...[
                      const SizedBox(height: 32),
                      AnimatedCrossFade(
                        duration: const Duration(milliseconds: 200),
                        firstChild: HealthCoveragesShimmer(showV10UI: true),
                        secondChild: const HealthPoliciesListView(),
                        crossFadeState: state is HealthAssetDetailsLoaded
                            ? CrossFadeState.showSecond
                            : CrossFadeState.showFirst,
                      ),
                      HealthAssetUpcomingPolicyListView(showV10UI: false),
                      const LifePoliciesListView(),
                    ],
                    HealthAssetPolicyStatusView(bloc: _bloc),
                    const HealthAssetEducationalDetailsView(),
                    if (_bloc.policyTypes.contains(PolicyTypeEnum.life) &&
                        (_bloc.lifeMyAccountModel?.allPolicies ?? []).length >=
                            1 &&
                        !_bloc.isLifeReinstateOrGracePeriod) ...[
                      const SizedBox(
                        height: 40,
                      ),
                      const LifeShareNomineeCard(),
                      const SizedBox(
                        height: 20,
                      ),
                    ],
                  ],
                  HealthAssetMoreActionsView(bloc: _bloc),
                  const SizedBox(height: 40,)
                ],
              ),
            ),
          );
        }
      },
    );
  }

  Container getDarkWidgetContainer(HealthAssetDetailsState state) {
    return Container(
      color: color121212,
      child: Column(
        children: getDarkWidgetsBasedOnv10Flag(state),
      ),
    );
  }

  List<Widget> getDarkWidgetsBasedOnv10Flag(HealthAssetDetailsState state) {
    List<Widget> widgets = [];
    if (_bloc.showV10UI) {
      widgets = [
        headerContainer(state),
      ];
    } else {
      widgets = [
        if (state is HealthAssetDetailsLoading && !_bloc.isFirstTimeLoad) ...[
          const HealthPDPAppBar(),
        ] else ...[
          HealthPDPAppBar(
            selectedHealthAsset: _bloc.filter,
            allHealthAssets: _bloc.allHealthAssets,
            onAssetSelection: (Map<dynamic, dynamic> selectedAsset) {
              _bloc.loadAssetDetails(
                familyMembersData,
                assetFilter: selectedAsset,
              );
            },
          ),
        ],
        headerContainer(state),
        const HealthAssetNotificationView(),
        AnimatedCrossFade(
          duration: const Duration(milliseconds: 500),
          firstChild: HealthCoveragesShimmer(),
          secondChild: const HealthAssetPoliciesListView(),
          crossFadeState: state is HealthAssetDetailsLoaded
              ? CrossFadeState.showSecond
              : CrossFadeState.showFirst,
        ),
        HealthAssetUpcomingPolicyListView(showV10UI: false),
        const LifeAssetPolicyListView(),
        const UpsellCards(),
        const AssetPrePolicyEditBanner(),
      ];
    }
    return widgets;
  }

  Widget headerContainer(HealthAssetDetailsState state) {
    return Container(
      width: double.infinity,
      decoration: _bloc.showV10UI
          ? const BoxDecoration(
              gradient: RadialGradient(
                colors: [color560098, color060606],
                center: Alignment.topCenter,
                radius: 0.5,
              ),
            )
          : const BoxDecoration(
              image: DecorationImage(
                  fit: BoxFit.fill,
                  image: NetworkImage(HealthConstants.HEALTH_ASSET_VIEW_BG)),
            ),
      child: Column(
        children: [
          const Padding(
            padding: EdgeInsets.only(top: 40),
            child: HealthAssetHeaderView(),
          ),
          const HealthAssetAbhaDetailsView(),
          if (_bloc.showV10UI) ...[
            getAssetSelectionWidget(_bloc.allHealthAssets, _bloc.filter, state),
            const SizedBox(
              height: 20,
            ),
            const HealthAssetNotificationView(),
            const UpsellCards(),
            const AssetPrePolicyEditBanner(),
          ]
        ],
      ),
    );
  }

  Widget getAssetSelectionWidget(List<Map>? allHealthAssets,
      Map? selectedHealthAsset, HealthAssetDetailsState state) {
    if (state is HealthAssetDetailsLoaded) {
      return Row(
          mainAxisAlignment: (allHealthAssets?.length ?? 1) > 1
              ? MainAxisAlignment.start
              : MainAxisAlignment.center,
          children: [
            if (allHealthAssets.isNotNullOrEmpty &&
                selectedHealthAsset != null) ...[
              AssetMemberSelectionCarouselView(
                defaultId: selectedHealthAsset['name'] as String? ?? '',
                circularWidgets: getAssetCircularWidgets(allHealthAssets!),
                onAssetSelection: (selectedAsset) {
                  Map? selectedAssetMap = allHealthAssets
                      ?.where((element) => element['name'] == selectedAsset)
                      .firstOrNull;
                  if (selectedAssetMap != null) {
                    _bloc.loadAssetDetails(
                      familyMembersData,
                      assetFilter: selectedAssetMap,
                    );
                  }
                },
              ),
              SizedBox(
                width: 8,
              ),
              InkWell(
                onTap: () {
                  _bloc.handleTapEventForAddFamily();
                  StateProvider _stateProvider = StateProvider();
                  Navigator.pushNamed(
                          context, Routes.PROFILE_FAMILY_MEMBER_DETAILED_VIEW)
                      .then((value) {
                    if (value is String &&
                        value.contains('Successfully added')) {
                      _stateProvider.notify(ObserverState.MyAccount_Refresh);
                    }
                  });
                },
                child: SDUIImage(
                  imageUrl: 'assets/images/add_member.svg',
                  width: 40,
                  height: 40,
                ),
              )
            ]
          ]);
    } else {
      return const AssetMemberCarouselShimmer();
    }
  }

  List<AssetCircularWidget> getAssetCircularWidgets(List<Map> allAssets) {
    var circularWidgets = <AssetCircularWidget>[];
    allAssets.forEach((element) {
      circularWidgets.add(AssetCircularWidget(
        id: element['name'] as String? ?? '',
        widget: Center(
          child: SDUIText(
            value: Util.getInitials(element['name'] as String? ?? '',
                    needWords: false) as String? ??
                '',
            textStyle: 'lXSmall',
            textColor: colorFFFFFF,
          ),
        ),
      ));
    });
    return circularWidgets;
  }

  Widget _loadingState() => SizedBox(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        child: Center(child: AckoServiceLoadingScreen()),
      );
}
