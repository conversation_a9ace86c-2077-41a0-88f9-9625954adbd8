import 'base.dart';

abstract class ErrorEvent extends BaseLoggerEvent {
  StackTrace? stackTrace;
  final ErrorPriority errorPriority;
  final String errorMessage;
  final String exceptionType;
  final dynamic error; /// It can be error/exception firebase logger also uses dynamic type

  ErrorEvent(
      {required this.errorPriority,
        required this.errorMessage,
        required this.exceptionType,
        this.error,
        this.stackTrace,
        super.data,
        super.page,
        super.journey,
      });

  @override
  Map<String, dynamic> getData() {
    final baseEventProperties = super.getData();
    final errorEventProperties = {
      "stackTrace": stackTrace.toString(),
      "errorPriority": errorPriority.name,
      "errorMessage": errorMessage,
      "exceptionType": exceptionType,
    };
    final Map<String, dynamic> combinedProperties = {
      ...baseEventProperties,
      ...errorEventProperties,
      ...getEventSpecificData()
    };

    combinedProperties.removeWhere((key, value) => value == null);
    return combinedProperties;
  }

  Map<String, dynamic> getEventSpecificData();
}



enum ErrorPriority { WARNING, CRITICAL, EMERGENCY }

/// WARNING - need to look at alert
/// CRITICAL - can break a feature / user impacting
/// EMERGENCY - business impacting, even with low instances alert should come

