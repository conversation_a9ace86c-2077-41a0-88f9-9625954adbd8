import 'base.dart';

abstract class InfoEvent extends BaseLoggerEvent {
  InfoEvent({required String super.page, super.journey, super.data});

  @override
  Map<String, dynamic> getData() {
    final baseEventProperties = super.getData();
    final combinedProperties = {
      ...baseEventProperties,
      ...getEventSpecificData()
    };
    combinedProperties.removeWhere((key, value) => value == null);
    return combinedProperties;
  }

  Map<String, dynamic> getEventSpecificData();
}