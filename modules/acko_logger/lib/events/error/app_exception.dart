import '../contract/error.dart';

class AppExceptionEvent extends ErrorEvent {
  AppExceptionEvent(
      {super.page,
      super.journey,
      super.data,
      super.error,
      required super.errorMessage,
      super.stackTrace,
      ErrorPriority? errorPriority})
      : super(errorPriority: errorPriority ?? ErrorPriority.WARNING, exceptionType: "AppException");

  @override
  String getMessage() {
    return "App exception found : $errorMessage";
  }

  @override
  Map<String, dynamic> getEventSpecificData() {
    return {};
  }
}
