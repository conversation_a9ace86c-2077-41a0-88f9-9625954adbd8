import '../contract/error.dart';

class ApiErrorEvent extends Error<PERSON>vent {
  final String? path;
  final String? errorResponse;
  final String? request;
  final String? type;
  final String? failureReason;
  final String? failureStatus;
  final String? exceptionName;
  final String? curl;
  final Map<String, dynamic>? headers;
  final dynamic arguments;
  final String? queryParams;

  ApiErrorEvent({
    ErrorPriority errorPriority = ErrorPriority.CRITICAL,
    this.path,
    required String super.errorMessage,
    this.errorResponse,
    this.request,
    this.arguments,
    this.type,
    this.failureReason,
    this.failureStatus,
    this.exceptionName,
    this.curl,
    this.headers,
    this.queryParams,
    super.data,
    super.page,
    super.journey,
  }) : super(errorPriority: errorPriority, exceptionType: "ApiError");

  @override
  String getMessage() {
    return "api exception for path: $path ";
  }

  @override
  Map<String, dynamic> getEventSpecificData() {
    Map<String, dynamic> eventObj = {
      "path": path,
      'error_response': errorResponse,
      "request": request,
      "type": type,
      "failure_reason": failureReason,
      "failure_status": failureStatus,
      "exception_name": exceptionName,
      "curl": curl,
      'headers': headers,
      'arguments': arguments,
      'query_params': queryParams,
    };

    return eventObj;
  }
}
