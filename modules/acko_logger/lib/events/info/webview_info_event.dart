import '../contract/info.dart';

class WebviewInfoEvent extends InfoEvent {
  final String? webviewCurrentState;
  final String url;

  WebviewInfoEvent(
      {required this.webviewCurrentState,
      required this.url,
      required String super.page,
      super.data,
      super.journey});

  @override
  String getMessage() {
    return "webview info log:: state: $webviewCurrentState for url: $url";
  }

  @override
  Map<String, dynamic> getEventSpecificData() {
    return {
      "state": webviewCurrentState,
      "url": url,
    };
  }
}
