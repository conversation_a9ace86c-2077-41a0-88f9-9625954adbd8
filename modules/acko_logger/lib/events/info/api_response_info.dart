import '../contract/info.dart';

class ApiResponseInfoEvent extends InfoEvent {
  final String path;
  final int statusCode;
  final String? type;
  final String response;
  final String? curl;
  final dynamic arguments;
  final Map<String, dynamic>? headers;

  ApiResponseInfoEvent({
    required String super.page,
    required this.path,
    required this.statusCode,
    required this.response,
    this.type,
    this.headers,
    super.journey,
    super.data,
    this.curl,
    this.arguments,
  });

  @override
  String getMessage() {
    return "Api info log for path: $path statusCode: $statusCode response: $response";
  }

  @override
  Map<String, dynamic> getEventSpecificData() {
    return {
      "path": path,
      "type": type,
      "statusCode": statusCode,
      "response": response,
      "curl": curl,
      "arguments": arguments,
      "headers": headers,
    };
  }
}
