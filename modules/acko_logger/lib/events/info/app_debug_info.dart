import '../contract/info.dart';

class AppDebugInfoEvent extends InfoEvent {
  final String infoMessage;

  AppDebugInfoEvent(
      {required String super.page,
      super.journey,
      required this.infoMessage,
      super.data});

  @override
  String getMessage() {
    return "App info log:: $infoMessage";
  }

  @override
  Map<String, dynamic> getEventSpecificData() {
    return {
      "info_message": infoMessage,
    };
  }
}
