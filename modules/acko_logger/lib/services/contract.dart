import 'package:acko_logger/platforms/platform_contract.dart';
import 'package:dio/dio.dart';

import '../events/contract/base.dart';
import '../events/contract/error.dart';
import '../model/user_context_model.dart';

abstract class LoggerServiceContract {

  Future<void> initialize(Map<String, dynamic>? config);

  Future<void> setUserContext(UserContextModel userContext);

  Future<void> logErrorEvent(ErrorEvent event);

  Future<void> logInfoEvent(BaseLoggerEvent event);

  void addCustomInterceptor(Function(Interceptor) addInterceptor);

  abstract LoggerPlatform platform;
}