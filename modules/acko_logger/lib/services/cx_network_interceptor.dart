import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:cx_flutter_plugin/cx_flutter_plugin.dart';

class CxDioInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    options.extra['startTime'] = DateTime.now();
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    final startTime = response.requestOptions.extra['startTime'] as DateTime?;
    final duration = startTime != null ? DateTime.now().difference(startTime) : Duration.zero;

    Map<String, dynamic> networkRequestContext = {
      'url': response.requestOptions.uri.toString(),
      'host': response.requestOptions.uri.host,
      'method': response.requestOptions.method,
      'status_code': response.statusCode,
      'duration': duration.inMilliseconds,
      'http_response_body_size': response?.data?.toString().length ?? 0,
      'fragments': response.requestOptions.uri.fragment,
      'schema': response.requestOptions.uri.scheme,
    };

    await CxFlutterPlugin.setNetworkRequestContext(networkRequestContext);

    super.onResponse(response, handler);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) async {
    final startTime = err.requestOptions.extra['startTime'] as DateTime?;
    final duration = startTime != null ? DateTime.now().difference(startTime) : Duration.zero;

    Map<String, dynamic> networkRequestContext = {
      'url': err.requestOptions.uri.toString(),
      'host': err.requestOptions.uri.host,
      'method': err.requestOptions.method,
      'status_code': err.response?.statusCode ?? -1,
      'duration': duration.inMilliseconds,
      'http_response_body_size': err.response?.data.toString().length ?? 0,
      'fragments': err.requestOptions.uri.fragment,
      'schema': err.requestOptions.uri.scheme,
    };

    await CxFlutterPlugin.setNetworkRequestContext(networkRequestContext);

    super.onError(err, handler);
  }
}
