import 'package:acko_logger/events/contract/error.dart';
import 'package:acko_logger/model/user_context_model.dart';
import 'package:acko_logger/platforms/platform_contract.dart';
import 'package:cx_flutter_plugin/cx_domain.dart';
import 'package:cx_flutter_plugin/cx_exporter_options.dart';
import 'package:cx_flutter_plugin/cx_flutter_plugin.dart';
import 'package:cx_flutter_plugin/cx_instrumentation_type.dart';
import 'package:cx_flutter_plugin/cx_types.dart';
import 'package:dio/dio.dart';
import '../events/contract/base.dart';
import 'contract.dart';
import 'cx_network_interceptor.dart';

class CorologixLogger extends LoggerServiceContract {
  bool isInitialized = false;
  
  @override
  LoggerPlatform platform = LoggerPlatform.corologix;

  final CxDioInterceptor cxDioInterceptor = CxDioInterceptor();

  @override
  Future<void> initialize(Map<String, dynamic>? config) async {
    bool isInitialized = await CxFlutterPlugin.isInitialized();
    if(isInitialized) return;

    var coralogixDomain = CXDomain.ap1;
    var options = CXExporterOptions(
      coralogixDomain: coralogixDomain,
      environment: config!['environment'],
      application: config['application_name'],
      enableSwizzling: false,
      version: config['application_version'],
      publicKey: config['coralogix_key'],
      ignoreUrls: [],
      ignoreErrors: [],
      sdkSampler: 100,
      mobileVitalsFPSSamplingRate: 150,
      instrumentations: {
        CXInstrumentationType.anr.value: false,
        CXInstrumentationType.custom.value: true,
        CXInstrumentationType.errors.value: true,
        CXInstrumentationType.lifeCycle.value: false,
        CXInstrumentationType.mobileVitals.value: false,
        CXInstrumentationType.network.value: true,
        CXInstrumentationType.userActions.value: true,
      },
      collectIPData: true,
      debug: true,
    );
    await CxFlutterPlugin.initSdk(options);
    isInitialized = true;
  }

  @override
  Future<void> setUserContext(UserContextModel userContext) async{
    await _ensureInitialized();
    await CxFlutterPlugin.setUserContext(UserMetadata(
        userId: userContext.userId,
        userName: '',
        userEmail: userContext.email ?? "",
        userMetadata: userContext.metaData ?? {}));
  }

  Future<void> _ensureInitialized() async {
    if(!isInitialized) {
      await Future.delayed(const Duration(seconds: 1));
    }
  }

  @override
  Future<void> logErrorEvent(BaseLoggerEvent event) async {
    await _ensureInitialized();
    await CxFlutterPlugin.log(CxLogSeverity.error,
        event.getMessage(), event.getData());
  }

  @override
  Future<void> logInfoEvent(BaseLoggerEvent event) async{
    await _ensureInitialized();
    await CxFlutterPlugin.log(CxLogSeverity.info,
        event.getMessage(), event.getData());
  }

  @override
  void addCustomInterceptor(Function(Interceptor) addInterceptor) {
    addInterceptor(cxDioInterceptor);
  }


}
