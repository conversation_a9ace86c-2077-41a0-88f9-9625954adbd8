import 'package:acko_logger/model/user_context_model.dart';
import 'package:acko_logger/platforms/platform_contract.dart';
import 'package:acko_logger/services/contract.dart';
import 'package:dio/src/dio_mixin.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';

import '../events/contract/base.dart';
import '../events/contract/error.dart';

class FirebaseLogger extends LoggerServiceContract {
  @override
  Future<void> initialize(Map<String, dynamic>? config) async {
    if (Firebase.apps.isEmpty) {
      await Firebase.initializeApp();
    }
  }

  @override
  LoggerPlatform platform = LoggerPlatform.firebase;

  @override
  Future<void> setUserContext(UserContextModel userContext) async{
    FirebaseCrashlytics.instance.setUserIdentifier(userContext.phoneNumber);
  }

  @override
  Future<void> logErrorEvent(ErrorEvent event) async{
    if(event.error != null) {
      await FirebaseCrashlytics.instance.recordError(
          event.error, event.stackTrace);
    }else{
      await FirebaseCrashlytics.instance.recordError(
          event.getMessage(), event.stackTrace,
          reason: event.getData());
    }
  }

  @override
  Future<void> logInfoEvent(BaseLoggerEvent event) async{
    await FirebaseCrashlytics.instance.log("${event.getMessage()} |  data :: ${event.getData().toString()}");
  }

  @override
  void addCustomInterceptor(Function(Interceptor) addInterceptor) {
    // TODO: implement addNetworkPerformanceInterceptor
  }
}
