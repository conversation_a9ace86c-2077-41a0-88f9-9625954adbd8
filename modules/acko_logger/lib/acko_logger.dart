library acko_logger;

import 'package:acko_logger/model/user_context_model.dart';
import 'package:acko_logger/services/corologix.dart';
import 'package:acko_logger/services/firebase.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:dio/dio.dart';

import 'events/contract/base.dart';
import 'events/contract/error.dart';
import 'services/contract.dart';

class AckoLoggerManager {
  AckoLoggerManager._();

  bool _initCalled = false;

  static final instance = AckoLoggerManager._();

  final List<LoggerServiceContract> _loggerServicesContract = [
    FirebaseLogger(),
    CorologixLogger(),
  ];

  initialize(Map<String, dynamic> config) async {
    if (_initCalled) return;
    _initCalled = true;

    try {
      await Future.wait(
        _loggerServicesContract
            .map((loggerService) => loggerService.initialize(config)),
      );
    } catch (e, s) {
      FirebaseCrashlytics.instance.recordError(e, s);
    }
  }

  setUserContext(UserContextModel userContextModel) async {
    await Future.wait(
      _loggerServicesContract.map(
          (loggerService) => loggerService.setUserContext(userContextModel)),
    );
  }

  Future<void> logError({required ErrorEvent event}) async {
    await Future.wait(
      _loggerServicesContract
          .map((service) => service.logErrorEvent(event)),
    );
  }

  Future<void> logInfo(
      {required BaseLoggerEvent event}) async {
    await Future.wait(
      _loggerServicesContract
          .map((service) => service.logInfoEvent(event)),
    );
  }

  void addCustomInterceptor(Function(Interceptor) addInterceptor) {
    for (final service in _loggerServicesContract) {
      service.addCustomInterceptor(addInterceptor);
    }
  }
}
